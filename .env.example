# WhimLabAI Environment Configuration
# Copy this file to .env and update values as needed

# Database
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres123
POSTGRES_DB=whimlabai

# Redis
REDIS_PASSWORD=redis123

# RabbitMQ
RABBITMQ_USER=whimlab
RABBITMQ_PASS=rabbitmq123

# MinIO
MINIO_ROOT_USER=admin
MINIO_ROOT_PASSWORD=admin123

# JWT Configuration
JWT_SECRET=ThisIsAVeryLongSecretKeyForJWT123456789012345678901234567890

# Application Environment
ASPNETCORE_ENVIRONMENT=Development

# Optional: AI Provider Keys (add your actual keys)
# OPENAI_API_KEY=sk-...
# ANTHROPIC_API_KEY=sk-ant-...

# Optional: Payment Gateway Test Keys
ALIPAY_APP_ID=test-appid
ALIPAY_PRIVATE_KEY=test-private-key
ALIPAY_PUBLIC_KEY=test-public-key
WECHAT_APP_ID=test-appid
WECHAT_MCH_ID=test-mchid
WECHAT_API_KEY=test-apikey

# Optional: Email Service (for development, can use fake SMTP)
SMTP_HOST=smtp.example.com
SMTP_PORT=465
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=password

# Optional: Monitoring Services (for production)
GRAFANA_USER=admin
GRAFANA_PASSWORD=admin