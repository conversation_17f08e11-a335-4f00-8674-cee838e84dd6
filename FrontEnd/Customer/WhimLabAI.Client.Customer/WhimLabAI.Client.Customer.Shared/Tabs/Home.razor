﻿@page "/"
@using WhimLabAI.Client.Customer.Shared.Services
@using WhimLabAI.Shared.Dtos
@inject IFormFactor FormFactor
@inject IAuthService AuthService
@inject AgentService AgentService
@inject NavigationManager Navigation

<PageTitle>WhimLab AI - 企业级AI智能体平台</PageTitle>

<MContainer Class="pa-0" Fluid>
    <!-- 欢迎区域 -->
    <MContainer Class="primary lighten-2" Style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);" Fluid>
        <MContainer Class="py-16">
            <MRow Justify="@JustifyTypes.Center" Align="@AlignTypes.Center">
                <MCol Md="8" Cols="12" Class="text-center">
                    <h1 class="white--text display-3 font-weight-bold mb-4">WhimLab AI</h1>
                    <p class="white--text text-h5 mb-8">探索智能对话的无限可能，让AI成为您的得力助手</p>
                    
                    @if (isAuthenticated)
                    {
                        <MCard Class="mx-auto pa-4" MaxWidth="600" Elevation="0" Style="background-color: rgba(255,255,255,0.1); backdrop-filter: blur(10px);">
                            <div class="d-flex align-center justify-center">
                                <MAvatar Size="48" Class="mr-3">
                                    @if (!string.IsNullOrEmpty(userInfo?.Avatar))
                                    {
                                        <MImage Src="@userInfo.Avatar"></MImage>
                                    }
                                    else
                                    {
                                        <MIcon Size="32" Color="white">mdi-account</MIcon>
                                    }
                                </MAvatar>
                                <div class="text-left">
                                    <p class="white--text text-h6 mb-0">欢迎回来，@(userInfo?.Nickname ?? userInfo?.Username ?? "用户")！</p>
                                    <p class="white--text text-body-2 mb-0 opacity-8">开始探索AI的世界吧</p>
                                </div>
                            </div>
                        </MCard>
                    }
                    else
                    {
                        <div class="d-flex justify-center gap-4">
                            <MButton Large Color="white" Outlined Rounded OnClick="@(() => Navigation.NavigateTo("/auth/login"))">
                                <MIcon Left>mdi-login</MIcon>
                                登录
                            </MButton>
                            <MButton Large Color="white" Depressed Rounded OnClick="@(() => Navigation.NavigateTo("/auth/register"))">
                                <MIcon Left>mdi-account-plus</MIcon>
                                注册账号
                            </MButton>
                        </div>
                    }
                </MCol>
            </MRow>
        </MContainer>
    </MContainer>

    <!-- 热门AI代理展示 -->
    <MContainer Class="py-12">
        <MRow>
            <MCol Cols="12" Class="text-center mb-8">
                <h2 class="text-h3 font-weight-bold mb-2">热门AI代理</h2>
                <p class="text-h6 grey--text">精选最受欢迎的AI助手，满足您的各种需求</p>
            </MCol>
        </MRow>
        
        @if (isLoadingAgents)
        {
            <MRow>
                @for (int i = 0; i < 8; i++)
                {
                    <MCol Lg="3" Md="4" Sm="6" Cols="12" Class="mb-4">
                        <MSkeletonLoader Type="card" Height="280"></MSkeletonLoader>
                    </MCol>
                }
            </MRow>
        }
        else if (hotAgents != null && hotAgents.Any())
        {
            <MRow>
                @foreach (var agent in hotAgents)
                {
                    <MCol Lg="3" Md="4" Sm="6" Cols="12" Class="mb-4">
                        <MCard Height="280" Hover Style="cursor: pointer;" OnClick="@(() => NavigateToAgentDetail(agent.Id))">
                            <MCardText Class="pa-4">
                                <div class="d-flex align-center mb-3">
                                    <MAvatar Size="48" Color="primary lighten-2" Class="mr-3">
                                        @if (!string.IsNullOrEmpty(agent.Avatar))
                                        {
                                            <MImage Src="@agent.Avatar"></MImage>
                                        }
                                        else
                                        {
                                            <MIcon Size="28" Dark>mdi-robot</MIcon>
                                        }
                                    </MAvatar>
                                    <div>
                                        <h3 class="text-h6 mb-0">@agent.Name</h3>
                                        <MChip Small Label Color="@(agent.IsOfficial ? "success" : "grey")" TextColor="white" Class="mt-1">
                                            @(agent.IsOfficial ? "官方" : "社区")
                                        </MChip>
                                    </div>
                                </div>
                                
                                <p class="text-body-2 grey--text text--darken-1 mb-3" style="height: 60px; overflow: hidden;">
                                    @(agent.Description ?? "暂无描述")
                                </p>
                                
                                <MDivider Class="my-3"></MDivider>
                                
                                <div class="d-flex justify-space-between align-center">
                                    <div class="d-flex align-center">
                                        <MRating Value="@((double)agent.Rating)" 
                                                 Readonly 
                                                 Dense 
                                                 Size="18" 
                                                 Color="amber">
                                        </MRating>
                                        <span class="ml-2 text-body-2">@agent.Rating.ToString("F1")</span>
                                    </div>
                                    <div class="text-body-2 grey--text">
                                        <MIcon Small>mdi-message-text</MIcon>
                                        @FormatUsageCount(agent.ConversationCount)
                                    </div>
                                </div>
                            </MCardText>
                        </MCard>
                    </MCol>
                }
            </MRow>
        }
        else
        {
            <MRow>
                <MCol Cols="12" Class="text-center py-8">
                    <MIcon Size="64" Color="grey">mdi-robot-off</MIcon>
                    <p class="text-h6 grey--text mt-4">暂无热门AI代理</p>
                </MCol>
            </MRow>
        }
        
        <MRow>
            <MCol Cols="12" Class="text-center mt-4">
                <MButton Large Color="primary" Rounded OnClick="@(() => Navigation.NavigateTo("/agents/marketplace"))">
                    探索更多AI代理
                    <MIcon Right>mdi-arrow-right</MIcon>
                </MButton>
            </MCol>
        </MRow>
    </MContainer>

    <!-- 分类导航 -->
    <MContainer Class="grey lighten-5 py-12" Fluid>
        <MContainer>
            <MRow>
                <MCol Cols="12" Class="text-center mb-8">
                    <h2 class="text-h3 font-weight-bold mb-2">分类浏览</h2>
                    <p class="text-h6 grey--text">按类别快速找到您需要的AI助手</p>
                </MCol>
            </MRow>
            
            <MRow>
                @foreach (var category in categories)
                {
                    <MCol Lg="3" Md="4" Sm="6" Cols="12" Class="mb-4">
                        <MCard Hover Style="cursor: pointer;" OnClick="@(() => NavigateToCategory(category.Key))">
                            <MCardText Class="pa-4">
                                <div class="d-flex align-center justify-space-between">
                                    <div class="d-flex align-center">
                                        <MIcon Size="32" Color="@category.Color" Class="mr-3">@category.Icon</MIcon>
                                        <div>
                                            <h4 class="text-h6 mb-0">@category.Name</h4>
                                            <p class="text-body-2 grey--text mb-0">@category.Count 个代理</p>
                                        </div>
                                    </div>
                                    <MIcon Color="grey">mdi-chevron-right</MIcon>
                                </div>
                            </MCardText>
                        </MCard>
                    </MCol>
                }
            </MRow>
        </MContainer>
    </MContainer>

    <!-- 功能特色 -->
    <MContainer Class="py-12">
        <MRow>
            <MCol Cols="12" Class="text-center mb-8">
                <h2 class="text-h3 font-weight-bold mb-2">为什么选择 WhimLab AI</h2>
                <p class="text-h6 grey--text">专业、安全、高效的企业级AI平台</p>
            </MCol>
        </MRow>
        
        <MRow>
            <MCol Lg="4" Cols="12" Class="mb-6">
                <div class="text-center">
                    <MAvatar Size="80" Color="primary lighten-4" Class="mb-4">
                        <MIcon Size="40" Color="primary">mdi-message-text-outline</MIcon>
                    </MAvatar>
                    <h3 class="text-h5 mb-3">智能对话</h3>
                    <p class="text-body-1 grey--text text--darken-1">
                        基于先进的AI模型，提供自然流畅的对话体验，理解上下文，精准回应您的需求
                    </p>
                </div>
            </MCol>
            
            <MCol Lg="4" Cols="12" Class="mb-6">
                <div class="text-center">
                    <MAvatar Size="80" Color="success lighten-4" Class="mb-4">
                        <MIcon Size="40" Color="success">mdi-image-multiple</MIcon>
                    </MAvatar>
                    <h3 class="text-h5 mb-3">多模态支持</h3>
                    <p class="text-body-1 grey--text text--darken-1">
                        支持文本、图片、文件等多种输入方式，满足不同场景下的AI交互需求
                    </p>
                </div>
            </MCol>
            
            <MCol Lg="4" Cols="12" Class="mb-6">
                <div class="text-center">
                    <MAvatar Size="80" Color="warning lighten-4" Class="mb-4">
                        <MIcon Size="40" Color="warning">mdi-shield-check</MIcon>
                    </MAvatar>
                    <h3 class="text-h5 mb-3">安全可靠</h3>
                    <p class="text-body-1 grey--text text--darken-1">
                        企业级安全防护，数据加密传输，严格的隐私保护，让您安心使用AI服务
                    </p>
                </div>
            </MCol>
        </MRow>
    </MContainer>

    <!-- 快速开始引导 -->
    <MContainer Class="primary lighten-5 py-12" Fluid>
        <MContainer>
            <MRow>
                <MCol Cols="12" Class="text-center mb-8">
                    <h2 class="text-h3 font-weight-bold mb-2">快速开始</h2>
                    <p class="text-h6 grey--text">简单三步，开启您的AI之旅</p>
                </MCol>
            </MRow>
            
            <MRow>
                <MCol Md="4" Cols="12" Class="mb-6">
                    <div class="text-center">
                        <MAvatar Size="64" Color="primary" Class="mb-4">
                            <span class="white--text text-h4">1</span>
                        </MAvatar>
                        <h3 class="text-h5 mb-2">选择AI代理</h3>
                        <p class="text-body-1 grey--text text--darken-1">
                            浏览AI市场，选择适合您需求的AI助手
                        </p>
                    </div>
                </MCol>
                
                <MCol Md="4" Cols="12" Class="mb-6">
                    <div class="text-center">
                        <MAvatar Size="64" Color="primary" Class="mb-4">
                            <span class="white--text text-h4">2</span>
                        </MAvatar>
                        <h3 class="text-h5 mb-2">开始对话</h3>
                        <p class="text-body-1 grey--text text--darken-1">
                            点击开始对话，向AI提出您的问题或需求
                        </p>
                    </div>
                </MCol>
                
                <MCol Md="4" Cols="12" Class="mb-6">
                    <div class="text-center">
                        <MAvatar Size="64" Color="primary" Class="mb-4">
                            <span class="white--text text-h4">3</span>
                        </MAvatar>
                        <h3 class="text-h5 mb-2">获得帮助</h3>
                        <p class="text-body-1 grey--text text--darken-1">
                            AI即刻响应，为您提供专业的解答和帮助
                        </p>
                    </div>
                </MCol>
            </MRow>
            
            @if (!isAuthenticated)
            {
                <MRow>
                    <MCol Cols="12" Class="text-center mt-6">
                        <MButton XLarge Color="primary" Rounded Depressed OnClick="@(() => Navigation.NavigateTo("/auth/register"))">
                            立即注册，免费体验
                            <MIcon Right>mdi-arrow-right</MIcon>
                        </MButton>
                    </MCol>
                </MRow>
            }
        </MContainer>
    </MContainer>
</MContainer>

@code {
    private bool isAuthenticated = false;
    private UserInfo? userInfo;
    private bool isLoadingAgents = true;
    private List<AgentMarketplaceDto>? hotAgents;
    
    private class CategoryInfo
    {
        public string Key { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public string Color { get; set; } = string.Empty;
        public int Count { get; set; }
    }
    
    private List<CategoryInfo> categories = new()
    {
        new CategoryInfo { Key = "writing", Name = "写作助手", Icon = "mdi-pencil", Color = "blue", Count = 0 },
        new CategoryInfo { Key = "coding", Name = "编程工具", Icon = "mdi-code-braces", Color = "green", Count = 0 },
        new CategoryInfo { Key = "data", Name = "数据分析", Icon = "mdi-chart-line", Color = "orange", Count = 0 },
        new CategoryInfo { Key = "design", Name = "设计创意", Icon = "mdi-palette", Color = "purple", Count = 0 },
        new CategoryInfo { Key = "education", Name = "教育学习", Icon = "mdi-school", Color = "indigo", Count = 0 },
        new CategoryInfo { Key = "business", Name = "商业办公", Icon = "mdi-briefcase", Color = "teal", Count = 0 },
        new CategoryInfo { Key = "life", Name = "生活助手", Icon = "mdi-home-heart", Color = "pink", Count = 0 },
        new CategoryInfo { Key = "other", Name = "其他工具", Icon = "mdi-dots-horizontal", Color = "grey", Count = 0 }
    };

    protected override async Task OnInitializedAsync()
    {
        // 检查认证状态
        isAuthenticated = await AuthService.IsAuthenticatedAsync();
        if (isAuthenticated)
        {
            userInfo = await AuthService.GetUserInfoAsync();
        }
        
        // 加载热门AI代理
        await LoadHotAgents();
        
        // 加载分类统计
        await LoadCategoryCounts();
    }
    
    private async Task LoadHotAgents()
    {
        try
        {
            isLoadingAgents = true;
            
            // 获取热门代理（按使用量排序，获取前8个）
            var searchDto = new AgentSearchDto
            {
                Page = 1,
                PageSize = 8,
                SortBy = "usage"
            };
            
            var result = await AgentService.GetMarketplaceAgentsAsync(searchDto);
            if (result != null)
            {
                hotAgents = result?.Take(8).ToList();
            }
        }
        finally
        {
            isLoadingAgents = false;
        }
    }
    
    private async Task LoadCategoryCounts()
    {
        var counts = await AgentService.GetCategoryCounts();
        if (counts != null)
        {
            foreach (var category in categories)
            {
                if (counts.TryGetValue(category.Key, out var count))
                {
                    category.Count = count;
                }
            }
        }
    }
    
    private void NavigateToAgentDetail(Guid agentId)
    {
        Navigation.NavigateTo($"/agents/{agentId}");
    }
    
    private void NavigateToCategory(string category)
    {
        Navigation.NavigateTo($"/agents/marketplace?category={category}");
    }
    
    private string FormatUsageCount(int count)
    {
        if (count >= 10000)
        {
            return $"{count / 1000}k+";
        }
        else if (count >= 1000)
        {
            return $"{count / 1000.0:F1}k";
        }
        return count.ToString();
    }
}
