@page "/profile"
@using WhimLabAI.Client.Customer.Shared.Services
@using WhimLabAI.Shared.Dtos.Customer.Profile
@using WhimLabAI.Shared.Enums
@using WhimLabAI.Shared.Dtos.Customer.Security
@inject HttpClient Http
@inject NavigationManager Navigation
@inject IClipboardService ClipboardService
@inject IConfirmService ConfirmService
@inject ProfileService ProfileService
@inject IAuthService AuthService
@inject TwoFactorService TwoFactorService
@inject IDownloadService DownloadService

<PageTitle>个人中心 - WhimLab AI</PageTitle>

<style>
    .profile-header {
        background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
        color: white;
        padding: 48px 24px;
        margin: -24px -24px 32px -24px;
        text-align: center;
    }

    .avatar-upload {
        position: relative;
        display: inline-block;
    }

    .avatar-upload-overlay {
        position: absolute;
        bottom: 0;
        right: 0;
        background: rgba(0, 0, 0, 0.7);
        color: white;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s;
    }

    .avatar-upload-overlay:hover {
        background: rgba(0, 0, 0, 0.9);
        transform: scale(1.1);
    }

    .security-item {
        padding: 16px;
        border-radius: 8px;
        transition: background 0.2s;
    }

    .security-item:hover {
        background: rgba(255, 255, 255, 0.05);
    }

    .api-key-item {
        font-family: monospace;
        background: rgba(255, 255, 255, 0.05);
        padding: 12px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
</style>

<div class="pa-6">
    <!-- 个人资料头部 -->
    <div class="profile-header">
        <div class="avatar-upload">
            <MAvatar Size="120">
                @if (!string.IsNullOrEmpty(_profile.Avatar))
                {
                    <MImage Src="@_profile.Avatar" />
                }
                else
                {
                    <span class="text-h3">@_profile.Nickname.Substring(0, 1)</span>
                }
            </MAvatar>
            <div class="avatar-upload-overlay" @onclick="UploadAvatar">
                <MIcon>mdi-camera</MIcon>
            </div>
            <InputFile id="avatar-upload" accept="image/*" hidden @change="OnAvatarSelected" />
        </div>
        <h1 class="text-h4 mt-4">@_profile.Nickname</h1>
        <p class="text-h6">@_profile.Username</p>
        <MChip Color="white" Text Class="mt-2">
            <MIcon Left>mdi-calendar-check</MIcon>
            加入于 @_profile.CreatedAt.ToString("yyyy年MM月dd日")
        </MChip>
    </div>

    <MRow>
        <!-- 左侧基本信息 -->
        <MCol Cols="12" Md="8">
            <!-- 基本信息 -->
            <MCard Class="mb-4">
                <MCardTitle>
                    <MIcon Class="mr-2">mdi-account-circle</MIcon>
                    基本信息
                    <MSpacer />
                    @if (!_editingProfile)
                    {
                        <MButton Text Small Color="primary" OnClick="() => _editingProfile = true">
                            <MIcon Left>mdi-pencil</MIcon>
                            编辑
                        </MButton>
                    }
                </MCardTitle>
                <MCardText>
                    @if (_editingProfile)
                    {
                        <MForm @ref="_profileForm">
                            <MRow>
                                <MCol Cols="12" Md="6">
                                    <MTextField @bind-Value="_profileEdit.Nickname"
                                               Label="昵称"
                                               Outlined
                                               Dense
                                               Counter="20"
                                               MaxLength="20" />
                                </MCol>
                                <MCol Cols="12" Md="6">
                                    <MTextField @bind-Value="_profileEdit.Email"
                                               Label="邮箱"
                                               Type="email"
                                               Outlined
                                               Dense />
                                </MCol>
                                <MCol Cols="12" Md="6">
                                    <MTextField @bind-Value="_profileEdit.Phone"
                                               Label="手机号"
                                               Outlined
                                               Dense />
                                </MCol>
                                <MCol Cols="12" Md="6">
                                    <MSelect TItem="GenderOption" TItemValue="string" TValue="string" @bind-Value="_profileEdit.Gender"
                                            Label="性别"
                                            Outlined
                                            Dense
                                            Items="_genderOptions"
                                            ItemText="item => item.Text"
                                            ItemValue="item => item.Value">
                                    </MSelect>
                                </MCol>
                                <MCol Cols="12">
                                    <MTextarea @bind-Value="_profileEdit.Bio"
                                              Label="个人简介"
                                              Outlined
                                              Rows="3"
                                              Counter="200"
                                              MaxLength="200" />
                                </MCol>
                                <MCol Cols="12" Class="text-right">
                                    <MButton Text OnClick="CancelEditProfile">取消</MButton>
                                    <MButton Color="primary" OnClick="SaveProfile" Loading="@_savingProfile">
                                        保存
                                    </MButton>
                                </MCol>
                            </MRow>
                        </MForm>
                    }
                    else
                    {
                        <MList Dense>
                            <MListItem>
                                <MListItemContent>
                                    <MListItemTitle>用户名</MListItemTitle>
                                    <MListItemSubtitle>@_profile.Username</MListItemSubtitle>
                                </MListItemContent>
                            </MListItem>
                            <MListItem>
                                <MListItemContent>
                                    <MListItemTitle>邮箱</MListItemTitle>
                                    <MListItemSubtitle>
                                        @_profile.Email
                                        @if (_profile.EmailVerified)
                                        {
                                            <MChip Small Color="success" Class="ml-2">
                                                <MIcon Left Small>mdi-check</MIcon>
                                                已验证
                                            </MChip>
                                        }
                                        else
                                        {
                                            <MChip Small Color="warning" Class="ml-2">
                                                未验证
                                            </MChip>
                                        }
                                    </MListItemSubtitle>
                                </MListItemContent>
                            </MListItem>
                            <MListItem>
                                <MListItemContent>
                                    <MListItemTitle>手机号</MListItemTitle>
                                    <MListItemSubtitle>@(_profile.Phone ?? "未设置")</MListItemSubtitle>
                                </MListItemContent>
                            </MListItem>
                            <MListItem>
                                <MListItemContent>
                                    <MListItemTitle>性别</MListItemTitle>
                                    <MListItemSubtitle>@(string.IsNullOrEmpty(_profile.Gender) ? "保密" : _profile.Gender)</MListItemSubtitle>
                                </MListItemContent>
                            </MListItem>
                            <MListItem>
                                <MListItemContent>
                                    <MListItemTitle>个人简介</MListItemTitle>
                                    <MListItemSubtitle>@(_profile.Bio ?? "暂无简介")</MListItemSubtitle>
                                </MListItemContent>
                            </MListItem>
                        </MList>
                    }
                </MCardText>
            </MCard>

            <!-- 安全设置 -->
            <MCard Class="mb-4">
                <MCardTitle>
                    <MIcon Class="mr-2">mdi-shield-check</MIcon>
                    安全设置
                </MCardTitle>
                <MCardText>
                    <div class="security-item" @onclick="ShowChangePassword">
                        <MRow Align="@("center")">
                            <MCol Cols="@("auto")">
                                <MIcon Size="32" Color="primary">mdi-lock-reset</MIcon>
                            </MCol>
                            <MCol>
                                <div class="font-weight-medium">修改密码</div>
                                <div class="text-caption">上次修改：@_profile.LastPasswordChange.ToString("yyyy-MM-dd")</div>
                            </MCol>
                            <MCol Cols="@("auto")">
                                <MIcon>mdi-chevron-right</MIcon>
                            </MCol>
                        </MRow>
                    </div>

                    <div class="security-item mt-3" @onclick="ShowTwoFactorAuth">
                        <MRow Align="@("center")">
                            <MCol Cols="@("auto")">
                                <MIcon Size="32" Color="@(_profile.TwoFactorEnabled ? "success" : "grey")">mdi-shield-lock</MIcon>
                            </MCol>
                            <MCol>
                                <div class="font-weight-medium">两步验证</div>
                                <div class="text-caption">
                                    @if (_profile.TwoFactorEnabled)
                                    {
                                        <span class="success--text">已启用</span>
                                    }
                                    else
                                    {
                                        <span class="warning--text">未启用</span>
                                    }
                                </div>
                            </MCol>
                            <MCol Cols="@("auto")">
                                <MIcon>mdi-chevron-right</MIcon>
                            </MCol>
                        </MRow>
                    </div>

                    <div class="security-item mt-3" @onclick="ShowLoginHistory">
                        <MRow Align="@("center")">
                            <MCol Cols="@("auto")">
                                <MIcon Size="32" Color="info">mdi-history</MIcon>
                            </MCol>
                            <MCol>
                                <div class="font-weight-medium">登录历史</div>
                                <div class="text-caption">查看最近的登录记录</div>
                            </MCol>
                            <MCol Cols="@("auto")">
                                <MIcon>mdi-chevron-right</MIcon>
                            </MCol>
                        </MRow>
                    </div>
                </MCardText>
            </MCard>

            <!-- API密钥 -->
            <MCard Class="mb-4">
                <MCardTitle>
                    <MIcon Class="mr-2">mdi-key</MIcon>
                    API密钥
                    <MSpacer />
                    <MButton Text Small Color="primary" OnClick="ShowCreateApiKey">
                        <MIcon Left>mdi-plus</MIcon>
                        创建新密钥
                    </MButton>
                </MCardTitle>
                <MCardText>
                    @if (_apiKeys.Any())
                    {
                        <MList Dense>
                            @foreach (var key in _apiKeys)
                            {
                                <MListItem>
                                    <MListItemContent>
                                        <MListItemTitle>@key.Name</MListItemTitle>
                                        <MListItemSubtitle>
                                            <div class="api-key-item mt-2">
                                                <code>@key.MaskedKey</code>
                                                <div>
                                                    <MButton Icon Small OnClick="() => CopyApiKey(key)">
                                                        <MIcon>mdi-content-copy</MIcon>
                                                    </MButton>
                                                    <MButton Icon Small Color="error" OnClick="() => DeleteApiKey(key)">
                                                        <MIcon>mdi-delete</MIcon>
                                                    </MButton>
                                                </div>
                                            </div>
                                            <div class="text-caption mt-1">
                                                创建于: @key.CreatedAt.ToString("yyyy-MM-dd") |
                                                上次使用: @(key.LastUsedAt?.ToString("yyyy-MM-dd") ?? "从未使用")
                                            </div>
                                        </MListItemSubtitle>
                                    </MListItemContent>
                                </MListItem>
                            }
                        </MList>
                    }
                    else
                    {
                        <div class="text-center py-6">
                            <MIcon Size="48" Color="grey">mdi-key-off</MIcon>
                            <p class="mt-2">您还没有创建任何API密钥</p>
                        </div>
                    }
                </MCardText>
            </MCard>
        </MCol>

        <!-- 右侧信息 -->
        <MCol Cols="12" Md="4">
            <!-- 通知设置 -->
            <MCard Class="mb-4">
                <MCardTitle>
                    <MIcon Class="mr-2">mdi-bell</MIcon>
                    通知设置
                </MCardTitle>
                <MCardText>
                    <MList Dense>
                        <MListItem>
                            <MListItemContent>
                                <MListItemTitle>邮件通知</MListItemTitle>
                            </MListItemContent>
                            <MListItemAction>
                                <MSwitch @bind-Value="_notifications.Email" Dense />
                            </MListItemAction>
                        </MListItem>
                        <MListItem>
                            <MListItemContent>
                                <MListItemTitle>短信通知</MListItemTitle>
                            </MListItemContent>
                            <MListItemAction>
                                <MSwitch @bind-Value="_notifications.Sms" Dense />
                            </MListItemAction>
                        </MListItem>
                        <MListItem>
                            <MListItemContent>
                                <MListItemTitle>站内通知</MListItemTitle>
                            </MListItemContent>
                            <MListItemAction>
                                <MSwitch @bind-Value="_notifications.InApp" Dense />
                            </MListItemAction>
                        </MListItem>
                    </MList>

                    <MSubheader Class="mt-4">通知类型</MSubheader>
                    <MList Dense>
                        <MListItem>
                            <MListItemContent>
                                <MListItemTitle>订阅提醒</MListItemTitle>
                            </MListItemContent>
                            <MListItemAction>
                                <MSwitch @bind-Value="_notifications.Subscription" Dense />
                            </MListItemAction>
                        </MListItem>
                        <MListItem>
                            <MListItemContent>
                                <MListItemTitle>新功能通知</MListItemTitle>
                            </MListItemContent>
                            <MListItemAction>
                                <MSwitch @bind-Value="_notifications.Features" Dense />
                            </MListItemAction>
                        </MListItem>
                        <MListItem>
                            <MListItemContent>
                                <MListItemTitle>营销活动</MListItemTitle>
                            </MListItemContent>
                            <MListItemAction>
                                <MSwitch @bind-Value="_notifications.Marketing" Dense />
                            </MListItemAction>
                        </MListItem>
                    </MList>

                    <MButton Block Color="primary" Small Class="mt-4" OnClick="SaveNotificationSettings" Loading="@_savingNotifications">
                        保存设置
                    </MButton>
                </MCardText>
            </MCard>

            <!-- 账户操作 -->
            <MCard>
                <MCardTitle>
                    <MIcon Class="mr-2">mdi-account-cog</MIcon>
                    账户操作
                </MCardTitle>
                <MCardText>
                    <MButton Block Color="warning" Outlined OnClick="ExportData">
                        <MIcon Left>mdi-download</MIcon>
                        导出我的数据
                    </MButton>
                    <MButton Block Color="error" Outlined Class="mt-3" OnClick="ShowDeleteAccount">
                        <MIcon Left>mdi-delete-forever</MIcon>
                        删除账户
                    </MButton>
                </MCardText>
            </MCard>
        </MCol>
    </MRow>
</div>

<!-- 修改密码对话框 -->
<MDialog @bind-Value="_showPasswordDialog" MaxWidth="500">
    <MCard>
        <MCardTitle>修改密码</MCardTitle>
        <MCardText>
            <MForm @ref="_passwordForm">
                <MTextField @bind-Value="_passwordChange.CurrentPassword"
                           Label="当前密码"
                           Type="password"
                           Outlined
                           Dense
                           Class="mb-3" />
                <MTextField @bind-Value="_passwordChange.NewPassword"
                           Label="新密码"
                           Type="password"
                           Outlined
                           Dense
                           Class="mb-3" />
                <MTextField @bind-Value="_passwordChange.ConfirmPassword"
                           Label="确认新密码"
                           Type="password"
                           Outlined
                           Dense />
            </MForm>
        </MCardText>
        <MCardActions>
            <MSpacer />
            <MButton Text OnClick="() => _showPasswordDialog = false">取消</MButton>
            <MButton Color="primary" OnClick="ChangePassword" Loading="@_changingPassword">确认</MButton>
        </MCardActions>
    </MCard>
</MDialog>

<!-- 创建API密钥对话框 -->
<MDialog @bind-Value="_showApiKeyDialog" MaxWidth="500">
    <MCard>
        <MCardTitle>创建API密钥</MCardTitle>
        <MCardText>
            @if (string.IsNullOrEmpty(_newApiKey))
            {
                <MTextField @bind-Value="_apiKeyName"
                           Label="密钥名称"
                           Outlined
                           Dense
                           Placeholder="例如：我的应用" />
            }
            else
            {
                <MAlert Type="AlertTypes.Warning" Class="mb-4">
                    请妥善保存此密钥，一旦关闭此窗口将无法再次查看完整密钥
                </MAlert>
                <div class="api-key-item">
                    <code>@_newApiKey</code>
                    <MButton Icon Small OnClick="() => CopyText(_newApiKey)">
                        <MIcon>mdi-content-copy</MIcon>
                    </MButton>
                </div>
            }
        </MCardText>
        <MCardActions>
            <MSpacer />
            @if (string.IsNullOrEmpty(_newApiKey))
            {
                <MButton Text OnClick="() => _showApiKeyDialog = false">取消</MButton>
                <MButton Color="primary" OnClick="CreateApiKey" Loading="@_creatingApiKey">创建</MButton>
            }
            else
            {
                <MButton Color="primary" OnClick="() => { _showApiKeyDialog = false; _newApiKey = string.Empty; }">完成</MButton>
            }
        </MCardActions>
    </MCard>
</MDialog>

<!-- 删除账户确认对话框 -->
<MDialog @bind-Value="_showDeleteDialog" MaxWidth="500">
    <MCard>
        <MCardTitle Class="error--text">删除账户</MCardTitle>
        <MCardText>
            <MAlert Type="AlertTypes.Error" Class="mb-4">
                <strong>警告：</strong>此操作不可恢复！删除账户后，您的所有数据将被永久删除。
            </MAlert>

            <p>删除账户将会：</p>
            <ul>
                <li>删除所有对话记录</li>
                <li>取消所有订阅</li>
                <li>删除所有自定义Agent</li>
                <li>清空所有个人数据</li>
            </ul>

            <MTextField @bind-Value="_deleteConfirmation"
                       Label='输入\"DELETE\"确认删除'
                       Outlined
                       Dense
                       Class="mt-4" />
        </MCardText>
        <MCardActions>
            <MSpacer />
            <MButton Text OnClick="() => _showDeleteDialog = false">取消</MButton>
            <MButton Color="error" OnClick="DeleteAccount" Loading="@_deletingAccount" Disabled="@(_deleteConfirmation != "DELETE")">
                永久删除
            </MButton>
        </MCardActions>
    </MCard>
</MDialog>

<!-- 两步验证对话框 -->
<MDialog @bind-Value="_showTwoFactorDialog" MaxWidth="600" Persistent>
    <MCard>
        <MCardTitle>
            <MIcon Class="mr-2">mdi-shield-lock</MIcon>
            两步验证设置
        </MCardTitle>
        <MCardText>
            @if (_twoFactorStep == 1)
            {
                <!-- 步骤1：输入密码 -->
                <div>
                    <MAlert Type="AlertTypes.Info" Class="mb-4">
                        启用两步验证可以大幅提高您账户的安全性。启用后，登录时除了密码外还需要输入手机应用生成的验证码。
                    </MAlert>

                    <MTextField @bind-Value="_twoFactorPassword"
                               Label="请输入您的密码"
                               Type="password"
                               Outlined
                               Dense
                               Placeholder="输入密码以继续" />
                </div>
            }
            else if (_twoFactorStep == 2)
            {
                <!-- 步骤2：显示QR码和密钥 -->
                <div>
                    <MStepper Value="2" Class="mb-4">
                        <MStepperItems>
                            <MStepperStep Complete Step="1">验证身份</MStepperStep>
                            <MStepperStep Step="2">设置验证器</MStepperStep>
                            <MStepperStep Step="3">确认设置</MStepperStep>
                        </MStepperItems>
                    </MStepper>

                    <h3 class="text-h6 mb-4">使用验证器应用扫描二维码</h3>

                    <MRow>
                        <MCol Cols="12" Md="6" Class="text-center">
                            @if (_twoFactorSetup != null)
                            {
                                <MCard Flat Class="pa-4 grey lighten-4">
                                    <img src="@($"https://api.qrserver.com/v1/create-qr-code/?size=200x200&data={Uri.EscapeDataString(_twoFactorSetup.QrCodeUri)}")"
                                         alt="Two Factor QR Code"
                                         style="width: 200px; height: 200px;" />
                                </MCard>
                            }
                        </MCol>
                        <MCol Cols="12" Md="6">
                            <p class="text-body-2 mb-3">推荐使用以下验证器应用：</p>
                            <MList Dense>
                                <MListItem>
                                    <MListItemIcon>
                                        <MIcon Color="green">mdi-google</MIcon>
                                    </MListItemIcon>
                                    <MListItemContent>
                                        <MListItemTitle>Google Authenticator</MListItemTitle>
                                    </MListItemContent>
                                </MListItem>
                                <MListItem>
                                    <MListItemIcon>
                                        <MIcon Color="blue">mdi-microsoft</MIcon>
                                    </MListItemIcon>
                                    <MListItemContent>
                                        <MListItemTitle>Microsoft Authenticator</MListItemTitle>
                                    </MListItemContent>
                                </MListItem>
                                <MListItem>
                                    <MListItemIcon>
                                        <MIcon Color="indigo">mdi-cellphone-key</MIcon>
                                    </MListItemIcon>
                                    <MListItemContent>
                                        <MListItemTitle>Authy</MListItemTitle>
                                    </MListItemContent>
                                </MListItem>
                            </MList>
                        </MCol>
                    </MRow>

                    <MDivider Class="my-4" />

                    <h4 class="text-subtitle-1 mb-2">无法扫描？手动输入密钥：</h4>
                    @if (_twoFactorSetup != null)
                    {
                        <MAlert Type="AlertTypes.Warning" Dense Border="Borders.Left">
                            <div class="d-flex align-center justify-space-between">
                                <code style="font-size: 14px;">@_twoFactorSetup.ManualEntryKey</code>
                                <MButton Icon Small OnClick="() => CopyText(_twoFactorSetup.ManualEntryKey)">
                                    <MIcon>mdi-content-copy</MIcon>
                                </MButton>
                            </div>
                        </MAlert>
                    }

                    <MDivider Class="my-4" />

                    <h4 class="text-subtitle-1 mb-2">输入验证码确认：</h4>
                    <MTextField @bind-Value="_twoFactorCode"
                               Label="6位验证码"
                               Outlined
                               Dense
                               MaxLength="6"
                               Placeholder="000000" />
                </div>
            }
            else if (_twoFactorStep == 3)
            {
                <!-- 步骤3：已启用，显示管理界面 -->
                <div>
                    @if (_showRecoveryCodes && _recoveryCodes.Any())
                    {
                        <!-- 显示恢复码 -->
                        <MAlert Type="AlertTypes.Success" Class="mb-4">
                            <h4 class="text-h6 mb-2">恢复码已生成</h4>
                            <p class="text-body-2">请将这些恢复码保存在安全的地方。每个恢复码只能使用一次。</p>
                        </MAlert>

                        <MCard Outlined Class="pa-4 mb-4">
                            <MRow Dense>
                                @foreach (var recoveryCode in _recoveryCodes)
                                {
                                    <MCol Cols="6" Sm="4" Md="3" Class="text-center pa-2">
                                        <code style="font-size: 14px;">@recoveryCode</code>
                                    </MCol>
                                }
                            </MRow>
                        </MCard>

                        <div class="d-flex gap-2 mb-4">
                            <MButton Color="primary" Small OnClick="DownloadRecoveryCodes">
                                <MIcon Left>mdi-download</MIcon>
                                下载恢复码
                            </MButton>
                            <MButton Outlined Small OnClick="CopyRecoveryCodes">
                                <MIcon Left>mdi-content-copy</MIcon>
                                复制恢复码
                            </MButton>
                        </div>
                    }
                    else
                    {
                        <!-- 管理界面 -->
                        <MAlert Type="AlertTypes.Success" Class="mb-4">
                            <MIcon Left>mdi-check-circle</MIcon>
                            两步验证已启用
                        </MAlert>

                        <MList ThreeLine>
                            <MListItem>
                                <MListItemIcon>
                                    <MIcon Size="40" Color="success">mdi-shield-check</MIcon>
                                </MListItemIcon>
                                <MListItemContent>
                                    <MListItemTitle>状态：已启用</MListItemTitle>
                                    <MListItemSubtitle>您的账户已受到两步验证保护</MListItemSubtitle>
                                </MListItemContent>
                            </MListItem>

                            <MDivider />

                            <MListItem>
                                <MListItemIcon>
                                    <MIcon Size="40" Color="orange">mdi-key-variant</MIcon>
                                </MListItemIcon>
                                <MListItemContent>
                                    <MListItemTitle>恢复码</MListItemTitle>
                                    <MListItemSubtitle>
                                        剩余 @_remainingRecoveryCodesCount 个可用恢复码
                                        @if (_remainingRecoveryCodesCount < 3)
                                        {
                                            <span class="error--text">（建议重新生成）</span>
                                        }
                                    </MListItemSubtitle>
                                </MListItemContent>
                                <MListItemAction>
                                    <MButton Small Color="primary" OnClick="RegenerateRecoveryCodes">
                                        重新生成
                                    </MButton>
                                </MListItemAction>
                            </MListItem>

                            <MDivider />

                            <MListItem>
                                <MListItemIcon>
                                    <MIcon Size="40" Color="error">mdi-shield-off</MIcon>
                                </MListItemIcon>
                                <MListItemContent>
                                    <MListItemTitle>禁用两步验证</MListItemTitle>
                                    <MListItemSubtitle>如需禁用，请输入密码和验证码</MListItemSubtitle>
                                </MListItemContent>
                            </MListItem>
                        </MList>

                        <MExpansionPanels Class="mt-4">
                            <MExpansionPanel>
                                <MExpansionPanelHeader>
                                    <span class="error--text">禁用两步验证</span>
                                </MExpansionPanelHeader>
                                <MExpansionPanelContent>
                                    <MAlert Type="AlertTypes.Warning" Class="mb-4">
                                        禁用两步验证将降低您账户的安全性
                                    </MAlert>

                                    <MTextField @bind-Value="_twoFactorPassword"
                                               Label="密码"
                                               Type="password"
                                               Outlined
                                               Dense
                                               Class="mb-3" />

                                    <MTextField @bind-Value="_disableTwoFactorCode"
                                               Label="验证码"
                                               Outlined
                                               Dense
                                               MaxLength="6"
                                               Class="mb-3" />

                                    <MButton Color="error" Block OnClick="DisableTwoFactor" Loading="@_loadingTwoFactor">
                                        确认禁用
                                    </MButton>
                                </MExpansionPanelContent>
                            </MExpansionPanel>
                        </MExpansionPanels>
                    }
                </div>
            }
        </MCardText>
        <MCardActions>
            <MSpacer />
            @if (_twoFactorStep == 1)
            {
                <MButton Text OnClick="() => _showTwoFactorDialog = false">取消</MButton>
                <MButton Color="primary" OnClick="StartTwoFactorSetup" Loading="@_loadingTwoFactor" Disabled="@string.IsNullOrWhiteSpace(_twoFactorPassword)">
                    下一步
                </MButton>
            }
            else if (_twoFactorStep == 2)
            {
                <MButton Text OnClick="() => _twoFactorStep = 1">上一步</MButton>
                <MButton Color="primary" OnClick="ConfirmTwoFactor" Loading="@_loadingTwoFactor" Disabled="@(string.IsNullOrWhiteSpace(_twoFactorCode) || _twoFactorCode.Length != 6)">
                    确认启用
                </MButton>
            }
            else if (_twoFactorStep == 3)
            {
                @if (_showRecoveryCodes)
                {
                    <MButton Color="primary" OnClick="() => { _showRecoveryCodes = false; _recoveryCodes.Clear(); }">
                        完成
                    </MButton>
                }
                else
                {
                    <MButton Color="primary" OnClick="() => _showTwoFactorDialog = false">
                        关闭
                    </MButton>
                }
            }
        </MCardActions>
    </MCard>
</MDialog>

<!-- 重新生成恢复码密码确认对话框 -->
<MDialog @bind-Value="_showPasswordForRecoveryCodesDialog" MaxWidth="400">
    <MCard>
        <MCardTitle>验证身份</MCardTitle>
        <MCardText>
            <MTextField @bind-Value="_twoFactorPassword"
                       Label="请输入密码"
                       Type="password"
                       Outlined
                       Dense />
        </MCardText>
        <MCardActions>
            <MSpacer />
            <MButton Text OnClick="() => { _showPasswordForRecoveryCodesDialog = false; _twoFactorPassword = string.Empty; }">取消</MButton>
            <MButton Color="primary" OnClick="async () => { _showPasswordForRecoveryCodesDialog = false; await GenerateRecoveryCodes(); }"
                    Disabled="@string.IsNullOrWhiteSpace(_twoFactorPassword)">
                确认
            </MButton>
        </MCardActions>
    </MCard>
</MDialog>

<!-- 错误提示 -->
<MSnackbar @bind-Value="_showError" Color="error" Top>
    <ChildContent>
        @_errorMessage
    </ChildContent>
    <ActionContent>
        <MButton Text OnClick="() => _showError = false">关闭</MButton>
    </ActionContent>
</MSnackbar>

@code {
    private UserProfile _profile = new();
    private UserProfile _profileEdit = new();
    private List<ApiKey> _apiKeys = new();
    private NotificationSettings _notifications = new();
    private PasswordChangeModel _passwordChange = new();

    private MForm? _profileForm;
    private MForm? _passwordForm;

    private bool _editingProfile = false;
    private bool _savingProfile = false;
    private bool _savingNotifications = false;
    private bool _changingPassword = false;
    private bool _creatingApiKey = false;
    private bool _deletingAccount = false;

    private bool _showPasswordDialog = false;
    private bool _showApiKeyDialog = false;
    private bool _showDeleteDialog = false;
    private bool _showTwoFactorDialog = false;

    private string _apiKeyName = string.Empty;
    private string _newApiKey = string.Empty;
    private string _deleteConfirmation = string.Empty;

    // 2FA相关字段
    private int _twoFactorStep = 1; // 1=输入密码, 2=显示QR码, 3=已启用管理界面
    private string _twoFactorPassword = string.Empty;
    private string _twoFactorCode = string.Empty;
    private string _disableTwoFactorCode = string.Empty;
    private WhimLabAI.Shared.Dtos.Customer.Security.TwoFactorSetupDto? _twoFactorSetup;
    private List<string> _recoveryCodes = new();
    private int _remainingRecoveryCodesCount = 0;
    private bool _loadingTwoFactor = false;
    private bool _generatingRecoveryCodes = false;
    private bool _showRecoveryCodes = false;
    private bool _showPasswordForRecoveryCodesDialog = false;

    private bool _showError = false;
    private string _errorMessage = string.Empty;

    private readonly List<GenderOption> _genderOptions = new()
    {
        new() { Value = "", Text = "保密" },
        new() { Value = "男", Text = "男" },
        new() { Value = "女", Text = "女" }
    };

    protected override async Task OnInitializedAsync()
    {
        await LoadProfile();
        await LoadApiKeys();
        await LoadNotificationSettings();
    }

    private async Task LoadProfile()
    {
        try
        {
            var profile = await ProfileService.GetProfileAsync();
            if (profile != null)
            {
                _profile = new UserProfile
                {
                    Id = profile.Id,
                    Username = profile.Username,
                    Nickname = profile.Nickname ?? profile.Username,
                    Email = profile.Email ?? string.Empty,
                    EmailVerified = profile.EmailVerified,
                    Phone = profile.PhoneNumber,
                    Avatar = profile.Avatar,
                    Gender = profile.Gender?.ToString(),
                    Bio = profile.Bio,
                    CreatedAt = profile.CreatedAt,
                    LastPasswordChange = DateTime.Now.AddDays(-45), // TODO: Get from actual data
                    TwoFactorEnabled = profile.TwoFactorEnabled
                };

                _profileEdit = new UserProfile
                {
                    Nickname = _profile.Nickname,
                    Email = _profile.Email,
                    Phone = _profile.Phone,
                    Gender = _profile.Gender,
                    Bio = _profile.Bio
                };
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"加载个人资料失败: {ex.Message}");
        }
    }

    private async Task LoadApiKeys()
    {
        try
        {
            _apiKeys = await Http.GetFromJsonAsync<List<ApiKey>>("api/profile/api-keys") ?? new();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"加载API密钥失败: {ex.Message}");
        }
    }

    private async Task LoadNotificationSettings()
    {
        try
        {
            _notifications = await Http.GetFromJsonAsync<NotificationSettings>("api/profile/notifications") ?? new();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"加载通知设置失败: {ex.Message}");
        }
    }

    private async Task UploadAvatar()
    {
        // 移除JavaScript调用，通过用户直接点击InputFile组件上传
        await Task.CompletedTask;
    }

    private async Task OnAvatarSelected(InputFileChangeEventArgs e)
    {
        var file = e.File;
        if (file.Size > 5 * 1024 * 1024) // 5MB
        {
            // TODO: 显示文件过大错误
            return;
        }

        try
        {
            using var stream = file.OpenReadStream(5 * 1024 * 1024);
            var avatarUrl = await ProfileService.UploadAvatarAsync(stream, file.Name);

            if (!string.IsNullOrEmpty(avatarUrl))
            {
                await LoadProfile();
                // TODO: 显示上传成功提示
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"上传头像失败: {ex.Message}");
        }
    }

    private void CancelEditProfile()
    {
        _editingProfile = false;
        _profileEdit = new UserProfile
        {
            Nickname = _profile.Nickname,
            Email = _profile.Email,
            Phone = _profile.Phone,
            Gender = _profile.Gender,
            Bio = _profile.Bio
        };
    }

    private async Task SaveProfile()
    {
        _savingProfile = true;

        try
        {
            var updateDto = new UpdateCustomerProfileDto
            {
                Nickname = _profileEdit.Nickname,
                Bio = _profileEdit.Bio,
                Gender = string.IsNullOrEmpty(_profileEdit.Gender) ? null :
                         _profileEdit.Gender == "男" ? Gender.Male :
                         _profileEdit.Gender == "女" ? Gender.Female :
                         Gender.Unknown
            };

            var success = await ProfileService.UpdateProfileAsync(updateDto);
            if (success)
            {
                await LoadProfile();
                _editingProfile = false;
                // TODO: 显示保存成功提示
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"保存个人资料失败: {ex.Message}");
        }
        finally
        {
            _savingProfile = false;
        }
    }

    private async Task SaveNotificationSettings()
    {
        _savingNotifications = true;

        try
        {
            var response = await Http.PutAsJsonAsync("api/profile/notifications", _notifications);
            if (response.IsSuccessStatusCode)
            {
                // TODO: 显示保存成功提示
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"保存通知设置失败: {ex.Message}");
        }
        finally
        {
            _savingNotifications = false;
        }
    }

    private void ShowChangePassword()
    {
        _passwordChange = new();
        _showPasswordDialog = true;
    }

    private async Task ChangePassword()
    {
        if (_passwordChange.NewPassword != _passwordChange.ConfirmPassword)
        {
            // TODO: 显示密码不匹配错误
            return;
        }

        _changingPassword = true;

        try
        {
            var response = await Http.PostAsJsonAsync("api/customer/auth/change-password", new
            {
                OldPassword = _passwordChange.CurrentPassword,
                NewPassword = _passwordChange.NewPassword
            });

            if (response.IsSuccessStatusCode)
            {
                _showPasswordDialog = false;
                _passwordChange = new();
                // TODO: 显示修改成功提示
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"修改密码失败: {ex.Message}");
        }
        finally
        {
            _changingPassword = false;
        }
    }

    private void ShowTwoFactorAuth()
    {
        _showTwoFactorDialog = true;
        _twoFactorStep = _profile.TwoFactorEnabled ? 3 : 1; // 如果已启用，显示管理界面
        _twoFactorPassword = string.Empty;
        _twoFactorCode = string.Empty;
        _disableTwoFactorCode = string.Empty;
        _twoFactorSetup = null;
        _recoveryCodes.Clear();
        _showRecoveryCodes = false;

        if (_profile.TwoFactorEnabled)
        {
            // 加载恢复码剩余数量
            _ = LoadRecoveryCodesCount();
        }
    }

    private async Task LoadRecoveryCodesCount()
    {
        try
        {
            _remainingRecoveryCodesCount = await TwoFactorService.GetRemainingRecoveryCodesCountAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"加载恢复码数量失败: {ex.Message}");
        }
    }

    private async Task StartTwoFactorSetup()
    {
        if (string.IsNullOrWhiteSpace(_twoFactorPassword))
        {
            ShowError("请输入密码");
            return;
        }

        _loadingTwoFactor = true;

        try
        {
            var setupDto = new WhimLabAI.Shared.Dtos.Customer.Security.EnableTwoFactorDto
            {
                Password = _twoFactorPassword
            };

            _twoFactorSetup = await TwoFactorService.GetSetupInfoAsync(setupDto);

            if (_twoFactorSetup != null)
            {
                _twoFactorStep = 2;
                _twoFactorPassword = string.Empty; // 清除密码
            }
            else
            {
                ShowError("获取两步验证设置失败，请检查密码是否正确");
            }
        }
        catch (Exception ex)
        {
            ShowError($"设置两步验证失败: {ex.Message}");
        }
        finally
        {
            _loadingTwoFactor = false;
        }
    }

    private async Task ConfirmTwoFactor()
    {
        if (string.IsNullOrWhiteSpace(_twoFactorCode) || _twoFactorCode.Length != 6)
        {
            ShowError("请输入6位验证码");
            return;
        }

        _loadingTwoFactor = true;

        try
        {
            var confirmDto = new WhimLabAI.Shared.Dtos.Customer.Security.ConfirmTwoFactorDto
            {
                Code = _twoFactorCode
            };

            var success = await TwoFactorService.ConfirmTwoFactorAsync(confirmDto);

            if (success)
            {
                // 生成恢复码
                await GenerateRecoveryCodes();

                // 更新用户状态
                _profile.TwoFactorEnabled = true;
                _twoFactorStep = 3;
                _twoFactorCode = string.Empty;

                ShowError("两步验证已成功启用");
            }
            else
            {
                ShowError("验证码不正确，请重试");
            }
        }
        catch (Exception ex)
        {
            ShowError($"确认两步验证失败: {ex.Message}");
        }
        finally
        {
            _loadingTwoFactor = false;
        }
    }

    private async Task DisableTwoFactor()
    {
        if (string.IsNullOrWhiteSpace(_twoFactorPassword) || string.IsNullOrWhiteSpace(_disableTwoFactorCode))
        {
            ShowError("请输入密码和验证码");
            return;
        }

        _loadingTwoFactor = true;

        try
        {
            var disableDto = new WhimLabAI.Shared.Dtos.Customer.Security.DisableTwoFactorDto
            {
                Password = _twoFactorPassword,
                Code = _disableTwoFactorCode
            };

            var success = await TwoFactorService.DisableTwoFactorAsync(disableDto);

            if (success)
            {
                _profile.TwoFactorEnabled = false;
                _showTwoFactorDialog = false;
                ShowError("两步验证已禁用");
            }
            else
            {
                ShowError("密码或验证码不正确");
            }
        }
        catch (Exception ex)
        {
            ShowError($"禁用两步验证失败: {ex.Message}");
        }
        finally
        {
            _loadingTwoFactor = false;
            _twoFactorPassword = string.Empty;
            _disableTwoFactorCode = string.Empty;
        }
    }

    private async Task GenerateRecoveryCodes()
    {
        _generatingRecoveryCodes = true;

        try
        {
            var generateDto = new WhimLabAI.Shared.Dtos.Customer.Security.GenerateRecoveryCodesDto
            {
                Password = _twoFactorPassword
            };

            var response = await TwoFactorService.GenerateRecoveryCodesAsync(generateDto);

            if (response != null)
            {
                _recoveryCodes = response.RecoveryCodes;
                _showRecoveryCodes = true;
                await LoadRecoveryCodesCount();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"生成恢复码失败: {ex.Message}");
        }
        finally
        {
            _generatingRecoveryCodes = false;
        }
    }

    private async Task RegenerateRecoveryCodes()
    {
        var confirmed = await ConfirmService.ShowAsync("重新生成恢复码将使现有的恢复码失效。确定要继续吗？");
        if (!confirmed) return;

        _twoFactorPassword = string.Empty;
        _showRecoveryCodes = false;
        _recoveryCodes.Clear();

        // 显示密码输入对话框
        _showPasswordForRecoveryCodesDialog = true;
    }

    private async Task DownloadRecoveryCodes()
    {
        if (!_recoveryCodes.Any()) return;

        var content = "WhimLab AI - 两步验证恢复码\n";
        content += "生成时间: " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + "\n";
        content += "=====================================\n\n";
        content += "请将这些恢复码保存在安全的地方。每个恢复码只能使用一次。\n\n";

        for (int i = 0; i < _recoveryCodes.Count; i++)
        {
            content += $"{i + 1}. {_recoveryCodes[i]}\n";
        }

        content += "\n=====================================\n";
        content += "警告：任何拥有这些恢复码的人都可以访问您的账户。\n";
        content += "请妥善保管，不要与他人分享。\n";

        // 创建并下载文件
        var bytes = System.Text.Encoding.UTF8.GetBytes(content);
        var filename = $"whimlab-recovery-codes-{DateTime.Now:yyyyMMddHHmmss}.txt";

        await DownloadService.DownloadFileFromBytesAsync(bytes, filename, "text/plain");
    }

    private async Task CopyRecoveryCodes()
    {
        if (!_recoveryCodes.Any()) return;

        var content = string.Join("\n", _recoveryCodes);
        await ClipboardService.CopyToClipboardAsync(content);
        ShowError("请手动复制恢复码");
    }

    private void ShowLoginHistory()
    {
        Navigation.NavigateTo("/profile/login-history");
    }

    private void ShowCreateApiKey()
    {
        _apiKeyName = string.Empty;
        _newApiKey = string.Empty;
        _showApiKeyDialog = true;
    }

    private async Task CreateApiKey()
    {
        if (string.IsNullOrWhiteSpace(_apiKeyName))
            return;

        _creatingApiKey = true;

        try
        {
            var response = await Http.PostAsJsonAsync("api/profile/api-keys", new { Name = _apiKeyName });
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<CreateApiKeyResponse>();
                if (result != null)
                {
                    _newApiKey = result.Key;
                    await LoadApiKeys();
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"创建API密钥失败: {ex.Message}");
        }
        finally
        {
            _creatingApiKey = false;
        }
    }

    private async Task CopyApiKey(ApiKey key)
    {
        // 只能复制遮掩后的密钥
        await CopyText(key.MaskedKey);
    }

    private async Task CopyText(string text)
    {
        await ClipboardService.CopyToClipboardAsync(text);
        // 显示复制提示
        ShowError("请手动复制文本");
    }

    private async Task DeleteApiKey(ApiKey key)
    {
        var confirmed = await ConfirmService.ShowAsync($"确定要删除API密钥 \"{key.Name}\" 吗？");
        if (confirmed)
        {
            try
            {
                await Http.DeleteAsync($"api/profile/api-keys/{key.Id}");
                await LoadApiKeys();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"删除API密钥失败: {ex.Message}");
            }
        }
    }

    private async Task ExportData()
    {
        try
        {
            var response = await Http.PostAsync("api/profile/export-data", null);
            if (response.IsSuccessStatusCode)
            {
                // TODO: 显示导出成功，发送邮件通知
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"导出数据失败: {ex.Message}");
        }
    }

    private void ShowDeleteAccount()
    {
        _deleteConfirmation = string.Empty;
        _showDeleteDialog = true;
    }

    private async Task DeleteAccount()
    {
        if (_deleteConfirmation != "DELETE")
            return;

        _deletingAccount = true;

        try
        {
            var deleteDto = new DeleteAccountDto
            {
                Password = string.Empty, // TODO: Add password confirmation field
                Reason = "User requested account deletion"
            };

            var success = await ProfileService.DeleteAccountAsync(deleteDto);
            if (success)
            {
                // 登出
                await AuthService.LogoutAsync();
                // 跳转到首页
                Navigation.NavigateTo("/", true);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"删除账户失败: {ex.Message}");
        }
        finally
        {
            _deletingAccount = false;
        }
    }

    // 数据模型
    class UserProfile
    {
        public Guid Id { get; set; }
        public string Username { get; set; } = string.Empty;
        public string Nickname { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public bool EmailVerified { get; set; }
        public string? Phone { get; set; }
        public string? Avatar { get; set; }
        public string? Gender { get; set; }
        public string? Bio { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.Now.AddMonths(-6);
        public DateTime LastPasswordChange { get; set; } = DateTime.Now.AddDays(-45);
        public bool TwoFactorEnabled { get; set; }
    }

    class ApiKey
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string MaskedKey { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime? LastUsedAt { get; set; }
    }

    class NotificationSettings
    {
        public bool Email { get; set; } = true;
        public bool Sms { get; set; }
        public bool InApp { get; set; } = true;
        public bool Subscription { get; set; } = true;
        public bool Features { get; set; } = true;
        public bool Marketing { get; set; }
    }

    class PasswordChangeModel
    {
        public string CurrentPassword { get; set; } = string.Empty;
        public string NewPassword { get; set; } = string.Empty;
        public string ConfirmPassword { get; set; } = string.Empty;
    }

    class CreateApiKeyResponse
    {
        public Guid Id { get; set; }
        public string Key { get; set; } = string.Empty;
    }

    class GenderOption
    {
        public string Value { get; set; } = string.Empty;
        public string Text { get; set; } = string.Empty;
    }

    private void ShowError(string message)
    {
        _errorMessage = message;
        _showError = true;
    }
}

