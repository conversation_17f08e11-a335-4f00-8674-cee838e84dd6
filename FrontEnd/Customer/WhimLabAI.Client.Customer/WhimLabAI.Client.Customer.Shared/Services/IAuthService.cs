namespace WhimLabAI.Client.Customer.Shared.Services;

/// <summary>
/// 认证服务接口
/// </summary>
public interface IAuthService
{
    /// <summary>
    /// 检查用户是否已认证
    /// </summary>
    Task<bool> IsAuthenticatedAsync();
    
    /// <summary>
    /// 获取当前用户Token
    /// </summary>
    Task<string?> GetTokenAsync();
    
    /// <summary>
    /// 获取当前用户ID
    /// </summary>
    Task<Guid?> GetUserIdAsync();
    
    /// <summary>
    /// 获取当前用户信息
    /// </summary>
    Task<UserInfo?> GetUserInfoAsync();
    
    /// <summary>
    /// 登录
    /// </summary>
    Task<bool> LoginAsync(string token, UserInfo userInfo);
    
    /// <summary>
    /// 登出
    /// </summary>
    Task LogoutAsync();
    
    /// <summary>
    /// 刷新Token
    /// </summary>
    Task<bool> RefreshTokenAsync();
}

/// <summary>
/// 用户信息
/// </summary>
public class UserInfo
{
    public Guid Id { get; set; }
    public string Username { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string? PhoneNumber { get; set; }
    public string? Avatar { get; set; }
    public string? Nickname { get; set; }
}