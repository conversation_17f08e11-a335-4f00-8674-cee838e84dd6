using System.Net.Http.Headers;
using System.Text.Json;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.Logging;

namespace WhimLabAI.Client.Customer.Shared.Services;

/// <summary>
/// Token自动刷新服务
/// </summary>
public class TokenRefreshService
{
    private readonly HttpClient _httpClient;
    private readonly IStorageService _storage;
    private readonly AuthenticationStateProvider _authStateProvider;
    private readonly ILogger<TokenRefreshService> _logger;
    private readonly SemaphoreSlim _refreshSemaphore = new(1, 1);
    private DateTime _lastRefreshTime = DateTime.MinValue;
    private const int RefreshBeforeExpiryMinutes = 5;

    public TokenRefreshService(
        HttpClient httpClient,
        IStorageService storage,
        AuthenticationStateProvider authStateProvider,
        ILogger<TokenRefreshService> logger)
    {
        _httpClient = httpClient;
        _storage = storage;
        _authStateProvider = authStateProvider;
        _logger = logger;
    }

    /// <summary>
    /// 检查并刷新Token
    /// </summary>
    public async Task<bool> CheckAndRefreshTokenAsync()
    {
        try
        {
            var token = await GetTokenAsync();
            if (string.IsNullOrEmpty(token))
                return false;

            // 解析JWT获取过期时间
            var expiry = GetTokenExpiry(token);
            if (expiry == null)
                return false;

            // 如果Token将在5分钟内过期，则刷新
            if (expiry.Value.AddMinutes(-RefreshBeforeExpiryMinutes) <= DateTime.UtcNow)
            {
                return await RefreshTokenAsync();
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking token expiry");
            return false;
        }
    }

    /// <summary>
    /// 刷新Token
    /// </summary>
    public async Task<bool> RefreshTokenAsync()
    {
        // 防止并发刷新
        if (!await _refreshSemaphore.WaitAsync(0))
        {
            // 如果其他线程正在刷新，等待完成
            await _refreshSemaphore.WaitAsync();
            _refreshSemaphore.Release();
            return true;
        }

        try
        {
            // 检查是否刚刚刷新过（防止重复刷新）
            if ((DateTime.UtcNow - _lastRefreshTime).TotalSeconds < 10)
            {
                return true;
            }

            var refreshToken = await GetRefreshTokenAsync();
            if (string.IsNullOrEmpty(refreshToken))
            {
                _logger.LogWarning("No refresh token available");
                return false;
            }

            var request = new HttpRequestMessage(HttpMethod.Post, "api/customer-auth/refresh");
            request.Content = new StringContent(
                JsonSerializer.Serialize(new { refreshToken }),
                System.Text.Encoding.UTF8,
                "application/json");

            var response = await _httpClient.SendAsync(request);
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var tokenResponse = JsonSerializer.Deserialize<TokenResponse>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (tokenResponse != null && !string.IsNullOrEmpty(tokenResponse.AccessToken))
                {
                    // 保存新的Token
                    await SaveTokensAsync(tokenResponse.AccessToken, tokenResponse.RefreshToken);
                    
                    // 更新HttpClient的默认请求头
                    _httpClient.DefaultRequestHeaders.Authorization = 
                        new AuthenticationHeaderValue("Bearer", tokenResponse.AccessToken);

                    // 通知认证状态变更
                    if (_authStateProvider is CustomerAuthStateProvider customerAuthProvider)
                    {
                        await customerAuthProvider.NotifyAuthenticationStateChanged();
                    }

                    _lastRefreshTime = DateTime.UtcNow;
                    _logger.LogInformation("Token refreshed successfully");
                    return true;
                }
            }
            else
            {
                _logger.LogWarning("Token refresh failed with status: {StatusCode}", response.StatusCode);
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error refreshing token");
            return false;
        }
        finally
        {
            _refreshSemaphore.Release();
        }
    }

    /// <summary>
    /// 从JWT获取过期时间
    /// </summary>
    private DateTime? GetTokenExpiry(string token)
    {
        try
        {
            var parts = token.Split('.');
            if (parts.Length != 3)
                return null;

            var payload = parts[1];
            var jsonBytes = ParseBase64WithoutPadding(payload);
            var json = System.Text.Encoding.UTF8.GetString(jsonBytes);
            
            using var doc = JsonDocument.Parse(json);
            if (doc.RootElement.TryGetProperty("exp", out var exp))
            {
                var unixTime = exp.GetInt64();
                return DateTimeOffset.FromUnixTimeSeconds(unixTime).UtcDateTime;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error parsing token expiry");
        }

        return null;
    }

    private byte[] ParseBase64WithoutPadding(string base64)
    {
        switch (base64.Length % 4)
        {
            case 2: base64 += "=="; break;
            case 3: base64 += "="; break;
        }
        return Convert.FromBase64String(base64);
    }

    private async Task<string?> GetTokenAsync()
    {
        try
        {
            return await _storage.GetAsync("access_token");
        }
        catch
        {
            return null;
        }
    }

    private async Task<string?> GetRefreshTokenAsync()
    {
        try
        {
            return await _storage.GetAsync("refresh_token");
        }
        catch
        {
            return null;
        }
    }

    private async Task SaveTokensAsync(string accessToken, string? refreshToken)
    {
        await _storage.SetAsync("access_token", accessToken);
        if (!string.IsNullOrEmpty(refreshToken))
        {
            await _storage.SetAsync("refresh_token", refreshToken);
        }
    }

    private class TokenResponse
    {
        public string AccessToken { get; set; } = string.Empty;
        public string? RefreshToken { get; set; }
        public int ExpiresIn { get; set; }
    }
}

/// <summary>
/// HTTP消息处理器，自动刷新Token
/// </summary>
public class TokenRefreshHandler : DelegatingHandler
{
    private readonly TokenRefreshService _tokenRefreshService;
    private readonly ILogger<TokenRefreshHandler> _logger;

    public TokenRefreshHandler(
        TokenRefreshService tokenRefreshService,
        ILogger<TokenRefreshHandler> logger)
    {
        _tokenRefreshService = tokenRefreshService;
        _logger = logger;
    }

    protected override async Task<HttpResponseMessage> SendAsync(
        HttpRequestMessage request, 
        CancellationToken cancellationToken)
    {
        // 在发送请求前检查Token
        await _tokenRefreshService.CheckAndRefreshTokenAsync();

        var response = await base.SendAsync(request, cancellationToken);

        // 如果收到401，尝试刷新Token并重试
        if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized)
        {
            _logger.LogInformation("Received 401, attempting to refresh token");
            
            if (await _tokenRefreshService.RefreshTokenAsync())
            {
                // 克隆原始请求
                var newRequest = CloneHttpRequestMessage(request);
                
                // 使用新Token重试
                response = await base.SendAsync(newRequest, cancellationToken);
            }
        }

        return response;
    }

    private static HttpRequestMessage CloneHttpRequestMessage(HttpRequestMessage req)
    {
        var clone = new HttpRequestMessage(req.Method, req.RequestUri)
        {
            Version = req.Version
        };

        // 复制请求内容
        if (req.Content != null)
        {
            var ms = new MemoryStream();
            req.Content.CopyTo(ms, null, CancellationToken.None);
            ms.Position = 0;
            clone.Content = new StreamContent(ms);

            // 复制内容头
            foreach (var header in req.Content.Headers)
            {
                clone.Content.Headers.TryAddWithoutValidation(header.Key, header.Value);
            }
        }

        // 复制请求头
        foreach (var header in req.Headers)
        {
            clone.Headers.TryAddWithoutValidation(header.Key, header.Value);
        }

        // 复制属性
        foreach (var prop in req.Options)
        {
            clone.Options.Set(new HttpRequestOptionsKey<object?>(prop.Key), prop.Value);
        }

        return clone;
    }
}