using System.Net.Http.Json;
using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Client.Customer.Shared.Services;

/// <summary>
/// AI代理服务
/// </summary>
public class AgentService
{
    private readonly HttpClient _httpClient;
    private readonly IAuthService _authService;

    public AgentService(HttpClient httpClient, IAuthService authService)
    {
        _httpClient = httpClient;
        _authService = authService;
    }

    /// <summary>
    /// 获取市场AI代理列表
    /// </summary>
    public async Task<PagedList<AgentMarketplaceDto>?> GetMarketplaceAgentsAsync(AgentSearchDto query)
    {
        try
        {
            var queryParams = new List<string>();
            
            if (!string.IsNullOrEmpty(query.Keyword))
                queryParams.Add($"keyword={Uri.EscapeDataString(query.Keyword)}");
            if (query.CategoryId.HasValue)
                queryParams.Add($"categoryId={query.CategoryId}");
            if (!string.IsNullOrEmpty(query.Tag))
                queryParams.Add($"tag={Uri.EscapeDataString(query.Tag)}");
            if (query.IsFree.HasValue)
                queryParams.Add($"isFree={query.IsFree.Value}");
            if (query.IsOfficial.HasValue)
                queryParams.Add($"isOfficial={query.IsOfficial.Value}");
            queryParams.Add($"page={query.Page}");
            queryParams.Add($"pageSize={query.PageSize}");
            if (!string.IsNullOrEmpty(query.SortBy))
                queryParams.Add($"sortBy={query.SortBy}");

            var queryString = string.Join("&", queryParams);
            var response = await _httpClient.GetAsync($"api/agent/marketplace?{queryString}");
            
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<ApiResponse<PagedList<AgentMarketplaceDto>>>();
                return result?.Data;
            }
            
            return null;
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// 获取AI代理详情
    /// </summary>
    public async Task<AgentDetailDto?> GetAgentDetailAsync(Guid agentId)
    {
        try
        {
            var response = await _httpClient.GetAsync($"api/agent/marketplace/{agentId}");
            
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<ApiResponse<AgentDetailDto>>();
                return result?.Data;
            }
            
            return null;
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// 获取分类统计
    /// </summary>
    public async Task<Dictionary<string, int>?> GetCategoryCounts()
    {
        try
        {
            var response = await _httpClient.GetAsync("api/agent/marketplace/categories");
            
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<ApiResponse<List<CategoryCountDto>>>();
                return result?.Data?.ToDictionary(x => x.Category, x => x.Count);
            }
            
            return null;
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// 评价AI代理
    /// </summary>
    public async Task<bool> RateAgentAsync(Guid agentId, RateAgentDto dto)
    {
        try
        {
            var response = await _httpClient.PostAsJsonAsync($"api/agent/{agentId}/rate", dto);
            return response.IsSuccessStatusCode;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 收藏/取消收藏AI代理
    /// </summary>
    public async Task<bool> ToggleFavoriteAsync(Guid agentId)
    {
        try
        {
            var response = await _httpClient.PostAsync($"api/agent/{agentId}/favorite", null);
            return response.IsSuccessStatusCode;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 获取用户与代理的交互状态
    /// </summary>
    public async Task<UserInteractionDto?> GetUserInteractionAsync(Guid agentId)
    {
        try
        {
            var response = await _httpClient.GetAsync($"api/agent/{agentId}/interaction");
            
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<ApiResponse<UserInteractionDto>>();
                return result?.Data;
            }
            
            return null;
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// 切换点赞状态
    /// </summary>
    public async Task<AgentLikeResultDto?> ToggleLikeAsync(Guid agentId)
    {
        try
        {
            var response = await _httpClient.PostAsync($"api/agent/{agentId}/like", null);
            
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<ApiResponse<AgentLikeResultDto>>();
                return result?.Data;
            }
            
            return null;
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// 获取代理评价列表
    /// </summary>
    public async Task<PagedList<AgentRatingDto>?> GetAgentRatingsAsync(Guid agentId, int page = 1, int pageSize = 10)
    {
        try
        {
            var response = await _httpClient.GetAsync($"api/agent/{agentId}/ratings?page={page}&pageSize={pageSize}");
            
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<ApiResponse<PagedList<AgentRatingDto>>>();
                return result?.Data;
            }
            
            return null;
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// 标记评价是否有用
    /// </summary>
    public async Task<bool> MarkRatingHelpfulAsync(Guid ratingId, bool isHelpful)
    {
        try
        {
            var response = await _httpClient.PostAsJsonAsync($"api/agent/rating/{ratingId}/helpful", new { IsHelpful = isHelpful });
            return response.IsSuccessStatusCode;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 记录分享行为
    /// </summary>
    public async Task<bool> RecordShareAsync(Guid agentId)
    {
        try
        {
            var response = await _httpClient.PostAsync($"api/agent/{agentId}/share", null);
            return response.IsSuccessStatusCode;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 举报AI代理
    /// </summary>
    public async Task<bool> ReportAgentAsync(Guid agentId, ReportAgentDto dto)
    {
        try
        {
            var response = await _httpClient.PostAsJsonAsync($"api/agent/{agentId}/report", dto);
            return response.IsSuccessStatusCode;
        }
        catch
        {
            return false;
        }
    }
}