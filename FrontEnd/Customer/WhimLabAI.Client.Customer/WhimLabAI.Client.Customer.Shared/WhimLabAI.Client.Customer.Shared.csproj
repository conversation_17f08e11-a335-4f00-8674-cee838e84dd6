<Project Sdk="Microsoft.NET.Sdk.Razor">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
        <SupportedPlatform Include="browser" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Markdig" Version="0.38.0" />
        <PackageReference Include="Masa.Blazor" Version="1.9.0" />
        <PackageReference Include="Microsoft.AspNetCore.Components.Authorization" Version="9.0.7" />
        <PackageReference Include="Microsoft.AspNetCore.Components.Web" Version="9.0.*" />
        <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="9.0.6" />
        <PackageReference Include="Microsoft.Extensions.Http.Polly" Version="9.0.7" />
        <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.7" />
        <PackageReference Include="Polly.Extensions.Http" Version="3.0.0" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\..\..\BackEnd\WhimLabAI.Shared\WhimLabAI.Shared.csproj" />
    </ItemGroup>

    <ItemGroup>
        <UpToDateCheckInput Remove="Components\ExternalLink.razor" />
    </ItemGroup>

</Project>
