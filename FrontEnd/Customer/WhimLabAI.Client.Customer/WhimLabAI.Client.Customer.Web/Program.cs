﻿using WhimLabAI.Client.Customer.Web.Components;
using WhimLabAI.Client.Customer.Shared.Services;
using WhimLabAI.Client.Customer.Web.Services;

var builder = WebApplication.CreateBuilder(args);

// 配置文件加载顺序
builder.Configuration
    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
    .AddJsonFile($"appsettings.{builder.Environment.EnvironmentName}.json", optional: true, reloadOnChange: true)
    .AddJsonFile("appsettings.QuickDev.json", optional: true, reloadOnChange: true)
    .AddEnvironmentVariables();

// Add services to the container.
builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents()
    .AddInteractiveWebAssemblyComponents();

builder.Services.AddMasaBlazor();

// Add device-specific services used by the WhimLabAI.Client.Customer.Shared project
builder.Services.AddSingleton<IFormFactor, FormFactor>();

// Add storage services
builder.Services.AddScoped<ILocalStorageService, InMemoryLocalStorageService>();
builder.Services.AddScoped<IStorageService, MemoryStorageService>();

// Add UI services
builder.Services.AddScoped<IConfirmService, ConfirmService>();
builder.Services.AddScoped<IClipboardService, ClipboardService>();

// Add authentication state provider
builder.Services.AddAuthorizationCore();
builder.Services.AddScoped<CustomerAuthStateProvider>();
builder.Services.AddScoped<Microsoft.AspNetCore.Components.Authorization.AuthenticationStateProvider>(
    provider => provider.GetRequiredService<CustomerAuthStateProvider>());

// Add token refresh service
builder.Services.AddScoped<TokenRefreshService>();
builder.Services.AddTransient<WhimLabAI.Client.Customer.Shared.Services.TokenRefreshHandler>();

// Configure HttpClient for API calls with token refresh
var apiBaseUrl = builder.Configuration["ApiSettings:BaseUrl"] ?? "https://localhost:5001/";
builder.Services.AddHttpClient("API", client =>
{
    client.BaseAddress = new Uri(apiBaseUrl);
})
.AddHttpMessageHandler<WhimLabAI.Client.Customer.Shared.Services.TokenRefreshHandler>();

// Add default HttpClient without message handler to avoid circular dependency
builder.Services.AddScoped(sp =>
{
    var httpClient = new HttpClient
    {
        BaseAddress = new Uri(apiBaseUrl)
    };
    return httpClient;
});

// Add authentication service (must be before error handling services that depend on it)
builder.Services.AddScoped<WhimLabAI.Client.Customer.Shared.Services.AuthService>();
builder.Services.AddScoped<IAuthService>(sp => sp.GetRequiredService<WhimLabAI.Client.Customer.Shared.Services.AuthService>());

// Add error handling services
builder.Services.AddScoped<ErrorHandlingHttpMessageHandler>();
builder.Services.AddScoped<HttpInterceptorService>();
builder.Services.AddScoped<ErrorLoggingService>();
builder.Services.AddScoped<NetworkStatusService>();

// Add download service
builder.Services.AddScoped<IDownloadService, DownloadService>();

// Add loading state services
builder.Services.AddSingleton<ILoadingStateService, LoadingStateService>();
builder.Services.AddScoped<LoadingHttpMessageHandler>();

// Add customer services
builder.Services.AddScoped<AgentService>();
builder.Services.AddScoped<ConversationService>();
builder.Services.AddScoped<SubscriptionService>();
builder.Services.AddScoped<PaymentService>();
builder.Services.AddScoped<ProfileService>();
builder.Services.AddScoped<QRCodeLoginService>();
builder.Services.AddScoped<TwoFactorService>(sp =>
{
    var httpClientFactory = sp.GetRequiredService<IHttpClientFactory>();
    var httpClient = httpClientFactory.CreateClient("API");
    var logger = sp.GetRequiredService<ILogger<TwoFactorService>>();
    return new TwoFactorService(httpClient, logger);
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseWebAssemblyDebugging();
}
else
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();

app.UseAntiforgery();

app.MapStaticAssets();
app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode()
    .AddInteractiveWebAssemblyRenderMode()
    .AddAdditionalAssemblies(
        typeof(WhimLabAI.Client.Customer.Shared._Imports).Assembly,
        typeof(WhimLabAI.Client.Customer.Web.Client._Imports).Assembly);

app.Run();