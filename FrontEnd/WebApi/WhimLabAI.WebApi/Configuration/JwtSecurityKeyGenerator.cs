using System.Security.Cryptography;

namespace WhimLabAI.WebApi.Configuration;

/// <summary>
/// Utility class for generating secure JWT secret keys
/// </summary>
public static class JwtSecurityKeyGenerator
{
    /// <summary>
    /// Generates a cryptographically secure JWT secret key
    /// </summary>
    /// <param name="keySizeInBytes">Size of the key in bytes (minimum 32 bytes for HS256)</param>
    /// <returns>Base64 encoded secret key</returns>
    public static string GenerateSecretKey(int keySizeInBytes = 64)
    {
        if (keySizeInBytes < 32)
        {
            throw new ArgumentException("Key size must be at least 32 bytes (256 bits) for HS256 algorithm", nameof(keySizeInBytes));
        }

        using var rng = RandomNumberGenerator.Create();
        var keyBytes = new byte[keySizeInBytes];
        rng.GetBytes(keyBytes);
        
        return Convert.ToBase64String(keyBytes);
    }

    /// <summary>
    /// Gets a secure JWT secret key from configuration or generates one if needed
    /// </summary>
    /// <param name="configuration">The configuration instance</param>
    /// <param name="logger">The logger instance</param>
    /// <returns>A secure JWT secret key</returns>
    public static string GetOrGenerateSecretKey(IConfiguration configuration, ILogger logger)
    {
        var jwtSection = configuration.GetSection("Jwt");
        var secretKey = jwtSection["SecretKey"];

        // Check if we're using the default insecure key
        if (string.IsNullOrEmpty(secretKey) || 
            secretKey == "your-256-bit-secret-key-for-development-only-must-be-at-least-32-characters" ||
            secretKey.Length < 32)
        {
            // In production, this should fail
            var environment = configuration["ASPNETCORE_ENVIRONMENT"] ?? "Production";
            
            if (environment == "Production")
            {
                throw new InvalidOperationException(
                    "JWT secret key is not properly configured for production. " +
                    "Please set a secure secret key in environment variables or configuration.");
            }
            
            // In development, generate a temporary key
            logger.LogWarning(
                "JWT secret key is not properly configured. Generating a temporary key for development. " +
                "This key will change on each application restart. " +
                "For production, set JWT:SecretKey in environment variables or user secrets.");
            
            secretKey = GenerateSecretKey();
            
            // Log the generated key for development reference
            logger.LogInformation(
                "Generated JWT secret key for development: {Key}. " +
                "Add this to your user secrets: dotnet user-secrets set \"Jwt:SecretKey\" \"{Key}\"",
                secretKey, secretKey);
        }

        return secretKey;
    }

    /// <summary>
    /// Validates if a JWT secret key meets security requirements
    /// </summary>
    /// <param name="secretKey">The secret key to validate</param>
    /// <returns>True if the key meets security requirements, false otherwise</returns>
    public static bool IsSecureKey(string secretKey)
    {
        if (string.IsNullOrEmpty(secretKey))
            return false;

        // Check minimum length (32 characters for base64 encoded 256-bit key)
        if (secretKey.Length < 32)
            return false;

        // Check if it's one of the known insecure defaults
        var insecureKeys = new[]
        {
            "your-256-bit-secret-key-for-development-only-must-be-at-least-32-characters",
            "your-256-bit-secret",
            "secret",
            "password",
            "12345678901234567890123456789012"
        };

        if (insecureKeys.Contains(secretKey, StringComparer.OrdinalIgnoreCase))
            return false;

        // Check for basic entropy (not all same character)
        if (secretKey.Distinct().Count() < 10)
            return false;

        return true;
    }
}