{"Security": {"Headers": {"UseHsts": true, "HstsMaxAge": 31536000, "XFrameOptions": "DENY", "ReferrerPolicy": "strict-origin-when-cross-origin", "ContentSecurityPolicy": "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com data:; img-src 'self' data: https: blob:; connect-src 'self' wss: https://api.whimlab.com; frame-ancestors 'none'; base-uri 'self'; form-action 'self'", "PermissionsPolicy": "accelerometer=(), camera=(), geolocation=(), gyroscope=(), magnetometer=(), microphone=(), payment=(), usb=()"}, "Cors": {"AllowedOrigins": ["http://*.whimlab.com", "http://whimlab.com", "http://www.whimlab.com", "http://admin.whimlab.com", "https://*.whimlab.com", "https://whimlab.com", "https://www.whimlab.com", "https://admin.whimlab.com", "http://localhost:5139", "http://localhost:5085", "http://localhost:3000"]}, "AntiBruteForce": {"DefaultSettings": {"MaxAttempts": 5, "WindowDurationMinutes": 15, "BaseLockMinutes": 5, "LockMultiplier": 2, "MaxLockMinutes": 1440}, "ActionSettings": {"login": {"MaxAttempts": 5, "WindowDurationMinutes": 15, "BaseLockMinutes": 5, "LockMultiplier": 2, "MaxLockMinutes": 60}, "password_reset": {"MaxAttempts": 3, "WindowDurationMinutes": 30, "BaseLockMinutes": 30, "LockMultiplier": 2, "MaxLockMinutes": 1440}, "api_key": {"MaxAttempts": 10, "WindowDurationMinutes": 5, "BaseLockMinutes": 1, "LockMultiplier": 1.5, "MaxLockMinutes": 60}, "api_request": {"MaxAttempts": 100, "WindowDurationMinutes": 1, "BaseLockMinutes": 0.5, "LockMultiplier": 1.2, "MaxLockMinutes": 5}}, "DistributedAttackThreshold": 100, "CriticalThreshold": 10}, "FileUpload": {"MaxFileSize": 10485760, "AllowedExtensions": [".jpg", ".jpeg", ".png", ".gif", ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".txt", ".csv", ".zip", ".rar"], "BlockedExtensions": [".exe", ".dll", ".bat", ".cmd", ".sh", ".ps1", ".vbs", ".js", ".jar", ".com", ".scr", ".msi", ".app", ".deb", ".rpm", ".dmg", ".pkg", ".run", ".asp", ".aspx", ".php", ".jsp", ".jspx", ".cgi", ".pl", ".py", ".rb"], "EnableVirusScan": true, "UploadDirectory": "uploads"}, "Sanitizer": {"StrictMode": false, "MaxLength": 10000, "AllowedTags": ["p", "br", "strong", "b", "em", "i", "u", "a", "ul", "ol", "li", "h1", "h2", "h3", "h4", "h5", "h6", "blockquote", "code", "pre", "table", "thead", "tbody", "tr", "th", "td", "img", "span", "div"], "AllowedAttributes": ["href", "src", "alt", "title", "class", "id", "target", "rel", "width", "height", "colspan", "rowspan"]}, "RateLimiting": {"EnableRateLimiting": true, "GlobalRateLimit": {"PermitLimit": 100, "Window": "00:01:00", "QueueProcessingOrder": "OldestFirs<PERSON>", "QueueLimit": 50}, "EndpointLimits": {"/api/auth/login": {"PermitLimit": 5, "Window": "00:05:00"}, "/api/auth/register": {"PermitLimit": 3, "Window": "00:10:00"}, "/api/ai/chat": {"PermitLimit": 30, "Window": "00:01:00"}}}, "Encryption": {"MasterKey": "your-256-bit-master-encryption-key-must-be-at-least-32-characters", "Salt": "whimlab-ai-2024-encryption-salt", "Iterations": 10000, "Mode": "CBC", "Padding": "PKCS7"}, "Audit": {"Enabled": true, "BufferSize": 100, "FlushIntervalSeconds": 10, "LogSensitiveData": false, "ExcludedPaths": ["/health", "/health/ready", "/health/live", "/swagger", "/api/audit/logs"]}}}