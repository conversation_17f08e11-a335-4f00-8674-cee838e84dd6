{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "REPLACE_WITH_PRODUCTION_DATABASE_CONNECTION_STRING", "Redis": "REPLACE_WITH_PRODUCTION_REDIS_CONNECTION_STRING"}, "RabbitMQ": {"Host": "REPLACE_WITH_PRODUCTION_HOST", "Port": 5672, "Username": "REPLACE_WITH_PRODUCTION_USERNAME", "Password": "REPLACE_WITH_PRODUCTION_PASSWORD", "VirtualHost": "/"}, "Jwt": {"SecretKey": "REPLACE_WITH_SECURE_KEY_IN_PRODUCTION", "Issuer": "WhimLabAI", "Audience": "WhimLabAI", "ExpirationMinutes": 1440, "RefreshExpirationDays": 7}, "Storage": {"Provider": "local", "Local": {"BasePath": "./wwwroot/uploads", "BaseUrl": "/uploads"}, "MinIO": {"Endpoint": "localhost:9000", "AccessKey": "REPLACE_IN_PRODUCTION", "SecretKey": "REPLACE_IN_PRODUCTION", "UseSSL": false, "DefaultBucket": "whimlab-ai", "DefaultUrlExpiryMinutes": 60, "PublicEndpoint": null}}, "CorsSettings": {"AllowedOrigins": ["https://localhost:7217", "http://localhost:7216", "https://localhost:7041", "http://localhost:7040"]}, "ApiSettings": {"RateLimitWindowSeconds": 60, "RateLimitRequestCount": 100}, "AI": {"SemanticKernel": {"ApiKey": "REPLACE_WITH_ACTUAL_API_KEY", "Endpoint": "", "DefaultModel": "gpt-3.5-turbo"}, "Dify": {"BaseUrl": "http://localhost:44080/v1", "AppTypes": {"Chat": "/chat-messages", "Completion": "/completion-messages", "Workflow": "/workflows/run"}}}, "Payment": {"BaseUrl": "https://localhost:5001", "CallbackUrl": "https://localhost:5001", "Security": {"EnableIpWhitelist": false, "IpWhitelist": ["*************/24", "**********/16", "***********/16", "***********/16", "***********/16"], "ReplayWindowMinutes": 5, "EnableSignatureValidation": true}, "Alipay": {"AppId": "REPLACE_WITH_ACTUAL_APP_ID", "PrivateKey": "REPLACE_WITH_ACTUAL_PRIVATE_KEY", "PublicKey": "REPLACE_WITH_ACTUAL_PUBLIC_KEY", "IsProduction": false, "Gateway": "https://openapi.alipaydev.com/gateway.do", "NotifyUrl": "https://localhost:5001/api/payment/alipay/notify", "ReturnUrl": "https://localhost:5001/payment/result"}, "WeChatPay": {"AppId": "REPLACE_WITH_ACTUAL_APP_ID", "MchId": "REPLACE_WITH_ACTUAL_MCH_ID", "ApiKey": "REPLACE_WITH_ACTUAL_API_KEY", "CertPath": "./certs/wechat/apiclient_cert.p12", "Gateway": "https://api.mch.weixin.qq.com/sandboxnew", "NotifyUrl": "https://localhost:5001/api/payment/wechatpay/notify", "ReturnUrl": "https://localhost:5001/payment/result"}}, "Verification": {"Code": {"CodeLength": 6, "ExpirationMinutes": 5, "MaxAttempts": 5, "ResendIntervalSeconds": 60, "RequireCaptcha": true}, "Captcha": {"Width": 120, "Height": 40, "CodeLength": 4, "ExpirationMinutes": 5, "NoiseLevel": 3, "Characters": "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789", "FontFamily": "<PERSON><PERSON>", "FontSize": 16}, "AdminCaptchaPolicy": {"Enabled": true, "AlwaysRequired": true, "FailedAttemptsThreshold": 3, "HourlyAttemptsThreshold": 10, "DisableInDevelopment": false}, "CustomerCaptchaPolicy": {"Enabled": true, "AlwaysRequired": false, "FailedAttemptsThreshold": 3, "HourlyAttemptsThreshold": 10, "DisableInDevelopment": false}}, "Email": {"Enabled": false, "SmtpServer": "smtp.whimlab.com", "SmtpPort": 587, "Username": "<EMAIL>", "Password": "REPLACE_IN_PRODUCTION", "FromAddress": "<EMAIL>", "FromName": "WhimLabAI", "EnableSsl": true}, "Sms": {"Enabled": false, "Provider": "twi<PERSON>", "Providers": {"Twilio": {"AccountSid": "REPLACE_WITH_ACTUAL_ACCOUNT_SID", "AuthToken": "REPLACE_WITH_ACTUAL_AUTH_TOKEN", "FromPhoneNumber": "+**********", "MessagingServiceSid": null, "ApiBaseUrl": "https://api.twilio.com/2010-04-01", "MaxRetries": 3, "RetryDelaySeconds": 5}, "Aliyun": {"AccessKeyId": "REPLACE_WITH_ACTUAL_ACCESS_KEY_ID", "AccessKeySecret": "REPLACE_WITH_ACTUAL_ACCESS_KEY_SECRET", "SignName": "WhimLabAI", "TemplateCode": "SMS_000000", "RegionId": "cn-hangzhou", "Product": "Dysmsapi", "Domain": "dysmsapi.aliyuncs.com", "Version": "2017-05-25", "Action": "SendSms"}, "Tencent": {"SecretId": "REPLACE_WITH_ACTUAL_SECRET_ID", "SecretKey": "REPLACE_WITH_ACTUAL_SECRET_KEY", "SmsSdkAppId": "REPLACE_WITH_ACTUAL_SMS_SDK_APP_ID", "SignName": "WhimLabAI", "TemplateId": "000000", "Region": "ap-guangzhou", "Endpoint": "sms.tencentcloudapi.com"}}, "RateLimiting": {"MaxSmsPerPhonePerDay": 10, "MaxSmsPerPhonePerHour": 5, "MaxSmsPerIpPerDay": 20}}, "SessionTimeout": {"AdminSessionTimeoutMinutes": 30, "CustomerSessionTimeoutMinutes": 60, "CleanupIntervalMinutes": 5, "Enabled": true, "WarningBeforeTimeoutMinutes": 5, "ActivityUpdateIntervalSeconds": 60}, "OAuth": {"BaseUrl": "https://localhost:5001", "StateTokenExpirationMinutes": 10, "Providers": {"WeChat": {"Enabled": true, "AppId": "REPLACE_WITH_ACTUAL_APP_ID", "AppSecret": "REPLACE_WITH_ACTUAL_APP_SECRET", "Scope": "snsapi_userinfo", "AuthorizationEndpoint": "https://open.weixin.qq.com/connect/oauth2/authorize", "TokenEndpoint": "https://api.weixin.qq.com/sns/oauth2/access_token", "UserInfoEndpoint": "https://api.weixin.qq.com/sns/userinfo"}, "GitHub": {"Enabled": true, "ClientId": "REPLACE_WITH_ACTUAL_CLIENT_ID", "ClientSecret": "REPLACE_WITH_ACTUAL_CLIENT_SECRET", "Scope": "user:email", "AuthorizationEndpoint": "https://github.com/login/oauth/authorize", "TokenEndpoint": "https://github.com/login/oauth/access_token", "UserInfoEndpoint": "https://api.github.com/user"}, "Google": {"Enabled": true, "ClientId": "REPLACE_WITH_ACTUAL_CLIENT_ID", "ClientSecret": "REPLACE_WITH_ACTUAL_CLIENT_SECRET", "Scope": "openid profile email", "AuthorizationEndpoint": "https://accounts.google.com/o/oauth2/v2/auth", "TokenEndpoint": "https://oauth2.googleapis.com/token", "UserInfoEndpoint": "https://www.googleapis.com/oauth2/v2/userinfo"}, "Microsoft": {"Enabled": false, "ClientId": "REPLACE_WITH_ACTUAL_CLIENT_ID", "ClientSecret": "REPLACE_WITH_ACTUAL_CLIENT_SECRET", "TenantId": "common", "Scope": "openid profile email", "AuthorizationEndpoint": "https://login.microsoftonline.com/{tenant}/oauth2/v2.0/authorize", "TokenEndpoint": "https://login.microsoftonline.com/{tenant}/oauth2/v2.0/token", "UserInfoEndpoint": "https://graph.microsoft.com/v1.0/me"}}}, "SubscriptionPricing": {"FreeTokenLimit": 5000, "BasicTokenLimit": 50000, "BasicPrice": 99, "ProTokenLimit": 200000, "ProPrice": 199, "UltraTokenLimit": 500000, "UltraPrice": 299}, "ExternalServices": {"AliyunSms": {"ApiUrl": "https://dysmsapi.aliyuncs.com"}, "Twilio": {"ApiUrl": "https://api.twilio.com/2010-04-01/Accounts"}, "Benchmark": {"BaseUrl": "https://localhost:5001", "MaxKeysToAnalyze": 5000}}, "Cache": {"DefaultExpirationMinutes": 30, "ShortExpirationMinutes": 5, "LongExpirationMinutes": 1440, "UserProfileExpirationMinutes": 60, "AgentListExpirationMinutes": 15, "SubscriptionPlanExpirationMinutes": 1440, "PermissionExpirationMinutes": 120, "SessionExpirationMinutes": 60, "StatisticsExpirationMinutes": 5, "SearchResultExpirationMinutes": 10, "EnableCacheWarming": true, "CacheWarmingBatchSize": 100, "EnableCompression": false, "CompressionThreshold": 1024}}