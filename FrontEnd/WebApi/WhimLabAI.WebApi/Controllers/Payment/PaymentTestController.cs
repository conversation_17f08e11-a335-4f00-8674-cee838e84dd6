using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WhimLabAI.Infrastructure.PaymentGateways;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.WebApi.Controllers.Payment;

/// <summary>
/// 支付测试控制器（仅开发环境）
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize(Roles = "Admin")]
public class PaymentTestController : ControllerBase
{
    private readonly PaymentTestHelper _testHelper;
    private readonly IWebHostEnvironment _environment;
    private readonly ILogger<PaymentTestController> _logger;

    public PaymentTestController(
        PaymentTestHelper testHelper,
        IWebHostEnvironment environment,
        ILogger<PaymentTestController> logger)
    {
        _testHelper = testHelper;
        _environment = environment;
        _logger = logger;
    }

    /// <summary>
    /// 模拟支付成功回调
    /// </summary>
    [HttpPost("simulate-callback/{paymentNo}")]
    public async Task<IActionResult> SimulatePaymentCallback(
        string paymentNo,
        [FromQuery] PaymentMethod method = PaymentMethod.Alipay,
        [FromQuery] bool success = true,
        CancellationToken cancellationToken = default)
    {
        if (!_environment.IsDevelopment())
        {
            return Forbid("此功能仅在开发环境可用");
        }

        var result = await _testHelper.SimulatePaymentCallbackAsync(
            method, paymentNo, success, cancellationToken);
            
        if (result.IsSuccess)
        {
            _logger.LogInformation(
                "Simulated payment callback: PaymentNo={PaymentNo}, Method={Method}, Success={Success}",
                paymentNo, method, success);
            return Ok(new { success = true, message = "支付回调模拟成功" });
        }
        
        return BadRequest(new { success = false, error = result.Error });
    }

    /// <summary>
    /// 模拟退款回调
    /// </summary>
    [HttpPost("simulate-refund-callback/{refundNo}")]
    public async Task<IActionResult> SimulateRefundCallback(
        string refundNo,
        [FromQuery] PaymentMethod method = PaymentMethod.Alipay,
        [FromQuery] bool success = true,
        CancellationToken cancellationToken = default)
    {
        if (!_environment.IsDevelopment())
        {
            return Forbid("此功能仅在开发环境可用");
        }

        var result = await _testHelper.SimulateRefundCallbackAsync(
            method, refundNo, success, cancellationToken);
            
        if (result.IsSuccess)
        {
            _logger.LogInformation(
                "Simulated refund callback: RefundNo={RefundNo}, Method={Method}, Success={Success}",
                refundNo, method, success);
            return Ok(new { success = true, message = "退款回调模拟成功" });
        }
        
        return BadRequest(new { success = false, error = result.Error });
    }

    /// <summary>
    /// 测试支付网关连接
    /// </summary>
    [HttpGet("test-gateway/{method}")]
    public async Task<IActionResult> TestPaymentGateway(
        PaymentMethod method,
        CancellationToken cancellationToken = default)
    {
        if (!_environment.IsDevelopment())
        {
            return Forbid("此功能仅在开发环境可用");
        }

        var result = await _testHelper.TestPaymentGatewayAsync(method, cancellationToken);
        
        _logger.LogInformation(
            "Tested payment gateway: Method={Method}, IsHealthy={IsHealthy}, Message={Message}",
            method, result.IsHealthy, result.Message);
            
        return Ok(result);
    }

    /// <summary>
    /// 获取支付测试信息
    /// </summary>
    [HttpGet("info")]
    public IActionResult GetTestInfo()
    {
        if (!_environment.IsDevelopment())
        {
            return Forbid("此功胝仅在开发环境可用");
        }

        return Ok(new
        {
            environment = _environment.EnvironmentName,
            availableMethods = new[] { "Alipay", "WeChatPay" },
            testEndpoints = new
            {
                simulateCallback = "/api/paymenttest/simulate-callback/{paymentNo}?method=Alipay&success=true",
                simulateRefund = "/api/paymenttest/simulate-refund-callback/{refundNo}?method=Alipay&success=true",
                testGateway = "/api/paymenttest/test-gateway/{method}"
            },
            note = "这些端点仅在开发环境可用，需要Admin权限"
        });
    }
}