using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.WebApi.Controllers.Payment;

/// <summary>
/// 支付管理控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class PaymentController : ControllerBase
{
    private readonly IPaymentService _paymentService;
    private readonly ILogger<PaymentController> _logger;

    public PaymentController(
        IPaymentService paymentService,
        ILogger<PaymentController> logger)
    {
        _paymentService = paymentService;
        _logger = logger;
    }

    /// <summary>
    /// 创建支付
    /// </summary>
    [HttpPost("create")]
    public async Task<IActionResult> CreatePayment([FromBody] ProcessPaymentDto request, CancellationToken cancellationToken)
    {
        request.ClientIp = HttpContext.Connection.RemoteIpAddress?.ToString();
        var result = await _paymentService.ProcessPaymentAsync(request, cancellationToken);
        
        if (result.IsSuccess)
        {
            _logger.LogInformation("Payment created for order {OrderId}", request.OrderId);
            return Ok(result);
        }
        
        return BadRequest(result);
    }

    /// <summary>
    /// 获取支付详情
    /// </summary>
    [HttpGet("{paymentId}")]
    public async Task<IActionResult> GetPayment(Guid paymentId, CancellationToken cancellationToken)
    {
        var result = await _paymentService.GetPaymentAsync(paymentId, cancellationToken);
        
        if (result.IsSuccess)
        {
            return Ok(result);
        }
        
        return NotFound(result);
    }

    /// <summary>
    /// 通过支付单号获取支付详情
    /// </summary>
    [HttpGet("by-no/{paymentNo}")]
    public async Task<IActionResult> GetPaymentByNo(string paymentNo, CancellationToken cancellationToken)
    {
        var result = await _paymentService.GetPaymentByNoAsync(paymentNo, cancellationToken);
        
        if (result.IsSuccess)
        {
            return Ok(result);
        }
        
        return NotFound(result);
    }

    /// <summary>
    /// 获取订单的所有支付记录
    /// </summary>
    [HttpGet("order/{orderId}")]
    public async Task<IActionResult> GetOrderPayments(Guid orderId, CancellationToken cancellationToken)
    {
        var result = await _paymentService.GetOrderPaymentsAsync(orderId, cancellationToken);
        
        if (result.IsSuccess)
        {
            return Ok(result);
        }
        
        return BadRequest(result);
    }

    /// <summary>
    /// 支付宝支付回调
    /// </summary>
    [HttpPost("callback/alipay")]
    [AllowAnonymous]
    public async Task<IActionResult> AlipayCallback([FromForm] Dictionary<string, string> parameters, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Received Alipay callback");
        
        try
        {
            // Check if parameters were already validated by middleware
            if (HttpContext.Items.TryGetValue("PaymentCallbackParameters", out var validatedParams))
            {
                parameters = (Dictionary<string, string>)validatedParams!;
                _logger.LogInformation("Using pre-validated parameters from middleware");
            }
            
            var result = await _paymentService.ProcessCallbackAsync(PaymentMethod.Alipay, parameters, cancellationToken);
            
            if (result.IsSuccess)
            {
                // Alipay expects "success" string for successful processing
                return Content("success");
            }
            
            // Return "fail" for Alipay to retry
            return Content("fail");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing Alipay callback");
            return Content("fail");
        }
    }

    /// <summary>
    /// 微信支付回调
    /// </summary>
    [HttpPost("callback/wechatpay")]
    [AllowAnonymous]
    public async Task<IActionResult> WeChatPayCallback(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Received WeChat Pay callback");
        
        try
        {
            Dictionary<string, string> parameters;
            
            // Check if parameters were already validated by middleware
            if (HttpContext.Items.TryGetValue("PaymentCallbackParameters", out var validatedParams))
            {
                parameters = (Dictionary<string, string>)validatedParams!;
                _logger.LogInformation("Using pre-validated parameters from middleware");
            }
            else
            {
                // Read XML body for WeChat Pay
                using var reader = new StreamReader(Request.Body);
                var xmlContent = await reader.ReadToEndAsync(cancellationToken);
                
                // Convert XML to dictionary (simplified, actual implementation would parse XML properly)
                parameters = new Dictionary<string, string>
                {
                    ["xml_content"] = xmlContent
                };
            }
            
            var result = await _paymentService.ProcessCallbackAsync(PaymentMethod.WeChatPay, parameters, cancellationToken);
            
            if (result.IsSuccess)
            {
                // WeChat Pay expects XML response
                var successXml = @"<xml>
                    <return_code><![CDATA[SUCCESS]]></return_code>
                    <return_msg><![CDATA[OK]]></return_msg>
                </xml>";
                return Content(successXml, "application/xml");
            }
            
            var failXml = @"<xml>
                <return_code><![CDATA[FAIL]]></return_code>
                <return_msg><![CDATA[处理失败]]></return_msg>
            </xml>";
            return Content(failXml, "application/xml");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing WeChat Pay callback");
            var errorXml = @"<xml>
                <return_code><![CDATA[FAIL]]></return_code>
                <return_msg><![CDATA[系统错误]]></return_msg>
            </xml>";
            return Content(errorXml, "application/xml");
        }
    }

    /// <summary>
    /// 申请退款
    /// </summary>
    [HttpPost("refund")]
    [Authorize(Roles = "Admin,Customer")]
    public async Task<IActionResult> ProcessRefund([FromBody] ProcessRefundDto request, CancellationToken cancellationToken)
    {
        // Set operator ID from current user
        request.OperatorId = User.Identity?.Name;
        
        var result = await _paymentService.ProcessRefundAsync(request, cancellationToken);
        
        if (result.IsSuccess)
        {
            _logger.LogInformation("Refund initiated for order {OrderId}", request.OrderId);
            return Ok(result);
        }
        
        return BadRequest(result);
    }

    /// <summary>
    /// 获取退款详情
    /// </summary>
    [HttpGet("refund/{refundId}")]
    public async Task<IActionResult> GetRefund(Guid refundId, CancellationToken cancellationToken)
    {
        var result = await _paymentService.GetRefundAsync(refundId, cancellationToken);
        
        if (result.IsSuccess)
        {
            return Ok(result);
        }
        
        return NotFound(result);
    }

    /// <summary>
    /// 获取订单的所有退款记录
    /// </summary>
    [HttpGet("refunds/order/{orderId}")]
    public async Task<IActionResult> GetOrderRefunds(Guid orderId, CancellationToken cancellationToken)
    {
        var result = await _paymentService.GetOrderRefundsAsync(orderId, cancellationToken);
        
        if (result.IsSuccess)
        {
            return Ok(result);
        }
        
        return BadRequest(result);
    }

    /// <summary>
    /// 查询支付状态
    /// </summary>
    [HttpPost("check-status/{paymentNo}")]
    public async Task<IActionResult> CheckPaymentStatus(string paymentNo, CancellationToken cancellationToken)
    {
        var result = await _paymentService.CheckPaymentStatusAsync(paymentNo, cancellationToken);
        
        if (result.IsSuccess)
        {
            return Ok(new { success = true, message = "支付状态已更新" });
        }
        
        return BadRequest(result);
    }

    /// <summary>
    /// 查询退款状态
    /// </summary>
    [HttpPost("check-refund-status/{refundNo}")]
    public async Task<IActionResult> CheckRefundStatus(string refundNo, CancellationToken cancellationToken)
    {
        var result = await _paymentService.CheckRefundStatusAsync(refundNo, cancellationToken);
        
        if (result.IsSuccess)
        {
            return Ok(new { success = true, message = "退款状态已更新" });
        }
        
        return BadRequest(result);
    }

    /// <summary>
    /// 获取支付统计
    /// </summary>
    [HttpGet("statistics")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> GetPaymentStatistics(
        [FromQuery] DateTime startDate, 
        [FromQuery] DateTime endDate, 
        CancellationToken cancellationToken)
    {
        if (startDate > endDate)
        {
            return BadRequest(new { error = "开始日期不能晚于结束日期" });
        }
        
        var result = await _paymentService.GetPaymentStatisticsAsync(startDate, endDate, cancellationToken);
        
        if (result.IsSuccess)
        {
            return Ok(result);
        }
        
        return BadRequest(result);
    }

    /// <summary>
    /// 支付宝退款回调
    /// </summary>
    [HttpPost("refund-callback/alipay")]
    [AllowAnonymous]
    public async Task<IActionResult> AlipayRefundCallback([FromForm] Dictionary<string, string> parameters, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Received Alipay refund callback with {Count} parameters", parameters.Count);
        
        try
        {
            // Extract refund information from callback
            var outRefundNo = parameters.GetValueOrDefault("out_biz_no", "");
            var refundStatus = parameters.GetValueOrDefault("refund_status", "");
            
            if (string.IsNullOrEmpty(outRefundNo))
            {
                _logger.LogWarning("Alipay refund callback missing out_biz_no");
                return Content("fail");
            }
            
            // Update refund status based on callback
            if (refundStatus == "REFUND_SUCCESS")
            {
                var checkResult = await _paymentService.CheckRefundStatusAsync(outRefundNo, cancellationToken);
                
                if (checkResult.IsSuccess)
                {
                    _logger.LogInformation("Alipay refund {RefundNo} status updated successfully", outRefundNo);
                    return Content("success");
                }
            }
            
            _logger.LogWarning("Alipay refund callback status not success: {Status}", refundStatus);
            return Content("success"); // Still return success to prevent retry
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing Alipay refund callback");
            return Content("fail");
        }
    }

    /// <summary>
    /// 微信支付退款回调
    /// </summary>
    [HttpPost("refund-callback/wechatpay")]
    [AllowAnonymous]
    public async Task<IActionResult> WeChatPayRefundCallback(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Received WeChat Pay refund callback");
        
        try
        {
            // Read encrypted XML body for WeChat Pay refund notification
            using var reader = new StreamReader(Request.Body);
            var xmlContent = await reader.ReadToEndAsync(cancellationToken);
            
            if (string.IsNullOrEmpty(xmlContent))
            {
                _logger.LogWarning("WeChat Pay refund callback with empty body");
                var failXml = @"<xml>
                    <return_code><![CDATA[FAIL]]></return_code>
                    <return_msg><![CDATA[参数错误]]></return_msg>
                </xml>";
                return Content(failXml, "application/xml");
            }
            
            // Parse XML to extract refund information
            // Note: WeChat Pay refund callbacks are encrypted and need decryption
            // This is simplified - in production you would decrypt the req_info field
            var doc = System.Xml.Linq.XDocument.Parse(xmlContent);
            var root = doc.Root;
            
            var returnCode = root?.Element("return_code")?.Value;
            if (returnCode != "SUCCESS")
            {
                _logger.LogWarning("WeChat Pay refund callback return_code not SUCCESS: {Code}", returnCode);
                var failXml = @"<xml>
                    <return_code><![CDATA[FAIL]]></return_code>
                    <return_msg><![CDATA[通信失败]]></return_msg>
                </xml>";
                return Content(failXml, "application/xml");
            }
            
            // In production, you would decrypt req_info and extract refund details
            // For now, acknowledge receipt
            _logger.LogInformation("WeChat Pay refund callback processed");
            
            var successXml = @"<xml>
                <return_code><![CDATA[SUCCESS]]></return_code>
                <return_msg><![CDATA[OK]]></return_msg>
            </xml>";
            return Content(successXml, "application/xml");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing WeChat Pay refund callback");
            var errorXml = @"<xml>
                <return_code><![CDATA[FAIL]]></return_code>
                <return_msg><![CDATA[系统错误]]></return_msg>
            </xml>";
            return Content(errorXml, "application/xml");
        }
    }
}