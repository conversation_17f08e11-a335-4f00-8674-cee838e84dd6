using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.WebApi.Controllers.Payment;

/// <summary>
/// 支付扩展控制器（用于前端兼容）
/// </summary>
[ApiController]
[Route("api/payment")]
[Authorize]
public class PaymentExtensionController : ControllerBase
{
    private readonly IPaymentService _paymentService;
    private readonly IOrderService _orderService;
    private readonly ILogger<PaymentExtensionController> _logger;

    public PaymentExtensionController(
        IPaymentService paymentService,
        IOrderService orderService,
        ILogger<PaymentExtensionController> logger)
    {
        _paymentService = paymentService;
        _orderService = orderService;
        _logger = logger;
    }

    /// <summary>
    /// 创建支付（前端兼容版本）
    /// </summary>
    [HttpPost("create")]
    public async Task<IActionResult> CreatePayment([FromBody] CreatePaymentRequest request, CancellationToken cancellationToken)
    {
        var userId = GetCurrentUserId();
        
        try
        {
            // 验证订单
            var orderResult = await _orderService.GetOrderAsync(request.OrderId, userId, cancellationToken);
            if (!orderResult.IsSuccess || orderResult.Value == null)
            {
                return BadRequest(new { error = "订单不存在" });
            }

            var order = orderResult.Value;
            if (order.Status != OrderStatus.Pending)
            {
                return BadRequest(new { error = "订单状态不允许支付" });
            }

            // 转换支付方式
            var paymentMethod = request.PaymentMethod.ToLower() switch
            {
                "alipay" => PaymentMethod.Alipay,
                "wechat" => PaymentMethod.WeChatPay,
                _ => PaymentMethod.Alipay
            };

            // 创建支付请求
            var paymentDto = new ProcessPaymentDto
            {
                OrderId = request.OrderId,
                PaymentMethod = paymentMethod,
                ReturnUrl = request.ReturnUrl,
                ClientIp = HttpContext.Connection.RemoteIpAddress?.ToString()
            };

            var result = await _paymentService.ProcessPaymentAsync(paymentDto, cancellationToken);
            
            if (result.IsSuccess && result.Value != null)
            {
                _logger.LogInformation("Payment created for order {OrderId}, PaymentId: {PaymentId}", 
                    request.OrderId, result.Value.PaymentId);
                
                var response = new CreatePaymentResponse
                {
                    PaymentId = result.Value.PaymentId,
                    PayUrl = result.Value.PaymentUrl,
                    QrCode = result.Value.QrCode
                };

                // 对于支付宝，检查是否返回了表单HTML
                if (paymentMethod == PaymentMethod.Alipay)
                {
                    if (!string.IsNullOrEmpty(result.Value.PaymentUrl) && result.Value.PaymentUrl.Contains("<form"))
                    {
                        // 如果是HTML表单，需要特殊处理
                        response.PayUrl = $"/api/payment/alipay/redirect/{result.Value.PaymentId}";
                    }
                    else if (string.IsNullOrEmpty(response.PayUrl) && HostEnvironment.IsDevelopment())
                    {
                        // 开发环境模拟
                        var returnUrlWithParams = $"{request.ReturnUrl}?orderId={request.OrderId}&paymentId={result.Value.PaymentId}";
                        response.PayUrl = $"/payment/simulate/alipay?orderId={request.OrderId}&returnUrl={Uri.EscapeDataString(returnUrlWithParams)}";
                    }
                }
                
                return Ok(response);
            }
            
            return BadRequest(new { error = result.Error ?? "创建支付失败" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating payment for order {OrderId}", request.OrderId);
            return StatusCode(500, new { error = "创建支付失败，请稍后重试" });
        }
    }

    /// <summary>
    /// 支付宝支付跳转页面
    /// </summary>
    [HttpGet("alipay/redirect/{paymentId}")]
    [AllowAnonymous]
    public async Task<IActionResult> AlipayRedirect(Guid paymentId, CancellationToken cancellationToken)
    {
        try
        {
            // 获取支付信息
            var paymentResult = await _paymentService.GetPaymentAsync(paymentId, cancellationToken);
            if (!paymentResult.IsSuccess || paymentResult.Value == null)
            {
                return NotFound("支付信息不存在");
            }

            var payment = paymentResult.Value;
            
            // 检查是否有HTML表单内容
            if (!string.IsNullOrEmpty(payment.GatewayResponse) && payment.GatewayResponse.Contains("<form"))
            {
                // 返回HTML页面，自动提交表单
                var html = $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='UTF-8'>
    <title>正在跳转到支付宝...</title>
    <style>
        body {{ font-family: Arial, sans-serif; text-align: center; margin-top: 100px; }}
        .loading {{ font-size: 18px; color: #666; }}
    </style>
</head>
<body>
    <div class='loading'>正在跳转到支付宝，请稍候...</div>
    {payment.GatewayResponse}
    <script>
        // 自动提交表单
        document.forms[0].submit();
    </script>
</body>
</html>";
                
                return Content(html, "text/html");
            }
            
            // 如果没有表单，直接跳转
            return Redirect(payment.GatewayResponse ?? "/payment/error");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error redirecting to Alipay for payment {PaymentId}", paymentId);
            return Redirect("/payment/error");
        }
    }

    /// <summary>
    /// 模拟支付成功（开发环境使用）
    /// </summary>
    [HttpGet("/payment/simulate/alipay")]
    [AllowAnonymous]
    public async Task<IActionResult> SimulateAlipayPayment(
        [FromQuery] Guid orderId, 
        [FromQuery] string returnUrl,
        CancellationToken cancellationToken)
    {
        // 在开发环境中模拟支付成功
        if (!HostEnvironment.IsDevelopment())
        {
            return NotFound();
        }

        try
        {
            // 模拟支付成功回调
            var parameters = new Dictionary<string, string>
            {
                ["out_trade_no"] = orderId.ToString(),
                ["trade_no"] = $"MOCK_{DateTime.Now.Ticks}",
                ["trade_status"] = "TRADE_SUCCESS",
                ["total_amount"] = "0.01",
                ["buyer_id"] = "mock_buyer_123"
            };

            await _paymentService.ProcessCallbackAsync(PaymentMethod.Alipay, parameters, cancellationToken);
            
            // 重定向到返回URL
            return Redirect(Uri.UnescapeDataString(returnUrl));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in payment simulation");
            return Redirect($"{Uri.UnescapeDataString(returnUrl)}&error=simulation_failed");
        }
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId))
        {
            throw new UnauthorizedAccessException("用户未认证");
        }
        return userId;
    }

    private IHostEnvironment HostEnvironment => HttpContext.RequestServices.GetRequiredService<IHostEnvironment>();
}

/// <summary>
/// 创建支付请求（前端版本）
/// </summary>
public class CreatePaymentRequest
{
    /// <summary>
    /// 订单ID
    /// </summary>
    public Guid OrderId { get; set; }
    
    /// <summary>
    /// 支付方式
    /// </summary>
    public string PaymentMethod { get; set; } = "alipay";
    
    /// <summary>
    /// 返回URL
    /// </summary>
    public string ReturnUrl { get; set; } = string.Empty;
}

/// <summary>
/// 创建支付响应
/// </summary>
public class CreatePaymentResponse
{
    public Guid PaymentId { get; set; }
    public string? PayUrl { get; set; }
    public string? QrCode { get; set; }
}