using Microsoft.AspNetCore.Mvc;

namespace WhimLabAI.WebApi.Controllers;

[ApiController]
[Route("api/[controller]")]
public class HealthController : ControllerBase
{
    private readonly ILogger<HealthController> _logger;

    public HealthController(ILogger<HealthController> logger)
    {
        _logger = logger;
    }

    [HttpGet("ping")]
    public IActionResult Ping()
    {
        _logger.LogInformation("Health check ping received");
        return Ok(new { message = "pong", timestamp = DateTime.UtcNow });
    }
}