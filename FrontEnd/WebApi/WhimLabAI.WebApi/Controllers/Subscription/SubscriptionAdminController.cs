using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Domain.Entities.Subscription;
using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Enums;
using WhimLabAI.WebApi.Attributes;

namespace WhimLabAI.WebApi.Controllers.Subscription;

/// <summary>
/// 订阅管理后台控制器
/// </summary>
[ApiController]
[Route("api/admin/subscriptions")]
[Authorize(Policy = "RequirePermission:subscription.manage")]
public class SubscriptionAdminController : ControllerBase
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<SubscriptionAdminController> _logger;

    public SubscriptionAdminController(
        IUnitOfWork unitOfWork,
        ILogger<SubscriptionAdminController> logger)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    /// <summary>
    /// 获取所有订阅套餐（包括未激活的）
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>订阅套餐列表</returns>
    [HttpGet("plans")]
    public async Task<IActionResult> GetAllSubscriptionPlans(CancellationToken cancellationToken = default)
    {
        var plans = await _unitOfWork.SubscriptionPlans.GetAllAsync(cancellationToken);
        return Ok(plans);
    }

    /// <summary>
    /// 创建订阅套餐
    /// </summary>
    /// <param name="request">创建请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>创建的套餐ID</returns>
    [HttpPost("plans")]
    public async Task<IActionResult> CreateSubscriptionPlan(
        [FromBody] CreateSubscriptionPlanRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var plan = new SubscriptionPlan(
                request.Name,
                request.Tier,
                Domain.ValueObjects.Money.Create(request.Price, request.Currency),
                request.MonthlyTokens,
                (BillingCycle)Enum.Parse(typeof(BillingCycle), "Monthly"),
                request.Description);

            await _unitOfWork.SubscriptionPlans.AddAsync(plan, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Created subscription plan {PlanId}", plan.Id);
            return Ok(new { planId = plan.Id });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating subscription plan");
            return BadRequest(new { error = "创建订阅套餐失败" });
        }
    }

    /// <summary>
    /// 更新订阅套餐
    /// </summary>
    /// <param name="planId">套餐ID</param>
    /// <param name="request">更新请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    [HttpPut("plans/{planId:guid}")]
    public async Task<IActionResult> UpdateSubscriptionPlan(
        Guid planId,
        [FromBody] UpdateSubscriptionPlanRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var planObj = await _unitOfWork.SubscriptionPlans.GetByIdAsync(planId, cancellationToken);
            if (planObj is not SubscriptionPlan plan)
            {
                return NotFound(new { error = "订阅套餐不存在" });
            }

            // Update price if provided
            if (request.NewPrice.HasValue)
            {
                plan.Update(
                    plan.Name,
                    request.Description ?? plan.Description,
                    Domain.ValueObjects.Money.Create(request.NewPrice.Value, "CNY"),
                    plan.TokenQuota
                );
            }
            else if (!string.IsNullOrEmpty(request.Description))
            {
                // Update description only
                plan.Update(
                    plan.Name,
                    request.Description,
                    plan.Price,
                    plan.TokenQuota
                );
            }

            // Update features if provided
            if (request.Features != null)
            {
                plan.SetFeatures(request.Features);
            }

            // Update advanced limits if any provided
            if (request.MaxAgents.HasValue || request.AllowCustomAgent.HasValue)
            {
                plan.SetAdvancedLimits(
                    request.MaxAgents ?? plan.MaxAgents,
                    plan.MaxConversationsPerDay,
                    request.AllowCustomAgent ?? plan.AllowCustomAgents,
                    plan.AllowPlugins,
                    plan.AllowKnowledgeBase
                );
            }

            // Update active status if provided
            if (request.IsActive.HasValue)
            {
                if (request.IsActive.Value)
                    plan.Activate();
                else
                    plan.Deactivate();
            }

            _unitOfWork.SubscriptionPlans.Update(plan);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Updated subscription plan {PlanId}", planId);
            return Ok(new { message = "订阅套餐更新成功" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating subscription plan {PlanId}", planId);
            return BadRequest(new { error = "更新订阅套餐失败" });
        }
    }

    /// <summary>
    /// 停用订阅套餐
    /// </summary>
    /// <param name="planId">套餐ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    [HttpPost("plans/{planId:guid}/deactivate")]
    public async Task<IActionResult> DeactivatePlan(
        Guid planId,
        CancellationToken cancellationToken = default)
    {
        var planObj = await _unitOfWork.SubscriptionPlans.GetByIdAsync(planId, cancellationToken);
        if (planObj is not SubscriptionPlan plan)
        {
            return NotFound(new { error = "订阅套餐不存在" });
        }
        
        plan.Deactivate();
        _unitOfWork.SubscriptionPlans.Update(plan);
        await _unitOfWork.SaveChangesAsync(cancellationToken);
        var result = true;
        
        if (result)
        {
            return Ok(new { message = "订阅套餐已停用" });
        }
        
        return BadRequest(new { error = "停用失败，可能存在使用该套餐的活跃订阅" });
    }

    /// <summary>
    /// 获取订阅统计信息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>订阅统计</returns>
    [HttpGet("statistics")]
    public async Task<IActionResult> GetSubscriptionStatistics(CancellationToken cancellationToken = default)
    {
        try
        {
            var allSubscriptions = await _unitOfWork.Subscriptions.GetAllAsync(cancellationToken);
            var subscriptions = allSubscriptions.Cast<Domain.Entities.Subscription.Subscription>().ToList();

            var statistics = new
            {
                TotalSubscriptions = subscriptions.Count,
                ActiveSubscriptions = subscriptions.Count(s => s.Status == SubscriptionStatus.Active),
                PendingSubscriptions = subscriptions.Count(s => s.Status == SubscriptionStatus.Pending),
                CancelledSubscriptions = subscriptions.Count(s => s.Status == SubscriptionStatus.Cancelled),
                ExpiredSubscriptions = subscriptions.Count(s => s.Status == SubscriptionStatus.Expired),
                SubscriptionsByTier = subscriptions
                    .Where(s => s.Status == SubscriptionStatus.Active)
                    .GroupBy(s => s.Plan?.Tier)
                    .ToDictionary(g => g.Key?.ToString() ?? "Unknown", g => g.Count()),
                MonthlyRevenue = subscriptions
                    .Where(s => s.Status == SubscriptionStatus.Active && s.Plan != null)
                    .Sum(s => s.Plan!.Price.Amount),
                ExpiringThisWeek = subscriptions
                    .Count(s => s.Status == SubscriptionStatus.Active && 
                               s.EndDate.HasValue && 
                               s.EndDate.Value <= DateTime.UtcNow.AddDays(7))
            };

            return Ok(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting subscription statistics");
            return BadRequest(new { error = "获取统计信息失败" });
        }
    }

    /// <summary>
    /// 获取即将过期的订阅
    /// </summary>
    /// <param name="days">天数（默认7天）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>即将过期的订阅列表</returns>
    [HttpGet("expiring")]
    public async Task<IActionResult> GetExpiringSubscriptions(
        [FromQuery] int days = 7,
        CancellationToken cancellationToken = default)
    {
        var expiringObj = await _unitOfWork.Subscriptions.GetExpiringSubscriptionsAsync(days, cancellationToken);
        var expiring = expiringObj.Cast<Domain.Entities.Subscription.Subscription>().ToList();

        var result = expiring.Select(s => new
        {
            s.Id,
            s.CustomerUserId,
            PlanName = s.Plan?.Name,
            s.Status,
            s.StartDate,
            s.EndDate,
            DaysRemaining = s.EndDate.HasValue ? (s.EndDate.Value - DateTime.UtcNow).Days : 0,
            s.AutoRenew
        });

        return Ok(result);
    }

    /// <summary>
    /// 获取高使用量用户
    /// </summary>
    /// <param name="topCount">返回数量（默认10）</param>
    /// <param name="days">统计天数（默认30）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>高使用量用户列表</returns>
    [HttpGet("top-users")]
    public async Task<IActionResult> GetTopUsers(
        [FromQuery] int topCount = 10,
        [FromQuery] int days = 30,
        CancellationToken cancellationToken = default)
    {
        // TODO: Implement GetTopUsersAsync in IUsageRecordRepository
        // For now, return empty list
        var topUsers = new List<object>();
        
        return Ok(topUsers);
    }

    /// <summary>
    /// 手动重置用户月度配额
    /// </summary>
    /// <param name="subscriptionId">订阅ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    [HttpPost("{subscriptionId:guid}/reset-quota")]
    [Authorize(Policy = "RequirePermission:subscription.reset-quota")]
    public async Task<IActionResult> ResetQuota(
        Guid subscriptionId,
        CancellationToken cancellationToken = default)
    {
        var result = await _unitOfWork.Subscriptions.ResetMonthlyTokensAsync(subscriptionId, cancellationToken);
        
        if (result)
        {
            _logger.LogInformation("Manually reset quota for subscription {SubscriptionId}", subscriptionId);
            return Ok(new { message = "配额重置成功" });
        }
        
        return BadRequest(new { error = "配额重置失败" });
    }
}

/// <summary>
/// 创建订阅套餐请求
/// </summary>
public class CreateSubscriptionPlanRequest
{
    public string Name { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public SubscriptionTier Tier { get; set; }
    public string? Description { get; set; }
    public decimal Price { get; set; }
    public string Currency { get; set; } = "CNY";
    public int MonthlyTokens { get; set; }
    public int DurationDays { get; set; } = 30;
    public List<string> Features { get; set; } = new();
    public int MaxAgents { get; set; } = 10;
    public bool AllowCustomAgent { get; set; } = false;
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// 更新订阅套餐请求
/// </summary>
public class UpdateSubscriptionPlanRequest
{
    public string? Description { get; set; }
    public decimal? NewPrice { get; set; }
    public List<string>? Features { get; set; }
    public int? MaxAgents { get; set; }
    public bool? AllowCustomAgent { get; set; }
    public bool? IsActive { get; set; }
}