using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.WebApi.Controllers.Orders;

/// <summary>
/// 订单管理控制器
/// </summary>
[ApiController]
[Route("api/orders")]
[Authorize]
public class OrdersController : ControllerBase
{
    private readonly IOrderService _orderService;
    private readonly ISubscriptionService _subscriptionService;
    private readonly ILogger<OrdersController> _logger;

    public OrdersController(
        IOrderService orderService,
        ISubscriptionService subscriptionService,
        ILogger<OrdersController> logger)
    {
        _orderService = orderService;
        _subscriptionService = subscriptionService;
        _logger = logger;
    }

    /// <summary>
    /// 创建订单（用于订阅购买）
    /// </summary>
    [HttpPost("create")]
    public async Task<IActionResult> CreateOrder([FromBody] CreateSubscriptionOrderRequest request, CancellationToken cancellationToken)
    {
        var userId = GetCurrentUserId();
        
        try
        {
            // 创建订单DTO
            var createOrderDto = new CreateOrderDto
            {
                CouponCode = request.CouponCode,
                Items = new List<OrderItemDto>
                {
                    new OrderItemDto
                    {
                        ProductId = request.PlanId,
                        ProductType = ProductType.SubscriptionPlan,
                        ProductName = "订阅套餐",
                        Quantity = 1,
                        UnitPrice = request.Amount
                    }
                }
            };

            var result = await _orderService.CreateOrderAsync(createOrderDto, userId, cancellationToken);
            
            if (result.IsSuccess && result.Value != null)
            {
                _logger.LogInformation("Subscription order created for user {UserId}, OrderId: {OrderId}", 
                    userId, result.Value.Id);
                
                return Ok(new CreateOrderResponse
                {
                    OrderId = result.Value.Id,
                    OrderNo = result.Value.OrderNo
                });
            }
            
            return BadRequest(new { error = result.Error });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating subscription order for user {UserId}", userId);
            return StatusCode(500, new { error = "创建订单失败，请稍后重试" });
        }
    }

    /// <summary>
    /// 获取订单状态
    /// </summary>
    [HttpGet("{orderId}/status")]
    public async Task<IActionResult> GetOrderStatus(Guid orderId, CancellationToken cancellationToken)
    {
        var userId = GetCurrentUserId();
        
        try
        {
            var result = await _orderService.GetOrderAsync(orderId, userId, cancellationToken);
            
            if (result.IsSuccess && result.Value != null)
            {
                var order = result.Value;
                
                // 映射支付状态
                var paymentStatus = order.Status switch
                {
                    OrderStatus.Paid => PaymentStatus.Success,
                    OrderStatus.Failed => PaymentStatus.Failed,
                    OrderStatus.Pending => PaymentStatus.Pending,
                    _ => PaymentStatus.Unknown
                };
                
                return Ok(new OrderStatusResponse
                {
                    PaymentStatus = paymentStatus,
                    OrderDetails = order.Status == OrderStatus.Paid ? new OrderDetailsDto
                    {
                        OrderNo = order.OrderNo,
                        PlanName = order.Items?.FirstOrDefault()?.ProductName ?? "未知套餐",
                        Amount = order.PayableAmount,
                        PaymentTime = order.PaidAt ?? DateTime.UtcNow
                    } : null,
                    ErrorMessage = order.Status == OrderStatus.Failed ? "支付失败，请重试" : null
                });
            }
            
            return Ok(new OrderStatusResponse
            {
                PaymentStatus = PaymentStatus.Unknown,
                ErrorMessage = "订单不存在"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting order status for OrderId: {OrderId}", orderId);
            return StatusCode(500, new { error = "查询订单状态失败" });
        }
    }

    /// <summary>
    /// 重试支付
    /// </summary>
    [HttpPost("{orderId}/retry-payment")]
    public async Task<IActionResult> RetryPayment(Guid orderId, CancellationToken cancellationToken)
    {
        var userId = GetCurrentUserId();
        
        try
        {
            // 获取订单信息
            var orderResult = await _orderService.GetOrderAsync(orderId, userId, cancellationToken);
            if (!orderResult.IsSuccess || orderResult.Value == null)
            {
                return BadRequest(new { error = "订单不存在" });
            }

            var order = orderResult.Value;
            if (order.Status != OrderStatus.Pending && order.Status != OrderStatus.Failed)
            {
                return BadRequest(new { error = "该订单状态不允许重试支付" });
            }

            // 发起新的支付
            var paymentResult = await _orderService.InitiatePaymentAsync(
                orderId, 
                PaymentMethod.Alipay, // 默认使用支付宝
                userId, 
                cancellationToken);
            
            if (paymentResult.IsSuccess && paymentResult.Value != null)
            {
                return Ok(new RetryPaymentResponse
                {
                    PaymentUrl = paymentResult.Value.PaymentUrl ?? string.Empty
                });
            }
            
            return BadRequest(new { error = "发起支付失败" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrying payment for OrderId: {OrderId}", orderId);
            return StatusCode(500, new { error = "重试支付失败，请稍后再试" });
        }
    }

    /// <summary>
    /// 创建订单（通用）
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> CreateOrder([FromBody] CreateOrderDto request, CancellationToken cancellationToken)
    {
        var userId = GetCurrentUserId();
        if (userId == Guid.Empty)
        {
            return Unauthorized(new { error = "无效的用户身份" });
        }

        request.ClientIp = HttpContext.Connection.RemoteIpAddress?.ToString();
        var result = await _orderService.CreateOrderAsync(request, userId, cancellationToken);
        
        if (result.IsSuccess)
        {
            _logger.LogInformation("Order created for user {UserId}", userId);
            return CreatedAtAction(nameof(GetOrder), new { orderId = result.Value!.Id }, result);
        }
        
        return BadRequest(result);
    }

    /// <summary>
    /// 获取订单详情
    /// </summary>
    [HttpGet("{orderId}")]
    public async Task<IActionResult> GetOrder(Guid orderId, CancellationToken cancellationToken)
    {
        var userId = GetCurrentUserId();
        if (userId == Guid.Empty)
        {
            return Unauthorized(new { error = "无效的用户身份" });
        }

        var result = await _orderService.GetOrderAsync(orderId, userId, cancellationToken);
        
        if (result.IsSuccess)
        {
            return Ok(result);
        }
        
        return NotFound(result);
    }

    /// <summary>
    /// 通过订单号获取订单
    /// </summary>
    [HttpGet("by-no/{orderNo}")]
    [Authorize(Roles = "Admin")] // Only admin can query by order number without user context
    public async Task<IActionResult> GetOrderByNo(string orderNo, CancellationToken cancellationToken)
    {
        var result = await _orderService.GetOrderByNoAsync(orderNo, cancellationToken);
        
        if (result.IsSuccess)
        {
            return Ok(result);
        }
        
        return NotFound(result);
    }

    /// <summary>
    /// 获取用户订单列表
    /// </summary>
    [HttpGet("my-orders")]
    public async Task<IActionResult> GetMyOrders([FromQuery] OrderQueryDto query, CancellationToken cancellationToken)
    {
        var userId = GetCurrentUserId();
        if (userId == Guid.Empty)
        {
            return Unauthorized(new { error = "无效的用户身份" });
        }

        var result = await _orderService.GetUserOrdersAsync(userId, query, cancellationToken);
        
        if (result.IsSuccess)
        {
            return Ok(result);
        }
        
        return BadRequest(result);
    }

    /// <summary>
    /// 取消订单
    /// </summary>
    [HttpPost("{orderId}/cancel")]
    public async Task<IActionResult> CancelOrder(Guid orderId, [FromBody] CancelOrderRequest request, CancellationToken cancellationToken)
    {
        var userId = GetCurrentUserId();
        if (userId == Guid.Empty)
        {
            return Unauthorized(new { error = "无效的用户身份" });
        }

        var result = await _orderService.CancelOrderAsync(orderId, userId, request.Reason, cancellationToken);
        
        if (result.IsSuccess)
        {
            _logger.LogInformation("Order {OrderId} cancelled by user {UserId}", orderId, userId);
            return Ok(new { success = true, message = "订单已取消" });
        }
        
        return BadRequest(result);
    }

    /// <summary>
    /// 发起支付
    /// </summary>
    [HttpPost("{orderId}/pay")]
    public async Task<IActionResult> InitiatePayment(Guid orderId, [FromBody] InitiatePaymentRequest request, CancellationToken cancellationToken)
    {
        var userId = GetCurrentUserId();
        if (userId == Guid.Empty)
        {
            return Unauthorized(new { error = "无效的用户身份" });
        }

        var result = await _orderService.InitiatePaymentAsync(orderId, request.PaymentMethod, userId, cancellationToken);
        
        if (result.IsSuccess)
        {
            _logger.LogInformation("Payment initiated for order {OrderId}", orderId);
            return Ok(result);
        }
        
        return BadRequest(result);
    }

    /// <summary>
    /// 更新订单状态（管理员）
    /// </summary>
    [HttpPut("{orderId}/status")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> UpdateOrderStatus(Guid orderId, [FromBody] UpdateOrderStatusRequest request, CancellationToken cancellationToken)
    {
        var result = await _orderService.UpdateOrderStatusAsync(orderId, request.Status, cancellationToken);
        
        if (result.IsSuccess)
        {
            _logger.LogInformation("Order {OrderId} status updated to {Status}", orderId, request.Status);
            return Ok(new { success = true, message = "订单状态已更新" });
        }
        
        return BadRequest(result);
    }

    /// <summary>
    /// 获取订单统计（管理员）
    /// </summary>
    [HttpGet("statistics")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> GetOrderStatistics(
        [FromQuery] DateTime startDate, 
        [FromQuery] DateTime endDate, 
        CancellationToken cancellationToken)
    {
        if (startDate > endDate)
        {
            return BadRequest(new { error = "开始日期不能晚于结束日期" });
        }
        
        var result = await _orderService.GetOrderStatisticsAsync(startDate, endDate, cancellationToken);
        
        if (result.IsSuccess)
        {
            return Ok(result);
        }
        
        return BadRequest(result);
    }

    /// <summary>
    /// 批量获取订单（管理员）
    /// </summary>
    [HttpPost("batch")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> GetOrdersBatch([FromBody] List<Guid> orderIds, CancellationToken cancellationToken)
    {
        if (!orderIds.Any() || orderIds.Count > 100)
        {
            return BadRequest(new { error = "订单ID数量必须在1-100之间" });
        }

        var orders = new List<OrderDto>();
        foreach (var orderId in orderIds)
        {
            var result = await _orderService.GetOrderAsync(orderId, Guid.Empty, cancellationToken);
            if (result.IsSuccess && result.Value != null)
            {
                orders.Add(result.Value);
            }
        }

        return Ok(new { orders });
    }

    /// <summary>
    /// 导出订单（管理员）
    /// </summary>
    [HttpGet("export")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> ExportOrders([FromQuery] OrderQueryDto query, CancellationToken cancellationToken)
    {
        // This would typically generate a CSV or Excel file
        // For now, just return the data that would be exported
        var allOrders = new List<OrderDto>();
        
        // Get all users' orders (admin feature)
        // Note: This is a simplified implementation
        // In production, you'd want to implement proper export with pagination
        
        return Ok(new
        {
            message = "订单导出功能尚未实现",
            exportFormat = "CSV",
            fields = new[] { "OrderNo", "UserId", "Status", "TotalAmount", "CreatedAt" }
        });
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId))
        {
            return Guid.Empty;
        }
        return userId;
    }
}

/// <summary>
/// 创建订阅订单请求
/// </summary>
public class CreateSubscriptionOrderRequest
{
    /// <summary>
    /// 套餐ID
    /// </summary>
    public Guid PlanId { get; set; }
    
    /// <summary>
    /// 金额
    /// </summary>
    public decimal Amount { get; set; }
    
    /// <summary>
    /// 优惠券代码
    /// </summary>
    public string? CouponCode { get; set; }
    
    /// <summary>
    /// 支付方式
    /// </summary>
    public string PaymentMethod { get; set; } = "alipay";
    
    /// <summary>
    /// 是否升级
    /// </summary>
    public bool IsUpgrade { get; set; }
}

/// <summary>
/// 创建订单响应
/// </summary>
public class CreateOrderResponse
{
    public Guid OrderId { get; set; }
    public string OrderNo { get; set; } = string.Empty;
}

/// <summary>
/// 订单状态响应
/// </summary>
public class OrderStatusResponse
{
    public PaymentStatus PaymentStatus { get; set; }
    public OrderDetailsDto? OrderDetails { get; set; }
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 订单详情DTO
/// </summary>
public class OrderDetailsDto
{
    public string OrderNo { get; set; } = string.Empty;
    public string PlanName { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public DateTime PaymentTime { get; set; }
}

/// <summary>
/// 重试支付响应
/// </summary>
public class RetryPaymentResponse
{
    public string PaymentUrl { get; set; } = string.Empty;
}

/// <summary>
/// 支付状态枚举
/// </summary>
public enum PaymentStatus
{
    Unknown,
    Success,
    Failed,
    Pending
}

/// <summary>
/// 取消订单请求
/// </summary>
public class CancelOrderRequest
{
    /// <summary>
    /// 取消原因
    /// </summary>
    public string Reason { get; set; } = string.Empty;
}

/// <summary>
/// 发起支付请求
/// </summary>
public class InitiatePaymentRequest
{
    /// <summary>
    /// 支付方式
    /// </summary>
    public PaymentMethod PaymentMethod { get; set; }
}

/// <summary>
/// 更新订单状态请求
/// </summary>
public class UpdateOrderStatusRequest
{
    /// <summary>
    /// 新状态
    /// </summary>
    public OrderStatus Status { get; set; }
}