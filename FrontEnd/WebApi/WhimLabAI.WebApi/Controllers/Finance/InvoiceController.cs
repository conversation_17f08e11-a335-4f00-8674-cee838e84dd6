using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Shared.Dtos.Invoice;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.WebApi.Controllers.Finance;

/// <summary>
/// 发票管理控制器
/// </summary>
[ApiController]
[Route("api/v1/[controller]")]
[Authorize]
public class InvoiceController : ControllerBase
{
    private readonly IInvoiceService _invoiceService;
    private readonly ILogger<InvoiceController> _logger;

    public InvoiceController(
        IInvoiceService invoiceService,
        ILogger<InvoiceController> logger)
    {
        _invoiceService = invoiceService;
        _logger = logger;
    }

    /// <summary>
    /// 创建发票
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public async Task<IActionResult> CreateInvoice([FromBody] CreateInvoiceDto dto)
    {
        try
        {
            var customerUserId = dto.CustomerUserId;
            var result = await _invoiceService.CreateInvoiceAsync(dto, customerUserId);
            
            if (!result.IsSuccess)
            {
                return BadRequest(new { error = result.Error });
            }
            
            return Ok(result.Value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建发票失败");
            return StatusCode(500, new { error = "创建发票失败" });
        }
    }

    /// <summary>
    /// 获取发票详情
    /// </summary>
    [HttpGet("{id}")]
    public async Task<IActionResult> GetInvoice(Guid id)
    {
        try
        {
            var userId = GetUserId();
            if (!userId.HasValue)
            {
                return Unauthorized();
            }
            
            var result = await _invoiceService.GetInvoiceAsync(id, userId.Value);
            
            if (!result.IsSuccess)
            {
                return NotFound(new { error = result.Error });
            }
            
            // 验证权限：管理员或发票所有者
            var invoice = result.Value;
            if (!User.IsInRole("Admin") && !User.IsInRole("SuperAdmin") && invoice.CustomerUserId != userId.Value)
            {
                return Forbid();
            }
            
            return Ok(invoice);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取发票详情失败");
            return StatusCode(500, new { error = "获取发票详情失败" });
        }
    }

    /// <summary>
    /// 获取发票列表（分页）
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> GetInvoices(
        [FromQuery] Guid? customerUserId,
        [FromQuery] DateTime? startDate,
        [FromQuery] DateTime? endDate,
        [FromQuery] InvoiceStatus? status,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20)
    {
        try
        {
            var userId = GetUserId();
            if (!userId.HasValue)
            {
                return Unauthorized();
            }
            
            // 非管理员只能查看自己的发票
            if (!User.IsInRole("Admin") && !User.IsInRole("SuperAdmin"))
            {
                customerUserId = userId.Value;
            }
            
            var result = await _invoiceService.GetInvoicesAsync(
                customerUserId ?? userId.Value,
                page,
                pageSize,
                status?.ToString(),
                startDate,
                endDate);
            
            if (!result.IsSuccess)
            {
                return BadRequest(new { error = result.Error });
            }
            
            return Ok(result.Value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取发票列表失败");
            return StatusCode(500, new { error = "获取发票列表失败" });
        }
    }

    /// <summary>
    /// 获取我的发票列表
    /// </summary>
    [HttpGet("my")]
    public async Task<IActionResult> GetMyInvoices(
        [FromQuery] DateTime? startDate,
        [FromQuery] DateTime? endDate,
        [FromQuery] InvoiceStatus? status,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20)
    {
        try
        {
            var userId = GetUserId();
            if (!userId.HasValue)
            {
                return Unauthorized();
            }
            
            var result = await _invoiceService.GetInvoicesAsync(
                userId.Value,
                page,
                pageSize,
                status?.ToString(),
                startDate,
                endDate);
            
            if (!result.IsSuccess)
            {
                return BadRequest(new { error = result.Error });
            }
            
            return Ok(result.Value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取我的发票列表失败");
            return StatusCode(500, new { error = "获取我的发票列表失败" });
        }
    }

    /// <summary>
    /// 更新发票
    /// </summary>
    [HttpPut("{id}")]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public async Task<IActionResult> UpdateInvoice(Guid id, [FromBody] UpdateInvoiceDto dto)
    {
        try
        {
            var userId = GetUserId();
            if (!userId.HasValue)
            {
                return Unauthorized();
            }
            
            var result = await _invoiceService.UpdateInvoiceAsync(id, dto, userId.Value);
            
            if (!result.IsSuccess)
            {
                return BadRequest(new { error = result.Error });
            }
            
            return Ok(result.Value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新发票失败");
            return StatusCode(500, new { error = "更新发票失败" });
        }
    }

    /// <summary>
    /// 标记发票为已支付
    /// </summary>
    [HttpPost("{id}/mark-paid")]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public async Task<IActionResult> MarkAsPaid(Guid id, [FromBody] MarkInvoicePaidDto dto)
    {
        try
        {
            var result = await _invoiceService.MarkAsPaidAsync(id, dto);
            
            if (!result.IsSuccess)
            {
                return BadRequest(new { error = result.Error });
            }
            
            return Ok(new { message = "发票已标记为已支付" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "标记发票为已支付失败");
            return StatusCode(500, new { error = "标记发票为已支付失败" });
        }
    }

    /// <summary>
    /// 取消发票
    /// </summary>
    [HttpPost("{id}/cancel")]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public async Task<IActionResult> CancelInvoice(Guid id, [FromBody] CancelInvoiceDto dto)
    {
        try
        {
            var userId = GetUserId();
            if (!userId.HasValue)
            {
                return Unauthorized();
            }
            
            var result = await _invoiceService.CancelInvoiceAsync(id, dto.Reason, userId.Value);
            
            if (!result.IsSuccess)
            {
                return BadRequest(new { error = result.Error });
            }
            
            return Ok(new { message = "发票已取消" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "取消发票失败");
            return StatusCode(500, new { error = "取消发票失败" });
        }
    }


    /// <summary>
    /// 下载发票PDF
    /// </summary>
    [HttpGet("{id}/download")]
    public async Task<IActionResult> DownloadInvoice(Guid id)
    {
        try
        {
            var userId = GetUserId();
            if (!userId.HasValue)
            {
                return Unauthorized();
            }
            
            // 获取发票验证权限
            var invoiceResult = await _invoiceService.GetInvoiceAsync(id, userId.Value);
            if (!invoiceResult.IsSuccess)
            {
                return NotFound(new { error = invoiceResult.Error });
            }
            
            var invoice = invoiceResult.Value;
            if (!User.IsInRole("Admin") && !User.IsInRole("SuperAdmin") && invoice.CustomerUserId != userId.Value)
            {
                return Forbid();
            }
            
            // 生成PDF
            var result = await _invoiceService.DownloadInvoiceAsync(id, userId.Value);
            
            if (!result.IsSuccess)
            {
                return BadRequest(new { error = result.Error });
            }
            
            return File(result.Value, "application/pdf", $"invoice_{invoice.InvoiceNumber}.pdf");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "下载发票PDF失败");
            return StatusCode(500, new { error = "下载发票PDF失败" });
        }
    }

    /// <summary>
    /// 发送发票邮件
    /// </summary>
    [HttpPost("{id}/send-email")]
    public async Task<IActionResult> SendInvoiceEmail(Guid id, [FromBody] SendInvoiceEmailDto dto)
    {
        try
        {
            var userId = GetUserId();
            if (!userId.HasValue)
            {
                return Unauthorized();
            }
            
            // 获取发票验证权限
            var invoiceResult = await _invoiceService.GetInvoiceAsync(id, userId.Value);
            if (!invoiceResult.IsSuccess)
            {
                return NotFound(new { error = invoiceResult.Error });
            }
            
            var invoice = invoiceResult.Value;
            if (!User.IsInRole("Admin") && !User.IsInRole("SuperAdmin") && invoice.CustomerUserId != userId.Value)
            {
                return Forbid();
            }
            
            var result = await _invoiceService.SendInvoiceEmailAsync(id, userId.Value, dto.Email ?? invoice.CustomerEmail);
            
            if (!result.IsSuccess)
            {
                return BadRequest(new { error = result.Error });
            }
            
            return Ok(new { message = "发票邮件已发送" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送发票邮件失败");
            return StatusCode(500, new { error = "发送发票邮件失败" });
        }
    }



    /// <summary>
    /// 获取发票统计信息
    /// </summary>
    [HttpGet("statistics")]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public async Task<IActionResult> GetInvoiceStatistics(
        [FromQuery] DateTime? startDate,
        [FromQuery] DateTime? endDate)
    {
        try
        {
            var userId = GetUserId();
            if (!userId.HasValue)
            {
                return Unauthorized();
            }
            
            var result = await _invoiceService.GetInvoiceStatisticsAsync(userId.Value);
            
            if (!result.IsSuccess)
            {
                return BadRequest(new { error = result.Error });
            }
            
            return Ok(result.Value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取发票统计信息失败");
            return StatusCode(500, new { error = "获取发票统计信息失败" });
        }
    }


    private Guid? GetUserId()
    {
        var userIdClaim = User.FindFirst("userId") ?? User.FindFirst("sub");
        if (userIdClaim != null && Guid.TryParse(userIdClaim.Value, out var userId))
        {
            return userId;
        }
        return null;
    }
}