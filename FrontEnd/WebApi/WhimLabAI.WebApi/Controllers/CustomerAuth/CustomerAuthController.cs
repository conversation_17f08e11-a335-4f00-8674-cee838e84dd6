using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Shared.Dtos.Auth;
using WhimLabAI.Shared.Dtos.Auth.Customer;
using WhimLabAI.Shared.Dtos.Customer.Auth;
using WhimLabAI.Shared.Dtos.Customer.Security;
using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Results;
using WhimLabAI.WebApi.Extensions;
using WhimLabAI.Infrastructure.ApiSecurity;

namespace WhimLabAI.WebApi.Controllers.CustomerAuth;

[ApiController]
[Route("api/customer/auth")]
[Produces("application/json")]
public class CustomerAuthController : ControllerBase
{
    private readonly ICustomerAuthService _authService;
    private readonly IOAuthService _oauthService;
    private readonly ILogger<CustomerAuthController> _logger;

    public CustomerAuthController(
        ICustomerAuthService authService,
        IOAuthService oauthService,
        ILogger<CustomerAuthController> logger)
    {
        _authService = authService;
        _oauthService = oauthService;
        _logger = logger;
    }

    /// <summary>
    /// 用户注册
    /// </summary>
    /// <param name="request">注册信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>认证响应</returns>
    [HttpPost("register")]
    [RegisterRateLimit]
    [ProducesResponseType(typeof(ApiResponse<AuthResponseDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Register(
        [FromBody] CustomerRegisterDto request,
        CancellationToken cancellationToken = default)
    {
        // 获取客户端信息
        request.IpAddress = HttpContext.GetClientIpAddress();
        request.UserAgent = Request.Headers["User-Agent"].ToString();

        var result = await _authService.RegisterAsync(request, cancellationToken);

        if (!result.IsSuccess)
        {
            return BadRequest(ApiResponse<ErrorResponse>.Fail(result.Error));
        }

        return Ok(ApiResponse<AuthResponseDto>.Ok(result.Value, "注册成功"));
    }
    
    /// <summary>
    /// 通过用户名注册
    /// </summary>
    /// <param name="request">注册信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>认证响应</returns>
    [HttpPost("register/username")]
    [RegisterRateLimit]
    [ProducesResponseType(typeof(ApiResponse<AuthResponseDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> RegisterByUsername(
        [FromBody] CustomerRegisterByUsernameDto request,
        CancellationToken cancellationToken = default)
    {
        var result = await _authService.RegisterByUsernameAsync(request, cancellationToken);

        if (!result.IsSuccess)
        {
            return BadRequest(ApiResponse<ErrorResponse>.Fail(result.Error));
        }

        return Ok(ApiResponse<AuthResponseDto>.Ok(result.Value, "注册成功"));
    }
    
    /// <summary>
    /// 通过邮箱注册
    /// </summary>
    /// <param name="request">注册信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>认证响应</returns>
    [HttpPost("register/email")]
    [RegisterRateLimit]
    [ProducesResponseType(typeof(ApiResponse<AuthResponseDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> RegisterByEmail(
        [FromBody] CustomerRegisterByEmailDto request,
        CancellationToken cancellationToken = default)
    {
        var result = await _authService.RegisterByEmailAsync(request, cancellationToken);

        if (!result.IsSuccess)
        {
            return BadRequest(ApiResponse<ErrorResponse>.Fail(result.Error));
        }

        return Ok(ApiResponse<AuthResponseDto>.Ok(result.Value, "注册成功"));
    }
    
    /// <summary>
    /// 通过手机号注册
    /// </summary>
    /// <param name="request">注册信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>认证响应</returns>
    [HttpPost("register/phone")]
    [RegisterRateLimit]
    [ProducesResponseType(typeof(ApiResponse<AuthResponseDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> RegisterByPhone(
        [FromBody] CustomerRegisterByPhoneDto request,
        CancellationToken cancellationToken = default)
    {
        var result = await _authService.RegisterByPhoneAsync(request, cancellationToken);

        if (!result.IsSuccess)
        {
            return BadRequest(ApiResponse<ErrorResponse>.Fail(result.Error));
        }

        return Ok(ApiResponse<AuthResponseDto>.Ok(result.Value, "注册成功"));
    }
    
    /// <summary>
    /// 检查账号是否存在
    /// </summary>
    /// <param name="request">检查请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>账号是否存在</returns>
    [HttpPost("check-account")]
    [ProducesResponseType(typeof(ApiResponse<bool>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> CheckAccountExists(
        [FromBody] CheckAccountExistsDto request,
        CancellationToken cancellationToken = default)
    {
        var result = await _authService.CheckAccountExistsAsync(request, cancellationToken);

        if (!result.IsSuccess)
        {
            return BadRequest(ApiResponse<ErrorResponse>.Fail(result.Error));
        }

        return Ok(ApiResponse<bool>.Ok(result.Value, result.Value ? "账号已存在" : "账号可用"));
    }
    
    /// <summary>
    /// 发送验证码
    /// </summary>
    /// <param name="request">发送验证码请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    [HttpPost("send-verification-code")]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> SendVerificationCode(
        [FromBody] WhimLabAI.Shared.Dtos.Customer.Auth.SendVerificationCodeDto request,
        CancellationToken cancellationToken = default)
    {
        var result = await _authService.SendVerificationCodeAsync(request, cancellationToken);

        if (!result.IsSuccess)
        {
            return BadRequest(ApiResponse<ErrorResponse>.Fail(result.Error));
        }

        return Ok(ApiResponse<object>.Ok(result.Value, "验证码已发送"));
    }

    /// <summary>
    /// 用户登录
    /// </summary>
    /// <param name="request">登录信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>认证响应</returns>
    [HttpPost("login")]
    [LoginRateLimit]
    [ProducesResponseType(typeof(ApiResponse<AuthResponseDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Login(
        [FromBody] CustomerLoginDto request,
        CancellationToken cancellationToken = default)
    {
        // 获取客户端信息
        request.IpAddress = HttpContext.GetClientIpAddress();
        request.UserAgent = Request.Headers["User-Agent"].ToString();

        var result = await _authService.LoginAsync(request, cancellationToken);

        if (!result.IsSuccess)
        {
            return BadRequest(ApiResponse<ErrorResponse>.Fail(result.Error));
        }

        return Ok(ApiResponse<AuthResponseDto>.Ok(result.Value, "登录成功"));
    }

    /// <summary>
    /// 验证码登录
    /// </summary>
    /// <param name="request">登录信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>认证响应</returns>
    [HttpPost("login/verification-code")]
    [LoginRateLimit]
    [ProducesResponseType(typeof(ApiResponse<AuthResponseDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> LoginByVerificationCode(
        [FromBody] CustomerLoginByVerificationCodeDto request,
        CancellationToken cancellationToken = default)
    {
        // 获取客户端信息
        request.IpAddress = HttpContext.GetClientIpAddress();
        request.UserAgent = Request.Headers["User-Agent"].ToString();

        var result = await _authService.LoginByVerificationCodeAsync(request, cancellationToken);

        if (!result.IsSuccess)
        {
            return BadRequest(ApiResponse<ErrorResponse>.Fail(result.Error));
        }

        return Ok(ApiResponse<AuthResponseDto>.Ok(result.Value, "登录成功"));
    }

    /// <summary>
    /// 用户登出
    /// </summary>
    /// <param name="deviceId">设备ID（可选）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    [HttpPost("logout")]
    [Authorize]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Logout(
        [FromQuery] string? deviceId = null,
        CancellationToken cancellationToken = default)
    {
        var userId = User.GetUserId();
        var result = await _authService.LogoutAsync(userId, deviceId, cancellationToken);

        if (!result.IsSuccess)
        {
            return BadRequest(ApiResponse<ErrorResponse>.Fail(result.Error));
        }

        return Ok(ApiResponse<object>.Ok(null, "登出成功"));
    }

    /// <summary>
    /// 刷新访问令牌
    /// </summary>
    /// <param name="request">刷新令牌请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>新的认证响应</returns>
    [HttpPost("refresh-token")]
    [ProducesResponseType(typeof(ApiResponse<AuthResponseDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> RefreshToken(
        [FromBody] CustomerRefreshTokenRequestDto request,
        CancellationToken cancellationToken = default)
    {
        var refreshTokenDto = new RefreshTokenDto { RefreshToken = request.RefreshToken };
        var result = await _authService.RefreshTokenAsync(refreshTokenDto, cancellationToken);

        if (!result.IsSuccess)
        {
            return BadRequest(ApiResponse<ErrorResponse>.Fail(result.Error));
        }

        return Ok(ApiResponse<AuthResponseDto>.Ok(result.Value, "刷新成功"));
    }

    /// <summary>
    /// 修改密码
    /// </summary>
    /// <param name="request">修改密码请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    [HttpPost("change-password")]
    [Authorize]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> ChangePassword(
        [FromBody] CustomerChangePasswordDto request,
        CancellationToken cancellationToken = default)
    {
        var userId = User.GetUserId();
        var result = await _authService.ChangePasswordAsync(
            userId, 
            request.OldPassword, 
            request.NewPassword, 
            cancellationToken);

        if (!result.IsSuccess)
        {
            return BadRequest(ApiResponse<ErrorResponse>.Fail(result.Error));
        }

        return Ok(ApiResponse<object>.Ok(null, "密码修改成功"));
    }

    /// <summary>
    /// 发送密码重置验证码
    /// </summary>
    /// <param name="request">发送验证码请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    [HttpPost("send-reset-code")]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> SendResetCode(
        [FromBody] CustomerSendResetCodeDto request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var verificationService = HttpContext.RequestServices.GetRequiredService<IVerificationService>();
            
            // 判断是邮箱还是手机号
            bool isEmail = request.Type.Equals("email", StringComparison.OrdinalIgnoreCase);
            Result<bool> result;
            
            if (isEmail)
            {
                if (!IsValidEmail(request.Account))
                {
                    return BadRequest(ApiResponse<ErrorResponse>.Fail("邮箱格式不正确"));
                }
                result = await verificationService.SendEmailCodeAsync(request.Account, VerificationType.ResetPassword, cancellationToken);
            }
            else
            {
                if (!IsValidPhoneNumber(request.Account))
                {
                    return BadRequest(ApiResponse<ErrorResponse>.Fail("手机号格式不正确"));
                }
                result = await verificationService.SendSmsCodeAsync(request.Account, VerificationType.ResetPassword, cancellationToken);
            }
            
            if (!result.IsSuccess)
            {
                return BadRequest(ApiResponse<ErrorResponse>.Fail(result.Error));
            }
            
            _logger.LogInformation("密码重置验证码发送成功: {Account}", request.Account);
            return Ok(ApiResponse<object>.Ok(null, "验证码已发送"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送密码重置验证码失败: {Account}", request.Account);
            return BadRequest(ApiResponse<ErrorResponse>.Fail("发送验证码失败，请稍后重试"));
        }
    }
    
    private static bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }
    
    private static bool IsValidPhoneNumber(string phone)
    {
        return System.Text.RegularExpressions.Regex.IsMatch(phone, @"^1[3-9]\d{9}$");
    }

    /// <summary>
    /// 重置密码
    /// </summary>
    /// <param name="request">重置密码请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    [HttpPost("reset-password")]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> ResetPassword(
        [FromBody] CustomerResetPasswordDto request,
        CancellationToken cancellationToken = default)
    {
        var result = await _authService.ResetPasswordAsync(
            request.Account, 
            request.NewPassword, 
            request.VerificationCode, 
            cancellationToken);

        if (!result.IsSuccess)
        {
            return BadRequest(ApiResponse<ErrorResponse>.Fail(result.Error));
        }

        return Ok(ApiResponse<object>.Ok(null, "密码重置成功"));
    }

    /// <summary>
    /// 验证访问令牌
    /// </summary>
    /// <returns>验证结果</returns>
    [HttpGet("verify")]
    [Authorize]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status200OK)]
    public IActionResult VerifyToken()
    {
        return Ok(ApiResponse<object>.Ok(null, "令牌有效"));
    }

    /// <summary>
    /// 获取OAuth授权URL
    /// </summary>
    /// <param name="provider">OAuth提供商（WeChat、GitHub、Google、Microsoft）</param>
    /// <param name="state">客户端状态（可选）</param>
    /// <param name="redirectUri">回调URL（可选）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>授权URL</returns>
    [HttpGet("oauth/{provider}/authorize")]
    [ProducesResponseType(typeof(ApiResponse<string>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GetOAuthAuthorizationUrl(
        string provider,
        [FromQuery] string? state = null,
        [FromQuery] string? redirectUri = null,
        CancellationToken cancellationToken = default)
    {
        var result = await _oauthService.GetAuthorizationUrlAsync(provider, state ?? string.Empty, redirectUri);
        
        if (!result.IsSuccess)
        {
            return BadRequest(ApiResponse<ErrorResponse>.Fail(result.Error));
        }
        
        return Ok(ApiResponse<string>.Ok(result.Value, "获取授权链接成功"));
    }

    /// <summary>
    /// OAuth回调处理
    /// </summary>
    /// <param name="provider">OAuth提供商</param>
    /// <param name="code">授权码</param>
    /// <param name="state">状态参数</param>
    /// <param name="redirectUri">回调URL（可选）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>认证响应</returns>
    [HttpGet("oauth/{provider}/callback")]
    [ProducesResponseType(typeof(ApiResponse<AuthResponseDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> OAuthCallback(
        string provider,
        [FromQuery] string code,
        [FromQuery] string state,
        [FromQuery] string? redirectUri = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Exchange code for user info
            var userInfoResult = await _oauthService.ExchangeCodeForTokenAsync(provider, code, state, redirectUri);
            if (!userInfoResult.IsSuccess)
            {
                return BadRequest(ApiResponse<ErrorResponse>.Fail(userInfoResult.Error));
            }
            
            // Login or create user
            var loginResult = await _oauthService.LoginOrCreateUserAsync(provider, userInfoResult.Value);
            if (!loginResult.IsSuccess)
            {
                return BadRequest(ApiResponse<ErrorResponse>.Fail(loginResult.Error));
            }
            
            var authResponse = new AuthResponseDto
            {
                AccessToken = loginResult.Value.AccessToken,
                RefreshToken = loginResult.Value.RefreshToken,
                ExpiresIn = 3600, // 1 hour
                User = new UserInfoDto
                {
                    Id = loginResult.Value.UserId,
                    Username = loginResult.Value.Username,
                    UserType = Shared.Enums.UserType.Customer
                }
            };
            
            return Ok(ApiResponse<AuthResponseDto>.Ok(authResponse, 
                loginResult.Value.IsNewUser ? "注册成功" : "登录成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "OAuth callback failed for provider {Provider}", provider);
            return BadRequest(ApiResponse<ErrorResponse>.Fail("OAuth认证失败"));
        }
    }

    /// <summary>
    /// 刷新OAuth访问令牌
    /// </summary>
    /// <param name="provider">OAuth提供商</param>
    /// <param name="refreshToken">刷新令牌</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>新的令牌信息</returns>
    [HttpPost("oauth/{provider}/refresh")]
    [Authorize]
    [ProducesResponseType(typeof(ApiResponse<OAuthTokenInfo>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> RefreshOAuthToken(
        string provider,
        [FromBody] CustomerRefreshOAuthTokenDto request,
        CancellationToken cancellationToken = default)
    {
        var result = await _oauthService.RefreshTokenAsync(provider, request.RefreshToken);
        
        if (!result.IsSuccess)
        {
            return BadRequest(ApiResponse<ErrorResponse>.Fail(result.Error));
        }
        
        return Ok(ApiResponse<OAuthTokenInfo>.Ok(result.Value, "刷新令牌成功"));
    }
    
    /// <summary>
    /// 生成二维码登录
    /// </summary>
    /// <param name="request">生成二维码请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>二维码信息</returns>
    [HttpPost("qr-code/generate")]
    [ProducesResponseType(typeof(ApiResponse<QRCodeResponseDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GenerateQRCode(
        [FromBody] GenerateQRCodeDto request,
        CancellationToken cancellationToken = default)
    {
        // 获取客户端信息
        request.IpAddress = HttpContext.GetClientIpAddress();
        request.UserAgent = Request.Headers["User-Agent"].ToString();
        
        var result = await _authService.GenerateQRCodeAsync(request, cancellationToken);
        
        if (!result.IsSuccess)
        {
            return BadRequest(ApiResponse<ErrorResponse>.Fail(result.Error));
        }
        
        return Ok(ApiResponse<QRCodeResponseDto>.Ok(result.Value, "二维码生成成功"));
    }
    
    /// <summary>
    /// 获取二维码状态
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>二维码状态</returns>
    [HttpGet("qr-code/{sessionId}/status")]
    [ProducesResponseType(typeof(ApiResponse<QRCodeStatusDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GetQRCodeStatus(
        string sessionId,
        CancellationToken cancellationToken = default)
    {
        var result = await _authService.GetQRCodeStatusAsync(sessionId, cancellationToken);
        
        if (!result.IsSuccess)
        {
            return BadRequest(ApiResponse<ErrorResponse>.Fail(result.Error));
        }
        
        return Ok(ApiResponse<QRCodeStatusDto>.Ok(result.Value, "获取状态成功"));
    }
    
    /// <summary>
    /// 扫描二维码
    /// </summary>
    /// <param name="request">扫描二维码请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>扫描结果</returns>
    [HttpPost("qr-code/scan")]
    [Authorize]
    [ProducesResponseType(typeof(ApiResponse<QRCodeScanResponseDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> ScanQRCode(
        [FromBody] ScanQRCodeDto request,
        CancellationToken cancellationToken = default)
    {
        // 获取客户端信息
        request.ScannerIp = HttpContext.GetClientIpAddress();
        request.ScannerUserAgent = Request.Headers["User-Agent"].ToString();
        
        var result = await _authService.ScanQRCodeAsync(request, cancellationToken);
        
        if (!result.IsSuccess)
        {
            return BadRequest(ApiResponse<ErrorResponse>.Fail(result.Error));
        }
        
        return Ok(ApiResponse<QRCodeScanResponseDto>.Ok(result.Value, "扫描成功"));
    }
    
    /// <summary>
    /// 确认二维码登录
    /// </summary>
    /// <param name="request">确认登录请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>认证响应</returns>
    [HttpPost("qr-code/confirm")]
    [Authorize]
    [ProducesResponseType(typeof(ApiResponse<AuthResponseDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> ConfirmQRCodeLogin(
        [FromBody] ConfirmQRCodeLoginDto request,
        CancellationToken cancellationToken = default)
    {
        // 获取客户端信息
        request.IpAddress = HttpContext.GetClientIpAddress();
        request.UserAgent = Request.Headers["User-Agent"].ToString();
        
        // 设置用户ID
        request.UserId = User.GetUserId();
        
        var result = await _authService.ConfirmQRCodeLoginAsync(request, cancellationToken);
        
        if (!result.IsSuccess)
        {
            return BadRequest(ApiResponse<ErrorResponse>.Fail(result.Error));
        }
        
        return Ok(ApiResponse<AuthResponseDto>.Ok(result.Value, "登录成功"));
    }
    
    /// <summary>
    /// 取消二维码登录
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    [HttpPost("qr-code/{sessionId}/cancel")]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> CancelQRCode(
        string sessionId,
        CancellationToken cancellationToken = default)
    {
        var result = await _authService.CancelQRCodeAsync(sessionId, cancellationToken);
        
        if (!result.IsSuccess)
        {
            return BadRequest(ApiResponse<ErrorResponse>.Fail(result.Error));
        }
        
        return Ok(ApiResponse<object>.Ok(null, "取消成功"));
    }
    
    /// <summary>
    /// 获取验证码
    /// </summary>
    /// <returns>验证码信息</returns>
    [HttpGet("captcha")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(ApiResponse<CaptchaDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GetCaptcha(CancellationToken cancellationToken = default)
    {
        var verificationService = HttpContext.RequestServices.GetRequiredService<IVerificationService>();
        var result = await verificationService.GenerateCaptchaAsync(cancellationToken);
        
        if (!result.IsSuccess)
        {
            return BadRequest(ApiResponse<ErrorResponse>.Fail("生成验证码失败"));
        }
        
        var captcha = new CaptchaDto
        {
            Key = result.Value.CaptchaId,
            Image = $"data:{result.Value.ContentType};base64,{Convert.ToBase64String(result.Value.ImageData)}"
        };

        return Ok(ApiResponse<CaptchaDto>.Ok(captcha));
    }
    
    /// <summary>
    /// 发送登录验证码
    /// </summary>
    /// <param name="request">发送验证码请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    [HttpPost("send-login-code")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> SendLoginCode(
        [FromBody] CustomerSendLoginCodeDto request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var verificationService = HttpContext.RequestServices.GetRequiredService<IVerificationService>();
            
            // 判断是邮箱还是手机号
            bool isEmail = request.Type.Equals("email", StringComparison.OrdinalIgnoreCase);
            Result<bool> result;
            
            if (isEmail)
            {
                if (!IsValidEmail(request.Account))
                {
                    return BadRequest(ApiResponse<ErrorResponse>.Fail("邮箱格式不正确"));
                }
                result = await verificationService.SendEmailCodeAsync(request.Account, VerificationType.Login, cancellationToken);
            }
            else
            {
                if (!IsValidPhoneNumber(request.Account))
                {
                    return BadRequest(ApiResponse<ErrorResponse>.Fail("手机号格式不正确"));
                }
                result = await verificationService.SendSmsCodeAsync(request.Account, VerificationType.Login, cancellationToken);
            }
            
            if (!result.IsSuccess)
            {
                return BadRequest(ApiResponse<ErrorResponse>.Fail(result.Error));
            }
            
            _logger.LogInformation("登录验证码发送成功: {Account}", request.Account);
            return Ok(ApiResponse<object>.Ok(null, "验证码已发送"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送登录验证码失败: {Account}", request.Account);
            return BadRequest(ApiResponse<ErrorResponse>.Fail("发送验证码失败，请稍后重试"));
        }
    }
    
    /// <summary>
    /// 使用恢复码登录
    /// </summary>
    /// <param name="request">恢复码登录请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>认证响应</returns>
    [HttpPost("login/recovery-code")]
    [LoginRateLimit]
    [ProducesResponseType(typeof(ApiResponse<AuthResponseDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<ErrorResponse>), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> LoginWithRecoveryCode(
        [FromBody] LoginWithRecoveryCodeDto request,
        CancellationToken cancellationToken = default)
    {
        // 获取客户端信息
        request.IpAddress = HttpContext.GetClientIpAddress();
        request.UserAgent = Request.Headers["User-Agent"].ToString();
        
        var result = await _authService.LoginWithRecoveryCodeAsync(request, cancellationToken);
        
        if (!result.IsSuccess)
        {
            return BadRequest(ApiResponse<ErrorResponse>.Fail(result.Error));
        }
        
        return Ok(ApiResponse<AuthResponseDto>.Ok(result.Value));
    }
}


