using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.WebApi.Attributes;
using WhimLabAI.WebApi.Authorization;

namespace WhimLabAI.WebApi.Controllers.Admin;

/// <summary>
/// 管理员安全管理控制器
/// </summary>
[ApiController]
[Route("api/admin/security")]
[Authorize(Roles = "Admin,SuperAdmin")]
public class AdminSecurityController : ControllerBase
{
    private readonly ISecurityValidationService _securityService;
    private readonly ILogger<AdminSecurityController> _logger;

    public AdminSecurityController(
        ISecurityValidationService securityService,
        ILogger<AdminSecurityController> logger)
    {
        _securityService = securityService;
        _logger = logger;
    }

    /// <summary>
    /// 运行完整的安全扫描
    /// </summary>
    [HttpPost("scan/full")]
    [RequirePermission("security.scan")]
    [ProducesResponseType(typeof(SecurityScanResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> RunFullSecurityScan()
    {
        _logger.LogInformation("Starting full security scan requested by {AdminId}", User.Identity?.Name);
        
        var result = await _securityService.RunFullSecurityScanAsync();
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        _logger.LogInformation("Security scan completed. Found {IssueCount} issues", result.Value!.Issues.Count);
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 检查SQL注入漏洞
    /// </summary>
    [HttpGet("vulnerabilities/sql-injection")]
    [RequirePermission("security.scan")]
    [ProducesResponseType(typeof(List<SqlInjectionVulnerability>), StatusCodes.Status200OK)]
    public async Task<IActionResult> CheckSqlInjectionVulnerabilities()
    {
        var result = await _securityService.CheckSqlInjectionVulnerabilitiesAsync();
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 检查XSS漏洞
    /// </summary>
    [HttpGet("vulnerabilities/xss")]
    [RequirePermission("security.scan")]
    [ProducesResponseType(typeof(List<XssVulnerability>), StatusCodes.Status200OK)]
    public async Task<IActionResult> CheckXssVulnerabilities()
    {
        var result = await _securityService.CheckXssVulnerabilitiesAsync();
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 检查CSRF保护状态
    /// </summary>
    [HttpGet("status/csrf")]
    [RequirePermission("security.view")]
    [ProducesResponseType(typeof(CsrfProtectionStatus), StatusCodes.Status200OK)]
    public async Task<IActionResult> CheckCsrfProtection()
    {
        var result = await _securityService.CheckCsrfProtectionAsync();
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 检查安全头配置
    /// </summary>
    [HttpGet("status/headers")]
    [RequirePermission("security.view")]
    [ProducesResponseType(typeof(SecurityHeadersStatus), StatusCodes.Status200OK)]
    public async Task<IActionResult> CheckSecurityHeaders()
    {
        var result = await _securityService.CheckSecurityHeadersAsync();
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 检查认证配置
    /// </summary>
    [HttpGet("status/authentication")]
    [RequirePermission("security.view")]
    [ProducesResponseType(typeof(AuthenticationConfigStatus), StatusCodes.Status200OK)]
    public async Task<IActionResult> CheckAuthenticationConfig()
    {
        var result = await _securityService.CheckAuthenticationConfigAsync();
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 检查加密配置
    /// </summary>
    [HttpGet("status/encryption")]
    [RequirePermission("security.view")]
    [ProducesResponseType(typeof(EncryptionConfigStatus), StatusCodes.Status200OK)]
    public async Task<IActionResult> CheckEncryptionConfig()
    {
        var result = await _securityService.CheckEncryptionConfigAsync();
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 检查API安全状态
    /// </summary>
    [HttpGet("status/api")]
    [RequirePermission("security.view")]
    [ProducesResponseType(typeof(ApiSecurityStatus), StatusCodes.Status200OK)]
    public async Task<IActionResult> CheckApiSecurity()
    {
        var result = await _securityService.CheckApiSecurityAsync();
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 检查依赖项漏洞
    /// </summary>
    [HttpGet("vulnerabilities/dependencies")]
    [RequirePermission("security.scan")]
    [ProducesResponseType(typeof(List<DependencyVulnerability>), StatusCodes.Status200OK)]
    public async Task<IActionResult> CheckDependencyVulnerabilities()
    {
        var result = await _securityService.CheckDependencyVulnerabilitiesAsync();
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 生成安全报告
    /// </summary>
    [HttpPost("reports/generate")]
    [RequirePermission("security.reports")]
    [ProducesResponseType(typeof(SecurityReport), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GenerateSecurityReport()
    {
        _logger.LogInformation("Generating security report requested by {AdminId}", User.Identity?.Name);
        
        var result = await _securityService.GenerateSecurityReportAsync();
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        _logger.LogInformation("Security report generated: {ReportId}, Grade: {Grade}", 
            result.Value!.ReportId, result.Value.OverallScore.Grade);
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 验证输入数据
    /// </summary>
    [HttpPost("validate/input")]
    [RequirePermission("security.test")]
    [ProducesResponseType(typeof(InputValidationResult), StatusCodes.Status200OK)]
    public IActionResult ValidateInput([FromBody] ValidateInputRequest request)
    {
        var result = _securityService.ValidateInput(request.Input, request.ValidationType);
        
        return Ok(new InputValidationResult
        {
            IsValid = result.IsSuccess && result.Value,
            ValidationType = request.ValidationType,
            Message = result.IsSuccess ? (result.Value ? "Input is valid" : "Input validation failed") : result.Error
        });
    }

    /// <summary>
    /// 清理HTML内容
    /// </summary>
    [HttpPost("sanitize/html")]
    [RequirePermission("security.test")]
    [ProducesResponseType(typeof(HtmlSanitizationResult), StatusCodes.Status200OK)]
    public IActionResult SanitizeHtml([FromBody] SanitizeHtmlRequest request)
    {
        var result = _securityService.SanitizeHtml(request.Html);
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        return Ok(new HtmlSanitizationResult
        {
            OriginalHtml = request.Html,
            SanitizedHtml = result.Value,
            WasSanitized = request.Html != result.Value
        });
    }

    /// <summary>
    /// 测试安全头配置
    /// </summary>
    [HttpGet("test/headers")]
    [RequirePermission("security.test")]
    [ProducesResponseType(typeof(Dictionary<string, string>), StatusCodes.Status200OK)]
    public IActionResult TestSecurityHeaders()
    {
        var headers = new Dictionary<string, string>();
        
        foreach (var header in Response.Headers)
        {
            headers[header.Key] = header.Value.ToString();
        }
        
        return Ok(headers);
    }
}

#region DTOs

/// <summary>
/// 输入验证请求
/// </summary>
public class ValidateInputRequest
{
    public string Input { get; set; } = string.Empty;
    public InputValidationType ValidationType { get; set; }
}

/// <summary>
/// 输入验证结果
/// </summary>
public class InputValidationResult
{
    public bool IsValid { get; set; }
    public InputValidationType ValidationType { get; set; }
    public string Message { get; set; } = string.Empty;
}

/// <summary>
/// HTML清理请求
/// </summary>
public class SanitizeHtmlRequest
{
    public string Html { get; set; } = string.Empty;
}

/// <summary>
/// HTML清理结果
/// </summary>
public class HtmlSanitizationResult
{
    public string OriginalHtml { get; set; } = string.Empty;
    public string SanitizedHtml { get; set; } = string.Empty;
    public bool WasSanitized { get; set; }
}

#endregion