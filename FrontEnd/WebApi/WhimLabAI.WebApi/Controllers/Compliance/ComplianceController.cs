using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Domain.Entities.Compliance;
using WhimLabAI.Shared.Dtos.Compliance;
using WhimLabAI.WebApi.Extensions;

namespace WhimLabAI.WebApi.Controllers.Compliance;

/// <summary>
/// 合规性管理控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class ComplianceController : ControllerBase
{
    private readonly IComplianceService _complianceService;
    private readonly ILogger<ComplianceController> _logger;

    public ComplianceController(
        IComplianceService complianceService,
        ILogger<ComplianceController> logger)
    {
        _complianceService = complianceService;
        _logger = logger;
    }

    #region 用户同意管理

    /// <summary>
    /// 记录用户同意
    /// </summary>
    [HttpPost("consent")]
    [ProducesResponseType(typeof(UserConsentDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> RecordConsent([FromBody] RecordConsentDto request)
    {
        var userId = User.GetUserId();
        var ipAddress = HttpContext.GetClientIpAddress();
        
        var result = await _complianceService.RecordConsentAsync(
            userId, 
            request.ConsentType, 
            request.IsGranted, 
            ipAddress);
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 获取用户同意记录
    /// </summary>
    [HttpGet("consent")]
    [ProducesResponseType(typeof(List<UserConsentDto>), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetUserConsents()
    {
        var userId = User.GetUserId();
        var result = await _complianceService.GetUserConsentsAsync(userId);
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 撤回同意
    /// </summary>
    [HttpPost("consent/revoke")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> RevokeConsent([FromBody] RevokeConsentDto request)
    {
        var userId = User.GetUserId();
        var ipAddress = HttpContext.GetClientIpAddress();
        
        var result = await _complianceService.RevokeConsentAsync(
            userId, 
            request.ConsentType, 
            ipAddress);
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        return NoContent();
    }

    /// <summary>
    /// 检查同意状态
    /// </summary>
    [HttpGet("consent/check")]
    [ProducesResponseType(typeof(ConsentCheckResult), StatusCodes.Status200OK)]
    public async Task<IActionResult> CheckConsent([FromQuery] string consentType)
    {
        var userId = User.GetUserId();
        var result = await _complianceService.CheckConsentAsync(userId, consentType);
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        return Ok(new ConsentCheckResult 
        { 
            ConsentType = consentType,
            IsGranted = result.Value 
        });
    }

    #endregion

    #region 隐私设置管理

    /// <summary>
    /// 获取隐私设置
    /// </summary>
    [HttpGet("privacy-settings")]
    [ProducesResponseType(typeof(PrivacySettingsDto), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetPrivacySettings()
    {
        var userId = User.GetUserId();
        var result = await _complianceService.GetPrivacySettingsAsync(userId);
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 更新隐私设置
    /// </summary>
    [HttpPut("privacy-settings")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> UpdatePrivacySettings([FromBody] WhimLabAI.Shared.Dtos.Compliance.PrivacySettingsUpdateRequest request)
    {
        var userId = User.GetUserId();
        var result = await _complianceService.UpdatePrivacySettingsAsync(userId, request);
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        return NoContent();
    }

    /// <summary>
    /// 启用严格隐私模式
    /// </summary>
    [HttpPost("privacy-settings/strict")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> EnableStrictPrivacy()
    {
        var userId = User.GetUserId();
        var result = await _complianceService.EnableStrictPrivacyAsync(userId);
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        return NoContent();
    }

    #endregion

    #region 数据导出

    /// <summary>
    /// 请求数据导出
    /// </summary>
    [HttpPost("data-export")]
    [ProducesResponseType(typeof(DataExportRequestDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> RequestDataExport([FromBody] DataExportRequestDto request)
    {
        var userId = User.GetUserId();
        var ipAddress = HttpContext.GetClientIpAddress();
        
        var result = await _complianceService.CreateDataExportRequestAsync(
            userId,
            request.ExportFormat,
            request.DataTypes,
            ipAddress);
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 获取数据导出请求
    /// </summary>
    [HttpGet("data-export/{requestId:guid}")]
    [ProducesResponseType(typeof(DataExportRequestDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetDataExportRequest(Guid requestId)
    {
        var userId = User.GetUserId();
        var result = await _complianceService.GetDataExportRequestAsync(requestId, userId);
        
        if (!result.IsSuccess)
        {
            return NotFound(new { error = result.Error });
        }
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 获取用户所有数据导出请求
    /// </summary>
    [HttpGet("data-export")]
    [ProducesResponseType(typeof(List<DataExportRequestDto>), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetUserDataExportRequests()
    {
        var userId = User.GetUserId();
        var result = await _complianceService.GetUserDataExportRequestsAsync(userId);
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        return Ok(result.Value);
    }

    /// <summary>
    /// 下载数据导出
    /// </summary>
    [HttpGet("data-export/{requestId:guid}/download")]
    [ProducesResponseType(typeof(DataExportDownloadResult), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> DownloadDataExport(Guid requestId)
    {
        var userId = User.GetUserId();
        var result = await _complianceService.DownloadDataExportAsync(requestId, userId);
        
        if (!result.IsSuccess)
        {
            return NotFound(new { error = result.Error });
        }
        
        return Ok(new DataExportDownloadResult { DownloadUrl = result.Value });
    }

    #endregion

    #region 数据删除

    /// <summary>
    /// 请求删除用户数据（被遗忘权）
    /// </summary>
    [HttpPost("data-deletion")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> RequestDataDeletion([FromBody] DataDeletionRequestDto request)
    {
        var userId = User.GetUserId();
        
        // 需要二次确认
        if (request.ConfirmationCode != "DELETE-MY-DATA")
        {
            return BadRequest(new { error = "Invalid confirmation code" });
        }
        
        var result = await _complianceService.DeleteUserDataAsync(userId, request.Reason);
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        // 登出用户
        await HttpContext.SignOutAsync();
        
        return NoContent();
    }

    /// <summary>
    /// 请求匿名化用户数据
    /// </summary>
    [HttpPost("data-anonymization")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> RequestDataAnonymization([FromBody] DataAnonymizationRequestDto request)
    {
        var userId = User.GetUserId();
        
        // 需要二次确认
        if (request.ConfirmationCode != "ANONYMIZE-MY-DATA")
        {
            return BadRequest(new { error = "Invalid confirmation code" });
        }
        
        var result = await _complianceService.AnonymizeUserDataAsync(userId);
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        // 登出用户
        await HttpContext.SignOutAsync();
        
        return NoContent();
    }

    #endregion

    #region 合规性报告

    /// <summary>
    /// 生成用户数据报告
    /// </summary>
    [HttpGet("user-data-report")]
    [ProducesResponseType(typeof(UserDataReport), StatusCodes.Status200OK)]
    public async Task<IActionResult> GenerateUserDataReport()
    {
        var userId = User.GetUserId();
        var result = await _complianceService.GenerateUserDataReportAsync(userId);
        
        if (!result.IsSuccess)
        {
            return BadRequest(new { error = result.Error });
        }
        
        return Ok(result.Value);
    }

    #endregion
}

#region DTOs

/// <summary>
/// 记录同意DTO
/// </summary>
public class RecordConsentDto
{
    public string ConsentType { get; set; } = string.Empty;
    public bool IsGranted { get; set; }
}

/// <summary>
/// 撤回同意DTO
/// </summary>
public class RevokeConsentDto
{
    public string ConsentType { get; set; } = string.Empty;
}

/// <summary>
/// 同意检查结果
/// </summary>
public class ConsentCheckResult
{
    public string ConsentType { get; set; } = string.Empty;
    public bool IsGranted { get; set; }
}

/// <summary>
/// 数据导出请求DTO
/// </summary>
public class DataExportRequestDto
{
    public string ExportFormat { get; set; } = "json";
    public List<string> DataTypes { get; set; } = new();
}

/// <summary>
/// 数据导出下载结果
/// </summary>
public class DataExportDownloadResult
{
    public string DownloadUrl { get; set; } = string.Empty;
}

/// <summary>
/// 数据删除请求DTO
/// </summary>
public class DataDeletionRequestDto
{
    public string Reason { get; set; } = string.Empty;
    public string ConfirmationCode { get; set; } = string.Empty;
}

/// <summary>
/// 数据匿名化请求DTO
/// </summary>
public class DataAnonymizationRequestDto
{
    public string ConfirmationCode { get; set; } = string.Empty;
}

#endregion