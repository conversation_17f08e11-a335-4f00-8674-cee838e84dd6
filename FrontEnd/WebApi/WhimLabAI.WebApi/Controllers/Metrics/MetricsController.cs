using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Infrastructure.Data;
using WhimLabAI.Shared.Dtos;

namespace WhimLabAI.WebApi.Controllers.Metrics;

/// <summary>
/// 指标API控制器
/// </summary>
[ApiController]
[Route("api/metrics")]
[AllowAnonymous] // Prometheus抓取不需要认证，但应该通过IP白名单等方式保护
public class MetricsController : ControllerBase
{
    private readonly IMetricsService _metricsService;
    private readonly WhimLabAIDbContext _dbContext;
    private readonly IUnitOfWork _unitOfWork;
    
    public MetricsController(
        IMetricsService metricsService,
        WhimLabAIDbContext dbContext,
        IUnitOfWork unitOfWork)
    {
        _metricsService = metricsService;
        _dbContext = dbContext;
        _unitOfWork = unitOfWork;
    }
    
    /// <summary>
    /// 获取业务指标
    /// </summary>
    [HttpGet("business")]
    public async Task<IActionResult> GetBusinessMetrics(CancellationToken cancellationToken)
    {
        var now = DateTime.UtcNow;
        var today = now.Date;
        var thirtyDaysAgo = today.AddDays(-30);
        
        var metrics = new BusinessMetrics
        {
            Timestamp = now
        };
        
        // 收集用户指标
        metrics.ActiveUsersDaily = await _dbContext.Set<Domain.Entities.Conversation.Conversation>()
            .Where(c => c.UpdatedAt >= today)
            .Select(c => c.CustomerUserId)
            .Distinct()
            .CountAsync(cancellationToken);
        
        metrics.ActiveUsersMonthly = await _dbContext.Set<Domain.Entities.Conversation.Conversation>()
            .Where(c => c.UpdatedAt >= thirtyDaysAgo)
            .Select(c => c.CustomerUserId)
            .Distinct()
            .CountAsync(cancellationToken);
        
        // 收集Agent指标
        metrics.TotalAgents = await _dbContext.Set<Domain.Entities.Agent.Agent>()
            .CountAsync(cancellationToken);
        
        metrics.ActiveAgents = await _dbContext.Set<Domain.Entities.Agent.Agent>()
            .Where(a => a.Status == Shared.Enums.AgentStatus.Published)
            .CountAsync(cancellationToken);
        
        // 收集对话指标
        metrics.TotalConversations = await _dbContext.Set<Domain.Entities.Conversation.Conversation>()
            .CountAsync(cancellationToken);
        
        var oneDayAgo = now.AddDays(-1);
        metrics.ActiveConversations = await _dbContext.Set<Domain.Entities.Conversation.Conversation>()
            .Where(c => c.UpdatedAt >= oneDayAgo)
            .CountAsync(cancellationToken);
        
        // 记录到Prometheus
        await _metricsService.RecordBusinessMetricsAsync(metrics, cancellationToken);
        
        return Ok(metrics);
    }
    
    /// <summary>
    /// 获取AI服务指标
    /// </summary>
    [HttpGet("ai")]
    public async Task<IActionResult> GetAIMetrics(CancellationToken cancellationToken)
    {
        var now = DateTime.UtcNow;
        var oneHourAgo = now.AddHours(-1);
        
        // 收集AI使用统计
        var aiUsageStats = await _dbContext.Set<Domain.Entities.Subscription.TokenUsage>()
            .Where(t => t.CreatedAt >= oneHourAgo)
            .GroupBy(t => t.Model)
            .Select(g => new
            {
                Model = g.Key,
                RequestCount = g.Count(),
                TotalTokens = g.Sum(t => t.Tokens)
            })
            .ToListAsync(cancellationToken);
        
        // 转换为指标格式
        var metrics = new
        {
            Timestamp = now,
            Models = aiUsageStats.Select(s => new
            {
                s.Model,
                s.RequestCount,
                s.TotalTokens,
                TokensPerRequest = s.RequestCount > 0 ? s.TotalTokens / s.RequestCount : 0
            })
        };
        
        return Ok(metrics);
    }
    
    /// <summary>
    /// 获取系统健康状态
    /// </summary>
    [HttpGet("health")]
    public async Task<IActionResult> GetHealthMetrics(CancellationToken cancellationToken)
    {
        var health = new
        {
            Status = "healthy",
            Timestamp = DateTime.UtcNow,
            Services = new
            {
                Database = await CheckDatabaseHealthAsync(cancellationToken),
                Cache = await CheckCacheHealthAsync(cancellationToken),
                Queue = await CheckQueueHealthAsync(cancellationToken)
            }
        };
        
        return Ok(health);
    }
    
    /// <summary>
    /// 获取性能指标快照
    /// </summary>
    [HttpGet("snapshot")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<ApiResponse<MetricsSnapshot>>> GetMetricsSnapshot(CancellationToken cancellationToken)
    {
        var snapshot = await _metricsService.GetMetricsSnapshotAsync(cancellationToken);
        return ApiResponse<MetricsSnapshot>.Ok(snapshot);
    }
    
    private async Task<object> CheckDatabaseHealthAsync(CancellationToken cancellationToken)
    {
        try
        {
            var canConnect = await _dbContext.Database.CanConnectAsync(cancellationToken);
            var openConnections = _dbContext.Database.GetDbConnection().State == System.Data.ConnectionState.Open ? 1 : 0;
            
            return new
            {
                Status = canConnect ? "healthy" : "unhealthy",
                Connections = openConnections
            };
        }
        catch
        {
            return new { Status = "unhealthy", Error = "Connection failed" };
        }
    }
    
    private async Task<object> CheckCacheHealthAsync(CancellationToken cancellationToken)
    {
        // 这里需要注入缓存服务来检查
        // 暂时返回模拟数据
        return await Task.FromResult(new
        {
            Status = "healthy",
            HitRate = 0.85
        });
    }
    
    private async Task<object> CheckQueueHealthAsync(CancellationToken cancellationToken)
    {
        // 这里需要注入消息队列服务来检查
        // 暂时返回模拟数据
        return await Task.FromResult(new
        {
            Status = "healthy",
            QueueDepth = 0
        });
    }
}