{
  "Payment": {
    "Security": {
      // IP白名单配置
      "EnableIpWhitelist": false,
      "IpWhitelist": [
        // 支付宝回调IP段
        "************/24",
        "************/24",
        "************/24",
        "************/24",
        "************/24",
        // 微信支付回调IP段
        "*************/24",
        "*************/24",
        "************/24",
        "***********/24",
        "*************/24",
        "*************/24",
        // 本地开发环境
        "127.0.0.1",
        "::1"
      ],

      // 防重放攻击配置
      "ReplayWindowMinutes": 5,
      "NonceExpirationMinutes": 5,
      "TimestampToleranceSeconds": 300,

      // 支付金额限制
      "MaxPaymentAmount": 100000.00,
      "MinPaymentAmount": 0.01,

      // 签名验证
      "RequireSignatureValidation": true,

      // 请求频率限制
      "RateLimitPerMinute": 60,
      "RateLimitPerHour": 1000
    },

    // 支付宝配置
    "Alipay": {
      "AppId": "your_alipay_app_id",
      "PrivateKey": "your_alipay_private_key_base64",
      "PublicKey": "your_alipay_public_key_base64",
      "AlipayPublicKey": "alipay_platform_public_key_base64",
      "IsProduction": false,
      "Gateway": "https://openapi.alipaydev.com/gateway.do",
      "NotifyUrl": "https://your-domain.com/api/payment/callback/alipay",
      "ReturnUrl": "https://your-domain.com/payment/return/alipay"
    },

    // 微信支付配置
    "WeChatPay": {
      "AppId": "your_wechat_app_id",
      "MchId": "your_wechat_merchant_id",
      "ApiKey": "your_wechat_api_key",
      "CertificatePath": "path/to/apiclient_cert.p12",
      "CertificatePassword": "your_certificate_password",
      "IsProduction": false,
      "Gateway": "https://api.mch.weixin.qq.com/sandboxnew",
      "NotifyUrl": "https://api.whimlab.com/api/payment/callback/wechatpay",
      "ReturnUrl": "https://api.whimlab.com/payment/return/wechatpay"
    },

    // 通用配置
    "BaseUrl": "https://api.whimlab.com",
    "CallbackUrl": "https://api.api.whimlab.com",
    "PaymentTimeout": 30,
    "RefundTimeout": 180
  },

  // 日志配置
  "Serilog": {
    "MinimumLevel": {
      "Override": {
        "WhimLabAI.Infrastructure.PaymentGateways": "Debug",
        "WhimLabAI.Application.Services.Payment": "Debug"
      }
    }
  }
}
