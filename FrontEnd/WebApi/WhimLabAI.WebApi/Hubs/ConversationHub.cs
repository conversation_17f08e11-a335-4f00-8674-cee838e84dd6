using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.SignalR;
using System.Security.Claims;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Shared.Dtos;

namespace WhimLabAI.WebApi.Hubs;

/// <summary>
/// 对话实时通信Hub
/// </summary>
[Authorize]
public class ConversationHub : Hub
{
    private readonly IConversationService _conversationService;
    private readonly ILogger<ConversationHub> _logger;

    public ConversationHub(IConversationService conversationService, ILogger<ConversationHub> logger)
    {
        _conversationService = conversationService;
        _logger = logger;
    }

    /// <summary>
    /// 客户端连接时
    /// </summary>
    public override async Task OnConnectedAsync()
    {
        var userId = GetCurrentUserId();
        _logger.LogInformation("User {UserId} connected to ConversationHub", userId);
        
        // Add user to their personal group
        await Groups.AddToGroupAsync(Context.ConnectionId, $"user-{userId}");
        
        await base.OnConnectedAsync();
    }

    /// <summary>
    /// 客户端断开连接时
    /// </summary>
    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        var userId = GetCurrentUserId();
        _logger.LogInformation("User {UserId} disconnected from ConversationHub", userId);
        
        // Remove user from their personal group
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"user-{userId}");
        
        await base.OnDisconnectedAsync(exception);
    }

    /// <summary>
    /// 加入对话房间
    /// </summary>
    /// <param name="conversationId">对话ID</param>
    public async Task JoinConversation(Guid conversationId)
    {
        var userId = GetCurrentUserId();
        
        // TODO: Verify user has access to this conversation
        
        await Groups.AddToGroupAsync(Context.ConnectionId, $"conversation-{conversationId}");
        _logger.LogInformation("User {UserId} joined conversation {ConversationId}", userId, conversationId);
        
        await Clients.Caller.SendAsync("JoinedConversation", conversationId);
    }

    /// <summary>
    /// 离开对话房间
    /// </summary>
    /// <param name="conversationId">对话ID</param>
    public async Task LeaveConversation(Guid conversationId)
    {
        var userId = GetCurrentUserId();
        
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"conversation-{conversationId}");
        _logger.LogInformation("User {UserId} left conversation {ConversationId}", userId, conversationId);
        
        await Clients.Caller.SendAsync("LeftConversation", conversationId);
    }

    /// <summary>
    /// 发送消息（流式）
    /// </summary>
    /// <param name="request">消息请求</param>
    public async Task SendMessage(SendMessageDto request)
    {
        var userId = GetCurrentUserId();
        
        try
        {
            // Start typing indicator
            await Clients.Group($"conversation-{request.ConversationId}")
                .SendAsync("TypingStarted", request.ConversationId);

            // Send user message to all clients in conversation
            await Clients.Group($"conversation-{request.ConversationId}")
                .SendAsync("UserMessage", new
                {
                    ConversationId = request.ConversationId,
                    Message = request.Message,
                    Timestamp = DateTime.UtcNow
                });

            // Get streaming response
            var messageId = Guid.NewGuid();
            await Clients.Group($"conversation-{request.ConversationId}")
                .SendAsync("StreamStarted", new { ConversationId = request.ConversationId, MessageId = messageId });

            var stream = await _conversationService.SendMessageStreamAsync(request, userId);
            await foreach (var chunk in stream)
            {
                if (!string.IsNullOrEmpty(chunk.Error))
                {
                    await Clients.Group($"conversation-{request.ConversationId}")
                        .SendAsync("StreamError", new
                        {
                            ConversationId = request.ConversationId,
                            MessageId = messageId,
                            Error = chunk.Error
                        });
                    break;
                }

                await Clients.Group($"conversation-{request.ConversationId}")
                    .SendAsync("StreamChunk", new
                    {
                        ConversationId = request.ConversationId,
                        MessageId = messageId,
                        Content = chunk.Content,
                        TokenCount = chunk.TokenCount
                    });

                if (chunk.IsComplete)
                {
                    await Clients.Group($"conversation-{request.ConversationId}")
                        .SendAsync("StreamCompleted", new
                        {
                            ConversationId = request.ConversationId,
                            MessageId = messageId,
                            TotalTokens = chunk.TokenCount
                        });
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending message in conversation {ConversationId}", request.ConversationId);
            await Clients.Caller.SendAsync("Error", new { Error = "发送消息失败" });
        }
        finally
        {
            // Stop typing indicator
            await Clients.Group($"conversation-{request.ConversationId}")
                .SendAsync("TypingStopped", request.ConversationId);
        }
    }

    /// <summary>
    /// 发送打字状态
    /// </summary>
    /// <param name="conversationId">对话ID</param>
    /// <param name="isTyping">是否正在打字</param>
    public async Task SendTypingStatus(Guid conversationId, bool isTyping)
    {
        var userId = GetCurrentUserId();
        
        await Clients.OthersInGroup($"conversation-{conversationId}")
            .SendAsync("UserTyping", new
            {
                ConversationId = conversationId,
                UserId = userId,
                IsTyping = isTyping
            });
    }

    /// <summary>
    /// 标记消息已读
    /// </summary>
    /// <param name="conversationId">对话ID</param>
    /// <param name="messageId">消息ID</param>
    public async Task MarkMessageAsRead(Guid conversationId, Guid messageId)
    {
        var userId = GetCurrentUserId();
        
        // TODO: Update read status in database
        
        await Clients.OthersInGroup($"conversation-{conversationId}")
            .SendAsync("MessageRead", new
            {
                ConversationId = conversationId,
                MessageId = messageId,
                UserId = userId,
                ReadAt = DateTime.UtcNow
            });
    }

    /// <summary>
    /// 删除消息
    /// </summary>
    /// <param name="conversationId">对话ID</param>
    /// <param name="messageId">消息ID</param>
    public async Task DeleteMessage(Guid conversationId, Guid messageId)
    {
        var userId = GetCurrentUserId();
        
        // TODO: Verify user can delete this message and update database
        
        await Clients.Group($"conversation-{conversationId}")
            .SendAsync("MessageDeleted", new
            {
                ConversationId = conversationId,
                MessageId = messageId,
                DeletedBy = userId,
                DeletedAt = DateTime.UtcNow
            });
    }

    /// <summary>
    /// 评价消息
    /// </summary>
    /// <param name="request">评价请求</param>
    public async Task RateMessage(RateResponseDto request)
    {
        var userId = GetCurrentUserId();
        
        var result = await _conversationService.RateResponseAsync(request, userId);
        
        if (result.IsSuccess)
        {
            await Clients.Caller.SendAsync("MessageRated", new
            {
                MessageId = request.MessageId,
                Score = request.Score,
                Feedback = request.Feedback
            });
        }
        else
        {
            await Clients.Caller.SendAsync("Error", new { Error = result.Error });
        }
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId))
        {
            throw new HubException("用户未认证");
        }
        return userId;
    }
}