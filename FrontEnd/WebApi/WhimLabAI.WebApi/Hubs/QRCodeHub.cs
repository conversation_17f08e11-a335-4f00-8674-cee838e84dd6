using Microsoft.AspNetCore.SignalR;
using System.Security.Claims;
using WhimLabAI.Shared.Dtos.Auth;
using WhimLabAI.Shared.Dtos.Customer.Auth;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.WebApi.Hubs;

/// <summary>
/// 扫码登录实时通信Hub
/// </summary>
public class QRCodeHub : Hub
{
    private readonly ILogger<QRCodeHub> _logger;
    
    public QRCodeHub(ILogger<QRCodeHub> logger)
    {
        _logger = logger;
    }
    
    /// <summary>
    /// 客户端连接时
    /// </summary>
    public override async Task OnConnectedAsync()
    {
        _logger.LogInformation("Client connected to QRCodeHub: {ConnectionId}", Context.ConnectionId);
        await base.OnConnectedAsync();
    }
    
    /// <summary>
    /// 客户端断开连接时
    /// </summary>
    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        _logger.LogInformation("Client disconnected from QRCodeHub: {ConnectionId}", Context.ConnectionId);
        
        // 清理该连接相关的组
        await base.OnDisconnectedAsync(exception);
    }
    
    /// <summary>
    /// PC端订阅特定的扫码会话
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    public async Task SubscribeToSession(string sessionId)
    {
        if (string.IsNullOrEmpty(sessionId))
        {
            await Clients.Caller.SendAsync("Error", new { Error = "会话ID不能为空" });
            return;
        }
        
        // 加入特定会话组
        await Groups.AddToGroupAsync(Context.ConnectionId, $"qr-session-{sessionId}");
        
        _logger.LogInformation("Client {ConnectionId} subscribed to QR session: {SessionId}", 
            Context.ConnectionId, sessionId);
        
        await Clients.Caller.SendAsync("SubscribedToSession", new
        {
            SessionId = sessionId,
            Message = "已订阅扫码会话通知"
        });
    }
    
    /// <summary>
    /// 取消订阅扫码会话
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    public async Task UnsubscribeFromSession(string sessionId)
    {
        if (string.IsNullOrEmpty(sessionId))
        {
            await Clients.Caller.SendAsync("Error", new { Error = "会话ID不能为空" });
            return;
        }
        
        // 从会话组移除
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"qr-session-{sessionId}");
        
        _logger.LogInformation("Client {ConnectionId} unsubscribed from QR session: {SessionId}", 
            Context.ConnectionId, sessionId);
        
        await Clients.Caller.SendAsync("UnsubscribedFromSession", new
        {
            SessionId = sessionId,
            Message = "已取消订阅扫码会话通知"
        });
    }
}

/// <summary>
/// QR码Hub助手类（供服务端使用）
/// </summary>
public static class QRCodeHubHelper
{
    /// <summary>
    /// 通知PC端二维码已被扫描
    /// </summary>
    public static async Task NotifyQRCodeScannedAsync(
        IHubContext<QRCodeHub> hubContext,
        string sessionId,
        string scannerDeviceId,
        string scannerIp)
    {
        await hubContext.Clients.Group($"qr-session-{sessionId}")
            .SendAsync("QRCodeScanned", new
            {
                SessionId = sessionId,
                Status = QRCodeSessionStatus.Scanned,
                ScannedAt = DateTime.UtcNow,
                ScannerInfo = new
                {
                    DeviceId = scannerDeviceId,
                    IpAddress = scannerIp
                },
                Message = "二维码已被扫描，等待用户确认"
            });
    }
    
    /// <summary>
    /// 通知PC端登录已确认
    /// </summary>
    public static async Task NotifyQRCodeAuthenticatedAsync(
        IHubContext<QRCodeHub> hubContext,
        string sessionId,
        AuthResponseDto authResponse)
    {
        await hubContext.Clients.Group($"qr-session-{sessionId}")
            .SendAsync("QRCodeAuthenticated", new
            {
                SessionId = sessionId,
                Status = QRCodeSessionStatus.Authenticated,
                AuthenticatedAt = DateTime.UtcNow,
                AuthResponse = authResponse,
                Message = "登录成功"
            });
    }
    
    /// <summary>
    /// 通知PC端登录被拒绝
    /// </summary>
    public static async Task NotifyQRCodeRejectedAsync(
        IHubContext<QRCodeHub> hubContext,
        string sessionId)
    {
        await hubContext.Clients.Group($"qr-session-{sessionId}")
            .SendAsync("QRCodeRejected", new
            {
                SessionId = sessionId,
                Status = QRCodeSessionStatus.Rejected,
                RejectedAt = DateTime.UtcNow,
                Message = "用户拒绝登录"
            });
    }
    
    /// <summary>
    /// 通知PC端二维码已取消
    /// </summary>
    public static async Task NotifyQRCodeCancelledAsync(
        IHubContext<QRCodeHub> hubContext,
        string sessionId)
    {
        await hubContext.Clients.Group($"qr-session-{sessionId}")
            .SendAsync("QRCodeCancelled", new
            {
                SessionId = sessionId,
                Status = QRCodeSessionStatus.Cancelled,
                CancelledAt = DateTime.UtcNow,
                Message = "二维码已取消"
            });
    }
    
    /// <summary>
    /// 通知PC端二维码已过期
    /// </summary>
    public static async Task NotifyQRCodeExpiredAsync(
        IHubContext<QRCodeHub> hubContext,
        string sessionId)
    {
        await hubContext.Clients.Group($"qr-session-{sessionId}")
            .SendAsync("QRCodeExpired", new
            {
                SessionId = sessionId,
                Status = QRCodeSessionStatus.Expired,
                ExpiredAt = DateTime.UtcNow,
                Message = "二维码已过期"
            });
    }
}