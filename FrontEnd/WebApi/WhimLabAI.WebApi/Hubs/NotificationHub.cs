using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.SignalR;
using System.Collections.Concurrent;
using System.Security.Claims;

namespace WhimLabAI.WebApi.Hubs;

/// <summary>
/// 系统通知实时推送Hub
/// </summary>
[Authorize]
public class NotificationHub : Hub
{
    private readonly ILogger<NotificationHub> _logger;
    
    // 存储用户在线状态和连接信息
    private static readonly ConcurrentDictionary<Guid, HashSet<string>> UserConnections = new();
    private static readonly ConcurrentDictionary<string, UserConnectionInfo> ConnectionInfos = new();

    public NotificationHub(ILogger<NotificationHub> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 客户端连接时
    /// </summary>
    public override async Task OnConnectedAsync()
    {
        var userId = GetCurrentUserId();
        var deviceId = Context.GetHttpContext()?.Request.Headers["X-Device-Id"].FirstOrDefault() ?? "unknown";
        var userAgent = Context.GetHttpContext()?.Request.Headers.UserAgent.FirstOrDefault() ?? "unknown";
        
        _logger.LogInformation("User connected to NotificationHub");
        
        // 记录连接信息
        var connectionInfo = new UserConnectionInfo
        {
            UserId = userId,
            ConnectionId = Context.ConnectionId,
            DeviceId = deviceId,
            UserAgent = userAgent,
            ConnectedAt = DateTime.UtcNow
        };
        
        ConnectionInfos.TryAdd(Context.ConnectionId, connectionInfo);
        
        // 添加到用户连接组
        UserConnections.AddOrUpdate(userId,
            new HashSet<string> { Context.ConnectionId },
            (key, connections) =>
            {
                connections.Add(Context.ConnectionId);
                return connections;
            });
        
        // 加入用户个人组
        await Groups.AddToGroupAsync(Context.ConnectionId, $"user-{userId}");
        
        // 通知其他客户端用户上线
        await Clients.Others.SendAsync("UserOnline", new
        {
            UserId = userId,
            OnlineAt = DateTime.UtcNow,
            DeviceCount = UserConnections.GetValueOrDefault(userId)?.Count ?? 0
        });
        
        await base.OnConnectedAsync();
    }

    /// <summary>
    /// 客户端断开连接时
    /// </summary>
    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        var userId = GetCurrentUserId();
        _logger.LogInformation("User disconnected from NotificationHub");
        
        // 移除连接信息
        ConnectionInfos.TryRemove(Context.ConnectionId, out _);
        
        // 从用户连接组中移除
        if (UserConnections.TryGetValue(userId, out var connections))
        {
            connections.Remove(Context.ConnectionId);
            
            // 如果用户没有其他连接，则移除用户
            if (connections.Count == 0)
            {
                UserConnections.TryRemove(userId, out _);
                
                // 通知其他客户端用户离线
                await Clients.Others.SendAsync("UserOffline", new
                {
                    UserId = userId,
                    OfflineAt = DateTime.UtcNow
                });
            }
        }
        
        // 从用户个人组移除
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"user-{userId}");
        
        await base.OnDisconnectedAsync(exception);
    }

    /// <summary>
    /// 订阅特定类型的通知
    /// </summary>
    /// <param name="notificationTypes">通知类型列表</param>
    public async Task SubscribeToNotifications(List<string> notificationTypes)
    {
        foreach (var type in notificationTypes)
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, $"notification-{type}");
        }
        
        await Clients.Caller.SendAsync("SubscribedToNotifications", notificationTypes);
    }

    /// <summary>
    /// 取消订阅特定类型的通知
    /// </summary>
    /// <param name="notificationTypes">通知类型列表</param>
    public async Task UnsubscribeFromNotifications(List<string> notificationTypes)
    {
        foreach (var type in notificationTypes)
        {
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"notification-{type}");
        }
        
        await Clients.Caller.SendAsync("UnsubscribedFromNotifications", notificationTypes);
    }

    /// <summary>
    /// 标记通知为已读
    /// </summary>
    /// <param name="notificationIds">通知ID列表</param>
    public async Task MarkNotificationsAsRead(List<Guid> notificationIds)
    {
        var userId = GetCurrentUserId();
        
        // TODO: 更新数据库中的通知状态
        
        // 通知其他设备同步已读状态
        await Clients.OthersInGroup($"user-{userId}")
            .SendAsync("NotificationsRead", new
            {
                NotificationIds = notificationIds,
                ReadAt = DateTime.UtcNow
            });
    }

    /// <summary>
    /// 删除通知
    /// </summary>
    /// <param name="notificationIds">通知ID列表</param>
    public async Task DeleteNotifications(List<Guid> notificationIds)
    {
        var userId = GetCurrentUserId();
        
        // TODO: 从数据库删除通知
        
        // 通知所有设备同步删除
        await Clients.Group($"user-{userId}")
            .SendAsync("NotificationsDeleted", new
            {
                NotificationIds = notificationIds,
                DeletedAt = DateTime.UtcNow
            });
    }

    /// <summary>
    /// 获取在线用户列表（仅管理员可用）
    /// </summary>
    [Authorize(Roles = "Admin")]
    public async Task GetOnlineUsers()
    {
        var onlineUsers = UserConnections.Select(kvp => new
        {
            UserId = kvp.Key,
            ConnectionCount = kvp.Value.Count,
            Connections = kvp.Value.Select(connId =>
            {
                ConnectionInfos.TryGetValue(connId, out var info);
                return new
                {
                    ConnectionId = connId,
                    DeviceId = info?.DeviceId,
                    UserAgent = info?.UserAgent,
                    ConnectedAt = info?.ConnectedAt
                };
            })
        }).ToList();
        
        await Clients.Caller.SendAsync("OnlineUsersList", onlineUsers);
    }

    /// <summary>
    /// 发送系统广播（仅管理员可用）
    /// </summary>
    /// <param name="message">广播消息</param>
    /// <param name="level">消息级别：info, warning, error</param>
    [Authorize(Roles = "Admin")]
    public async Task BroadcastSystemMessage(string message, string level = "info")
    {
        await Clients.All.SendAsync("SystemBroadcast", new
        {
            Message = message,
            Level = level,
            Timestamp = DateTime.UtcNow,
            BroadcastBy = GetCurrentUserId()
        });
        
        _logger.LogInformation("System broadcast sent: {Message}", message);
    }

    /// <summary>
    /// 更新用户在线状态
    /// </summary>
    /// <param name="status">状态：online, away, busy, offline</param>
    public async Task UpdatePresenceStatus(string status)
    {
        var userId = GetCurrentUserId();
        
        // TODO: 更新数据库中的用户状态
        
        await Clients.Others.SendAsync("UserPresenceChanged", new
        {
            UserId = userId,
            Status = status,
            UpdatedAt = DateTime.UtcNow
        });
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId))
        {
            throw new HubException("用户未认证");
        }
        return userId;
    }

    private class UserConnectionInfo
    {
        public Guid UserId { get; set; }
        public string ConnectionId { get; set; } = string.Empty;
        public string DeviceId { get; set; } = string.Empty;
        public string UserAgent { get; set; } = string.Empty;
        public DateTime ConnectedAt { get; set; }
    }
}

/// <summary>
/// 通知推送助手类（供服务端使用）
/// </summary>
public static class NotificationHubHelper
{
    /// <summary>
    /// 发送通知给特定用户
    /// </summary>
    public static async Task SendNotificationToUserAsync(
        IHubContext<NotificationHub> hubContext,
        Guid userId,
        object notification)
    {
        await hubContext.Clients.Group($"user-{userId}")
            .SendAsync("NewNotification", notification);
    }

    /// <summary>
    /// 发送通知给多个用户
    /// </summary>
    public static async Task SendNotificationToUsersAsync(
        IHubContext<NotificationHub> hubContext,
        List<Guid> userIds,
        object notification)
    {
        var groups = userIds.Select(id => $"user-{id}").ToList();
        await hubContext.Clients.Groups(groups)
            .SendAsync("NewNotification", notification);
    }

    /// <summary>
    /// 发送通知给特定类型的订阅者
    /// </summary>
    public static async Task SendNotificationToSubscribersAsync(
        IHubContext<NotificationHub> hubContext,
        string notificationType,
        object notification)
    {
        await hubContext.Clients.Group($"notification-{notificationType}")
            .SendAsync("NewNotification", notification);
    }

    /// <summary>
    /// 发送系统通知给所有用户
    /// </summary>
    public static async Task SendSystemNotificationAsync(
        IHubContext<NotificationHub> hubContext,
        object notification)
    {
        await hubContext.Clients.All
            .SendAsync("SystemNotification", notification);
    }
}