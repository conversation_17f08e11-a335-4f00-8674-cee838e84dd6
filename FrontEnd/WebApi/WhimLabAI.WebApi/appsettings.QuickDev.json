{"Logging": {"LogLevel": {"Default": "Warning", "Microsoft": "Warning", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning", "System": "Warning"}}, "Development": {"DisableCaptcha": true, "DisableRateLimiting": true, "UseMockPayment": true, "EnableDetailedErrors": true, "AutoSeedData": true}, "ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=whimlabai_dev;Username=postgres;Password=***********;Pooling=true;MinPoolSize=2;MaxPoolSize=20;ConnectionLifetime=300;CommandTimeout=30;Timeout=30", "Redis": "localhost:6379,password=redis123,defaultDatabase=1,abortConnect=false,connectTimeout=5000,syncTimeout=5000,asyncTimeout=5000,keepAlive=30,connectRetry=3"}, "Jwt": {"ExpirationMinutes": 10080, "RefreshExpirationDays": 30}, "Verification": {"AdminCaptchaPolicy": {"Enabled": false, "AlwaysRequired": false, "DisableInDevelopment": true}, "CustomerCaptchaPolicy": {"Enabled": false, "AlwaysRequired": false, "FailedAttemptsThreshold": 999, "HourlyAttemptsThreshold": 999, "DisableInDevelopment": true}}, "Payment": {"UseMockProvider": true, "Security": {"EnableIpWhitelist": false, "EnableSignatureValidation": false}}, "RateLimiting": {"DisableAllLimits": true}}