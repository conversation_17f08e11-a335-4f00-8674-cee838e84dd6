从 /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/WebApi/WhimLabAI.WebApi/Properties/launchSettings.json 使用启动设置...
正在生成...
/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/WebApi/WhimLabAI.WebApi/WhimLabAI.WebApi.csproj : warning NU1603: WhimLabAI.WebApi depends on AspNetCore.HealthChecks.Redis (>= 8.0.2) but AspNetCore.HealthChecks.Redis 8.0.2 was not found. AspNetCore.HealthChecks.Redis 9.0.0 was resolved instead.
/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/WebApi/WhimLabAI.WebApi/WhimLabAI.WebApi.csproj : warning NU1603: WhimLabAI.WebApi depends on Serilog.AspNetCore (>= 8.0.5) but Serilog.AspNetCore 8.0.5 was not found. Serilog.AspNetCore 9.0.0 was resolved instead.
/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/WebApi/WhimLabAI.WebApi/WhimLabAI.WebApi.csproj : warning NU1603: WhimLabAI.WebApi depends on AspNetCore.HealthChecks.Redis (>= 8.0.2) but AspNetCore.HealthChecks.Redis 8.0.2 was not found. AspNetCore.HealthChecks.Redis 9.0.0 was resolved instead.
/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/WebApi/WhimLabAI.WebApi/WhimLabAI.WebApi.csproj : warning NU1603: WhimLabAI.WebApi depends on Serilog.AspNetCore (>= 8.0.5) but Serilog.AspNetCore 8.0.5 was not found. Serilog.AspNetCore 9.0.0 was resolved instead.
[13:23:37 INF] Starting WhimLabAI Web API
[13:23:38 INF] Start installing Hangfire SQL objects...
[13:23:38 INF] Hangfire SQL objects installed.
[13:23:38 INF] Configured endpoint SendEmail, Consumer: WhimLabAI.Infrastructure.Messaging.Consumers.SendEmailConsumer
[13:23:38 INF] Starting database migration...
[13:23:38 INF] Executed DbCommand (3ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[13:23:39 INF] Executed DbCommand (3ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" character varying(150) NOT NULL,
    "ProductVersion" character varying(32) NOT NULL,
    CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY ("MigrationId")
);
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
LOCK TABLE "__EFMigrationsHistory" IN ACCESS EXCLUSIVE MODE
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
[13:23:39 INF] No migrations were applied. The database is already up to date.
[13:23:39 INF] Database migration completed
[13:23:39 INF] Starting database seeding...
[13:23:39 INF] 开始执行数据种子...
[13:23:39 INF] 执行优先级 Core 的种子数据...
[13:23:39 INF] 执行 RoleSeeder: 角色基础数据
[13:23:39 INF] Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM roles AS r)
[13:23:39 INF] Role seeding skipped - data already exists
[13:23:39 INF] RoleSeeder 执行成功
[13:23:39 INF] 执行 PermissionSeeder: 权限基础数据
[13:23:39 INF] Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM permissions AS p)
[13:23:39 INF] Permission seeding skipped - data already exists
[13:23:39 INF] PermissionSeeder 执行成功
[13:23:39 INF] 执行优先级 Configuration 的种子数据...
[13:23:39 INF] 执行 SystemConfigurationSeeder: 系统配置
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']

            CREATE TABLE IF NOT EXISTS SystemConfigurations (
                Id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                Category VARCHAR(100) NOT NULL,
                Key VARCHAR(200) NOT NULL,
                Value TEXT NOT NULL,
                DataType VARCHAR(50) NOT NULL,
                Description TEXT,
                IsEncrypted BOOLEAN NOT NULL DEFAULT FALSE,
                IsPublic BOOLEAN NOT NULL DEFAULT FALSE,
                LastModifiedBy VARCHAR(100),
                LastModifiedAt TIMESTAMP,
                CreatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(Category, Key)
            );
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT 1 FROM SystemConfigurations LIMIT 1
[13:23:39 INF] Executed DbCommand (4ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?', @p4='?', @p5='?', @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']

                INSERT INTO SystemConfigurations (Id, Category, Key, Value, DataType, Description, IsEncrypted, IsPublic, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8)
                ON CONFLICT (Category, Key) DO NOTHING
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?', @p4='?', @p5='?', @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']

                INSERT INTO SystemConfigurations (Id, Category, Key, Value, DataType, Description, IsEncrypted, IsPublic, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8)
                ON CONFLICT (Category, Key) DO NOTHING
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?', @p4='?', @p5='?', @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']

                INSERT INTO SystemConfigurations (Id, Category, Key, Value, DataType, Description, IsEncrypted, IsPublic, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8)
                ON CONFLICT (Category, Key) DO NOTHING
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?', @p4='?', @p5='?', @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']

                INSERT INTO SystemConfigurations (Id, Category, Key, Value, DataType, Description, IsEncrypted, IsPublic, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8)
                ON CONFLICT (Category, Key) DO NOTHING
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?', @p4='?', @p5='?', @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']

                INSERT INTO SystemConfigurations (Id, Category, Key, Value, DataType, Description, IsEncrypted, IsPublic, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8)
                ON CONFLICT (Category, Key) DO NOTHING
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?', @p4='?', @p5='?', @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']

                INSERT INTO SystemConfigurations (Id, Category, Key, Value, DataType, Description, IsEncrypted, IsPublic, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8)
                ON CONFLICT (Category, Key) DO NOTHING
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?', @p4='?', @p5='?', @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']

                INSERT INTO SystemConfigurations (Id, Category, Key, Value, DataType, Description, IsEncrypted, IsPublic, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8)
                ON CONFLICT (Category, Key) DO NOTHING
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?', @p4='?', @p5='?', @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']

                INSERT INTO SystemConfigurations (Id, Category, Key, Value, DataType, Description, IsEncrypted, IsPublic, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8)
                ON CONFLICT (Category, Key) DO NOTHING
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?', @p4='?', @p5='?', @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']

                INSERT INTO SystemConfigurations (Id, Category, Key, Value, DataType, Description, IsEncrypted, IsPublic, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8)
                ON CONFLICT (Category, Key) DO NOTHING
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?', @p4='?', @p5='?', @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']

                INSERT INTO SystemConfigurations (Id, Category, Key, Value, DataType, Description, IsEncrypted, IsPublic, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8)
                ON CONFLICT (Category, Key) DO NOTHING
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?', @p4='?', @p5='?', @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']

                INSERT INTO SystemConfigurations (Id, Category, Key, Value, DataType, Description, IsEncrypted, IsPublic, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8)
                ON CONFLICT (Category, Key) DO NOTHING
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?', @p4='?', @p5='?', @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']

                INSERT INTO SystemConfigurations (Id, Category, Key, Value, DataType, Description, IsEncrypted, IsPublic, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8)
                ON CONFLICT (Category, Key) DO NOTHING
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?', @p4='?', @p5='?', @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']

                INSERT INTO SystemConfigurations (Id, Category, Key, Value, DataType, Description, IsEncrypted, IsPublic, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8)
                ON CONFLICT (Category, Key) DO NOTHING
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?', @p4='?', @p5='?', @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']

                INSERT INTO SystemConfigurations (Id, Category, Key, Value, DataType, Description, IsEncrypted, IsPublic, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8)
                ON CONFLICT (Category, Key) DO NOTHING
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?', @p4='?', @p5='?', @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']

                INSERT INTO SystemConfigurations (Id, Category, Key, Value, DataType, Description, IsEncrypted, IsPublic, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8)
                ON CONFLICT (Category, Key) DO NOTHING
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?', @p4='?', @p5='?', @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']

                INSERT INTO SystemConfigurations (Id, Category, Key, Value, DataType, Description, IsEncrypted, IsPublic, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8)
                ON CONFLICT (Category, Key) DO NOTHING
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?', @p4='?', @p5='?', @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']

                INSERT INTO SystemConfigurations (Id, Category, Key, Value, DataType, Description, IsEncrypted, IsPublic, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8)
                ON CONFLICT (Category, Key) DO NOTHING
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?', @p4='?', @p5='?', @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']

                INSERT INTO SystemConfigurations (Id, Category, Key, Value, DataType, Description, IsEncrypted, IsPublic, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8)
                ON CONFLICT (Category, Key) DO NOTHING
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?', @p4='?', @p5='?', @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']

                INSERT INTO SystemConfigurations (Id, Category, Key, Value, DataType, Description, IsEncrypted, IsPublic, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8)
                ON CONFLICT (Category, Key) DO NOTHING
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?', @p4='?', @p5='?', @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']

                INSERT INTO SystemConfigurations (Id, Category, Key, Value, DataType, Description, IsEncrypted, IsPublic, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8)
                ON CONFLICT (Category, Key) DO NOTHING
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?', @p4='?', @p5='?', @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']

                INSERT INTO SystemConfigurations (Id, Category, Key, Value, DataType, Description, IsEncrypted, IsPublic, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8)
                ON CONFLICT (Category, Key) DO NOTHING
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?', @p4='?', @p5='?', @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']

                INSERT INTO SystemConfigurations (Id, Category, Key, Value, DataType, Description, IsEncrypted, IsPublic, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8)
                ON CONFLICT (Category, Key) DO NOTHING
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?', @p4='?', @p5='?', @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']

                INSERT INTO SystemConfigurations (Id, Category, Key, Value, DataType, Description, IsEncrypted, IsPublic, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8)
                ON CONFLICT (Category, Key) DO NOTHING
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?', @p4='?', @p5='?', @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']

                INSERT INTO SystemConfigurations (Id, Category, Key, Value, DataType, Description, IsEncrypted, IsPublic, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8)
                ON CONFLICT (Category, Key) DO NOTHING
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?', @p4='?', @p5='?', @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']

                INSERT INTO SystemConfigurations (Id, Category, Key, Value, DataType, Description, IsEncrypted, IsPublic, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8)
                ON CONFLICT (Category, Key) DO NOTHING
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?', @p4='?', @p5='?', @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']

                INSERT INTO SystemConfigurations (Id, Category, Key, Value, DataType, Description, IsEncrypted, IsPublic, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8)
                ON CONFLICT (Category, Key) DO NOTHING
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?', @p4='?', @p5='?', @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']

                INSERT INTO SystemConfigurations (Id, Category, Key, Value, DataType, Description, IsEncrypted, IsPublic, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8)
                ON CONFLICT (Category, Key) DO NOTHING
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?', @p4='?', @p5='?', @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']

                INSERT INTO SystemConfigurations (Id, Category, Key, Value, DataType, Description, IsEncrypted, IsPublic, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8)
                ON CONFLICT (Category, Key) DO NOTHING
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?', @p4='?', @p5='?', @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']

                INSERT INTO SystemConfigurations (Id, Category, Key, Value, DataType, Description, IsEncrypted, IsPublic, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8)
                ON CONFLICT (Category, Key) DO NOTHING
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?', @p4='?', @p5='?', @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']

                INSERT INTO SystemConfigurations (Id, Category, Key, Value, DataType, Description, IsEncrypted, IsPublic, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8)
                ON CONFLICT (Category, Key) DO NOTHING
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?', @p4='?', @p5='?', @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']

                INSERT INTO SystemConfigurations (Id, Category, Key, Value, DataType, Description, IsEncrypted, IsPublic, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8)
                ON CONFLICT (Category, Key) DO NOTHING
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?', @p4='?', @p5='?', @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']

                INSERT INTO SystemConfigurations (Id, Category, Key, Value, DataType, Description, IsEncrypted, IsPublic, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8)
                ON CONFLICT (Category, Key) DO NOTHING
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?', @p4='?', @p5='?', @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']

                INSERT INTO SystemConfigurations (Id, Category, Key, Value, DataType, Description, IsEncrypted, IsPublic, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8)
                ON CONFLICT (Category, Key) DO NOTHING
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?', @p4='?', @p5='?', @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']

                INSERT INTO SystemConfigurations (Id, Category, Key, Value, DataType, Description, IsEncrypted, IsPublic, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8)
                ON CONFLICT (Category, Key) DO NOTHING
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?', @p4='?', @p5='?', @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']

                INSERT INTO SystemConfigurations (Id, Category, Key, Value, DataType, Description, IsEncrypted, IsPublic, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8)
                ON CONFLICT (Category, Key) DO NOTHING
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?', @p4='?', @p5='?', @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']

                INSERT INTO SystemConfigurations (Id, Category, Key, Value, DataType, Description, IsEncrypted, IsPublic, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8)
                ON CONFLICT (Category, Key) DO NOTHING
[13:23:39 INF] SystemConfigurationSeeder 执行成功
[13:23:39 INF] 执行 AIProviderSeeder: AI提供商配置
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']

            CREATE TABLE IF NOT EXISTS AIProviderConfigurations (
                Id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                ProviderName VARCHAR(100) NOT NULL UNIQUE,
                DisplayName VARCHAR(200) NOT NULL,
                Description TEXT,
                ProviderType VARCHAR(50) NOT NULL,
                Configuration JSONB NOT NULL,
                IsActive BOOLEAN NOT NULL DEFAULT TRUE,
                SortOrder INTEGER NOT NULL DEFAULT 0,
                CreatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                UpdatedAt TIMESTAMP
            );
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT 1 FROM AIProviderConfigurations LIMIT 1
[13:23:39 INF] Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?', @p4='?', @p5='?', @p6='?' (DbType = Boolean), @p7='?' (DbType = Int32), @p8='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']

                INSERT INTO AIProviderConfigurations 
                (Id, ProviderName, DisplayName, Description, ProviderType, Configuration, IsActive, SortOrder, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5::jsonb, @p6, @p7, @p8)
                ON CONFLICT (ProviderName) DO NOTHING
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?', @p4='?', @p5='?', @p6='?' (DbType = Boolean), @p7='?' (DbType = Int32), @p8='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']

                INSERT INTO AIProviderConfigurations 
                (Id, ProviderName, DisplayName, Description, ProviderType, Configuration, IsActive, SortOrder, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5::jsonb, @p6, @p7, @p8)
                ON CONFLICT (ProviderName) DO NOTHING
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?', @p4='?', @p5='?', @p6='?' (DbType = Boolean), @p7='?' (DbType = Int32), @p8='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']

                INSERT INTO AIProviderConfigurations 
                (Id, ProviderName, DisplayName, Description, ProviderType, Configuration, IsActive, SortOrder, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5::jsonb, @p6, @p7, @p8)
                ON CONFLICT (ProviderName) DO NOTHING
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?', @p4='?', @p5='?', @p6='?' (DbType = Boolean), @p7='?' (DbType = Int32), @p8='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']

                INSERT INTO AIProviderConfigurations 
                (Id, ProviderName, DisplayName, Description, ProviderType, Configuration, IsActive, SortOrder, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5::jsonb, @p6, @p7, @p8)
                ON CONFLICT (ProviderName) DO NOTHING
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?', @p4='?', @p5='?', @p6='?' (DbType = Boolean), @p7='?' (DbType = Int32), @p8='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']

                INSERT INTO AIProviderConfigurations 
                (Id, ProviderName, DisplayName, Description, ProviderType, Configuration, IsActive, SortOrder, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5::jsonb, @p6, @p7, @p8)
                ON CONFLICT (ProviderName) DO NOTHING
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?', @p4='?', @p5='?', @p6='?' (DbType = Boolean), @p7='?' (DbType = Int32), @p8='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']

                INSERT INTO AIProviderConfigurations 
                (Id, ProviderName, DisplayName, Description, ProviderType, Configuration, IsActive, SortOrder, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5::jsonb, @p6, @p7, @p8)
                ON CONFLICT (ProviderName) DO NOTHING
[13:23:39 INF] AIProviderSeeder 执行成功
[13:23:39 INF] 执行优先级 Master 的种子数据...
[13:23:39 INF] 执行 SubscriptionPlanSeeder: 订阅套餐
[13:23:39 INF] Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "SubscriptionPlans" AS s)
[13:23:39 INF] SubscriptionPlan seeding skipped - data already exists
[13:23:39 INF] SubscriptionPlanSeeder 执行成功
[13:23:39 INF] 执行 AgentCategorySeeder: 智能体分类
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM agent_categories AS a)
[13:23:39 INF] AgentCategory seeding skipped - data already exists
[13:23:39 INF] AgentCategorySeeder 执行成功
[13:23:39 INF] 执行优先级 User 的种子数据...
[13:23:39 INF] 执行 AdminUserSeeder: 管理员用户
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM admin_users AS a)
[13:23:39 INF] AdminUser seeding skipped - data already exists
[13:23:39 INF] AdminUserSeeder 执行成功
[13:23:39 INF] 执行优先级 Business 的种子数据...
[13:23:39 INF] 执行 AgentSeeder: 系统预置智能体
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM agents AS a)
[13:23:39 INF] Agent seeding skipped - data already exists
[13:23:39 INF] AgentSeeder 执行成功
[13:23:39 INF] 开始执行示例数据种子...
[13:23:39 INF] 执行优先级 Sample 的种子数据...
[13:23:39 INF] 执行 SampleCustomerUserSeeder: 示例客户用户
[13:23:39 INF] Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM customer_users AS c)
[13:23:39 INF] CustomerUser seeding skipped - data already exists
[13:23:39 INF] SampleCustomerUserSeeder 执行成功
[13:23:39 INF] 执行 SampleAgentSeeder: 示例智能体
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM agents AS a)
[13:23:39 INF] Agent seeding skipped - data already exists
[13:23:39 INF] SampleAgentSeeder 执行成功
[13:23:39 INF] 执行 SampleSubscriptionSeeder: 示例订阅
[13:23:39 INF] Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "Subscriptions" AS s)
[13:23:39 INF] Subscription seeding skipped - data already exists
[13:23:39 INF] SampleSubscriptionSeeder 执行成功
[13:23:39 INF] 执行 SampleConversationSeeder: 示例对话
[13:23:39 INF] Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM conversations AS c)
[13:23:39 INF] Conversation seeding skipped - data already exists
[13:23:39 INF] SampleConversationSeeder 执行成功
[13:23:39 INF] 数据种子执行完成
[13:23:39 INF] Database seeding completed
[13:23:39 INF] Hangfire recurring jobs configured
[13:23:39 INF] User profile is available. Using '/Users/<USER>/.aspnet/DataProtection-Keys' as key repository; keys will not be encrypted at rest.
[13:23:39 INF] Starting cache warming service
[13:23:39 INF] Subscription Maintenance Service started
[13:23:39 INF] Starting subscription maintenance at 07/16/2025 05:23:39
[13:23:39 INF] Executed DbCommand (1ms) [Parameters=[@__tomorrow_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT s."Id", s."AutoRenew", s."CancellationDate", s."CancellationReason", s."CreatedAt", s."CustomerUserId", s."EndDate", s."ExpiredAt", s."LastRenewalDate", s."LastResetDate", s."NextBillingDate", s."NextPlanId", s."NextResetDate", s."OrderId", s."PauseDate", s."PaymentMethod", s."PlanId", s."RemainingTokens", s."ResumeDate", s."StartDate", s."Status", s."UpdatedAt", s."UpdatedBy", s."PaidAmount", s."PaidCurrency", s."TokenQuotaTotal", s."TokenQuotaUsed"
FROM "Subscriptions" AS s
WHERE s."EndDate" <= @__tomorrow_0 AND s."Status" = 'Active'
[13:23:39 INF] Token Reset Service started
[13:23:39 INF] Usage Cleanup Service started
[13:23:39 INF] Processed 0 expiring subscriptions
[13:23:39 INF] Processed 0 expiring subscriptions
[13:23:39 INF] Completed subscription maintenance at 07/16/2025 05:23:39
[13:23:39 INF] Performance monitoring service started with interval: 60 seconds
[13:23:39 INF] 会话清理服务已启动
[13:23:39 INF] Business metrics collector started
[13:23:39 INF] Executed DbCommand (1ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT c.user_id
    FROM conversations AS c
    WHERE c.updated_at >= @__today_0
) AS c0
[13:23:39 INF] Executed DbCommand (1ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT c.user_id
    FROM conversations AS c
    WHERE c.updated_at >= @__thirtyDaysAgo_0
) AS c0
[13:23:39 INF] Executed DbCommand (1ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM customer_users AS c
WHERE c.created_at >= @__today_0
[13:23:39 INF] Executed DbCommand (1ms) [Parameters=[@__yesterday_0='?' (DbType = DateTime), @__today_1='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT DISTINCT c.user_id
FROM conversations AS c
WHERE c.updated_at >= @__yesterday_0 AND c.updated_at < @__today_1
[13:23:39 INF] Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
[13:23:39 INF] Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
WHERE a.status = 'Published'
[13:23:39 WRN] Payment signature validation is disabled in configuration. This should only be used in development!
[13:23:39 INF] Executed DbCommand (1ms) [Parameters=[@__oneHourAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT c.agent_id AS "AgentId", count(*)::int AS "Count"
FROM conversations AS c
WHERE c.created_at >= @__oneHourAgo_0 AND c.agent_id <> '00000000-0000-0000-0000-000000000000'
GROUP BY c.agent_id
[13:23:39 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM conversations AS c
[13:23:39 INF] Executed DbCommand (1ms) [Parameters=[@__oneDayAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM conversations AS c
WHERE c.updated_at >= @__oneDayAgo_0
[13:23:39 INF] Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT c.id AS "Id", c.message_count AS "MessageCount"
FROM conversations AS c
[13:23:39 INF] Executed DbCommand (1ms) [Parameters=[@__AddHours_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT c.created_at AS "CreatedAt", c.updated_at AS "UpdatedAt"
FROM conversations AS c
WHERE c.updated_at < @__AddHours_0
[13:23:39 INF] Executed DbCommand (1ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT t."Model", t."SubscriptionId", COALESCE(sum(t."Tokens"), 0)::int AS "TotalTokens"
FROM "TokenUsages" AS t
WHERE t."CreatedAt" >= @__today_0
GROUP BY t."Model", t."SubscriptionId"
[13:23:39 INF] Bus started: rabbitmq://localhost/
[13:23:39 INF] Executed DbCommand (53ms) [Parameters=[@__subscriptionIds_0='?' (DbType = Object)], CommandType='Text', CommandTimeout='30']
SELECT s."Id", s."CustomerUserId", s0."Tier"
FROM "Subscriptions" AS s
INNER JOIN "SubscriptionPlans" AS s0 ON s."PlanId" = s0."Id"
WHERE s."Id" = ANY (@__subscriptionIds_0) AND s."Status" = 'Active'
[13:23:39 INF] Now listening on: https://localhost:7025
[13:23:39 INF] Now listening on: http://localhost:5269
[13:23:39 INF] Starting Hangfire Server using job storage: 'PostgreSQL Server: Host: localhost, DB: whimlabai, Schema: hangfire'
[13:23:39 INF] Using the following options for PostgreSQL job storage:
[13:23:39 INF]     Queue poll interval: 00:00:15.
[13:23:39 INF]     Invisibility timeout: 00:30:00.
[13:23:39 INF]     Use sliding invisibility timeout: False.
[13:23:39 INF] Using the following options for Hangfire Server:
    Worker count: 24
    Listening queues: 'critical', 'default', 'low'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
[13:23:39 INF] Application started. Press Ctrl+C to shut down.
[13:23:39 INF] Hosting environment: Development
[13:23:39 INF] Content root path: /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/WebApi/WhimLabAI.WebApi
[13:23:39 INF] Executed DbCommand (1ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT o."Type" AS "OrderType", (
    SELECT COALESCE(sum(o0."FinalAmount"), 0.0)
    FROM "Orders" AS o0
    WHERE o0."Status" = 1 AND o0."UpdatedAt" >= @__thirtyDaysAgo_0 AND o."Type" = o0."Type") AS "TotalAmount"
FROM "Orders" AS o
WHERE o."Status" = 1 AND o."UpdatedAt" >= @__thirtyDaysAgo_0
GROUP BY o."Type"
[13:23:39 INF] Server whimlabai-lewvandemacbook-pro:96831:bc7ce549 successfully announced in 5.7709 ms
[13:23:39 INF] Executed DbCommand (1ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM "Orders" AS o
WHERE o."CreatedAt" >= @__thirtyDaysAgo_0
[13:23:39 INF] Server whimlabai-lewvandemacbook-pro:96831:bc7ce549 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
[13:23:39 INF] 1 servers were removed due to timeout
[13:23:39 INF] Executed DbCommand (1ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM "Orders" AS o
WHERE o."CreatedAt" >= @__thirtyDaysAgo_0 AND o."Status" = 1
[13:23:39 INF] Executed DbCommand (1ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT o."CustomerUserId"
    FROM "Orders" AS o
    WHERE o."Status" = 1 AND o."UpdatedAt" >= @__thirtyDaysAgo_0
) AS o0
[13:23:39 INF] Server whimlabai-lewvandemacbook-pro:96831:bc7ce549 all the dispatchers started
[13:23:39 INF] Business metrics collected successfully
[13:23:39 INF] Starting payment timeout check job
[13:23:39 INF] Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT o."Id", o."CouponCode", o."CouponId", o."CreatedAt", o."CustomerUserId", o."DeletedAt", o."ExpireAt", o."IsDeleted", o."Metadata", o."OrderNo", o."PaidAt", o."PaymentMethod", o."ProductId", o."ProductName", o."RefundedAt", o."Remark", o."Status", o."Type", o."UpdatedAt", o."UpdatedBy", o."Amount", o."AmountCurrency", o."DiscountAmount", o."DiscountCurrency", o."FinalAmount", o."FinalCurrency", o."PayableAmount", o."PayableCurrency"
FROM "Orders" AS o
WHERE o."Status" = 0 AND o."ExpireAt" < now()
[13:23:39 INF] Executed DbCommand (1ms) [Parameters=[@__expiryTime_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT p."Id", p."CompletedAt", p."CreatedAt", p."ExpireAt", p."FailReason", p."FailedAt", p."OrderId", p."PayerAccount", p."PayerName", p."PaymentMethod", p."PaymentNo", p."RawData", p."Status", p."TransactionId", p."UpdatedAt", p."UpdatedBy", p.amount, p.currency
FROM "PaymentTransactions" AS p
WHERE p."Status" = 0 AND p."CreatedAt" < @__expiryTime_0
[13:23:39 INF] Payment timeout check job completed. Cancelled orders: 0, Errors: 0
[13:23:54 ERR] Failed to warm up system configuration cache
StackExchange.Redis.RedisTimeoutException: Timeout awaiting response (outbound=0KiB, inbound=0KiB, 5052ms elapsed, timeout is 5000ms), command=SETEX, next: SELECT, inst: 0, qu: 0, qs: 3, aw: False, bw: Inactive, rs: ReadAsync, ws: Idle, in: 0, in-pipe: 0, out-pipe: 0, last-in: 352, cur-in: 0, sync-ops: 0, async-ops: 2, serverEndpoint: localhost:6379, conn-sec: 15.03, aoc: 0, mc: 1/1/0, mgr: 10 of 10 available, clientName: lewvandeMacBook-Pro(SE.Redis-v2.8.16.12844), IOCP: (Busy=0,Free=1000,Min=1,Max=1000), WORKER: (Busy=2,Free=32765,Min=12,Max=32767), POOL: (Threads=8,QueuedItems=0,CompletedItems=1418,Timers=18), v: 2.8.16.12844 (Please take a look at this article for some common client-side issues that can cause timeouts: https://stackexchange.github.io/StackExchange.Redis/Timeouts)
   at WhimLabAI.Infrastructure.Caching.RedisCacheService.SetAsync[T](String key, T value, Nullable`1 expiry, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/WhimLabAI/BackEnd/WhimLabAI.Infrastructure/Caching/RedisCacheService.cs:line 44
   at WhimLabAI.Infrastructure.Caching.CacheWarmingService.WarmUpSystemConfigurationAsync(IServiceScope scope, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/WhimLabAI/BackEnd/WhimLabAI.Infrastructure/Caching/CacheWarmingService.cs:line 174
[13:23:54 INF] Cache warming completed in 5077ms
[13:23:55 ERR] Error collecting performance metrics
StackExchange.Redis.RedisTimeoutException: Timeout awaiting response (outbound=0KiB, inbound=0KiB, 5930ms elapsed, timeout is 5000ms), command=SETEX, next: SELECT, inst: 0, qu: 0, qs: 3, aw: False, bw: Inactive, rs: ReadAsync, ws: Idle, in: 0, in-pipe: 0, out-pipe: 0, last-in: 352, cur-in: 0, sync-ops: 0, async-ops: 2, serverEndpoint: localhost:6379, conn-sec: 16.02, aoc: 0, mc: 1/1/0, mgr: 10 of 10 available, clientName: lewvandeMacBook-Pro(SE.Redis-v2.8.16.12844), IOCP: (Busy=0,Free=1000,Min=1,Max=1000), WORKER: (Busy=2,Free=32765,Min=12,Max=32767), POOL: (Threads=8,QueuedItems=0,CompletedItems=1494,Timers=18), v: 2.8.16.12844 (Please take a look at this article for some common client-side issues that can cause timeouts: https://stackexchange.github.io/StackExchange.Redis/Timeouts)
   at WhimLabAI.Infrastructure.Caching.RedisCacheService.SetAsync[T](String key, T value, Nullable`1 expiry, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/WhimLabAI/BackEnd/WhimLabAI.Infrastructure/Caching/RedisCacheService.cs:line 44
   at WhimLabAI.Infrastructure.BackgroundServices.PerformanceMonitoringService.CollectPerformanceMetrics(CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/WhimLabAI/BackEnd/WhimLabAI.Infrastructure/BackgroundServices/PerformanceMonitoringService.cs:line 73
[13:23:56 INF] Executed DbCommand (7,105ms) [Parameters=[@__now_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT s."Id", s."AutoRenew", s."CancellationDate", s."CancellationReason", s."CreatedAt", s."CustomerUserId", s."EndDate", s."ExpiredAt", s."LastRenewalDate", s."LastResetDate", s."NextBillingDate", s."NextPlanId", s."NextResetDate", s."OrderId", s."PauseDate", s."PaymentMethod", s."PlanId", s."RemainingTokens", s."ResumeDate", s."StartDate", s."Status", s."UpdatedAt", s."UpdatedBy", s."PaidAmount", s."PaidCurrency", s."TokenQuotaTotal", s."TokenQuotaUsed"
FROM "Subscriptions" AS s
WHERE s."Status" = 'Active' AND s."NextResetDate" <= @__now_0
[13:24:39 INF] Executed DbCommand (1ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT c.user_id
    FROM conversations AS c
    WHERE c.updated_at >= @__today_0
) AS c0
[13:24:39 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT c.user_id
    FROM conversations AS c
    WHERE c.updated_at >= @__thirtyDaysAgo_0
) AS c0
[13:24:39 INF] Executed DbCommand (0ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM customer_users AS c
WHERE c.created_at >= @__today_0
[13:24:39 INF] Executed DbCommand (0ms) [Parameters=[@__yesterday_0='?' (DbType = DateTime), @__today_1='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT DISTINCT c.user_id
FROM conversations AS c
WHERE c.updated_at >= @__yesterday_0 AND c.updated_at < @__today_1
[13:24:39 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
[13:24:39 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
WHERE a.status = 'Published'
[13:24:39 INF] Executed DbCommand (0ms) [Parameters=[@__oneHourAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT c.agent_id AS "AgentId", count(*)::int AS "Count"
FROM conversations AS c
WHERE c.created_at >= @__oneHourAgo_0 AND c.agent_id <> '00000000-0000-0000-0000-000000000000'
GROUP BY c.agent_id
[13:24:39 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM conversations AS c
[13:24:39 INF] Executed DbCommand (0ms) [Parameters=[@__oneDayAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM conversations AS c
WHERE c.updated_at >= @__oneDayAgo_0
[13:24:39 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT c.id AS "Id", c.message_count AS "MessageCount"
FROM conversations AS c
[13:24:39 INF] Executed DbCommand (0ms) [Parameters=[@__AddHours_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT c.created_at AS "CreatedAt", c.updated_at AS "UpdatedAt"
FROM conversations AS c
WHERE c.updated_at < @__AddHours_0
[13:24:39 INF] Executed DbCommand (0ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT t."Model", t."SubscriptionId", COALESCE(sum(t."Tokens"), 0)::int AS "TotalTokens"
FROM "TokenUsages" AS t
WHERE t."CreatedAt" >= @__today_0
GROUP BY t."Model", t."SubscriptionId"
[13:24:39 INF] Executed DbCommand (0ms) [Parameters=[@__subscriptionIds_0='?' (DbType = Object)], CommandType='Text', CommandTimeout='30']
SELECT s."Id", s."CustomerUserId", s0."Tier"
FROM "Subscriptions" AS s
INNER JOIN "SubscriptionPlans" AS s0 ON s."PlanId" = s0."Id"
WHERE s."Id" = ANY (@__subscriptionIds_0) AND s."Status" = 'Active'
[13:24:39 INF] Executed DbCommand (1ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT o."Type" AS "OrderType", (
    SELECT COALESCE(sum(o0."FinalAmount"), 0.0)
    FROM "Orders" AS o0
    WHERE o0."Status" = 1 AND o0."UpdatedAt" >= @__thirtyDaysAgo_0 AND o."Type" = o0."Type") AS "TotalAmount"
FROM "Orders" AS o
WHERE o."Status" = 1 AND o."UpdatedAt" >= @__thirtyDaysAgo_0
GROUP BY o."Type"
[13:24:39 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM "Orders" AS o
WHERE o."CreatedAt" >= @__thirtyDaysAgo_0
[13:24:39 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM "Orders" AS o
WHERE o."CreatedAt" >= @__thirtyDaysAgo_0 AND o."Status" = 1
[13:24:39 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT o."CustomerUserId"
    FROM "Orders" AS o
    WHERE o."Status" = 1 AND o."UpdatedAt" >= @__thirtyDaysAgo_0
) AS o0
[13:24:39 INF] Business metrics collected successfully
[13:24:49 INF] Performance metrics collected: CPU=0.2%, Memory=327MB, Threads=81
[13:24:55 INF] Request starting HTTP/2 POST https://localhost:7025/api/v1/admin/auth/login - application/json 58
[13:24:55 INF] Executing endpoint 'WhimLabAI.WebApi.Controllers.AdminAuth.AdminAuthController.Login (WhimLabAI.WebApi)'
[13:24:55 INF] Route matched with {action = "Login", controller = "AdminAuth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(WhimLabAI.Shared.Dtos.Auth.AdminLoginDto, System.Threading.CancellationToken) on controller WhimLabAI.WebApi.Controllers.AdminAuth.AdminAuthController (WhimLabAI.WebApi).
[13:24:55 INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType156`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType155`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], WhimLabAI.Infrastructure, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
[13:24:55 INF] Executed action WhimLabAI.WebApi.Controllers.AdminAuth.AdminAuthController.Login (WhimLabAI.WebApi) in 16.9347ms
[13:24:55 INF] Executed endpoint 'WhimLabAI.WebApi.Controllers.AdminAuth.AdminAuthController.Login (WhimLabAI.WebApi)'
[13:24:55 INF] HTTP POST /api/v1/admin/auth/login responded 400 in 63.5204 ms
[13:24:55 INF] Request finished HTTP/2 POST https://localhost:7025/api/v1/admin/auth/login - 400 null application/json; charset=utf-8 112.5882ms
[13:24:59 INF] Executed DbCommand (3ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?' (DbType = Object), @p4='?', @p5='?', @p6='?', @p7='?', @p8='?' (DbType = DateTime), @p9='?', @p10='?', @p11='?', @p12='?', @p13='?', @p14='?' (DbType = Int64), @p15='?', @p16='?', @p17='?', @p18='?' (DbType = Boolean), @p19='?' (DbType = Boolean), @p20='?', @p21='?', @p22='?' (DbType = Object), @p23='?' (DbType = Object), @p24='?', @p25='?' (DbType = Object), @p26='?', @p27='?' (DbType = Int32), @p28='?', @p29='?' (DbType = DateTime), @p30='?', @p31='?', @p32='?' (DbType = Guid), @p33='?', @p34='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "AuditLogs" ("Id", "Action", "ActionName", "AdditionalData", "ChangedProperties", "ClientInfo", "ControllerName", "CorrelationId", "CreatedAt", "Description", "EntityId", "EntityType", "ErrorMessage", "ExceptionStack", "ExecutionDuration", "GeoLocation", "HttpMethod", "IpAddress", "IsSensitive", "IsSuccess", "JwtId", "Module", "NewValues", "OldValues", "RequestId", "RequestParameters", "RequestUrl", "ResponseStatusCode", "RiskLevel", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId", "UserName", "UserType")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34);
[13:24:59 INF] Flushed 1 audit logs to database
[13:25:10 INF] Request starting HTTP/2 GET https://localhost:7025/api/v1/admin/auth/captcha - null null
[13:25:10 INF] Executing endpoint 'WhimLabAI.WebApi.Controllers.AdminAuth.AdminAuthController.GetCaptcha (WhimLabAI.WebApi)'
[13:25:10 INF] Route matched with {action = "GetCaptcha", controller = "AdminAuth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCaptcha(System.Threading.CancellationToken) on controller WhimLabAI.WebApi.Controllers.AdminAuth.AdminAuthController (WhimLabAI.WebApi).
[13:25:10 INF] 图形验证码已生成: CaptchaId=88df4f05-f0c0-46af-8221-64ca5de4e82e
[13:25:10 INF] Executing OkObjectResult, writing value of type 'WhimLabAI.Shared.Dtos.ApiResponse`1[[WhimLabAI.Shared.Dtos.CaptchaDto, WhimLabAI.Shared, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
[13:25:10 INF] Executed action WhimLabAI.WebApi.Controllers.AdminAuth.AdminAuthController.GetCaptcha (WhimLabAI.WebApi) in 6.4662ms
[13:25:10 INF] Executed endpoint 'WhimLabAI.WebApi.Controllers.AdminAuth.AdminAuthController.GetCaptcha (WhimLabAI.WebApi)'
[13:25:10 INF] HTTP GET /api/v1/admin/auth/captcha responded 200 in 10.4638 ms
[13:25:10 INF] Request finished HTTP/2 GET https://localhost:7025/api/v1/admin/auth/captcha - 200 null application/json; charset=utf-8 14.4282ms
[13:25:11 INF] Starting payment timeout check job
[13:25:11 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT o."Id", o."CouponCode", o."CouponId", o."CreatedAt", o."CustomerUserId", o."DeletedAt", o."ExpireAt", o."IsDeleted", o."Metadata", o."OrderNo", o."PaidAt", o."PaymentMethod", o."ProductId", o."ProductName", o."RefundedAt", o."Remark", o."Status", o."Type", o."UpdatedAt", o."UpdatedBy", o."Amount", o."AmountCurrency", o."DiscountAmount", o."DiscountCurrency", o."FinalAmount", o."FinalCurrency", o."PayableAmount", o."PayableCurrency"
FROM "Orders" AS o
WHERE o."Status" = 0 AND o."ExpireAt" < now()
[13:25:11 INF] Executed DbCommand (0ms) [Parameters=[@__expiryTime_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT p."Id", p."CompletedAt", p."CreatedAt", p."ExpireAt", p."FailReason", p."FailedAt", p."OrderId", p."PayerAccount", p."PayerName", p."PaymentMethod", p."PaymentNo", p."RawData", p."Status", p."TransactionId", p."UpdatedAt", p."UpdatedBy", p.amount, p.currency
FROM "PaymentTransactions" AS p
WHERE p."Status" = 0 AND p."CreatedAt" < @__expiryTime_0
[13:25:11 INF] Payment timeout check job completed. Cancelled orders: 0, Errors: 0
[13:25:19 INF] Executed DbCommand (2ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?' (DbType = Object), @p4='?', @p5='?', @p6='?', @p7='?', @p8='?' (DbType = DateTime), @p9='?', @p10='?', @p11='?', @p12='?', @p13='?', @p14='?' (DbType = Int64), @p15='?', @p16='?', @p17='?', @p18='?' (DbType = Boolean), @p19='?' (DbType = Boolean), @p20='?', @p21='?', @p22='?' (DbType = Object), @p23='?' (DbType = Object), @p24='?', @p25='?' (DbType = Object), @p26='?', @p27='?' (DbType = Int32), @p28='?', @p29='?' (DbType = DateTime), @p30='?', @p31='?', @p32='?' (DbType = Guid), @p33='?', @p34='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "AuditLogs" ("Id", "Action", "ActionName", "AdditionalData", "ChangedProperties", "ClientInfo", "ControllerName", "CorrelationId", "CreatedAt", "Description", "EntityId", "EntityType", "ErrorMessage", "ExceptionStack", "ExecutionDuration", "GeoLocation", "HttpMethod", "IpAddress", "IsSensitive", "IsSuccess", "JwtId", "Module", "NewValues", "OldValues", "RequestId", "RequestParameters", "RequestUrl", "ResponseStatusCode", "RiskLevel", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId", "UserName", "UserType")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34);
[13:25:19 INF] Flushed 1 audit logs to database
[13:25:23 INF] Request starting HTTP/1.1 GET https://localhost:7025/api/v1/admin/auth/captcha - null null
[13:25:23 INF] Executing endpoint 'WhimLabAI.WebApi.Controllers.AdminAuth.AdminAuthController.GetCaptcha (WhimLabAI.WebApi)'
[13:25:23 INF] Route matched with {action = "GetCaptcha", controller = "AdminAuth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCaptcha(System.Threading.CancellationToken) on controller WhimLabAI.WebApi.Controllers.AdminAuth.AdminAuthController (WhimLabAI.WebApi).
[13:25:23 INF] 图形验证码已生成: CaptchaId=730e0e26-c6ed-4a8e-be96-d8ddd0f83c64
[13:25:23 INF] Executing OkObjectResult, writing value of type 'WhimLabAI.Shared.Dtos.ApiResponse`1[[WhimLabAI.Shared.Dtos.CaptchaDto, WhimLabAI.Shared, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
[13:25:23 INF] Executed action WhimLabAI.WebApi.Controllers.AdminAuth.AdminAuthController.GetCaptcha (WhimLabAI.WebApi) in 4.3176ms
[13:25:23 INF] Executed endpoint 'WhimLabAI.WebApi.Controllers.AdminAuth.AdminAuthController.GetCaptcha (WhimLabAI.WebApi)'
[13:25:23 INF] HTTP GET /api/v1/admin/auth/captcha responded 200 in 6.4440 ms
[13:25:23 INF] Request finished HTTP/1.1 GET https://localhost:7025/api/v1/admin/auth/captcha - 200 null application/json; charset=utf-8 6.7152ms
[13:25:29 INF] Executed DbCommand (3ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?' (DbType = Object), @p4='?', @p5='?', @p6='?', @p7='?', @p8='?' (DbType = DateTime), @p9='?', @p10='?', @p11='?', @p12='?', @p13='?', @p14='?' (DbType = Int64), @p15='?', @p16='?', @p17='?', @p18='?' (DbType = Boolean), @p19='?' (DbType = Boolean), @p20='?', @p21='?', @p22='?' (DbType = Object), @p23='?' (DbType = Object), @p24='?', @p25='?' (DbType = Object), @p26='?', @p27='?' (DbType = Int32), @p28='?', @p29='?' (DbType = DateTime), @p30='?', @p31='?', @p32='?' (DbType = Guid), @p33='?', @p34='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "AuditLogs" ("Id", "Action", "ActionName", "AdditionalData", "ChangedProperties", "ClientInfo", "ControllerName", "CorrelationId", "CreatedAt", "Description", "EntityId", "EntityType", "ErrorMessage", "ExceptionStack", "ExecutionDuration", "GeoLocation", "HttpMethod", "IpAddress", "IsSensitive", "IsSuccess", "JwtId", "Module", "NewValues", "OldValues", "RequestId", "RequestParameters", "RequestUrl", "ResponseStatusCode", "RiskLevel", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId", "UserName", "UserType")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34);
[13:25:29 INF] Flushed 1 audit logs to database
[13:25:39 INF] Executed DbCommand (2ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT c.user_id
    FROM conversations AS c
    WHERE c.updated_at >= @__today_0
) AS c0
[13:25:39 INF] Executed DbCommand (1ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT c.user_id
    FROM conversations AS c
    WHERE c.updated_at >= @__thirtyDaysAgo_0
) AS c0
[13:25:39 INF] Executed DbCommand (1ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM customer_users AS c
WHERE c.created_at >= @__today_0
[13:25:39 INF] Executed DbCommand (1ms) [Parameters=[@__yesterday_0='?' (DbType = DateTime), @__today_1='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT DISTINCT c.user_id
FROM conversations AS c
WHERE c.updated_at >= @__yesterday_0 AND c.updated_at < @__today_1
[13:25:39 INF] Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
[13:25:39 INF] Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
WHERE a.status = 'Published'
[13:25:39 INF] Executed DbCommand (1ms) [Parameters=[@__oneHourAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT c.agent_id AS "AgentId", count(*)::int AS "Count"
FROM conversations AS c
WHERE c.created_at >= @__oneHourAgo_0 AND c.agent_id <> '00000000-0000-0000-0000-000000000000'
GROUP BY c.agent_id
[13:25:39 INF] Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM conversations AS c
[13:25:39 INF] Executed DbCommand (1ms) [Parameters=[@__oneDayAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM conversations AS c
WHERE c.updated_at >= @__oneDayAgo_0
[13:25:39 INF] Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT c.id AS "Id", c.message_count AS "MessageCount"
FROM conversations AS c
[13:25:39 INF] Executed DbCommand (1ms) [Parameters=[@__AddHours_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT c.created_at AS "CreatedAt", c.updated_at AS "UpdatedAt"
FROM conversations AS c
WHERE c.updated_at < @__AddHours_0
[13:25:39 INF] Executed DbCommand (1ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT t."Model", t."SubscriptionId", COALESCE(sum(t."Tokens"), 0)::int AS "TotalTokens"
FROM "TokenUsages" AS t
WHERE t."CreatedAt" >= @__today_0
GROUP BY t."Model", t."SubscriptionId"
[13:25:39 INF] Executed DbCommand (1ms) [Parameters=[@__subscriptionIds_0='?' (DbType = Object)], CommandType='Text', CommandTimeout='30']
SELECT s."Id", s."CustomerUserId", s0."Tier"
FROM "Subscriptions" AS s
INNER JOIN "SubscriptionPlans" AS s0 ON s."PlanId" = s0."Id"
WHERE s."Id" = ANY (@__subscriptionIds_0) AND s."Status" = 'Active'
[13:25:39 INF] Executed DbCommand (1ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT o."Type" AS "OrderType", (
    SELECT COALESCE(sum(o0."FinalAmount"), 0.0)
    FROM "Orders" AS o0
    WHERE o0."Status" = 1 AND o0."UpdatedAt" >= @__thirtyDaysAgo_0 AND o."Type" = o0."Type") AS "TotalAmount"
FROM "Orders" AS o
WHERE o."Status" = 1 AND o."UpdatedAt" >= @__thirtyDaysAgo_0
GROUP BY o."Type"
[13:25:39 INF] Executed DbCommand (1ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM "Orders" AS o
WHERE o."CreatedAt" >= @__thirtyDaysAgo_0
[13:25:39 INF] Executed DbCommand (1ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM "Orders" AS o
WHERE o."CreatedAt" >= @__thirtyDaysAgo_0 AND o."Status" = 1
[13:25:39 INF] Executed DbCommand (1ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT o."CustomerUserId"
    FROM "Orders" AS o
    WHERE o."Status" = 1 AND o."UpdatedAt" >= @__thirtyDaysAgo_0
) AS o0
[13:25:39 INF] Business metrics collected successfully
[13:25:49 INF] Performance metrics collected: CPU=0.2%, Memory=327MB, Threads=81
[13:26:15 INF] Request starting HTTP/2 POST https://localhost:7025/api/v1/admin/auth/login - application/json 141
[13:26:15 INF] Executing endpoint 'WhimLabAI.WebApi.Controllers.AdminAuth.AdminAuthController.Login (WhimLabAI.WebApi)'
[13:26:15 INF] Route matched with {action = "Login", controller = "AdminAuth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(WhimLabAI.Shared.Dtos.Auth.AdminLoginDto, System.Threading.CancellationToken) on controller WhimLabAI.WebApi.Controllers.AdminAuth.AdminAuthController (WhimLabAI.WebApi).
[13:26:15 INF] 图形验证码验证成功: CaptchaId=88df4f05-f0c0-46af-8221-64ca5de4e82e
[13:26:15 INF] Executed DbCommand (2ms) [Parameters=[@__ToLower_0='?'], CommandType='Text', CommandTimeout='30']
SELECT a.id, a.avatar, a.created_at, a.email, a.enable_ip_whitelist, a.login_failed_count, a.ip_whitelist, a."IsActive", a.is_super, a.last_login_at, a.last_login_ip, a.locked_until, a.nickname, a.password_expired_at, a.password_hash, a.status, a.two_factor_enabled, a.two_factor_secret, a.updated_at, a."UpdatedBy", a.username, a.phone_country_code, a.phone_number
FROM admin_users AS a
WHERE lower(a.username) = @__ToLower_0
LIMIT 1
[13:26:15 INF] Executed DbCommand (2ms) [Parameters=[@__p_0='?' (DbType = Guid)], CommandType='Text', CommandTimeout='30']
SELECT a.user_id, a.role_id, a.created_at, r.id, r.code, r.created_at, r.description, r.display_order, r.is_enabled, r.is_system, r.name, r.updated_at, r.updated_by
FROM admin_user_roles AS a
INNER JOIN roles AS r ON a.role_id = r.id
WHERE a.user_id = @__p_0
[13:26:15 INF] Executed DbCommand (2ms) [Parameters=[@p2='?' (DbType = Guid), @p0='?' (DbType = DateTime), @p1='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE admin_users SET last_login_at = @p0, updated_at = @p1
WHERE id = @p2;
[13:26:15 INF] 管理员登录成功: admin
[13:26:15 INF] Executed DbCommand (1ms) [Parameters=[@__adminUserId_0='?' (DbType = Guid)], CommandType='Text', CommandTimeout='30']
SELECT a."Id", a."AdminUserId", a."City", a."Country", a."CreatedAt", a."DeviceId", a."DeviceName", a."ExpiresAt", a."IpAddress", a."IsActive", a."IsAnomalousLocation", a."LastActivityAt", a."Latitude", a."Longitude", a."MfaVerified", a."MfaVerifiedAt", a."RefreshToken", a."RefreshTokenExpiresAt", a."Region", a."RequiresMfa", a."SessionToken", a."UpdatedAt", a."UpdatedBy", a."UserAgent"
FROM "AdminUserSessions" AS a
WHERE a."AdminUserId" = @__adminUserId_0 AND a."IsActive" AND a."ExpiresAt" > now()
ORDER BY a."LastActivityAt" DESC
[13:26:15 INF] Executed DbCommand (2ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = Guid), @p2='?', @p3='?', @p4='?' (DbType = DateTime), @p5='?', @p6='?', @p7='?' (DbType = DateTime), @p8='?', @p9='?' (DbType = Boolean), @p10='?' (DbType = Boolean), @p11='?' (DbType = DateTime), @p12='?' (DbType = Double), @p13='?' (DbType = Double), @p14='?' (DbType = Boolean), @p15='?' (DbType = DateTime), @p16='?', @p17='?' (DbType = DateTime), @p18='?', @p19='?' (DbType = Boolean), @p20='?', @p21='?' (DbType = DateTime), @p22='?', @p23='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "AdminUserSessions" ("Id", "AdminUserId", "City", "Country", "CreatedAt", "DeviceId", "DeviceName", "ExpiresAt", "IpAddress", "IsActive", "IsAnomalousLocation", "LastActivityAt", "Latitude", "Longitude", "MfaVerified", "MfaVerifiedAt", "RefreshToken", "RefreshTokenExpiresAt", "Region", "RequiresMfa", "SessionToken", "UpdatedAt", "UpdatedBy", "UserAgent")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23);
[13:26:15 INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='Text', CommandTimeout='30']
SELECT a.is_super AS "IsSuperAdmin"
FROM admin_users AS a
WHERE a.id = @__userId_0
LIMIT 1
[13:26:15 INF] Executed DbCommand (2ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='Text', CommandTimeout='30']
SELECT DISTINCT p.id, p.category, p.code, p.created_at, p.description, p.display_order, p.is_enabled, p.is_system, p.name, p.parent_id, p.updated_at, p."UpdatedBy"
FROM admin_user_roles AS a
INNER JOIN roles AS r ON a.role_id = r.id
INNER JOIN role_permissions AS r0 ON r.id = r0.role_id
INNER JOIN permissions AS p ON r0.permission_id = p.id
WHERE a.user_id = @__userId_0 AND r.is_enabled AND p.is_enabled
[13:26:15 INF] Executing OkObjectResult, writing value of type 'WhimLabAI.Shared.Dtos.ApiResponse`1[[WhimLabAI.Shared.Dtos.Auth.AdminAuthResponseDto, WhimLabAI.Shared, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
[13:26:15 INF] Executed action WhimLabAI.WebApi.Controllers.AdminAuth.AdminAuthController.Login (WhimLabAI.WebApi) in 157.3854ms
[13:26:15 INF] Executed endpoint 'WhimLabAI.WebApi.Controllers.AdminAuth.AdminAuthController.Login (WhimLabAI.WebApi)'
[13:26:15 INF] HTTP POST /api/v1/admin/auth/login responded 200 in 158.0900 ms
[13:26:15 INF] Request finished HTTP/2 POST https://localhost:7025/api/v1/admin/auth/login - 200 null application/json; charset=utf-8 158.2574ms
[13:26:19 INF] Executed DbCommand (2ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?' (DbType = Object), @p4='?', @p5='?', @p6='?', @p7='?', @p8='?' (DbType = DateTime), @p9='?', @p10='?', @p11='?', @p12='?', @p13='?', @p14='?' (DbType = Int64), @p15='?', @p16='?', @p17='?', @p18='?' (DbType = Boolean), @p19='?' (DbType = Boolean), @p20='?', @p21='?', @p22='?' (DbType = Object), @p23='?' (DbType = Object), @p24='?', @p25='?' (DbType = Object), @p26='?', @p27='?' (DbType = Int32), @p28='?', @p29='?' (DbType = DateTime), @p30='?', @p31='?', @p32='?' (DbType = Guid), @p33='?', @p34='?', @p35='?' (DbType = Guid), @p36='?', @p37='?', @p38='?' (DbType = Object), @p39='?', @p40='?', @p41='?', @p42='?', @p43='?' (DbType = DateTime), @p44='?', @p45='?', @p46='?', @p47='?', @p48='?', @p49='?' (DbType = Int64), @p50='?', @p51='?', @p52='?', @p53='?' (DbType = Boolean), @p54='?' (DbType = Boolean), @p55='?', @p56='?', @p57='?' (DbType = Object), @p58='?' (DbType = Object), @p59='?', @p60='?' (DbType = Object), @p61='?', @p62='?' (DbType = Int32), @p63='?', @p64='?' (DbType = DateTime), @p65='?', @p66='?', @p67='?' (DbType = Guid), @p68='?', @p69='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "AuditLogs" ("Id", "Action", "ActionName", "AdditionalData", "ChangedProperties", "ClientInfo", "ControllerName", "CorrelationId", "CreatedAt", "Description", "EntityId", "EntityType", "ErrorMessage", "ExceptionStack", "ExecutionDuration", "GeoLocation", "HttpMethod", "IpAddress", "IsSensitive", "IsSuccess", "JwtId", "Module", "NewValues", "OldValues", "RequestId", "RequestParameters", "RequestUrl", "ResponseStatusCode", "RiskLevel", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId", "UserName", "UserType")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34);
INSERT INTO "AuditLogs" ("Id", "Action", "ActionName", "AdditionalData", "ChangedProperties", "ClientInfo", "ControllerName", "CorrelationId", "CreatedAt", "Description", "EntityId", "EntityType", "ErrorMessage", "ExceptionStack", "ExecutionDuration", "GeoLocation", "HttpMethod", "IpAddress", "IsSensitive", "IsSuccess", "JwtId", "Module", "NewValues", "OldValues", "RequestId", "RequestParameters", "RequestUrl", "ResponseStatusCode", "RiskLevel", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId", "UserName", "UserType")
VALUES (@p35, @p36, @p37, @p38, @p39, @p40, @p41, @p42, @p43, @p44, @p45, @p46, @p47, @p48, @p49, @p50, @p51, @p52, @p53, @p54, @p55, @p56, @p57, @p58, @p59, @p60, @p61, @p62, @p63, @p64, @p65, @p66, @p67, @p68, @p69);
[13:26:19 INF] Flushed 2 audit logs to database
[13:26:30 INF] Request starting HTTP/2 POST https://localhost:7025/api/v1/customer/auth/login - application/json 60
[13:26:30 INF] HTTP POST /api/v1/customer/auth/login responded 404 in 1.0883 ms
[13:26:30 INF] Request finished HTTP/2 POST https://localhost:7025/api/v1/customer/auth/login - 404 0 null 1.4774ms
[13:26:30 INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: POST https://localhost:7025/api/v1/customer/auth/login, Response status code: 404
[13:26:53 INF] Executed DbCommand (13,957ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?' (DbType = Object), @p4='?', @p5='?', @p6='?', @p7='?', @p8='?' (DbType = DateTime), @p9='?', @p10='?', @p11='?', @p12='?', @p13='?', @p14='?' (DbType = Int64), @p15='?', @p16='?', @p17='?', @p18='?' (DbType = Boolean), @p19='?' (DbType = Boolean), @p20='?', @p21='?', @p22='?' (DbType = Object), @p23='?' (DbType = Object), @p24='?', @p25='?' (DbType = Object), @p26='?', @p27='?' (DbType = Int32), @p28='?', @p29='?' (DbType = DateTime), @p30='?', @p31='?', @p32='?' (DbType = Guid), @p33='?', @p34='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "AuditLogs" ("Id", "Action", "ActionName", "AdditionalData", "ChangedProperties", "ClientInfo", "ControllerName", "CorrelationId", "CreatedAt", "Description", "EntityId", "EntityType", "ErrorMessage", "ExceptionStack", "ExecutionDuration", "GeoLocation", "HttpMethod", "IpAddress", "IsSensitive", "IsSuccess", "JwtId", "Module", "NewValues", "OldValues", "RequestId", "RequestParameters", "RequestUrl", "ResponseStatusCode", "RiskLevel", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId", "UserName", "UserType")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34);
[13:26:53 INF] Flushed 1 audit logs to database
[13:26:54 INF] Performance metrics collected: CPU=0.2%, Memory=327MB, Threads=81
[13:26:55 ERR] An error occurred using the connection to database 'whimlabai' on server 'tcp://localhost:5432'.
[13:26:55 INF] A transient exception occurred during execution. The operation will be retried after 0ms.
Npgsql.NpgsqlException (0x80004005): The operation has timed out
 ---> System.TimeoutException: The operation has timed out.
   at Npgsql.ThrowHelper.ThrowNpgsqlExceptionWithInnerTimeoutException(String message)
   at Npgsql.Util.NpgsqlTimeout.Check()
   at Npgsql.Util.NpgsqlTimeout.CheckAndGetTimeLeft()
   at Npgsql.Util.NpgsqlTimeout.CheckAndApply(NpgsqlConnector connector)
   at Npgsql.Internal.NpgsqlConnector.Authenticate(String username, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenInternalAsync(Boolean errorsExpected, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenInternalAsync(Boolean errorsExpected, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenAsync(CancellationToken cancellationToken, Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
Npgsql.NpgsqlException (0x80004005): The operation has timed out
 ---> System.TimeoutException: The operation has timed out.
   at Npgsql.ThrowHelper.ThrowNpgsqlExceptionWithInnerTimeoutException(String message)
   at Npgsql.Util.NpgsqlTimeout.Check()
   at Npgsql.Util.NpgsqlTimeout.CheckAndGetTimeLeft()
   at Npgsql.Util.NpgsqlTimeout.CheckAndApply(NpgsqlConnector connector)
   at Npgsql.Internal.NpgsqlConnector.Authenticate(String username, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenInternalAsync(Boolean errorsExpected, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenInternalAsync(Boolean errorsExpected, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenAsync(CancellationToken cancellationToken, Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
[13:26:55 INF] Executed DbCommand (1ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT c.user_id
    FROM conversations AS c
    WHERE c.updated_at >= @__today_0
) AS c0
[13:26:55 INF] Executed DbCommand (3ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT c.user_id
    FROM conversations AS c
    WHERE c.updated_at >= @__thirtyDaysAgo_0
) AS c0
[13:26:55 INF] Executed DbCommand (2ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM customer_users AS c
WHERE c.created_at >= @__today_0
[13:26:55 INF] Executed DbCommand (1ms) [Parameters=[@__yesterday_0='?' (DbType = DateTime), @__today_1='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT DISTINCT c.user_id
FROM conversations AS c
WHERE c.updated_at >= @__yesterday_0 AND c.updated_at < @__today_1
[13:26:55 INF] Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
[13:26:55 INF] Executed DbCommand (2ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
WHERE a.status = 'Published'
[13:26:55 INF] Executed DbCommand (1ms) [Parameters=[@__oneHourAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT c.agent_id AS "AgentId", count(*)::int AS "Count"
FROM conversations AS c
WHERE c.created_at >= @__oneHourAgo_0 AND c.agent_id <> '00000000-0000-0000-0000-000000000000'
GROUP BY c.agent_id
[13:26:55 INF] Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM conversations AS c
[13:26:55 INF] Executed DbCommand (1ms) [Parameters=[@__oneDayAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM conversations AS c
WHERE c.updated_at >= @__oneDayAgo_0
[13:26:55 INF] Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT c.id AS "Id", c.message_count AS "MessageCount"
FROM conversations AS c
[13:26:55 INF] Executed DbCommand (1ms) [Parameters=[@__AddHours_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT c.created_at AS "CreatedAt", c.updated_at AS "UpdatedAt"
FROM conversations AS c
WHERE c.updated_at < @__AddHours_0
[13:26:55 INF] Executed DbCommand (2ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT t."Model", t."SubscriptionId", COALESCE(sum(t."Tokens"), 0)::int AS "TotalTokens"
FROM "TokenUsages" AS t
WHERE t."CreatedAt" >= @__today_0
GROUP BY t."Model", t."SubscriptionId"
[13:26:55 INF] Executed DbCommand (3ms) [Parameters=[@__subscriptionIds_0='?' (DbType = Object)], CommandType='Text', CommandTimeout='30']
SELECT s."Id", s."CustomerUserId", s0."Tier"
FROM "Subscriptions" AS s
INNER JOIN "SubscriptionPlans" AS s0 ON s."PlanId" = s0."Id"
WHERE s."Id" = ANY (@__subscriptionIds_0) AND s."Status" = 'Active'
[13:26:55 INF] Executed DbCommand (1ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT o."Type" AS "OrderType", (
    SELECT COALESCE(sum(o0."FinalAmount"), 0.0)
    FROM "Orders" AS o0
    WHERE o0."Status" = 1 AND o0."UpdatedAt" >= @__thirtyDaysAgo_0 AND o."Type" = o0."Type") AS "TotalAmount"
FROM "Orders" AS o
WHERE o."Status" = 1 AND o."UpdatedAt" >= @__thirtyDaysAgo_0
GROUP BY o."Type"
[13:26:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM "Orders" AS o
WHERE o."CreatedAt" >= @__thirtyDaysAgo_0
[13:26:55 INF] Executed DbCommand (1ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM "Orders" AS o
WHERE o."CreatedAt" >= @__thirtyDaysAgo_0 AND o."Status" = 1
[13:26:55 INF] Executed DbCommand (1ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT o."CustomerUserId"
    FROM "Orders" AS o
    WHERE o."Status" = 1 AND o."UpdatedAt" >= @__thirtyDaysAgo_0
) AS o0
[13:26:55 INF] Business metrics collected successfully
[13:27:19 INF] Request starting HTTP/2 POST https://localhost:7025/api/v1/customer/auth/login - application/json 107
[13:27:19 INF] HTTP POST /api/v1/customer/auth/login responded 404 in 1.6385 ms
[13:27:19 INF] Request finished HTTP/2 POST https://localhost:7025/api/v1/customer/auth/login - 404 0 null 1.8438ms
[13:27:19 INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: POST https://localhost:7025/api/v1/customer/auth/login, Response status code: 404
[13:27:19 ERR] Unexpected exception in TimingPipeFlusher.FlushAsync.
System.IO.IOException: The encryption operation failed, see inner exception.
 ---> System.ComponentModel.Win32Exception (14): Bad address
   --- End of inner exception stack trace ---
   at System.Net.Security.SslStream.WriteSingleChunk[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.PipeWriterHelpers.ConcurrentPipeWriter.FlushAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.PipeWriterHelpers.TimingPipeFlusher.FlushAsync(MinDataRate minRate, Int64 count, IHttpOutputAborter outputAborter, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2FrameWriter.WriteGoAwayAsync(Int32 lastStreamId, Http2ErrorCode errorCode)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2Connection.UpdateConnectionState()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2Connection.ProcessRequestsAsync[TContext](IHttpApplication`1 application)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecuteFromThreadPool(Thread threadPoolThread)
   at System.Threading.ThreadPoolWorkQueue.Dispatch()
   at System.Threading.PortableThreadPool.WorkerThread.WorkerThreadStart()
   at System.Threading.Thread.StartCallback()
--- End of stack trace from previous location ---
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.PipeWriterHelpers.ConcurrentPipeWriter.FlushAsyncAwaited(ValueTask`1 flushTask, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.PipeWriterHelpers.TimingPipeFlusher.TimeFlushAsyncAwaited(ValueTask`1 pipeFlushTask, MinDataRate minRate, IHttpOutputAborter outputAborter, CancellationToken cancellationToken)
[13:27:19 ERR] Unhandled exception while processing 0HNE454CJSCTM.
System.IO.IOException: The encryption operation failed, see inner exception.
 ---> System.ComponentModel.Win32Exception (14): Bad address
   --- End of inner exception stack trace ---
   at System.Net.Security.SslStream.WriteSingleChunk[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.DuplexPipeStreamAdapter`1.DisposeAsync()
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.DuplexPipeStreamAdapter`1.DisposeAsync()
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
   at System.Threading.Tasks.AwaitTaskContinuation.RunOrScheduleAction(IAsyncStateMachineBox box, Boolean allowInlining)
   at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetExistingTaskResult(Task`1 task, TResult result)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetResult()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.HttpConnection.ProcessRequestsAsync[TContext](IHttpApplication`1 httpApplication)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
   at System.Threading.Tasks.AwaitTaskContinuation.RunOrScheduleAction(IAsyncStateMachineBox box, Boolean allowInlining)
   at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetExistingTaskResult(Task`1 task, TResult result)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetResult()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2Connection.ProcessRequestsAsync[TContext](IHttpApplication`1 application)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
   at System.Threading.Tasks.AwaitTaskContinuation.RunOrScheduleAction(IAsyncStateMachineBox box, Boolean allowInlining)
   at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
   at System.Threading.Tasks.Task`1.TrySetResult(TResult result)
   at System.Threading.Tasks.UnwrapPromise`1.TrySetFromTask(Task task, Boolean lookForOce)
   at System.Threading.Tasks.UnwrapPromise`1.Invoke(Task completingTask)
   at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetExistingTaskResult(Task`1 task, TResult result)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetResult()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2FrameWriter.WriteToOutputPipe()
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
   at System.Threading.ThreadPoolWorkQueue.Dispatch()
   at System.Threading.PortableThreadPool.WorkerThread.WorkerThreadStart()
   at System.Threading.Thread.StartCallback()
--- End of stack trace from previous location ---
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.DuplexPipeStreamAdapter`1.DisposeAsync()
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.KestrelConnection`1.ExecuteAsync()
[13:27:19 INF] Executed DbCommand (3ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?' (DbType = Object), @p4='?', @p5='?', @p6='?', @p7='?', @p8='?' (DbType = DateTime), @p9='?', @p10='?', @p11='?', @p12='?', @p13='?', @p14='?' (DbType = Int64), @p15='?', @p16='?', @p17='?', @p18='?' (DbType = Boolean), @p19='?' (DbType = Boolean), @p20='?', @p21='?', @p22='?' (DbType = Object), @p23='?' (DbType = Object), @p24='?', @p25='?' (DbType = Object), @p26='?', @p27='?' (DbType = Int32), @p28='?', @p29='?' (DbType = DateTime), @p30='?', @p31='?', @p32='?' (DbType = Guid), @p33='?', @p34='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "AuditLogs" ("Id", "Action", "ActionName", "AdditionalData", "ChangedProperties", "ClientInfo", "ControllerName", "CorrelationId", "CreatedAt", "Description", "EntityId", "EntityType", "ErrorMessage", "ExceptionStack", "ExecutionDuration", "GeoLocation", "HttpMethod", "IpAddress", "IsSensitive", "IsSuccess", "JwtId", "Module", "NewValues", "OldValues", "RequestId", "RequestParameters", "RequestUrl", "ResponseStatusCode", "RiskLevel", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId", "UserName", "UserType")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34);
[13:27:19 INF] Flushed 1 audit logs to database
[13:27:49 INF] Performance metrics collected: CPU=0.1%, Memory=327MB, Threads=81
[13:27:55 INF] Executed DbCommand (1ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT c.user_id
    FROM conversations AS c
    WHERE c.updated_at >= @__today_0
) AS c0
[13:27:55 INF] Executed DbCommand (1ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT c.user_id
    FROM conversations AS c
    WHERE c.updated_at >= @__thirtyDaysAgo_0
) AS c0
[13:27:55 INF] Executed DbCommand (1ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM customer_users AS c
WHERE c.created_at >= @__today_0
[13:27:55 INF] Executed DbCommand (0ms) [Parameters=[@__yesterday_0='?' (DbType = DateTime), @__today_1='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT DISTINCT c.user_id
FROM conversations AS c
WHERE c.updated_at >= @__yesterday_0 AND c.updated_at < @__today_1
[13:27:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
[13:27:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
WHERE a.status = 'Published'
[13:27:55 INF] Executed DbCommand (0ms) [Parameters=[@__oneHourAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT c.agent_id AS "AgentId", count(*)::int AS "Count"
FROM conversations AS c
WHERE c.created_at >= @__oneHourAgo_0 AND c.agent_id <> '00000000-0000-0000-0000-000000000000'
GROUP BY c.agent_id
[13:27:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM conversations AS c
[13:27:55 INF] Executed DbCommand (0ms) [Parameters=[@__oneDayAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM conversations AS c
WHERE c.updated_at >= @__oneDayAgo_0
[13:27:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT c.id AS "Id", c.message_count AS "MessageCount"
FROM conversations AS c
[13:27:55 INF] Executed DbCommand (0ms) [Parameters=[@__AddHours_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT c.created_at AS "CreatedAt", c.updated_at AS "UpdatedAt"
FROM conversations AS c
WHERE c.updated_at < @__AddHours_0
[13:27:55 INF] Executed DbCommand (0ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT t."Model", t."SubscriptionId", COALESCE(sum(t."Tokens"), 0)::int AS "TotalTokens"
FROM "TokenUsages" AS t
WHERE t."CreatedAt" >= @__today_0
GROUP BY t."Model", t."SubscriptionId"
[13:27:55 INF] Executed DbCommand (0ms) [Parameters=[@__subscriptionIds_0='?' (DbType = Object)], CommandType='Text', CommandTimeout='30']
SELECT s."Id", s."CustomerUserId", s0."Tier"
FROM "Subscriptions" AS s
INNER JOIN "SubscriptionPlans" AS s0 ON s."PlanId" = s0."Id"
WHERE s."Id" = ANY (@__subscriptionIds_0) AND s."Status" = 'Active'
[13:27:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT o."Type" AS "OrderType", (
    SELECT COALESCE(sum(o0."FinalAmount"), 0.0)
    FROM "Orders" AS o0
    WHERE o0."Status" = 1 AND o0."UpdatedAt" >= @__thirtyDaysAgo_0 AND o."Type" = o0."Type") AS "TotalAmount"
FROM "Orders" AS o
WHERE o."Status" = 1 AND o."UpdatedAt" >= @__thirtyDaysAgo_0
GROUP BY o."Type"
[13:27:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM "Orders" AS o
WHERE o."CreatedAt" >= @__thirtyDaysAgo_0
[13:27:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM "Orders" AS o
WHERE o."CreatedAt" >= @__thirtyDaysAgo_0 AND o."Status" = 1
[13:27:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT o."CustomerUserId"
    FROM "Orders" AS o
    WHERE o."Status" = 1 AND o."UpdatedAt" >= @__thirtyDaysAgo_0
) AS o0
[13:27:55 INF] Business metrics collected successfully
[13:28:35 INF] Request starting HTTP/2 GET https://localhost:7025/swagger/v1/swagger.json - null null
[13:28:35 INF] HTTP GET /swagger/v1/swagger.json responded 200 in 227.9420 ms
[13:28:35 INF] Request finished HTTP/2 GET https://localhost:7025/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 228.103ms
[13:28:39 INF] Executed DbCommand (3ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT a."Id", a."AdminUserId", a."City", a."Country", a."CreatedAt", a."DeviceId", a."DeviceName", a."ExpiresAt", a."IpAddress", a."IsActive", a."IsAnomalousLocation", a."LastActivityAt", a."Latitude", a."Longitude", a."MfaVerified", a."MfaVerifiedAt", a."RefreshToken", a."RefreshTokenExpiresAt", a."Region", a."RequiresMfa", a."SessionToken", a."UpdatedAt", a."UpdatedBy", a."UserAgent"
FROM "AdminUserSessions" AS a
WHERE a."IsActive"
[13:28:39 INF] 清理超时的管理员会话: SessionId=8a61aa38-1245-49cc-996b-fba141bcbd72, AdminUserId=c222**************-2222-************, LastActivity=07/15/2025 23:49:14
[13:28:39 INF] Executed DbCommand (2ms) [Parameters=[@p23='?' (DbType = Guid), @p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?' (DbType = DateTime), @p4='?', @p5='?', @p6='?' (DbType = DateTime), @p7='?', @p8='?' (DbType = Boolean), @p9='?' (DbType = Boolean), @p10='?' (DbType = DateTime), @p11='?' (DbType = Double), @p12='?' (DbType = Double), @p13='?' (DbType = Boolean), @p14='?' (DbType = DateTime), @p15='?', @p16='?' (DbType = DateTime), @p17='?', @p18='?' (DbType = Boolean), @p19='?', @p20='?' (DbType = DateTime), @p21='?', @p22='?'], CommandType='Text', CommandTimeout='30']
UPDATE "AdminUserSessions" SET "AdminUserId" = @p0, "City" = @p1, "Country" = @p2, "CreatedAt" = @p3, "DeviceId" = @p4, "DeviceName" = @p5, "ExpiresAt" = @p6, "IpAddress" = @p7, "IsActive" = @p8, "IsAnomalousLocation" = @p9, "LastActivityAt" = @p10, "Latitude" = @p11, "Longitude" = @p12, "MfaVerified" = @p13, "MfaVerifiedAt" = @p14, "RefreshToken" = @p15, "RefreshTokenExpiresAt" = @p16, "Region" = @p17, "RequiresMfa" = @p18, "SessionToken" = @p19, "UpdatedAt" = @p20, "UpdatedBy" = @p21, "UserAgent" = @p22
WHERE "Id" = @p23;
[13:28:39 INF] 会话清理完成: 检查会话数=2, 已过期=0, 因超时清理=1
[13:28:49 INF] Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?' (DbType = Object), @p4='?', @p5='?', @p6='?', @p7='?', @p8='?' (DbType = DateTime), @p9='?', @p10='?', @p11='?', @p12='?', @p13='?', @p14='?' (DbType = Int64), @p15='?', @p16='?', @p17='?', @p18='?' (DbType = Boolean), @p19='?' (DbType = Boolean), @p20='?', @p21='?', @p22='?' (DbType = Object), @p23='?' (DbType = Object), @p24='?', @p25='?' (DbType = Object), @p26='?', @p27='?' (DbType = Int32), @p28='?', @p29='?' (DbType = DateTime), @p30='?', @p31='?', @p32='?' (DbType = Guid), @p33='?', @p34='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "AuditLogs" ("Id", "Action", "ActionName", "AdditionalData", "ChangedProperties", "ClientInfo", "ControllerName", "CorrelationId", "CreatedAt", "Description", "EntityId", "EntityType", "ErrorMessage", "ExceptionStack", "ExecutionDuration", "GeoLocation", "HttpMethod", "IpAddress", "IsSensitive", "IsSuccess", "JwtId", "Module", "NewValues", "OldValues", "RequestId", "RequestParameters", "RequestUrl", "ResponseStatusCode", "RiskLevel", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId", "UserName", "UserType")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34);
[13:28:49 INF] Flushed 1 audit logs to database
[13:28:49 INF] Performance metrics collected: CPU=0.2%, Memory=327MB, Threads=81
[13:28:49 INF] Request starting HTTP/2 POST https://localhost:7025/api/customer/auth/login - application/json 107
[13:28:49 INF] Executing endpoint 'WhimLabAI.WebApi.Controllers.CustomerAuth.CustomerAuthController.Login (WhimLabAI.WebApi)'
[13:28:49 INF] Route matched with {action = "Login", controller = "CustomerAuth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(WhimLabAI.Shared.Dtos.Auth.CustomerLoginDto, System.Threading.CancellationToken) on controller WhimLabAI.WebApi.Controllers.CustomerAuth.CustomerAuthController (WhimLabAI.WebApi).
[13:28:49 INF] Executed DbCommand (2ms) [Parameters=[@__username_0='?'], CommandType='Text', CommandTimeout='30']
SELECT c.id, c."Avatar", c."Bio", c."Birthday", c.created_at, c."Gender", c."Industry", c.is_active, c.is_banned, c.is_email_verified, c.is_phone_verified, c.last_login_at, c.last_login_ip, c."Nickname", c."Position", c.refresh_token, c."Region", c."RegisterIp", c.two_factor_enabled, c.updated_at, c."UpdatedBy", c.username, c.locked_until, c.login_failed_count, c.refresh_token_expiry, c.two_factor_secret, c.email, n."CustomerUserId", n.notification_email, n.notification_features, n.notification_newsletter, n.notification_promotion, n.notification_quota, n.notification_security, n.notification_sms, n.notification_system, c.password_hash, c.country_code, c.phone_number, c0.id, c0.address, c0.city, c0.company, c0.country, c0.created_at, c0.custom_fields, c0.department, c0.github, c0.job_title, c0.linkedin, c0.postal_code, c0.province, c0.twitter, c0.updated_at, c0."UpdatedBy", c0.user_id, c0.website, c0.work_email, c0.work_phone
FROM customer_users AS c
LEFT JOIN customer_profiles AS c0 ON c.id = c0.user_id
LEFT JOIN "NotificationSettings" AS n ON c.id = n."CustomerUserId"
WHERE c.username = @__username_0
LIMIT 1
[13:28:49 INF] Executed DbCommand (3ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?' (DbType = DateTime), @p3='?', @p4='?', @p5='?' (DbType = Boolean), @p6='?', @p7='?' (DbType = DateTime), @p8='?', @p9='?' (DbType = Int32), @p10='?' (DbType = DateTime), @p11='?', @p12='?' (DbType = DateTime), @p13='?', @p14='?', @p15='?' (DbType = Guid), @p18='?' (DbType = Guid), @p16='?' (DbType = DateTime), @p17='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
INSERT INTO "LoginHistories" ("Id", "Browser", "CreatedAt", "Device", "FailureReason", "IsSuccess", "Location", "LoginAt", "LoginIp", "LoginMethod", "LogoutAt", "SessionToken", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15);
UPDATE customer_users SET last_login_at = @p16, updated_at = @p17
WHERE id = @p18;
[13:28:49 INF] 用户登录成功: demo_user
[13:28:49 INF] Executed DbCommand (1ms) [Parameters=[@p3='?' (DbType = Guid), @p0='?', @p1='?' (DbType = DateTime), @p2='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE customer_users SET refresh_token = @p0, updated_at = @p1, refresh_token_expiry = @p2
WHERE id = @p3;
[13:28:49 INF] Executing OkObjectResult, writing value of type 'WhimLabAI.Shared.Dtos.ApiResponse`1[[WhimLabAI.Shared.Dtos.Auth.AuthResponseDto, WhimLabAI.Shared, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
[13:28:49 INF] Executed action WhimLabAI.WebApi.Controllers.CustomerAuth.CustomerAuthController.Login (WhimLabAI.WebApi) in 96.2955ms
[13:28:49 INF] Executed endpoint 'WhimLabAI.WebApi.Controllers.CustomerAuth.CustomerAuthController.Login (WhimLabAI.WebApi)'
[13:28:49 INF] HTTP POST /api/customer/auth/login responded 200 in 98.3828 ms
[13:28:49 INF] Request finished HTTP/2 POST https://localhost:7025/api/customer/auth/login - 200 null application/json; charset=utf-8 98.5551ms
[13:28:49 ERR] Unexpected exception in TimingPipeFlusher.FlushAsync.
System.IO.IOException: The encryption operation failed, see inner exception.
 ---> System.ComponentModel.Win32Exception (14): Bad address
   --- End of inner exception stack trace ---
   at System.Net.Security.SslStream.WriteSingleChunk[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.Net.Security.SslStream.WriteAsync(ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.PipeWriterHelpers.ConcurrentPipeWriter.FlushAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.PipeWriterHelpers.TimingPipeFlusher.FlushAsync(MinDataRate minRate, Int64 count, IHttpOutputAborter outputAborter, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2FrameWriter.WriteGoAwayAsync(Int32 lastStreamId, Http2ErrorCode errorCode)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2Connection.UpdateConnectionState()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2Connection.ProcessRequestsAsync[TContext](IHttpApplication`1 application)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecuteFromThreadPool(Thread threadPoolThread)
   at System.Threading.ThreadPoolWorkQueue.Dispatch()
   at System.Threading.PortableThreadPool.WorkerThread.WorkerThreadStart()
   at System.Threading.Thread.StartCallback()
--- End of stack trace from previous location ---
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.PipeWriterHelpers.ConcurrentPipeWriter.FlushAsyncAwaited(ValueTask`1 flushTask, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.PipeWriterHelpers.TimingPipeFlusher.TimeFlushAsyncAwaited(ValueTask`1 pipeFlushTask, MinDataRate minRate, IHttpOutputAborter outputAborter, CancellationToken cancellationToken)
[13:28:49 ERR] Unhandled exception while processing 0HNE454CJSCTO.
System.IO.IOException: The encryption operation failed, see inner exception.
 ---> System.ComponentModel.Win32Exception (14): Bad address
   --- End of inner exception stack trace ---
   at System.Net.Security.SslStream.WriteSingleChunk[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.Net.Security.SslStream.WriteAsync(ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.DuplexPipeStreamAdapter`1.DisposeAsync()
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.DuplexPipeStreamAdapter`1.DisposeAsync()
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
   at System.Threading.Tasks.AwaitTaskContinuation.RunOrScheduleAction(IAsyncStateMachineBox box, Boolean allowInlining)
   at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetExistingTaskResult(Task`1 task, TResult result)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetResult()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.HttpConnection.ProcessRequestsAsync[TContext](IHttpApplication`1 httpApplication)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
   at System.Threading.Tasks.AwaitTaskContinuation.RunOrScheduleAction(IAsyncStateMachineBox box, Boolean allowInlining)
   at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetExistingTaskResult(Task`1 task, TResult result)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetResult()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2Connection.ProcessRequestsAsync[TContext](IHttpApplication`1 application)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
   at System.Threading.Tasks.AwaitTaskContinuation.RunOrScheduleAction(IAsyncStateMachineBox box, Boolean allowInlining)
   at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
   at System.Threading.Tasks.Task`1.TrySetResult(TResult result)
   at System.Threading.Tasks.UnwrapPromise`1.TrySetFromTask(Task task, Boolean lookForOce)
   at System.Threading.Tasks.UnwrapPromise`1.Invoke(Task completingTask)
   at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetExistingTaskResult(Task`1 task, TResult result)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetResult()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2FrameWriter.WriteToOutputPipe()
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
   at System.Threading.ThreadPoolWorkQueue.Dispatch()
   at System.Threading.PortableThreadPool.WorkerThread.WorkerThreadStart()
   at System.Threading.Thread.StartCallback()
--- End of stack trace from previous location ---
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.DuplexPipeStreamAdapter`1.DisposeAsync()
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.KestrelConnection`1.ExecuteAsync()
[13:28:55 INF] Executed DbCommand (1ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT c.user_id
    FROM conversations AS c
    WHERE c.updated_at >= @__today_0
) AS c0
[13:28:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT c.user_id
    FROM conversations AS c
    WHERE c.updated_at >= @__thirtyDaysAgo_0
) AS c0
[13:28:55 INF] Executed DbCommand (0ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM customer_users AS c
WHERE c.created_at >= @__today_0
[13:28:55 INF] Executed DbCommand (0ms) [Parameters=[@__yesterday_0='?' (DbType = DateTime), @__today_1='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT DISTINCT c.user_id
FROM conversations AS c
WHERE c.updated_at >= @__yesterday_0 AND c.updated_at < @__today_1
[13:28:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
[13:28:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
WHERE a.status = 'Published'
[13:28:55 INF] Executed DbCommand (0ms) [Parameters=[@__oneHourAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT c.agent_id AS "AgentId", count(*)::int AS "Count"
FROM conversations AS c
WHERE c.created_at >= @__oneHourAgo_0 AND c.agent_id <> '00000000-0000-0000-0000-000000000000'
GROUP BY c.agent_id
[13:28:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM conversations AS c
[13:28:55 INF] Executed DbCommand (0ms) [Parameters=[@__oneDayAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM conversations AS c
WHERE c.updated_at >= @__oneDayAgo_0
[13:28:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT c.id AS "Id", c.message_count AS "MessageCount"
FROM conversations AS c
[13:28:55 INF] Executed DbCommand (0ms) [Parameters=[@__AddHours_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT c.created_at AS "CreatedAt", c.updated_at AS "UpdatedAt"
FROM conversations AS c
WHERE c.updated_at < @__AddHours_0
[13:28:55 INF] Executed DbCommand (0ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT t."Model", t."SubscriptionId", COALESCE(sum(t."Tokens"), 0)::int AS "TotalTokens"
FROM "TokenUsages" AS t
WHERE t."CreatedAt" >= @__today_0
GROUP BY t."Model", t."SubscriptionId"
[13:28:55 INF] Executed DbCommand (0ms) [Parameters=[@__subscriptionIds_0='?' (DbType = Object)], CommandType='Text', CommandTimeout='30']
SELECT s."Id", s."CustomerUserId", s0."Tier"
FROM "Subscriptions" AS s
INNER JOIN "SubscriptionPlans" AS s0 ON s."PlanId" = s0."Id"
WHERE s."Id" = ANY (@__subscriptionIds_0) AND s."Status" = 'Active'
[13:28:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT o."Type" AS "OrderType", (
    SELECT COALESCE(sum(o0."FinalAmount"), 0.0)
    FROM "Orders" AS o0
    WHERE o0."Status" = 1 AND o0."UpdatedAt" >= @__thirtyDaysAgo_0 AND o."Type" = o0."Type") AS "TotalAmount"
FROM "Orders" AS o
WHERE o."Status" = 1 AND o."UpdatedAt" >= @__thirtyDaysAgo_0
GROUP BY o."Type"
[13:28:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM "Orders" AS o
WHERE o."CreatedAt" >= @__thirtyDaysAgo_0
[13:28:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM "Orders" AS o
WHERE o."CreatedAt" >= @__thirtyDaysAgo_0 AND o."Status" = 1
[13:28:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT o."CustomerUserId"
    FROM "Orders" AS o
    WHERE o."Status" = 1 AND o."UpdatedAt" >= @__thirtyDaysAgo_0
) AS o0
[13:28:55 INF] Business metrics collected successfully
[13:28:59 INF] Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?' (DbType = Object), @p4='?', @p5='?', @p6='?', @p7='?', @p8='?' (DbType = DateTime), @p9='?', @p10='?', @p11='?', @p12='?', @p13='?', @p14='?' (DbType = Int64), @p15='?', @p16='?', @p17='?', @p18='?' (DbType = Boolean), @p19='?' (DbType = Boolean), @p20='?', @p21='?', @p22='?' (DbType = Object), @p23='?' (DbType = Object), @p24='?', @p25='?' (DbType = Object), @p26='?', @p27='?' (DbType = Int32), @p28='?', @p29='?' (DbType = DateTime), @p30='?', @p31='?', @p32='?' (DbType = Guid), @p33='?', @p34='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "AuditLogs" ("Id", "Action", "ActionName", "AdditionalData", "ChangedProperties", "ClientInfo", "ControllerName", "CorrelationId", "CreatedAt", "Description", "EntityId", "EntityType", "ErrorMessage", "ExceptionStack", "ExecutionDuration", "GeoLocation", "HttpMethod", "IpAddress", "IsSensitive", "IsSuccess", "JwtId", "Module", "NewValues", "OldValues", "RequestId", "RequestParameters", "RequestUrl", "ResponseStatusCode", "RiskLevel", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId", "UserName", "UserType")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34);
[13:28:59 INF] Flushed 1 audit logs to database
[13:29:49 INF] Performance metrics collected: CPU=0.1%, Memory=327MB, Threads=81
[13:29:55 INF] Executed DbCommand (1ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT c.user_id
    FROM conversations AS c
    WHERE c.updated_at >= @__today_0
) AS c0
[13:29:55 INF] Executed DbCommand (1ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT c.user_id
    FROM conversations AS c
    WHERE c.updated_at >= @__thirtyDaysAgo_0
) AS c0
[13:29:55 INF] Executed DbCommand (1ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM customer_users AS c
WHERE c.created_at >= @__today_0
[13:29:55 INF] Executed DbCommand (1ms) [Parameters=[@__yesterday_0='?' (DbType = DateTime), @__today_1='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT DISTINCT c.user_id
FROM conversations AS c
WHERE c.updated_at >= @__yesterday_0 AND c.updated_at < @__today_1
[13:29:55 INF] Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
[13:29:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
WHERE a.status = 'Published'
[13:29:55 INF] Executed DbCommand (0ms) [Parameters=[@__oneHourAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT c.agent_id AS "AgentId", count(*)::int AS "Count"
FROM conversations AS c
WHERE c.created_at >= @__oneHourAgo_0 AND c.agent_id <> '00000000-0000-0000-0000-000000000000'
GROUP BY c.agent_id
[13:29:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM conversations AS c
[13:29:55 INF] Executed DbCommand (0ms) [Parameters=[@__oneDayAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM conversations AS c
WHERE c.updated_at >= @__oneDayAgo_0
[13:29:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT c.id AS "Id", c.message_count AS "MessageCount"
FROM conversations AS c
[13:29:55 INF] Executed DbCommand (0ms) [Parameters=[@__AddHours_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT c.created_at AS "CreatedAt", c.updated_at AS "UpdatedAt"
FROM conversations AS c
WHERE c.updated_at < @__AddHours_0
[13:29:55 INF] Executed DbCommand (0ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT t."Model", t."SubscriptionId", COALESCE(sum(t."Tokens"), 0)::int AS "TotalTokens"
FROM "TokenUsages" AS t
WHERE t."CreatedAt" >= @__today_0
GROUP BY t."Model", t."SubscriptionId"
[13:29:55 INF] Executed DbCommand (1ms) [Parameters=[@__subscriptionIds_0='?' (DbType = Object)], CommandType='Text', CommandTimeout='30']
SELECT s."Id", s."CustomerUserId", s0."Tier"
FROM "Subscriptions" AS s
INNER JOIN "SubscriptionPlans" AS s0 ON s."PlanId" = s0."Id"
WHERE s."Id" = ANY (@__subscriptionIds_0) AND s."Status" = 'Active'
[13:29:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT o."Type" AS "OrderType", (
    SELECT COALESCE(sum(o0."FinalAmount"), 0.0)
    FROM "Orders" AS o0
    WHERE o0."Status" = 1 AND o0."UpdatedAt" >= @__thirtyDaysAgo_0 AND o."Type" = o0."Type") AS "TotalAmount"
FROM "Orders" AS o
WHERE o."Status" = 1 AND o."UpdatedAt" >= @__thirtyDaysAgo_0
GROUP BY o."Type"
[13:29:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM "Orders" AS o
WHERE o."CreatedAt" >= @__thirtyDaysAgo_0
[13:29:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM "Orders" AS o
WHERE o."CreatedAt" >= @__thirtyDaysAgo_0 AND o."Status" = 1
[13:29:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT o."CustomerUserId"
    FROM "Orders" AS o
    WHERE o."Status" = 1 AND o."UpdatedAt" >= @__thirtyDaysAgo_0
) AS o0
[13:29:55 INF] Business metrics collected successfully
[13:30:06 INF] Request starting HTTP/2 GET https://localhost:7025/api/v1/admin/dashboard/stats - null null
[13:30:06 INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (DbType = Guid)], CommandType='Text', CommandTimeout='30']
SELECT a."Id", a."AdminUserId", a."City", a."Country", a."CreatedAt", a."DeviceId", a."DeviceName", a."ExpiresAt", a."IpAddress", a."IsActive", a."IsAnomalousLocation", a."LastActivityAt", a."Latitude", a."Longitude", a."MfaVerified", a."MfaVerifiedAt", a."RefreshToken", a."RefreshTokenExpiresAt", a."Region", a."RequiresMfa", a."SessionToken", a."UpdatedAt", a."UpdatedBy", a."UserAgent"
FROM "AdminUserSessions" AS a
WHERE a."Id" = @__p_0
LIMIT 1
[13:30:06 INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (DbType = Guid)], CommandType='Text', CommandTimeout='30']
SELECT a."Id", a."AdminUserId", a."City", a."Country", a."CreatedAt", a."DeviceId", a."DeviceName", a."ExpiresAt", a."IpAddress", a."IsActive", a."IsAnomalousLocation", a."LastActivityAt", a."Latitude", a."Longitude", a."MfaVerified", a."MfaVerifiedAt", a."RefreshToken", a."RefreshTokenExpiresAt", a."Region", a."RequiresMfa", a."SessionToken", a."UpdatedAt", a."UpdatedBy", a."UserAgent"
FROM "AdminUserSessions" AS a
WHERE a."Id" = @__p_0
LIMIT 1
[13:30:06 INF] Executed DbCommand (1ms) [Parameters=[@p23='?' (DbType = Guid), @p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?' (DbType = DateTime), @p4='?', @p5='?', @p6='?' (DbType = DateTime), @p7='?', @p8='?' (DbType = Boolean), @p9='?' (DbType = Boolean), @p10='?' (DbType = DateTime), @p11='?' (DbType = Double), @p12='?' (DbType = Double), @p13='?' (DbType = Boolean), @p14='?' (DbType = DateTime), @p15='?', @p16='?' (DbType = DateTime), @p17='?', @p18='?' (DbType = Boolean), @p19='?', @p20='?' (DbType = DateTime), @p21='?', @p22='?'], CommandType='Text', CommandTimeout='30']
UPDATE "AdminUserSessions" SET "AdminUserId" = @p0, "City" = @p1, "Country" = @p2, "CreatedAt" = @p3, "DeviceId" = @p4, "DeviceName" = @p5, "ExpiresAt" = @p6, "IpAddress" = @p7, "IsActive" = @p8, "IsAnomalousLocation" = @p9, "LastActivityAt" = @p10, "Latitude" = @p11, "Longitude" = @p12, "MfaVerified" = @p13, "MfaVerifiedAt" = @p14, "RefreshToken" = @p15, "RefreshTokenExpiresAt" = @p16, "Region" = @p17, "RequiresMfa" = @p18, "SessionToken" = @p19, "UpdatedAt" = @p20, "UpdatedBy" = @p21, "UserAgent" = @p22
WHERE "Id" = @p23;
[13:30:06 INF] HTTP GET /api/v1/admin/dashboard/stats responded 404 in 28.0787 ms
[13:30:06 INF] Request finished HTTP/2 GET https://localhost:7025/api/v1/admin/dashboard/stats - 404 0 null 28.2469ms
[13:30:06 INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7025/api/v1/admin/dashboard/stats, Response status code: 404
[13:30:06 ERR] Unexpected exception in TimingPipeFlusher.FlushAsync.
System.IO.IOException: The encryption operation failed, see inner exception.
 ---> System.ComponentModel.Win32Exception (14): Bad address
   --- End of inner exception stack trace ---
   at System.Net.Security.SslStream.WriteSingleChunk[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.Net.Security.SslStream.WriteAsync(ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.PipeWriterHelpers.ConcurrentPipeWriter.FlushAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.PipeWriterHelpers.TimingPipeFlusher.FlushAsync(MinDataRate minRate, Int64 count, IHttpOutputAborter outputAborter, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2FrameWriter.WriteGoAwayAsync(Int32 lastStreamId, Http2ErrorCode errorCode)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2Connection.UpdateConnectionState()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2Connection.ProcessRequestsAsync[TContext](IHttpApplication`1 application)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecuteFromThreadPool(Thread threadPoolThread)
   at System.Threading.ThreadPoolWorkQueue.Dispatch()
   at System.Threading.PortableThreadPool.WorkerThread.WorkerThreadStart()
   at System.Threading.Thread.StartCallback()
--- End of stack trace from previous location ---
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.PipeWriterHelpers.ConcurrentPipeWriter.FlushAsyncAwaited(ValueTask`1 flushTask, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.PipeWriterHelpers.TimingPipeFlusher.TimeFlushAsyncAwaited(ValueTask`1 pipeFlushTask, MinDataRate minRate, IHttpOutputAborter outputAborter, CancellationToken cancellationToken)
[13:30:06 ERR] Unhandled exception while processing 0HNE454CJSCTP.
System.IO.IOException: The encryption operation failed, see inner exception.
 ---> System.ComponentModel.Win32Exception (14): Bad address
   --- End of inner exception stack trace ---
   at System.Net.Security.SslStream.WriteSingleChunk[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.Net.Security.SslStream.WriteAsync(ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.DuplexPipeStreamAdapter`1.DisposeAsync()
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.DuplexPipeStreamAdapter`1.DisposeAsync()
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
   at System.Threading.Tasks.AwaitTaskContinuation.RunOrScheduleAction(IAsyncStateMachineBox box, Boolean allowInlining)
   at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetExistingTaskResult(Task`1 task, TResult result)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetResult()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.HttpConnection.ProcessRequestsAsync[TContext](IHttpApplication`1 httpApplication)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
   at System.Threading.Tasks.AwaitTaskContinuation.RunOrScheduleAction(IAsyncStateMachineBox box, Boolean allowInlining)
   at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetExistingTaskResult(Task`1 task, TResult result)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetResult()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2Connection.ProcessRequestsAsync[TContext](IHttpApplication`1 application)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
   at System.Threading.Tasks.AwaitTaskContinuation.RunOrScheduleAction(IAsyncStateMachineBox box, Boolean allowInlining)
   at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
   at System.Threading.Tasks.Task`1.TrySetResult(TResult result)
   at System.Threading.Tasks.UnwrapPromise`1.TrySetFromTask(Task task, Boolean lookForOce)
   at System.Threading.Tasks.UnwrapPromise`1.Invoke(Task completingTask)
   at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetExistingTaskResult(Task`1 task, TResult result)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetResult()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2FrameWriter.WriteToOutputPipe()
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
   at System.Threading.ThreadPoolWorkQueue.Dispatch()
   at System.Threading.PortableThreadPool.WorkerThread.WorkerThreadStart()
   at System.Threading.Thread.StartCallback()
--- End of stack trace from previous location ---
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.DuplexPipeStreamAdapter`1.DisposeAsync()
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.KestrelConnection`1.ExecuteAsync()
[13:30:09 INF] Executed DbCommand (442ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?' (DbType = Object), @p4='?', @p5='?', @p6='?', @p7='?', @p8='?' (DbType = DateTime), @p9='?', @p10='?', @p11='?', @p12='?', @p13='?', @p14='?' (DbType = Int64), @p15='?', @p16='?', @p17='?', @p18='?' (DbType = Boolean), @p19='?' (DbType = Boolean), @p20='?', @p21='?', @p22='?' (DbType = Object), @p23='?' (DbType = Object), @p24='?', @p25='?' (DbType = Object), @p26='?', @p27='?' (DbType = Int32), @p28='?', @p29='?' (DbType = DateTime), @p30='?', @p31='?', @p32='?' (DbType = Guid), @p33='?', @p34='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "AuditLogs" ("Id", "Action", "ActionName", "AdditionalData", "ChangedProperties", "ClientInfo", "ControllerName", "CorrelationId", "CreatedAt", "Description", "EntityId", "EntityType", "ErrorMessage", "ExceptionStack", "ExecutionDuration", "GeoLocation", "HttpMethod", "IpAddress", "IsSensitive", "IsSuccess", "JwtId", "Module", "NewValues", "OldValues", "RequestId", "RequestParameters", "RequestUrl", "ResponseStatusCode", "RiskLevel", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId", "UserName", "UserType")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34);
[13:30:09 INF] Flushed 1 audit logs to database
[13:30:10 INF] Starting payment timeout check job
[13:30:10 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT o."Id", o."CouponCode", o."CouponId", o."CreatedAt", o."CustomerUserId", o."DeletedAt", o."ExpireAt", o."IsDeleted", o."Metadata", o."OrderNo", o."PaidAt", o."PaymentMethod", o."ProductId", o."ProductName", o."RefundedAt", o."Remark", o."Status", o."Type", o."UpdatedAt", o."UpdatedBy", o."Amount", o."AmountCurrency", o."DiscountAmount", o."DiscountCurrency", o."FinalAmount", o."FinalCurrency", o."PayableAmount", o."PayableCurrency"
FROM "Orders" AS o
WHERE o."Status" = 0 AND o."ExpireAt" < now()
[13:30:10 INF] Executed DbCommand (0ms) [Parameters=[@__expiryTime_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT p."Id", p."CompletedAt", p."CreatedAt", p."ExpireAt", p."FailReason", p."FailedAt", p."OrderId", p."PayerAccount", p."PayerName", p."PaymentMethod", p."PaymentNo", p."RawData", p."Status", p."TransactionId", p."UpdatedAt", p."UpdatedBy", p.amount, p.currency
FROM "PaymentTransactions" AS p
WHERE p."Status" = 0 AND p."CreatedAt" < @__expiryTime_0
[13:30:10 INF] Payment timeout check job completed. Cancelled orders: 0, Errors: 0
[13:30:19 INF] Request starting HTTP/2 GET https://localhost:7025/swagger/v1/swagger.json - null null
[13:30:19 INF] HTTP GET /swagger/v1/swagger.json responded 200 in 104.5373 ms
[13:30:19 INF] Request finished HTTP/2 GET https://localhost:7025/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 104.6647ms
[13:30:19 ERR] Unexpected exception in TimingPipeFlusher.FlushAsync.
System.IO.IOException: The encryption operation failed, see inner exception.
 ---> System.ComponentModel.Win32Exception (14): Bad address
   --- End of inner exception stack trace ---
   at System.Net.Security.SslStream.WriteSingleChunk[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.Net.Security.SslStream.WriteAsync(ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.PipeWriterHelpers.ConcurrentPipeWriter.FlushAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.PipeWriterHelpers.TimingPipeFlusher.FlushAsync(MinDataRate minRate, Int64 count, IHttpOutputAborter outputAborter, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2FrameWriter.WriteGoAwayAsync(Int32 lastStreamId, Http2ErrorCode errorCode)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2Connection.UpdateConnectionState()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2Connection.ProcessRequestsAsync[TContext](IHttpApplication`1 application)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecuteFromThreadPool(Thread threadPoolThread)
   at System.Threading.ThreadPoolWorkQueue.Dispatch()
   at System.Threading.PortableThreadPool.WorkerThread.WorkerThreadStart()
   at System.Threading.Thread.StartCallback()
--- End of stack trace from previous location ---
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.PipeWriterHelpers.ConcurrentPipeWriter.FlushAsyncAwaited(ValueTask`1 flushTask, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.PipeWriterHelpers.TimingPipeFlusher.TimeFlushAsyncAwaited(ValueTask`1 pipeFlushTask, MinDataRate minRate, IHttpOutputAborter outputAborter, CancellationToken cancellationToken)
[13:30:19 ERR] Unhandled exception while processing 0HNE454CJSCTQ.
System.IO.IOException: The encryption operation failed, see inner exception.
 ---> System.ComponentModel.Win32Exception (14): Bad address
   --- End of inner exception stack trace ---
   at System.Net.Security.SslStream.WriteSingleChunk[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.Net.Security.SslStream.WriteAsync(ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.DuplexPipeStreamAdapter`1.DisposeAsync()
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.DuplexPipeStreamAdapter`1.DisposeAsync()
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
   at System.Threading.Tasks.AwaitTaskContinuation.RunOrScheduleAction(IAsyncStateMachineBox box, Boolean allowInlining)
   at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetExistingTaskResult(Task`1 task, TResult result)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetResult()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.HttpConnection.ProcessRequestsAsync[TContext](IHttpApplication`1 httpApplication)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
   at System.Threading.Tasks.AwaitTaskContinuation.RunOrScheduleAction(IAsyncStateMachineBox box, Boolean allowInlining)
   at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetExistingTaskResult(Task`1 task, TResult result)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetResult()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2Connection.ProcessRequestsAsync[TContext](IHttpApplication`1 application)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
   at System.Threading.Tasks.AwaitTaskContinuation.RunOrScheduleAction(IAsyncStateMachineBox box, Boolean allowInlining)
   at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
   at System.Threading.Tasks.Task`1.TrySetResult(TResult result)
   at System.Threading.Tasks.UnwrapPromise`1.TrySetFromTask(Task task, Boolean lookForOce)
   at System.Threading.Tasks.UnwrapPromise`1.Invoke(Task completingTask)
   at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetExistingTaskResult(Task`1 task, TResult result)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetResult()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2FrameWriter.WriteToOutputPipe()
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
   at System.Threading.ThreadPoolWorkQueue.Dispatch()
   at System.Threading.PortableThreadPool.WorkerThread.WorkerThreadStart()
   at System.Threading.Thread.StartCallback()
--- End of stack trace from previous location ---
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.DuplexPipeStreamAdapter`1.DisposeAsync()
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.KestrelConnection`1.ExecuteAsync()
[13:30:37 INF] Request starting HTTP/2 GET https://localhost:7025/api/admin/dashboard/stats - null null
[13:30:37 INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (DbType = Guid)], CommandType='Text', CommandTimeout='30']
SELECT a."Id", a."AdminUserId", a."City", a."Country", a."CreatedAt", a."DeviceId", a."DeviceName", a."ExpiresAt", a."IpAddress", a."IsActive", a."IsAnomalousLocation", a."LastActivityAt", a."Latitude", a."Longitude", a."MfaVerified", a."MfaVerifiedAt", a."RefreshToken", a."RefreshTokenExpiresAt", a."Region", a."RequiresMfa", a."SessionToken", a."UpdatedAt", a."UpdatedBy", a."UserAgent"
FROM "AdminUserSessions" AS a
WHERE a."Id" = @__p_0
LIMIT 1
[13:30:37 INF] Executing endpoint 'WhimLabAI.WebApi.Controllers.Admin.AdminDashboardController.GetDashboardStats (WhimLabAI.WebApi)'
[13:30:37 INF] Route matched with {action = "GetDashboardStats", controller = "AdminDashboard"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetDashboardStats() on controller WhimLabAI.WebApi.Controllers.Admin.AdminDashboardController (WhimLabAI.WebApi).
[13:30:37 INF] Executing OkObjectResult, writing value of type 'WhimLabAI.Shared.Dtos.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
[13:30:37 INF] Executed action WhimLabAI.WebApi.Controllers.Admin.AdminDashboardController.GetDashboardStats (WhimLabAI.WebApi) in 1.3589ms
[13:30:37 INF] Executed endpoint 'WhimLabAI.WebApi.Controllers.Admin.AdminDashboardController.GetDashboardStats (WhimLabAI.WebApi)'
[13:30:37 INF] HTTP GET /api/admin/dashboard/stats responded 200 in 6.9960 ms
[13:30:37 INF] Request finished HTTP/2 GET https://localhost:7025/api/admin/dashboard/stats - 200 null application/json; charset=utf-8 7.129ms
[13:30:37 ERR] Unexpected exception in TimingPipeFlusher.FlushAsync.
System.IO.IOException: The encryption operation failed, see inner exception.
 ---> System.ComponentModel.Win32Exception (14): Bad address
   --- End of inner exception stack trace ---
   at System.Net.Security.SslStream.WriteSingleChunk[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.Net.Security.SslStream.WriteAsync(ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.PipeWriterHelpers.ConcurrentPipeWriter.FlushAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.PipeWriterHelpers.TimingPipeFlusher.FlushAsync(MinDataRate minRate, Int64 count, IHttpOutputAborter outputAborter, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2FrameWriter.WriteGoAwayAsync(Int32 lastStreamId, Http2ErrorCode errorCode)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2Connection.UpdateConnectionState()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2Connection.ProcessRequestsAsync[TContext](IHttpApplication`1 application)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecuteFromThreadPool(Thread threadPoolThread)
   at System.Threading.ThreadPoolWorkQueue.Dispatch()
   at System.Threading.PortableThreadPool.WorkerThread.WorkerThreadStart()
   at System.Threading.Thread.StartCallback()
--- End of stack trace from previous location ---
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.PipeWriterHelpers.ConcurrentPipeWriter.FlushAsyncAwaited(ValueTask`1 flushTask, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.PipeWriterHelpers.TimingPipeFlusher.TimeFlushAsyncAwaited(ValueTask`1 pipeFlushTask, MinDataRate minRate, IHttpOutputAborter outputAborter, CancellationToken cancellationToken)
[13:30:37 ERR] Unhandled exception while processing 0HNE454CJSCTR.
System.IO.IOException: The encryption operation failed, see inner exception.
 ---> System.ComponentModel.Win32Exception (14): Bad address
   --- End of inner exception stack trace ---
   at System.Net.Security.SslStream.WriteSingleChunk[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.Net.Security.SslStream.WriteAsync(ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.DuplexPipeStreamAdapter`1.DisposeAsync()
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.DuplexPipeStreamAdapter`1.DisposeAsync()
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
   at System.Threading.Tasks.AwaitTaskContinuation.RunOrScheduleAction(IAsyncStateMachineBox box, Boolean allowInlining)
   at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetExistingTaskResult(Task`1 task, TResult result)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetResult()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.HttpConnection.ProcessRequestsAsync[TContext](IHttpApplication`1 httpApplication)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
   at System.Threading.Tasks.AwaitTaskContinuation.RunOrScheduleAction(IAsyncStateMachineBox box, Boolean allowInlining)
   at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetExistingTaskResult(Task`1 task, TResult result)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetResult()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2Connection.ProcessRequestsAsync[TContext](IHttpApplication`1 application)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
   at System.Threading.Tasks.AwaitTaskContinuation.RunOrScheduleAction(IAsyncStateMachineBox box, Boolean allowInlining)
   at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
   at System.Threading.Tasks.Task`1.TrySetResult(TResult result)
   at System.Threading.Tasks.UnwrapPromise`1.TrySetFromTask(Task task, Boolean lookForOce)
   at System.Threading.Tasks.UnwrapPromise`1.Invoke(Task completingTask)
   at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetExistingTaskResult(Task`1 task, TResult result)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetResult()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2FrameWriter.WriteToOutputPipe()
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
   at System.Threading.ThreadPoolWorkQueue.Dispatch()
   at System.Threading.PortableThreadPool.WorkerThread.WorkerThreadStart()
   at System.Threading.Thread.StartCallback()
--- End of stack trace from previous location ---
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.DuplexPipeStreamAdapter`1.DisposeAsync()
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.KestrelConnection`1.ExecuteAsync()
[13:30:39 INF] Executed DbCommand (4ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?' (DbType = Object), @p4='?', @p5='?', @p6='?', @p7='?', @p8='?' (DbType = DateTime), @p9='?', @p10='?', @p11='?', @p12='?', @p13='?', @p14='?' (DbType = Int64), @p15='?', @p16='?', @p17='?', @p18='?' (DbType = Boolean), @p19='?' (DbType = Boolean), @p20='?', @p21='?', @p22='?' (DbType = Object), @p23='?' (DbType = Object), @p24='?', @p25='?' (DbType = Object), @p26='?', @p27='?' (DbType = Int32), @p28='?', @p29='?' (DbType = DateTime), @p30='?', @p31='?', @p32='?' (DbType = Guid), @p33='?', @p34='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "AuditLogs" ("Id", "Action", "ActionName", "AdditionalData", "ChangedProperties", "ClientInfo", "ControllerName", "CorrelationId", "CreatedAt", "Description", "EntityId", "EntityType", "ErrorMessage", "ExceptionStack", "ExecutionDuration", "GeoLocation", "HttpMethod", "IpAddress", "IsSensitive", "IsSuccess", "JwtId", "Module", "NewValues", "OldValues", "RequestId", "RequestParameters", "RequestUrl", "ResponseStatusCode", "RiskLevel", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId", "UserName", "UserType")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34);
[13:30:39 INF] Flushed 1 audit logs to database
[13:30:49 INF] Performance metrics collected: CPU=0.1%, Memory=327MB, Threads=81
[13:30:55 INF] Executed DbCommand (0ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT c.user_id
    FROM conversations AS c
    WHERE c.updated_at >= @__today_0
) AS c0
[13:30:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT c.user_id
    FROM conversations AS c
    WHERE c.updated_at >= @__thirtyDaysAgo_0
) AS c0
[13:30:55 INF] Executed DbCommand (0ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM customer_users AS c
WHERE c.created_at >= @__today_0
[13:30:55 INF] Executed DbCommand (0ms) [Parameters=[@__yesterday_0='?' (DbType = DateTime), @__today_1='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT DISTINCT c.user_id
FROM conversations AS c
WHERE c.updated_at >= @__yesterday_0 AND c.updated_at < @__today_1
[13:30:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
[13:30:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
WHERE a.status = 'Published'
[13:30:55 INF] Executed DbCommand (0ms) [Parameters=[@__oneHourAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT c.agent_id AS "AgentId", count(*)::int AS "Count"
FROM conversations AS c
WHERE c.created_at >= @__oneHourAgo_0 AND c.agent_id <> '00000000-0000-0000-0000-000000000000'
GROUP BY c.agent_id
[13:30:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM conversations AS c
[13:30:55 INF] Executed DbCommand (0ms) [Parameters=[@__oneDayAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM conversations AS c
WHERE c.updated_at >= @__oneDayAgo_0
[13:30:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT c.id AS "Id", c.message_count AS "MessageCount"
FROM conversations AS c
[13:30:55 INF] Executed DbCommand (0ms) [Parameters=[@__AddHours_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT c.created_at AS "CreatedAt", c.updated_at AS "UpdatedAt"
FROM conversations AS c
WHERE c.updated_at < @__AddHours_0
[13:30:55 INF] Executed DbCommand (0ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT t."Model", t."SubscriptionId", COALESCE(sum(t."Tokens"), 0)::int AS "TotalTokens"
FROM "TokenUsages" AS t
WHERE t."CreatedAt" >= @__today_0
GROUP BY t."Model", t."SubscriptionId"
[13:30:55 INF] Executed DbCommand (0ms) [Parameters=[@__subscriptionIds_0='?' (DbType = Object)], CommandType='Text', CommandTimeout='30']
SELECT s."Id", s."CustomerUserId", s0."Tier"
FROM "Subscriptions" AS s
INNER JOIN "SubscriptionPlans" AS s0 ON s."PlanId" = s0."Id"
WHERE s."Id" = ANY (@__subscriptionIds_0) AND s."Status" = 'Active'
[13:30:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT o."Type" AS "OrderType", (
    SELECT COALESCE(sum(o0."FinalAmount"), 0.0)
    FROM "Orders" AS o0
    WHERE o0."Status" = 1 AND o0."UpdatedAt" >= @__thirtyDaysAgo_0 AND o."Type" = o0."Type") AS "TotalAmount"
FROM "Orders" AS o
WHERE o."Status" = 1 AND o."UpdatedAt" >= @__thirtyDaysAgo_0
GROUP BY o."Type"
[13:30:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM "Orders" AS o
WHERE o."CreatedAt" >= @__thirtyDaysAgo_0
[13:30:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM "Orders" AS o
WHERE o."CreatedAt" >= @__thirtyDaysAgo_0 AND o."Status" = 1
[13:30:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT o."CustomerUserId"
    FROM "Orders" AS o
    WHERE o."Status" = 1 AND o."UpdatedAt" >= @__thirtyDaysAgo_0
) AS o0
[13:30:55 INF] Business metrics collected successfully
[13:30:57 INF] Request starting HTTP/2 GET https://localhost:7025/api/customer/profile - null null
[13:30:57 INF] Executing endpoint 'WhimLabAI.WebApi.Controllers.CustomerUser.CustomerUserController.GetProfile (WhimLabAI.WebApi)'
[13:30:57 INF] Route matched with {action = "GetProfile", controller = "CustomerUser"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProfile(System.Threading.CancellationToken) on controller WhimLabAI.WebApi.Controllers.CustomerUser.CustomerUserController (WhimLabAI.WebApi).
[13:30:57 INF] Executed DbCommand (2ms) [Parameters=[@__id_0='?' (DbType = Guid)], CommandType='Text', CommandTimeout='30']
SELECT c.id, c."Avatar", c."Bio", c."Birthday", c.created_at, c."Gender", c."Industry", c.is_active, c.is_banned, c.is_email_verified, c.is_phone_verified, c.last_login_at, c.last_login_ip, c."Nickname", c."Position", c.refresh_token, c."Region", c."RegisterIp", c.two_factor_enabled, c.updated_at, c."UpdatedBy", c.username, c.locked_until, c.login_failed_count, c.refresh_token_expiry, c.two_factor_secret, c.email, n."CustomerUserId", n.notification_email, n.notification_features, n.notification_newsletter, n.notification_promotion, n.notification_quota, n.notification_security, n.notification_sms, n.notification_system, c.password_hash, c.country_code, c.phone_number, c0.id, c0.address, c0.city, c0.company, c0.country, c0.created_at, c0.custom_fields, c0.department, c0.github, c0.job_title, c0.linkedin, c0.postal_code, c0.province, c0.twitter, c0.updated_at, c0."UpdatedBy", c0.user_id, c0.website, c0.work_email, c0.work_phone
FROM customer_users AS c
LEFT JOIN customer_profiles AS c0 ON c.id = c0.user_id
LEFT JOIN "NotificationSettings" AS n ON c.id = n."CustomerUserId"
WHERE c.id = @__id_0
LIMIT 1
[13:30:57 INF] Executing OkObjectResult, writing value of type 'WhimLabAI.Shared.Dtos.ApiResponse`1[[WhimLabAI.Abstractions.Application.CustomerProfileDto, WhimLabAI.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
[13:30:57 INF] Executed action WhimLabAI.WebApi.Controllers.CustomerUser.CustomerUserController.GetProfile (WhimLabAI.WebApi) in 44.1884ms
[13:30:57 INF] Executed endpoint 'WhimLabAI.WebApi.Controllers.CustomerUser.CustomerUserController.GetProfile (WhimLabAI.WebApi)'
[13:30:57 INF] HTTP GET /api/customer/profile responded 200 in 47.3282 ms
[13:30:57 INF] Request finished HTTP/2 GET https://localhost:7025/api/customer/profile - 200 null application/json; charset=utf-8 47.7462ms
[13:30:59 INF] Executed DbCommand (3ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?' (DbType = Object), @p4='?', @p5='?', @p6='?', @p7='?', @p8='?' (DbType = DateTime), @p9='?', @p10='?', @p11='?', @p12='?', @p13='?', @p14='?' (DbType = Int64), @p15='?', @p16='?', @p17='?', @p18='?' (DbType = Boolean), @p19='?' (DbType = Boolean), @p20='?', @p21='?', @p22='?' (DbType = Object), @p23='?' (DbType = Object), @p24='?', @p25='?' (DbType = Object), @p26='?', @p27='?' (DbType = Int32), @p28='?', @p29='?' (DbType = DateTime), @p30='?', @p31='?', @p32='?' (DbType = Guid), @p33='?', @p34='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "AuditLogs" ("Id", "Action", "ActionName", "AdditionalData", "ChangedProperties", "ClientInfo", "ControllerName", "CorrelationId", "CreatedAt", "Description", "EntityId", "EntityType", "ErrorMessage", "ExceptionStack", "ExecutionDuration", "GeoLocation", "HttpMethod", "IpAddress", "IsSensitive", "IsSuccess", "JwtId", "Module", "NewValues", "OldValues", "RequestId", "RequestParameters", "RequestUrl", "ResponseStatusCode", "RiskLevel", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId", "UserName", "UserType")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34);
[13:30:59 INF] Flushed 1 audit logs to database
[13:31:10 INF] Request starting HTTP/2 GET https://localhost:7025/api/agents - null null
[13:31:10 INF] HTTP GET /api/agents responded 404 in 0.6823 ms
[13:31:10 INF] Request finished HTTP/2 GET https://localhost:7025/api/agents - 404 0 null 0.8376ms
[13:31:10 INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7025/api/agents, Response status code: 404
[13:31:10 ERR] Unexpected exception in TimingPipeFlusher.FlushAsync.
System.IO.IOException: The encryption operation failed, see inner exception.
 ---> System.ComponentModel.Win32Exception (14): Bad address
   --- End of inner exception stack trace ---
   at System.Net.Security.SslStream.WriteSingleChunk[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.PipeWriterHelpers.TimingPipeFlusher.FlushAsync(MinDataRate minRate, Int64 count, IHttpOutputAborter outputAborter, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2FrameWriter.WriteGoAwayAsync(Int32 lastStreamId, Http2ErrorCode errorCode)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2Connection.UpdateConnectionState()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2Connection.ProcessRequestsAsync[TContext](IHttpApplication`1 application)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecuteFromThreadPool(Thread threadPoolThread)
   at System.Threading.ThreadPoolWorkQueue.Dispatch()
   at System.Threading.PortableThreadPool.WorkerThread.WorkerThreadStart()
   at System.Threading.Thread.StartCallback()
--- End of stack trace from previous location ---
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.PipeWriterHelpers.ConcurrentPipeWriter.FlushAsyncAwaited(ValueTask`1 flushTask, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.PipeWriterHelpers.TimingPipeFlusher.TimeFlushAsyncAwaited(ValueTask`1 pipeFlushTask, MinDataRate minRate, IHttpOutputAborter outputAborter, CancellationToken cancellationToken)
[13:31:10 ERR] Unhandled exception while processing 0HNE454CJSCTT.
System.IO.IOException: The encryption operation failed, see inner exception.
 ---> System.ComponentModel.Win32Exception (14): Bad address
   --- End of inner exception stack trace ---
   at System.Net.Security.SslStream.WriteSingleChunk[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.DuplexPipeStreamAdapter`1.DisposeAsync()
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.DuplexPipeStreamAdapter`1.DisposeAsync()
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
   at System.Threading.Tasks.AwaitTaskContinuation.RunOrScheduleAction(IAsyncStateMachineBox box, Boolean allowInlining)
   at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetExistingTaskResult(Task`1 task, TResult result)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetResult()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.HttpConnection.ProcessRequestsAsync[TContext](IHttpApplication`1 httpApplication)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
   at System.Threading.Tasks.AwaitTaskContinuation.RunOrScheduleAction(IAsyncStateMachineBox box, Boolean allowInlining)
   at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetExistingTaskResult(Task`1 task, TResult result)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetResult()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2Connection.ProcessRequestsAsync[TContext](IHttpApplication`1 application)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
   at System.Threading.Tasks.AwaitTaskContinuation.RunOrScheduleAction(IAsyncStateMachineBox box, Boolean allowInlining)
   at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
   at System.Threading.Tasks.Task`1.TrySetResult(TResult result)
   at System.Threading.Tasks.UnwrapPromise`1.TrySetFromTask(Task task, Boolean lookForOce)
   at System.Threading.Tasks.UnwrapPromise`1.Invoke(Task completingTask)
   at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetExistingTaskResult(Task`1 task, TResult result)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetResult()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2FrameWriter.WriteToOutputPipe()
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
   at System.Threading.ThreadPoolWorkQueue.Dispatch()
   at System.Threading.PortableThreadPool.WorkerThread.WorkerThreadStart()
   at System.Threading.Thread.StartCallback()
--- End of stack trace from previous location ---
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.DuplexPipeStreamAdapter`1.DisposeAsync()
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.KestrelConnection`1.ExecuteAsync()
[13:31:19 INF] Executed DbCommand (3ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?' (DbType = Object), @p4='?', @p5='?', @p6='?', @p7='?', @p8='?' (DbType = DateTime), @p9='?', @p10='?', @p11='?', @p12='?', @p13='?', @p14='?' (DbType = Int64), @p15='?', @p16='?', @p17='?', @p18='?' (DbType = Boolean), @p19='?' (DbType = Boolean), @p20='?', @p21='?', @p22='?' (DbType = Object), @p23='?' (DbType = Object), @p24='?', @p25='?' (DbType = Object), @p26='?', @p27='?' (DbType = Int32), @p28='?', @p29='?' (DbType = DateTime), @p30='?', @p31='?', @p32='?' (DbType = Guid), @p33='?', @p34='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "AuditLogs" ("Id", "Action", "ActionName", "AdditionalData", "ChangedProperties", "ClientInfo", "ControllerName", "CorrelationId", "CreatedAt", "Description", "EntityId", "EntityType", "ErrorMessage", "ExceptionStack", "ExecutionDuration", "GeoLocation", "HttpMethod", "IpAddress", "IsSensitive", "IsSuccess", "JwtId", "Module", "NewValues", "OldValues", "RequestId", "RequestParameters", "RequestUrl", "ResponseStatusCode", "RiskLevel", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId", "UserName", "UserType")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34);
[13:31:19 INF] Flushed 1 audit logs to database
[13:31:45 INF] Request starting HTTP/2 GET https://localhost:7025/swagger/v1/swagger.json - null null
[13:31:45 INF] HTTP GET /swagger/v1/swagger.json responded 200 in 87.7060 ms
[13:31:45 INF] Request finished HTTP/2 GET https://localhost:7025/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 87.87ms
[13:31:49 INF] Performance metrics collected: CPU=0.1%, Memory=327MB, Threads=81
[13:31:55 INF] Executed DbCommand (1ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT c.user_id
    FROM conversations AS c
    WHERE c.updated_at >= @__today_0
) AS c0
[13:31:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT c.user_id
    FROM conversations AS c
    WHERE c.updated_at >= @__thirtyDaysAgo_0
) AS c0
[13:31:55 INF] Executed DbCommand (0ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM customer_users AS c
WHERE c.created_at >= @__today_0
[13:31:55 INF] Executed DbCommand (0ms) [Parameters=[@__yesterday_0='?' (DbType = DateTime), @__today_1='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT DISTINCT c.user_id
FROM conversations AS c
WHERE c.updated_at >= @__yesterday_0 AND c.updated_at < @__today_1
[13:31:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
[13:31:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
WHERE a.status = 'Published'
[13:31:55 INF] Executed DbCommand (0ms) [Parameters=[@__oneHourAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT c.agent_id AS "AgentId", count(*)::int AS "Count"
FROM conversations AS c
WHERE c.created_at >= @__oneHourAgo_0 AND c.agent_id <> '00000000-0000-0000-0000-000000000000'
GROUP BY c.agent_id
[13:31:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM conversations AS c
[13:31:55 INF] Executed DbCommand (0ms) [Parameters=[@__oneDayAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM conversations AS c
WHERE c.updated_at >= @__oneDayAgo_0
[13:31:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT c.id AS "Id", c.message_count AS "MessageCount"
FROM conversations AS c
[13:31:55 INF] Executed DbCommand (0ms) [Parameters=[@__AddHours_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT c.created_at AS "CreatedAt", c.updated_at AS "UpdatedAt"
FROM conversations AS c
WHERE c.updated_at < @__AddHours_0
[13:31:55 INF] Executed DbCommand (0ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT t."Model", t."SubscriptionId", COALESCE(sum(t."Tokens"), 0)::int AS "TotalTokens"
FROM "TokenUsages" AS t
WHERE t."CreatedAt" >= @__today_0
GROUP BY t."Model", t."SubscriptionId"
[13:31:55 INF] Executed DbCommand (1ms) [Parameters=[@__subscriptionIds_0='?' (DbType = Object)], CommandType='Text', CommandTimeout='30']
SELECT s."Id", s."CustomerUserId", s0."Tier"
FROM "Subscriptions" AS s
INNER JOIN "SubscriptionPlans" AS s0 ON s."PlanId" = s0."Id"
WHERE s."Id" = ANY (@__subscriptionIds_0) AND s."Status" = 'Active'
[13:31:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT o."Type" AS "OrderType", (
    SELECT COALESCE(sum(o0."FinalAmount"), 0.0)
    FROM "Orders" AS o0
    WHERE o0."Status" = 1 AND o0."UpdatedAt" >= @__thirtyDaysAgo_0 AND o."Type" = o0."Type") AS "TotalAmount"
FROM "Orders" AS o
WHERE o."Status" = 1 AND o."UpdatedAt" >= @__thirtyDaysAgo_0
GROUP BY o."Type"
[13:31:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM "Orders" AS o
WHERE o."CreatedAt" >= @__thirtyDaysAgo_0
[13:31:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM "Orders" AS o
WHERE o."CreatedAt" >= @__thirtyDaysAgo_0 AND o."Status" = 1
[13:31:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT o."CustomerUserId"
    FROM "Orders" AS o
    WHERE o."Status" = 1 AND o."UpdatedAt" >= @__thirtyDaysAgo_0
) AS o0
[13:31:55 INF] Business metrics collected successfully
[13:32:00 INF] Request starting HTTP/2 GET https://localhost:7025/api/marketplace/agents/trending - null null
[13:32:00 INF] Executing endpoint 'WhimLabAI.WebApi.Controllers.Agent.AgentMarketplaceController.GetTrendingAgents (WhimLabAI.WebApi)'
[13:32:00 INF] Route matched with {action = "GetTrendingAgents", controller = "AgentMarketplace"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetTrendingAgents(Int32, System.Threading.CancellationToken) on controller WhimLabAI.WebApi.Controllers.Agent.AgentMarketplaceController (WhimLabAI.WebApi).
[13:32:00 INF] Executed DbCommand (3ms) [Parameters=[@__agentStatus_0='?'], CommandType='Text', CommandTimeout='30']
SELECT a.id, a."AgentCategoryId", a.archived_at, a.average_rating, a.category_id, a.cover, a.created_at, a.creator_id, a.current_version_id, a.description, a.detailed_intro, a.icon, a."LikeCount", a.metadata, a.name, a.published_at, a.rating_count, a.status, a.unique_key, a.updated_at, a."UpdatedBy", a.usage_count, a.view_count, a.tags, a0.id, a0.agent_count, a0.created_at, a0.description, a0.display_name, a0.icon, a0.is_active, a0.name, a0."ParentId", a0.sort_order, a0.updated_at, a0."UpdatedBy"
FROM agents AS a
LEFT JOIN agent_categories AS a0 ON a.category_id = a0.id
WHERE a.status = @__agentStatus_0
ORDER BY a.created_at DESC
[13:32:00 INF] Executed DbCommand (1ms) [Parameters=[@__query_Status_0='?'], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
WHERE a.status::text = @__query_Status_0
[13:32:00 INF] Executed DbCommand (1ms) [Parameters=[@__agentId_0='?' (DbType = Guid)], CommandType='Text', CommandTimeout='30']
SELECT c.id, c.content, c.conversation_id, c.created_at, c.deleted_at, c.is_deleted, c.role, c.sequence_number, c.token_count, c.updated_at, c."UpdatedBy", c.rating_feedback, c.rated_at, c.rating_score, c."Rating_UpdatedAt"
FROM conversation_messages AS c
INNER JOIN conversations AS c0 ON c.conversation_id = c0.id
WHERE c0.agent_id = @__agentId_0 AND c.rated_at IS NOT NULL AND c.rating_score IS NOT NULL
[13:32:00 INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (DbType = Guid)], CommandType='Text', CommandTimeout='30']
SELECT c.id, c."Avatar", c."Bio", c."Birthday", c.created_at, c."Gender", c."Industry", c.is_active, c.is_banned, c.is_email_verified, c.is_phone_verified, c.last_login_at, c.last_login_ip, c."Nickname", c."Position", c.refresh_token, c."Region", c."RegisterIp", c.two_factor_enabled, c.updated_at, c."UpdatedBy", c.username, c.locked_until, c.login_failed_count, c.refresh_token_expiry, c.two_factor_secret, c.email, n."CustomerUserId", n.notification_email, n.notification_features, n.notification_newsletter, n.notification_promotion, n.notification_quota, n.notification_security, n.notification_sms, n.notification_system, c.password_hash, c.country_code, c.phone_number
FROM customer_users AS c
LEFT JOIN "NotificationSettings" AS n ON c.id = n."CustomerUserId"
WHERE c.id = @__p_0
LIMIT 1
[13:32:00 INF] Executed DbCommand (1ms) [Parameters=[@__agentId_0='?' (DbType = Guid)], CommandType='Text', CommandTimeout='30']
SELECT c.id, c.content, c.conversation_id, c.created_at, c.deleted_at, c.is_deleted, c.role, c.sequence_number, c.token_count, c.updated_at, c."UpdatedBy", c.rating_feedback, c.rated_at, c.rating_score, c."Rating_UpdatedAt"
FROM conversation_messages AS c
INNER JOIN conversations AS c0 ON c.conversation_id = c0.id
WHERE c0.agent_id = @__agentId_0 AND c.rated_at IS NOT NULL AND c.rating_score IS NOT NULL
[13:32:00 INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (DbType = Guid)], CommandType='Text', CommandTimeout='30']
SELECT c.id, c."Avatar", c."Bio", c."Birthday", c.created_at, c."Gender", c."Industry", c.is_active, c.is_banned, c.is_email_verified, c.is_phone_verified, c.last_login_at, c.last_login_ip, c."Nickname", c."Position", c.refresh_token, c."Region", c."RegisterIp", c.two_factor_enabled, c.updated_at, c."UpdatedBy", c.username, c.locked_until, c.login_failed_count, c.refresh_token_expiry, c.two_factor_secret, c.email, n."CustomerUserId", n.notification_email, n.notification_features, n.notification_newsletter, n.notification_promotion, n.notification_quota, n.notification_security, n.notification_sms, n.notification_system, c.password_hash, c.country_code, c.phone_number
FROM customer_users AS c
LEFT JOIN "NotificationSettings" AS n ON c.id = n."CustomerUserId"
WHERE c.id = @__p_0
LIMIT 1
[13:32:00 INF] Executed DbCommand (1ms) [Parameters=[@__agentId_0='?' (DbType = Guid)], CommandType='Text', CommandTimeout='30']
SELECT c.id, c.content, c.conversation_id, c.created_at, c.deleted_at, c.is_deleted, c.role, c.sequence_number, c.token_count, c.updated_at, c."UpdatedBy", c.rating_feedback, c.rated_at, c.rating_score, c."Rating_UpdatedAt"
FROM conversation_messages AS c
INNER JOIN conversations AS c0 ON c.conversation_id = c0.id
WHERE c0.agent_id = @__agentId_0 AND c.rated_at IS NOT NULL AND c.rating_score IS NOT NULL
[13:32:00 INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (DbType = Guid)], CommandType='Text', CommandTimeout='30']
SELECT c.id, c."Avatar", c."Bio", c."Birthday", c.created_at, c."Gender", c."Industry", c.is_active, c.is_banned, c.is_email_verified, c.is_phone_verified, c.last_login_at, c.last_login_ip, c."Nickname", c."Position", c.refresh_token, c."Region", c."RegisterIp", c.two_factor_enabled, c.updated_at, c."UpdatedBy", c.username, c.locked_until, c.login_failed_count, c.refresh_token_expiry, c.two_factor_secret, c.email, n."CustomerUserId", n.notification_email, n.notification_features, n.notification_newsletter, n.notification_promotion, n.notification_quota, n.notification_security, n.notification_sms, n.notification_system, c.password_hash, c.country_code, c.phone_number
FROM customer_users AS c
LEFT JOIN "NotificationSettings" AS n ON c.id = n."CustomerUserId"
WHERE c.id = @__p_0
LIMIT 1
[13:32:00 INF] Executing OkObjectResult, writing value of type 'WhimLabAI.Shared.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[WhimLabAI.Shared.Dtos.AgentListDto, WhimLabAI.Shared, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
[13:32:00 INF] Executed action WhimLabAI.WebApi.Controllers.Agent.AgentMarketplaceController.GetTrendingAgents (WhimLabAI.WebApi) in 77.4656ms
[13:32:00 INF] Executed endpoint 'WhimLabAI.WebApi.Controllers.Agent.AgentMarketplaceController.GetTrendingAgents (WhimLabAI.WebApi)'
[13:32:00 INF] HTTP GET /api/marketplace/agents/trending responded 200 in 87.5772 ms
[13:32:00 INF] Request finished HTTP/2 GET https://localhost:7025/api/marketplace/agents/trending - 200 null application/json; charset=utf-8 87.845ms
[13:32:00 ERR] Unexpected exception in TimingPipeFlusher.FlushAsync.
System.IO.IOException: The encryption operation failed, see inner exception.
 ---> System.ComponentModel.Win32Exception (14): Bad address
   --- End of inner exception stack trace ---
   at System.Net.Security.SslStream.WriteSingleChunk[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.PipeWriterHelpers.TimingPipeFlusher.FlushAsync(MinDataRate minRate, Int64 count, IHttpOutputAborter outputAborter, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2FrameWriter.WriteGoAwayAsync(Int32 lastStreamId, Http2ErrorCode errorCode)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2Connection.UpdateConnectionState()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2Connection.ProcessRequestsAsync[TContext](IHttpApplication`1 application)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecuteFromThreadPool(Thread threadPoolThread)
   at System.Threading.ThreadPoolWorkQueue.Dispatch()
   at System.Threading.PortableThreadPool.WorkerThread.WorkerThreadStart()
   at System.Threading.Thread.StartCallback()
--- End of stack trace from previous location ---
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.PipeWriterHelpers.ConcurrentPipeWriter.FlushAsyncAwaited(ValueTask`1 flushTask, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.PipeWriterHelpers.TimingPipeFlusher.TimeFlushAsyncAwaited(ValueTask`1 pipeFlushTask, MinDataRate minRate, IHttpOutputAborter outputAborter, CancellationToken cancellationToken)
[13:32:00 ERR] Unhandled exception while processing 0HNE454CJSCTV.
System.IO.IOException: The encryption operation failed, see inner exception.
 ---> System.ComponentModel.Win32Exception (14): Bad address
   --- End of inner exception stack trace ---
   at System.Net.Security.SslStream.WriteSingleChunk[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.DuplexPipeStreamAdapter`1.DisposeAsync()
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.DuplexPipeStreamAdapter`1.DisposeAsync()
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
   at System.Threading.Tasks.AwaitTaskContinuation.RunOrScheduleAction(IAsyncStateMachineBox box, Boolean allowInlining)
   at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetExistingTaskResult(Task`1 task, TResult result)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetResult()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.HttpConnection.ProcessRequestsAsync[TContext](IHttpApplication`1 httpApplication)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
   at System.Threading.Tasks.AwaitTaskContinuation.RunOrScheduleAction(IAsyncStateMachineBox box, Boolean allowInlining)
   at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetExistingTaskResult(Task`1 task, TResult result)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetResult()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2Connection.ProcessRequestsAsync[TContext](IHttpApplication`1 application)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
   at System.Threading.Tasks.AwaitTaskContinuation.RunOrScheduleAction(IAsyncStateMachineBox box, Boolean allowInlining)
   at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
   at System.Threading.Tasks.Task`1.TrySetResult(TResult result)
   at System.Threading.Tasks.UnwrapPromise`1.TrySetFromTask(Task task, Boolean lookForOce)
   at System.Threading.Tasks.UnwrapPromise`1.Invoke(Task completingTask)
   at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetExistingTaskResult(Task`1 task, TResult result)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetResult()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2FrameWriter.WriteToOutputPipe()
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
   at System.Threading.ThreadPoolWorkQueue.Dispatch()
   at System.Threading.PortableThreadPool.WorkerThread.WorkerThreadStart()
   at System.Threading.Thread.StartCallback()
--- End of stack trace from previous location ---
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.DuplexPipeStreamAdapter`1.DisposeAsync()
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.KestrelConnection`1.ExecuteAsync()
[13:32:09 INF] Executed DbCommand (5ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?' (DbType = Object), @p4='?', @p5='?', @p6='?', @p7='?', @p8='?' (DbType = DateTime), @p9='?', @p10='?', @p11='?', @p12='?', @p13='?', @p14='?' (DbType = Int64), @p15='?', @p16='?', @p17='?', @p18='?' (DbType = Boolean), @p19='?' (DbType = Boolean), @p20='?', @p21='?', @p22='?' (DbType = Object), @p23='?' (DbType = Object), @p24='?', @p25='?' (DbType = Object), @p26='?', @p27='?' (DbType = Int32), @p28='?', @p29='?' (DbType = DateTime), @p30='?', @p31='?', @p32='?' (DbType = Guid), @p33='?', @p34='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "AuditLogs" ("Id", "Action", "ActionName", "AdditionalData", "ChangedProperties", "ClientInfo", "ControllerName", "CorrelationId", "CreatedAt", "Description", "EntityId", "EntityType", "ErrorMessage", "ExceptionStack", "ExecutionDuration", "GeoLocation", "HttpMethod", "IpAddress", "IsSensitive", "IsSuccess", "JwtId", "Module", "NewValues", "OldValues", "RequestId", "RequestParameters", "RequestUrl", "ResponseStatusCode", "RiskLevel", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId", "UserName", "UserType")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34);
[13:32:09 INF] Flushed 1 audit logs to database
[13:32:16 INF] Request starting HTTP/2 POST https://localhost:7025/api/v1/admin/auth/refresh-token - application/json 116
[13:32:16 INF] Executing endpoint 'WhimLabAI.WebApi.Controllers.AdminAuth.AdminAuthController.RefreshToken (WhimLabAI.WebApi)'
[13:32:16 INF] Route matched with {action = "RefreshToken", controller = "AdminAuth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] RefreshToken(WhimLabAI.Shared.Dtos.Auth.Admin.AdminRefreshTokenRequestDto, System.Threading.CancellationToken) on controller WhimLabAI.WebApi.Controllers.AdminAuth.AdminAuthController (WhimLabAI.WebApi).
[13:32:16 INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='Text', CommandTimeout='30']
SELECT a.id, a.avatar, a.created_at, a.email, a.enable_ip_whitelist, a.login_failed_count, a.ip_whitelist, a."IsActive", a.is_super, a.last_login_at, a.last_login_ip, a.locked_until, a.nickname, a.password_expired_at, a.password_hash, a.status, a.two_factor_enabled, a.two_factor_secret, a.updated_at, a."UpdatedBy", a.username, a.phone_country_code, a.phone_number
FROM admin_users AS a
WHERE a.id = @__userId_0
ORDER BY a.id
LIMIT 1
[13:32:16 INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='Text', CommandTimeout='30']
SELECT s.user_id, s.role_id, s.created_at, s.id, s.code, s.created_at0, s.description, s.display_order, s.is_enabled, s.is_system, s.name, s.updated_at, s.updated_by, a1.id
FROM (
    SELECT a.id
    FROM admin_users AS a
    WHERE a.id = @__userId_0
    LIMIT 1
) AS a1
INNER JOIN (
    SELECT a0.user_id, a0.role_id, a0.created_at, r.id, r.code, r.created_at AS created_at0, r.description, r.display_order, r.is_enabled, r.is_system, r.name, r.updated_at, r.updated_by
    FROM admin_user_roles AS a0
    INNER JOIN roles AS r ON a0.role_id = r.id
) AS s ON a1.id = s.user_id
ORDER BY a1.id, s.user_id, s.role_id, s.id
[13:32:16 INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='Text', CommandTimeout='30']
SELECT s0.id, s0.created_at, s0.permission_id, s0.role_id, s0.updated_at, s0."UpdatedBy", s0.id0, s0.category, s0.code, s0.created_at0, s0.description, s0.display_order, s0.is_enabled, s0.is_system, s0.name, s0.parent_id, s0.updated_at0, s0."UpdatedBy0", a1.id, s.user_id, s.role_id, s.id
FROM (
    SELECT a.id
    FROM admin_users AS a
    WHERE a.id = @__userId_0
    LIMIT 1
) AS a1
INNER JOIN (
    SELECT a0.user_id, a0.role_id, r.id
    FROM admin_user_roles AS a0
    INNER JOIN roles AS r ON a0.role_id = r.id
) AS s ON a1.id = s.user_id
INNER JOIN (
    SELECT r0.id, r0.created_at, r0.permission_id, r0.role_id, r0.updated_at, r0."UpdatedBy", p.id AS id0, p.category, p.code, p.created_at AS created_at0, p.description, p.display_order, p.is_enabled, p.is_system, p.name, p.parent_id, p.updated_at AS updated_at0, p."UpdatedBy" AS "UpdatedBy0"
    FROM role_permissions AS r0
    INNER JOIN permissions AS p ON r0.permission_id = p.id
) AS s0 ON s.id = s0.role_id
ORDER BY a1.id, s.user_id, s.role_id, s.id
[13:32:16 INF] Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = Guid), @p2='?', @p3='?', @p4='?' (DbType = DateTime), @p5='?', @p6='?', @p7='?' (DbType = DateTime), @p8='?', @p9='?' (DbType = Boolean), @p10='?' (DbType = Boolean), @p11='?' (DbType = DateTime), @p12='?' (DbType = Double), @p13='?' (DbType = Double), @p14='?' (DbType = Boolean), @p15='?' (DbType = DateTime), @p16='?', @p17='?' (DbType = DateTime), @p18='?', @p19='?' (DbType = Boolean), @p20='?', @p21='?' (DbType = DateTime), @p22='?', @p23='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "AdminUserSessions" ("Id", "AdminUserId", "City", "Country", "CreatedAt", "DeviceId", "DeviceName", "ExpiresAt", "IpAddress", "IsActive", "IsAnomalousLocation", "LastActivityAt", "Latitude", "Longitude", "MfaVerified", "MfaVerifiedAt", "RefreshToken", "RefreshTokenExpiresAt", "Region", "RequiresMfa", "SessionToken", "UpdatedAt", "UpdatedBy", "UserAgent")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23);
[13:32:16 INF] Executed DbCommand (0ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='Text', CommandTimeout='30']
SELECT a.is_super AS "IsSuperAdmin"
FROM admin_users AS a
WHERE a.id = @__userId_0
LIMIT 1
[13:32:16 INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (DbType = Guid)], CommandType='Text', CommandTimeout='30']
SELECT DISTINCT p.id, p.category, p.code, p.created_at, p.description, p.display_order, p.is_enabled, p.is_system, p.name, p.parent_id, p.updated_at, p."UpdatedBy"
FROM admin_user_roles AS a
INNER JOIN roles AS r ON a.role_id = r.id
INNER JOIN role_permissions AS r0 ON r.id = r0.role_id
INNER JOIN permissions AS p ON r0.permission_id = p.id
WHERE a.user_id = @__userId_0 AND r.is_enabled AND p.is_enabled
[13:32:16 INF] Executing OkObjectResult, writing value of type 'WhimLabAI.Shared.Dtos.ApiResponse`1[[WhimLabAI.Shared.Dtos.Auth.AdminAuthResponseDto, WhimLabAI.Shared, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
[13:32:16 INF] Executed action WhimLabAI.WebApi.Controllers.AdminAuth.AdminAuthController.RefreshToken (WhimLabAI.WebApi) in 37.195ms
[13:32:16 INF] Executed endpoint 'WhimLabAI.WebApi.Controllers.AdminAuth.AdminAuthController.RefreshToken (WhimLabAI.WebApi)'
[13:32:16 INF] HTTP POST /api/v1/admin/auth/refresh-token responded 200 in 38.9431 ms
[13:32:16 INF] Request finished HTTP/2 POST https://localhost:7025/api/v1/admin/auth/refresh-token - 200 null application/json; charset=utf-8 39.0707ms
[13:32:16 ERR] Unexpected exception in TimingPipeFlusher.FlushAsync.
System.IO.IOException: The encryption operation failed, see inner exception.
 ---> System.ComponentModel.Win32Exception (14): Bad address
   --- End of inner exception stack trace ---
   at System.Net.Security.SslStream.WriteSingleChunk[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.PipeWriterHelpers.TimingPipeFlusher.FlushAsync(MinDataRate minRate, Int64 count, IHttpOutputAborter outputAborter, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2FrameWriter.WriteGoAwayAsync(Int32 lastStreamId, Http2ErrorCode errorCode)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2Connection.UpdateConnectionState()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2Connection.ProcessRequestsAsync[TContext](IHttpApplication`1 application)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecuteFromThreadPool(Thread threadPoolThread)
   at System.Threading.ThreadPoolWorkQueue.Dispatch()
   at System.Threading.PortableThreadPool.WorkerThread.WorkerThreadStart()
   at System.Threading.Thread.StartCallback()
--- End of stack trace from previous location ---
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.PipeWriterHelpers.ConcurrentPipeWriter.FlushAsyncAwaited(ValueTask`1 flushTask, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.PipeWriterHelpers.TimingPipeFlusher.TimeFlushAsyncAwaited(ValueTask`1 pipeFlushTask, MinDataRate minRate, IHttpOutputAborter outputAborter, CancellationToken cancellationToken)
[13:32:16 ERR] Unhandled exception while processing 0HNE454CJSCU0.
System.IO.IOException: The encryption operation failed, see inner exception.
 ---> System.ComponentModel.Win32Exception (14): Bad address
   --- End of inner exception stack trace ---
   at System.Net.Security.SslStream.WriteSingleChunk[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.DuplexPipeStreamAdapter`1.DisposeAsync()
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.DuplexPipeStreamAdapter`1.DisposeAsync()
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
   at System.Threading.Tasks.AwaitTaskContinuation.RunOrScheduleAction(IAsyncStateMachineBox box, Boolean allowInlining)
   at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetExistingTaskResult(Task`1 task, TResult result)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetResult()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.HttpConnection.ProcessRequestsAsync[TContext](IHttpApplication`1 httpApplication)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
   at System.Threading.Tasks.AwaitTaskContinuation.RunOrScheduleAction(IAsyncStateMachineBox box, Boolean allowInlining)
   at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetExistingTaskResult(Task`1 task, TResult result)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetResult()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2Connection.ProcessRequestsAsync[TContext](IHttpApplication`1 application)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
   at System.Threading.Tasks.AwaitTaskContinuation.RunOrScheduleAction(IAsyncStateMachineBox box, Boolean allowInlining)
   at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
   at System.Threading.Tasks.Task`1.TrySetResult(TResult result)
   at System.Threading.Tasks.UnwrapPromise`1.TrySetFromTask(Task task, Boolean lookForOce)
   at System.Threading.Tasks.UnwrapPromise`1.Invoke(Task completingTask)
   at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetExistingTaskResult(Task`1 task, TResult result)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetResult()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2FrameWriter.WriteToOutputPipe()
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
   at System.Threading.ThreadPoolWorkQueue.Dispatch()
   at System.Threading.PortableThreadPool.WorkerThread.WorkerThreadStart()
   at System.Threading.Thread.StartCallback()
--- End of stack trace from previous location ---
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.DuplexPipeStreamAdapter`1.DisposeAsync()
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.KestrelConnection`1.ExecuteAsync()
[13:32:19 INF] Executed DbCommand (4ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?' (DbType = Object), @p4='?', @p5='?', @p6='?', @p7='?', @p8='?' (DbType = DateTime), @p9='?', @p10='?', @p11='?', @p12='?', @p13='?', @p14='?' (DbType = Int64), @p15='?', @p16='?', @p17='?', @p18='?' (DbType = Boolean), @p19='?' (DbType = Boolean), @p20='?', @p21='?', @p22='?' (DbType = Object), @p23='?' (DbType = Object), @p24='?', @p25='?' (DbType = Object), @p26='?', @p27='?' (DbType = Int32), @p28='?', @p29='?' (DbType = DateTime), @p30='?', @p31='?', @p32='?' (DbType = Guid), @p33='?', @p34='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "AuditLogs" ("Id", "Action", "ActionName", "AdditionalData", "ChangedProperties", "ClientInfo", "ControllerName", "CorrelationId", "CreatedAt", "Description", "EntityId", "EntityType", "ErrorMessage", "ExceptionStack", "ExecutionDuration", "GeoLocation", "HttpMethod", "IpAddress", "IsSensitive", "IsSuccess", "JwtId", "Module", "NewValues", "OldValues", "RequestId", "RequestParameters", "RequestUrl", "ResponseStatusCode", "RiskLevel", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId", "UserName", "UserType")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34);
[13:32:19 INF] Flushed 1 audit logs to database
[13:32:25 INF] Request starting HTTP/2 GET https://localhost:7025/api/admin/dashboard/stats - null null
[13:32:25 INF] Failed to validate the token.
Microsoft.IdentityModel.Tokens.SecurityTokenMalformedException: IDX14100: JWT is not well formed, there are no dots (.).
The token needs to be in JWS or JWE Compact Serialization Format. (JWS): 'EncodedHeader.EncodedPayload.EncodedSignature'. (JWE): 'EncodedProtectedHeader.EncodedEncryptedKey.EncodedInitializationVector.EncodedCiphertext.EncodedAuthenticationTag'.
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebToken.ReadToken(ReadOnlyMemory`1 encodedTokenMemory)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebToken..ctor(String jwtEncodedString)
   at Microsoft.IdentityModel.JsonWebTokens.JsonWebTokenHandler.ReadToken(String token, TokenValidationParameters validationParameters)
[13:32:25 WRN] JWT认证失败: IDX14100: JWT is not well formed, there are no dots (.).
The token needs to be in JWS or JWE Compact Serialization Format. (JWS): 'EncodedHeader.EncodedPayload.EncodedSignature'. (JWE): 'EncodedProtectedHeader.EncodedEncryptedKey.EncodedInitializationVector.EncodedCiphertext.EncodedAuthenticationTag'.
[13:32:25 INF] Bearer was not authenticated. Failure message: IDX14100: JWT is not well formed, there are no dots (.).
The token needs to be in JWS or JWE Compact Serialization Format. (JWS): 'EncodedHeader.EncodedPayload.EncodedSignature'. (JWE): 'EncodedProtectedHeader.EncodedEncryptedKey.EncodedInitializationVector.EncodedCiphertext.EncodedAuthenticationTag'.
[13:32:25 INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
[13:32:25 INF] AuthenticationScheme: Bearer was challenged.
[13:32:25 INF] HTTP GET /api/admin/dashboard/stats responded 401 in 4.3867 ms
[13:32:25 INF] Request finished HTTP/2 GET https://localhost:7025/api/admin/dashboard/stats - 401 0 null 6.2768ms
[13:32:25 ERR] Unexpected exception in TimingPipeFlusher.FlushAsync.
System.IO.IOException: The encryption operation failed, see inner exception.
 ---> System.ComponentModel.Win32Exception (14): Bad address
   --- End of inner exception stack trace ---
   at System.Net.Security.SslStream.WriteSingleChunk[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.PipeWriterHelpers.TimingPipeFlusher.FlushAsync(MinDataRate minRate, Int64 count, IHttpOutputAborter outputAborter, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2FrameWriter.WriteGoAwayAsync(Int32 lastStreamId, Http2ErrorCode errorCode)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2Connection.UpdateConnectionState()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2Connection.ProcessRequestsAsync[TContext](IHttpApplication`1 application)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecuteFromThreadPool(Thread threadPoolThread)
   at System.Threading.ThreadPoolWorkQueue.Dispatch()
   at System.Threading.PortableThreadPool.WorkerThread.WorkerThreadStart()
   at System.Threading.Thread.StartCallback()
--- End of stack trace from previous location ---
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.PipeWriterHelpers.ConcurrentPipeWriter.FlushAsyncAwaited(ValueTask`1 flushTask, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.PipeWriterHelpers.TimingPipeFlusher.TimeFlushAsyncAwaited(ValueTask`1 pipeFlushTask, MinDataRate minRate, IHttpOutputAborter outputAborter, CancellationToken cancellationToken)
[13:32:25 ERR] Unhandled exception while processing 0HNE454CJSCU1.
System.IO.IOException: The encryption operation failed, see inner exception.
 ---> System.ComponentModel.Win32Exception (14): Bad address
   --- End of inner exception stack trace ---
   at System.Net.Security.SslStream.WriteSingleChunk[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.DuplexPipeStreamAdapter`1.DisposeAsync()
   at System.Runtime.CompilerServices.AsyncMethodBuilderCore.Start[TStateMachine](TStateMachine& stateMachine)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.DuplexPipeStreamAdapter`1.DisposeAsync()
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
   at System.Threading.Tasks.AwaitTaskContinuation.RunOrScheduleAction(IAsyncStateMachineBox box, Boolean allowInlining)
   at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetExistingTaskResult(Task`1 task, TResult result)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.HttpConnection.ProcessRequestsAsync[TContext](IHttpApplication`1 httpApplication)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
   at System.Threading.Tasks.AwaitTaskContinuation.RunOrScheduleAction(IAsyncStateMachineBox box, Boolean allowInlining)
   at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetExistingTaskResult(Task`1 task, TResult result)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetResult()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2Connection.ProcessRequestsAsync[TContext](IHttpApplication`1 application)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
   at System.Threading.Tasks.AwaitTaskContinuation.RunOrScheduleAction(IAsyncStateMachineBox box, Boolean allowInlining)
   at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
   at System.Threading.Tasks.Task`1.TrySetResult(TResult result)
   at System.Threading.Tasks.UnwrapPromise`1.TrySetFromTask(Task task, Boolean lookForOce)
   at System.Threading.Tasks.UnwrapPromise`1.Invoke(Task completingTask)
   at System.Threading.Tasks.Task.RunContinuations(Object continuationObject)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetExistingTaskResult(Task`1 task, TResult result)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetResult()
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http2.Http2FrameWriter.WriteToOutputPipe()
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.ExecutionContextCallback(Object s)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext(Thread threadPoolThread)
   at System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AsyncStateMachineBox`1.MoveNext()
   at System.Threading.ThreadPoolWorkQueue.Dispatch()
   at System.Threading.PortableThreadPool.WorkerThread.WorkerThreadStart()
   at System.Threading.Thread.StartCallback()
--- End of stack trace from previous location ---
   at System.Net.Security.SslStream.WriteAsyncInternal[TIOAdapter](ReadOnlyMemory`1 buffer, CancellationToken cancellationToken)
   at System.IO.Pipelines.StreamPipeWriter.FlushAsyncInternal(Boolean writeToStream, ReadOnlyMemory`1 data, CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at System.IO.Pipelines.StreamPipeWriter.CompleteAsync(Exception exception)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.DuplexPipeStreamAdapter`1.DisposeAsync()
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.KestrelConnection`1.ExecuteAsync()
[13:32:49 INF] Performance metrics collected: CPU=0.1%, Memory=327MB, Threads=81
[13:32:55 INF] Executed DbCommand (19ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT c.user_id
    FROM conversations AS c
    WHERE c.updated_at >= @__today_0
) AS c0
[13:32:55 INF] Executed DbCommand (7ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT c.user_id
    FROM conversations AS c
    WHERE c.updated_at >= @__thirtyDaysAgo_0
) AS c0
[13:32:55 INF] Executed DbCommand (2ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM customer_users AS c
WHERE c.created_at >= @__today_0
[13:32:55 INF] Executed DbCommand (0ms) [Parameters=[@__yesterday_0='?' (DbType = DateTime), @__today_1='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT DISTINCT c.user_id
FROM conversations AS c
WHERE c.updated_at >= @__yesterday_0 AND c.updated_at < @__today_1
[13:32:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
[13:32:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
WHERE a.status = 'Published'
[13:32:55 INF] Executed DbCommand (0ms) [Parameters=[@__oneHourAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT c.agent_id AS "AgentId", count(*)::int AS "Count"
FROM conversations AS c
WHERE c.created_at >= @__oneHourAgo_0 AND c.agent_id <> '00000000-0000-0000-0000-000000000000'
GROUP BY c.agent_id
[13:32:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM conversations AS c
[13:32:55 INF] Executed DbCommand (0ms) [Parameters=[@__oneDayAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM conversations AS c
WHERE c.updated_at >= @__oneDayAgo_0
[13:32:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT c.id AS "Id", c.message_count AS "MessageCount"
FROM conversations AS c
[13:32:55 INF] Executed DbCommand (0ms) [Parameters=[@__AddHours_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT c.created_at AS "CreatedAt", c.updated_at AS "UpdatedAt"
FROM conversations AS c
WHERE c.updated_at < @__AddHours_0
[13:32:55 INF] Executed DbCommand (0ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT t."Model", t."SubscriptionId", COALESCE(sum(t."Tokens"), 0)::int AS "TotalTokens"
FROM "TokenUsages" AS t
WHERE t."CreatedAt" >= @__today_0
GROUP BY t."Model", t."SubscriptionId"
[13:32:55 INF] Executed DbCommand (0ms) [Parameters=[@__subscriptionIds_0='?' (DbType = Object)], CommandType='Text', CommandTimeout='30']
SELECT s."Id", s."CustomerUserId", s0."Tier"
FROM "Subscriptions" AS s
INNER JOIN "SubscriptionPlans" AS s0 ON s."PlanId" = s0."Id"
WHERE s."Id" = ANY (@__subscriptionIds_0) AND s."Status" = 'Active'
[13:32:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT o."Type" AS "OrderType", (
    SELECT COALESCE(sum(o0."FinalAmount"), 0.0)
    FROM "Orders" AS o0
    WHERE o0."Status" = 1 AND o0."UpdatedAt" >= @__thirtyDaysAgo_0 AND o."Type" = o0."Type") AS "TotalAmount"
FROM "Orders" AS o
WHERE o."Status" = 1 AND o."UpdatedAt" >= @__thirtyDaysAgo_0
GROUP BY o."Type"
[13:32:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM "Orders" AS o
WHERE o."CreatedAt" >= @__thirtyDaysAgo_0
[13:32:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM "Orders" AS o
WHERE o."CreatedAt" >= @__thirtyDaysAgo_0 AND o."Status" = 1
[13:32:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT o."CustomerUserId"
    FROM "Orders" AS o
    WHERE o."Status" = 1 AND o."UpdatedAt" >= @__thirtyDaysAgo_0
) AS o0
[13:32:55 INF] Business metrics collected successfully
[13:32:57 INF] Request starting HTTP/2 GET https://localhost:7025/api/marketplace/agents/categories - null null
[13:32:57 INF] Executing endpoint 'WhimLabAI.WebApi.Controllers.Agent.AgentMarketplaceController.GetCategories (WhimLabAI.WebApi)'
[13:32:57 INF] Route matched with {action = "GetCategories", controller = "AgentMarketplace"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCategories(System.Threading.CancellationToken) on controller WhimLabAI.WebApi.Controllers.Agent.AgentMarketplaceController (WhimLabAI.WebApi).
[13:33:08 INF] Executed DbCommand (10,219ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT a.id, a.agent_count, a.created_at, a.description, a.display_name, a.icon, a.is_active, a.name, a."ParentId", a.sort_order, a.updated_at, a."UpdatedBy"
FROM agent_categories AS a
[13:33:08 INF] Executed DbCommand (21ms) [Parameters=[@__categoryId_0='?' (DbType = Guid)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
WHERE a.category_id = @__categoryId_0
[13:33:08 INF] Executed DbCommand (9ms) [Parameters=[@__categoryId_0='?' (DbType = Guid)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
WHERE a.category_id = @__categoryId_0
[13:33:08 INF] Executed DbCommand (3ms) [Parameters=[@__categoryId_0='?' (DbType = Guid)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
WHERE a.category_id = @__categoryId_0
[13:33:08 INF] Executed DbCommand (1ms) [Parameters=[@__categoryId_0='?' (DbType = Guid)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
WHERE a.category_id = @__categoryId_0
[13:33:08 INF] Executed DbCommand (3ms) [Parameters=[@__categoryId_0='?' (DbType = Guid)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
WHERE a.category_id = @__categoryId_0
[13:33:08 INF] Executed DbCommand (1ms) [Parameters=[@__categoryId_0='?' (DbType = Guid)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
WHERE a.category_id = @__categoryId_0
[13:33:08 INF] Executed DbCommand (1ms) [Parameters=[@__categoryId_0='?' (DbType = Guid)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
WHERE a.category_id = @__categoryId_0
[13:33:08 INF] Executed DbCommand (1ms) [Parameters=[@__categoryId_0='?' (DbType = Guid)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
WHERE a.category_id = @__categoryId_0
[13:33:08 INF] Executing OkObjectResult, writing value of type 'WhimLabAI.Shared.Dtos.ApiResponse`1[[System.Collections.Generic.List`1[[WhimLabAI.Shared.Dtos.AgentCategoryDto, WhimLabAI.Shared, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
[13:33:08 INF] Executed action WhimLabAI.WebApi.Controllers.Agent.AgentMarketplaceController.GetCategories (WhimLabAI.WebApi) in 10410.1884ms
[13:33:08 INF] Executed endpoint 'WhimLabAI.WebApi.Controllers.Agent.AgentMarketplaceController.GetCategories (WhimLabAI.WebApi)'
[13:33:08 INF] HTTP GET /api/marketplace/agents/categories responded 200 in 10412.4077 ms
[13:33:08 INF] Request finished HTTP/2 GET https://localhost:7025/api/marketplace/agents/categories - 200 null application/json; charset=utf-8 10413.1745ms
[13:33:09 INF] Executed DbCommand (1ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?', @p2='?', @p3='?' (DbType = Object), @p4='?', @p5='?', @p6='?', @p7='?', @p8='?' (DbType = DateTime), @p9='?', @p10='?', @p11='?', @p12='?', @p13='?', @p14='?' (DbType = Int64), @p15='?', @p16='?', @p17='?', @p18='?' (DbType = Boolean), @p19='?' (DbType = Boolean), @p20='?', @p21='?', @p22='?' (DbType = Object), @p23='?' (DbType = Object), @p24='?', @p25='?' (DbType = Object), @p26='?', @p27='?' (DbType = Int32), @p28='?', @p29='?' (DbType = DateTime), @p30='?', @p31='?', @p32='?' (DbType = Guid), @p33='?', @p34='?'], CommandType='Text', CommandTimeout='30']
INSERT INTO "AuditLogs" ("Id", "Action", "ActionName", "AdditionalData", "ChangedProperties", "ClientInfo", "ControllerName", "CorrelationId", "CreatedAt", "Description", "EntityId", "EntityType", "ErrorMessage", "ExceptionStack", "ExecutionDuration", "GeoLocation", "HttpMethod", "IpAddress", "IsSensitive", "IsSuccess", "JwtId", "Module", "NewValues", "OldValues", "RequestId", "RequestParameters", "RequestUrl", "ResponseStatusCode", "RiskLevel", "UpdatedAt", "UpdatedBy", "UserAgent", "UserId", "UserName", "UserType")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34);
[13:33:09 INF] Flushed 1 audit logs to database
[13:33:39 INF] Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT a."Id", a."AdminUserId", a."City", a."Country", a."CreatedAt", a."DeviceId", a."DeviceName", a."ExpiresAt", a."IpAddress", a."IsActive", a."IsAnomalousLocation", a."LastActivityAt", a."Latitude", a."Longitude", a."MfaVerified", a."MfaVerifiedAt", a."RefreshToken", a."RefreshTokenExpiresAt", a."Region", a."RequiresMfa", a."SessionToken", a."UpdatedAt", a."UpdatedBy", a."UserAgent"
FROM "AdminUserSessions" AS a
WHERE a."IsActive"
[13:33:39 INF] 会话清理完成: 检查会话数=2, 已过期=0, 因超时清理=0
[13:33:49 INF] Performance metrics collected: CPU=0.1%, Memory=327MB, Threads=81
[13:33:55 INF] Executed DbCommand (1ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT c.user_id
    FROM conversations AS c
    WHERE c.updated_at >= @__today_0
) AS c0
[13:33:55 INF] Executed DbCommand (1ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT c.user_id
    FROM conversations AS c
    WHERE c.updated_at >= @__thirtyDaysAgo_0
) AS c0
[13:33:55 INF] Executed DbCommand (1ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM customer_users AS c
WHERE c.created_at >= @__today_0
[13:33:55 INF] Executed DbCommand (1ms) [Parameters=[@__yesterday_0='?' (DbType = DateTime), @__today_1='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT DISTINCT c.user_id
FROM conversations AS c
WHERE c.updated_at >= @__yesterday_0 AND c.updated_at < @__today_1
[13:33:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
[13:33:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
WHERE a.status = 'Published'
[13:33:55 INF] Executed DbCommand (1ms) [Parameters=[@__oneHourAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT c.agent_id AS "AgentId", count(*)::int AS "Count"
FROM conversations AS c
WHERE c.created_at >= @__oneHourAgo_0 AND c.agent_id <> '00000000-0000-0000-0000-000000000000'
GROUP BY c.agent_id
[13:33:55 INF] Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM conversations AS c
[13:33:55 INF] Executed DbCommand (0ms) [Parameters=[@__oneDayAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM conversations AS c
WHERE c.updated_at >= @__oneDayAgo_0
[13:33:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT c.id AS "Id", c.message_count AS "MessageCount"
FROM conversations AS c
[13:33:55 INF] Executed DbCommand (0ms) [Parameters=[@__AddHours_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT c.created_at AS "CreatedAt", c.updated_at AS "UpdatedAt"
FROM conversations AS c
WHERE c.updated_at < @__AddHours_0
[13:33:55 INF] Executed DbCommand (0ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT t."Model", t."SubscriptionId", COALESCE(sum(t."Tokens"), 0)::int AS "TotalTokens"
FROM "TokenUsages" AS t
WHERE t."CreatedAt" >= @__today_0
GROUP BY t."Model", t."SubscriptionId"
[13:33:55 INF] Executed DbCommand (0ms) [Parameters=[@__subscriptionIds_0='?' (DbType = Object)], CommandType='Text', CommandTimeout='30']
SELECT s."Id", s."CustomerUserId", s0."Tier"
FROM "Subscriptions" AS s
INNER JOIN "SubscriptionPlans" AS s0 ON s."PlanId" = s0."Id"
WHERE s."Id" = ANY (@__subscriptionIds_0) AND s."Status" = 'Active'
[13:33:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT o."Type" AS "OrderType", (
    SELECT COALESCE(sum(o0."FinalAmount"), 0.0)
    FROM "Orders" AS o0
    WHERE o0."Status" = 1 AND o0."UpdatedAt" >= @__thirtyDaysAgo_0 AND o."Type" = o0."Type") AS "TotalAmount"
FROM "Orders" AS o
WHERE o."Status" = 1 AND o."UpdatedAt" >= @__thirtyDaysAgo_0
GROUP BY o."Type"
[13:33:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM "Orders" AS o
WHERE o."CreatedAt" >= @__thirtyDaysAgo_0
[13:33:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM "Orders" AS o
WHERE o."CreatedAt" >= @__thirtyDaysAgo_0 AND o."Status" = 1
[13:33:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT o."CustomerUserId"
    FROM "Orders" AS o
    WHERE o."Status" = 1 AND o."UpdatedAt" >= @__thirtyDaysAgo_0
) AS o0
[13:33:55 INF] Business metrics collected successfully
[13:34:49 INF] Performance metrics collected: CPU=0.1%, Memory=327MB, Threads=81
[13:34:55 INF] Executed DbCommand (1ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT c.user_id
    FROM conversations AS c
    WHERE c.updated_at >= @__today_0
) AS c0
[13:34:55 INF] Executed DbCommand (1ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT c.user_id
    FROM conversations AS c
    WHERE c.updated_at >= @__thirtyDaysAgo_0
) AS c0
[13:34:55 INF] Executed DbCommand (1ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM customer_users AS c
WHERE c.created_at >= @__today_0
[13:34:55 INF] Executed DbCommand (1ms) [Parameters=[@__yesterday_0='?' (DbType = DateTime), @__today_1='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT DISTINCT c.user_id
FROM conversations AS c
WHERE c.updated_at >= @__yesterday_0 AND c.updated_at < @__today_1
[13:34:55 INF] Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
[13:34:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
WHERE a.status = 'Published'
[13:34:55 INF] Executed DbCommand (0ms) [Parameters=[@__oneHourAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT c.agent_id AS "AgentId", count(*)::int AS "Count"
FROM conversations AS c
WHERE c.created_at >= @__oneHourAgo_0 AND c.agent_id <> '00000000-0000-0000-0000-000000000000'
GROUP BY c.agent_id
[13:34:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM conversations AS c
[13:34:55 INF] Executed DbCommand (1ms) [Parameters=[@__oneDayAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM conversations AS c
WHERE c.updated_at >= @__oneDayAgo_0
[13:34:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT c.id AS "Id", c.message_count AS "MessageCount"
FROM conversations AS c
[13:34:55 INF] Executed DbCommand (0ms) [Parameters=[@__AddHours_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT c.created_at AS "CreatedAt", c.updated_at AS "UpdatedAt"
FROM conversations AS c
WHERE c.updated_at < @__AddHours_0
[13:34:55 INF] Executed DbCommand (0ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT t."Model", t."SubscriptionId", COALESCE(sum(t."Tokens"), 0)::int AS "TotalTokens"
FROM "TokenUsages" AS t
WHERE t."CreatedAt" >= @__today_0
GROUP BY t."Model", t."SubscriptionId"
[13:34:55 INF] Executed DbCommand (0ms) [Parameters=[@__subscriptionIds_0='?' (DbType = Object)], CommandType='Text', CommandTimeout='30']
SELECT s."Id", s."CustomerUserId", s0."Tier"
FROM "Subscriptions" AS s
INNER JOIN "SubscriptionPlans" AS s0 ON s."PlanId" = s0."Id"
WHERE s."Id" = ANY (@__subscriptionIds_0) AND s."Status" = 'Active'
[13:34:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT o."Type" AS "OrderType", (
    SELECT COALESCE(sum(o0."FinalAmount"), 0.0)
    FROM "Orders" AS o0
    WHERE o0."Status" = 1 AND o0."UpdatedAt" >= @__thirtyDaysAgo_0 AND o."Type" = o0."Type") AS "TotalAmount"
FROM "Orders" AS o
WHERE o."Status" = 1 AND o."UpdatedAt" >= @__thirtyDaysAgo_0
GROUP BY o."Type"
[13:34:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM "Orders" AS o
WHERE o."CreatedAt" >= @__thirtyDaysAgo_0
[13:34:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM "Orders" AS o
WHERE o."CreatedAt" >= @__thirtyDaysAgo_0 AND o."Status" = 1
[13:34:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT o."CustomerUserId"
    FROM "Orders" AS o
    WHERE o."Status" = 1 AND o."UpdatedAt" >= @__thirtyDaysAgo_0
) AS o0
[13:34:55 INF] Business metrics collected successfully
[13:35:10 INF] Starting payment timeout check job
[13:35:10 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT o."Id", o."CouponCode", o."CouponId", o."CreatedAt", o."CustomerUserId", o."DeletedAt", o."ExpireAt", o."IsDeleted", o."Metadata", o."OrderNo", o."PaidAt", o."PaymentMethod", o."ProductId", o."ProductName", o."RefundedAt", o."Remark", o."Status", o."Type", o."UpdatedAt", o."UpdatedBy", o."Amount", o."AmountCurrency", o."DiscountAmount", o."DiscountCurrency", o."FinalAmount", o."FinalCurrency", o."PayableAmount", o."PayableCurrency"
FROM "Orders" AS o
WHERE o."Status" = 0 AND o."ExpireAt" < now()
[13:35:10 INF] Executed DbCommand (0ms) [Parameters=[@__expiryTime_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT p."Id", p."CompletedAt", p."CreatedAt", p."ExpireAt", p."FailReason", p."FailedAt", p."OrderId", p."PayerAccount", p."PayerName", p."PaymentMethod", p."PaymentNo", p."RawData", p."Status", p."TransactionId", p."UpdatedAt", p."UpdatedBy", p.amount, p.currency
FROM "PaymentTransactions" AS p
WHERE p."Status" = 0 AND p."CreatedAt" < @__expiryTime_0
[13:35:10 INF] Payment timeout check job completed. Cancelled orders: 0, Errors: 0
[13:35:49 INF] Performance metrics collected: CPU=0.1%, Memory=327MB, Threads=81
[13:35:55 INF] Executed DbCommand (1ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT c.user_id
    FROM conversations AS c
    WHERE c.updated_at >= @__today_0
) AS c0
[13:35:55 INF] Executed DbCommand (1ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT c.user_id
    FROM conversations AS c
    WHERE c.updated_at >= @__thirtyDaysAgo_0
) AS c0
[13:35:55 INF] Executed DbCommand (1ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM customer_users AS c
WHERE c.created_at >= @__today_0
[13:35:55 INF] Executed DbCommand (1ms) [Parameters=[@__yesterday_0='?' (DbType = DateTime), @__today_1='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT DISTINCT c.user_id
FROM conversations AS c
WHERE c.updated_at >= @__yesterday_0 AND c.updated_at < @__today_1
[13:35:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
[13:35:55 INF] Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
WHERE a.status = 'Published'
[13:35:55 INF] Executed DbCommand (1ms) [Parameters=[@__oneHourAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT c.agent_id AS "AgentId", count(*)::int AS "Count"
FROM conversations AS c
WHERE c.created_at >= @__oneHourAgo_0 AND c.agent_id <> '00000000-0000-0000-0000-000000000000'
GROUP BY c.agent_id
[13:35:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM conversations AS c
[13:35:55 INF] Executed DbCommand (0ms) [Parameters=[@__oneDayAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM conversations AS c
WHERE c.updated_at >= @__oneDayAgo_0
[13:35:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT c.id AS "Id", c.message_count AS "MessageCount"
FROM conversations AS c
[13:35:55 INF] Executed DbCommand (1ms) [Parameters=[@__AddHours_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT c.created_at AS "CreatedAt", c.updated_at AS "UpdatedAt"
FROM conversations AS c
WHERE c.updated_at < @__AddHours_0
[13:35:55 INF] Executed DbCommand (0ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT t."Model", t."SubscriptionId", COALESCE(sum(t."Tokens"), 0)::int AS "TotalTokens"
FROM "TokenUsages" AS t
WHERE t."CreatedAt" >= @__today_0
GROUP BY t."Model", t."SubscriptionId"
[13:35:55 INF] Executed DbCommand (0ms) [Parameters=[@__subscriptionIds_0='?' (DbType = Object)], CommandType='Text', CommandTimeout='30']
SELECT s."Id", s."CustomerUserId", s0."Tier"
FROM "Subscriptions" AS s
INNER JOIN "SubscriptionPlans" AS s0 ON s."PlanId" = s0."Id"
WHERE s."Id" = ANY (@__subscriptionIds_0) AND s."Status" = 'Active'
[13:35:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT o."Type" AS "OrderType", (
    SELECT COALESCE(sum(o0."FinalAmount"), 0.0)
    FROM "Orders" AS o0
    WHERE o0."Status" = 1 AND o0."UpdatedAt" >= @__thirtyDaysAgo_0 AND o."Type" = o0."Type") AS "TotalAmount"
FROM "Orders" AS o
WHERE o."Status" = 1 AND o."UpdatedAt" >= @__thirtyDaysAgo_0
GROUP BY o."Type"
[13:35:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM "Orders" AS o
WHERE o."CreatedAt" >= @__thirtyDaysAgo_0
[13:35:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM "Orders" AS o
WHERE o."CreatedAt" >= @__thirtyDaysAgo_0 AND o."Status" = 1
[13:35:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT o."CustomerUserId"
    FROM "Orders" AS o
    WHERE o."Status" = 1 AND o."UpdatedAt" >= @__thirtyDaysAgo_0
) AS o0
[13:35:55 INF] Business metrics collected successfully
[13:36:49 INF] Performance metrics collected: CPU=0.1%, Memory=327MB, Threads=81
[13:36:55 INF] Executed DbCommand (1ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT c.user_id
    FROM conversations AS c
    WHERE c.updated_at >= @__today_0
) AS c0
[13:36:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT c.user_id
    FROM conversations AS c
    WHERE c.updated_at >= @__thirtyDaysAgo_0
) AS c0
[13:36:55 INF] Executed DbCommand (0ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM customer_users AS c
WHERE c.created_at >= @__today_0
[13:36:55 INF] Executed DbCommand (0ms) [Parameters=[@__yesterday_0='?' (DbType = DateTime), @__today_1='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT DISTINCT c.user_id
FROM conversations AS c
WHERE c.updated_at >= @__yesterday_0 AND c.updated_at < @__today_1
[13:36:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
[13:36:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
WHERE a.status = 'Published'
[13:36:55 INF] Executed DbCommand (0ms) [Parameters=[@__oneHourAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT c.agent_id AS "AgentId", count(*)::int AS "Count"
FROM conversations AS c
WHERE c.created_at >= @__oneHourAgo_0 AND c.agent_id <> '00000000-0000-0000-0000-000000000000'
GROUP BY c.agent_id
[13:36:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM conversations AS c
[13:36:55 INF] Executed DbCommand (0ms) [Parameters=[@__oneDayAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM conversations AS c
WHERE c.updated_at >= @__oneDayAgo_0
[13:36:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT c.id AS "Id", c.message_count AS "MessageCount"
FROM conversations AS c
[13:36:55 INF] Executed DbCommand (0ms) [Parameters=[@__AddHours_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT c.created_at AS "CreatedAt", c.updated_at AS "UpdatedAt"
FROM conversations AS c
WHERE c.updated_at < @__AddHours_0
[13:36:55 INF] Executed DbCommand (0ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT t."Model", t."SubscriptionId", COALESCE(sum(t."Tokens"), 0)::int AS "TotalTokens"
FROM "TokenUsages" AS t
WHERE t."CreatedAt" >= @__today_0
GROUP BY t."Model", t."SubscriptionId"
[13:36:55 INF] Executed DbCommand (1ms) [Parameters=[@__subscriptionIds_0='?' (DbType = Object)], CommandType='Text', CommandTimeout='30']
SELECT s."Id", s."CustomerUserId", s0."Tier"
FROM "Subscriptions" AS s
INNER JOIN "SubscriptionPlans" AS s0 ON s."PlanId" = s0."Id"
WHERE s."Id" = ANY (@__subscriptionIds_0) AND s."Status" = 'Active'
[13:36:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT o."Type" AS "OrderType", (
    SELECT COALESCE(sum(o0."FinalAmount"), 0.0)
    FROM "Orders" AS o0
    WHERE o0."Status" = 1 AND o0."UpdatedAt" >= @__thirtyDaysAgo_0 AND o."Type" = o0."Type") AS "TotalAmount"
FROM "Orders" AS o
WHERE o."Status" = 1 AND o."UpdatedAt" >= @__thirtyDaysAgo_0
GROUP BY o."Type"
[13:36:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM "Orders" AS o
WHERE o."CreatedAt" >= @__thirtyDaysAgo_0
[13:36:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM "Orders" AS o
WHERE o."CreatedAt" >= @__thirtyDaysAgo_0 AND o."Status" = 1
[13:36:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT o."CustomerUserId"
    FROM "Orders" AS o
    WHERE o."Status" = 1 AND o."UpdatedAt" >= @__thirtyDaysAgo_0
) AS o0
[13:36:55 INF] Business metrics collected successfully
[13:37:49 INF] Performance metrics collected: CPU=0.1%, Memory=327MB, Threads=81
[13:37:55 INF] Executed DbCommand (1ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT c.user_id
    FROM conversations AS c
    WHERE c.updated_at >= @__today_0
) AS c0
[13:37:55 INF] Executed DbCommand (1ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT c.user_id
    FROM conversations AS c
    WHERE c.updated_at >= @__thirtyDaysAgo_0
) AS c0
[13:37:55 INF] Executed DbCommand (1ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM customer_users AS c
WHERE c.created_at >= @__today_0
[13:37:55 INF] Executed DbCommand (1ms) [Parameters=[@__yesterday_0='?' (DbType = DateTime), @__today_1='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT DISTINCT c.user_id
FROM conversations AS c
WHERE c.updated_at >= @__yesterday_0 AND c.updated_at < @__today_1
[13:37:55 INF] Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
[13:37:55 INF] Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
WHERE a.status = 'Published'
[13:37:55 INF] Executed DbCommand (1ms) [Parameters=[@__oneHourAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT c.agent_id AS "AgentId", count(*)::int AS "Count"
FROM conversations AS c
WHERE c.created_at >= @__oneHourAgo_0 AND c.agent_id <> '00000000-0000-0000-0000-000000000000'
GROUP BY c.agent_id
[13:37:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM conversations AS c
[13:37:55 INF] Executed DbCommand (0ms) [Parameters=[@__oneDayAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM conversations AS c
WHERE c.updated_at >= @__oneDayAgo_0
[13:37:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT c.id AS "Id", c.message_count AS "MessageCount"
FROM conversations AS c
[13:37:55 INF] Executed DbCommand (1ms) [Parameters=[@__AddHours_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT c.created_at AS "CreatedAt", c.updated_at AS "UpdatedAt"
FROM conversations AS c
WHERE c.updated_at < @__AddHours_0
[13:37:55 INF] Executed DbCommand (0ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT t."Model", t."SubscriptionId", COALESCE(sum(t."Tokens"), 0)::int AS "TotalTokens"
FROM "TokenUsages" AS t
WHERE t."CreatedAt" >= @__today_0
GROUP BY t."Model", t."SubscriptionId"
[13:37:55 INF] Executed DbCommand (1ms) [Parameters=[@__subscriptionIds_0='?' (DbType = Object)], CommandType='Text', CommandTimeout='30']
SELECT s."Id", s."CustomerUserId", s0."Tier"
FROM "Subscriptions" AS s
INNER JOIN "SubscriptionPlans" AS s0 ON s."PlanId" = s0."Id"
WHERE s."Id" = ANY (@__subscriptionIds_0) AND s."Status" = 'Active'
[13:37:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT o."Type" AS "OrderType", (
    SELECT COALESCE(sum(o0."FinalAmount"), 0.0)
    FROM "Orders" AS o0
    WHERE o0."Status" = 1 AND o0."UpdatedAt" >= @__thirtyDaysAgo_0 AND o."Type" = o0."Type") AS "TotalAmount"
FROM "Orders" AS o
WHERE o."Status" = 1 AND o."UpdatedAt" >= @__thirtyDaysAgo_0
GROUP BY o."Type"
[13:37:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM "Orders" AS o
WHERE o."CreatedAt" >= @__thirtyDaysAgo_0
[13:37:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM "Orders" AS o
WHERE o."CreatedAt" >= @__thirtyDaysAgo_0 AND o."Status" = 1
[13:37:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT o."CustomerUserId"
    FROM "Orders" AS o
    WHERE o."Status" = 1 AND o."UpdatedAt" >= @__thirtyDaysAgo_0
) AS o0
[13:37:55 INF] Business metrics collected successfully
[13:38:39 INF] Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT a."Id", a."AdminUserId", a."City", a."Country", a."CreatedAt", a."DeviceId", a."DeviceName", a."ExpiresAt", a."IpAddress", a."IsActive", a."IsAnomalousLocation", a."LastActivityAt", a."Latitude", a."Longitude", a."MfaVerified", a."MfaVerifiedAt", a."RefreshToken", a."RefreshTokenExpiresAt", a."Region", a."RequiresMfa", a."SessionToken", a."UpdatedAt", a."UpdatedBy", a."UserAgent"
FROM "AdminUserSessions" AS a
WHERE a."IsActive"
[13:38:39 INF] 会话清理完成: 检查会话数=2, 已过期=0, 因超时清理=0
[13:38:49 INF] Performance metrics collected: CPU=0.1%, Memory=327MB, Threads=81
[13:38:55 INF] Executed DbCommand (1ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT c.user_id
    FROM conversations AS c
    WHERE c.updated_at >= @__today_0
) AS c0
[13:38:55 INF] Executed DbCommand (1ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT c.user_id
    FROM conversations AS c
    WHERE c.updated_at >= @__thirtyDaysAgo_0
) AS c0
[13:38:55 INF] Executed DbCommand (1ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM customer_users AS c
WHERE c.created_at >= @__today_0
[13:38:55 INF] Executed DbCommand (1ms) [Parameters=[@__yesterday_0='?' (DbType = DateTime), @__today_1='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT DISTINCT c.user_id
FROM conversations AS c
WHERE c.updated_at >= @__yesterday_0 AND c.updated_at < @__today_1
[13:38:55 INF] Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
[13:38:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
WHERE a.status = 'Published'
[13:38:55 INF] Executed DbCommand (0ms) [Parameters=[@__oneHourAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT c.agent_id AS "AgentId", count(*)::int AS "Count"
FROM conversations AS c
WHERE c.created_at >= @__oneHourAgo_0 AND c.agent_id <> '00000000-0000-0000-0000-000000000000'
GROUP BY c.agent_id
[13:38:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM conversations AS c
[13:38:55 INF] Executed DbCommand (0ms) [Parameters=[@__oneDayAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM conversations AS c
WHERE c.updated_at >= @__oneDayAgo_0
[13:38:55 INF] Executed DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT c.id AS "Id", c.message_count AS "MessageCount"
FROM conversations AS c
[13:38:55 INF] Executed DbCommand (0ms) [Parameters=[@__AddHours_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT c.created_at AS "CreatedAt", c.updated_at AS "UpdatedAt"
FROM conversations AS c
WHERE c.updated_at < @__AddHours_0
[13:38:55 INF] Executed DbCommand (0ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT t."Model", t."SubscriptionId", COALESCE(sum(t."Tokens"), 0)::int AS "TotalTokens"
FROM "TokenUsages" AS t
WHERE t."CreatedAt" >= @__today_0
GROUP BY t."Model", t."SubscriptionId"
[13:38:55 INF] Executed DbCommand (0ms) [Parameters=[@__subscriptionIds_0='?' (DbType = Object)], CommandType='Text', CommandTimeout='30']
SELECT s."Id", s."CustomerUserId", s0."Tier"
FROM "Subscriptions" AS s
INNER JOIN "SubscriptionPlans" AS s0 ON s."PlanId" = s0."Id"
WHERE s."Id" = ANY (@__subscriptionIds_0) AND s."Status" = 'Active'
[13:38:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT o."Type" AS "OrderType", (
    SELECT COALESCE(sum(o0."FinalAmount"), 0.0)
    FROM "Orders" AS o0
    WHERE o0."Status" = 1 AND o0."UpdatedAt" >= @__thirtyDaysAgo_0 AND o."Type" = o0."Type") AS "TotalAmount"
FROM "Orders" AS o
WHERE o."Status" = 1 AND o."UpdatedAt" >= @__thirtyDaysAgo_0
GROUP BY o."Type"
[13:38:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM "Orders" AS o
WHERE o."CreatedAt" >= @__thirtyDaysAgo_0
[13:38:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM "Orders" AS o
WHERE o."CreatedAt" >= @__thirtyDaysAgo_0 AND o."Status" = 1
[13:38:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT o."CustomerUserId"
    FROM "Orders" AS o
    WHERE o."Status" = 1 AND o."UpdatedAt" >= @__thirtyDaysAgo_0
) AS o0
[13:38:55 INF] Business metrics collected successfully
[13:39:49 INF] Performance metrics collected: CPU=0.1%, Memory=327MB, Threads=81
[13:39:55 INF] Executed DbCommand (1ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT c.user_id
    FROM conversations AS c
    WHERE c.updated_at >= @__today_0
) AS c0
[13:39:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT c.user_id
    FROM conversations AS c
    WHERE c.updated_at >= @__thirtyDaysAgo_0
) AS c0
[13:39:55 INF] Executed DbCommand (1ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM customer_users AS c
WHERE c.created_at >= @__today_0
[13:39:55 INF] Executed DbCommand (0ms) [Parameters=[@__yesterday_0='?' (DbType = DateTime), @__today_1='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT DISTINCT c.user_id
FROM conversations AS c
WHERE c.updated_at >= @__yesterday_0 AND c.updated_at < @__today_1
[13:39:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
[13:39:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
WHERE a.status = 'Published'
[13:39:55 INF] Executed DbCommand (0ms) [Parameters=[@__oneHourAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT c.agent_id AS "AgentId", count(*)::int AS "Count"
FROM conversations AS c
WHERE c.created_at >= @__oneHourAgo_0 AND c.agent_id <> '00000000-0000-0000-0000-000000000000'
GROUP BY c.agent_id
[13:39:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM conversations AS c
[13:39:55 INF] Executed DbCommand (0ms) [Parameters=[@__oneDayAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM conversations AS c
WHERE c.updated_at >= @__oneDayAgo_0
[13:39:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT c.id AS "Id", c.message_count AS "MessageCount"
FROM conversations AS c
[13:39:55 INF] Executed DbCommand (0ms) [Parameters=[@__AddHours_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT c.created_at AS "CreatedAt", c.updated_at AS "UpdatedAt"
FROM conversations AS c
WHERE c.updated_at < @__AddHours_0
[13:39:55 INF] Executed DbCommand (1ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT t."Model", t."SubscriptionId", COALESCE(sum(t."Tokens"), 0)::int AS "TotalTokens"
FROM "TokenUsages" AS t
WHERE t."CreatedAt" >= @__today_0
GROUP BY t."Model", t."SubscriptionId"
[13:39:55 INF] Executed DbCommand (1ms) [Parameters=[@__subscriptionIds_0='?' (DbType = Object)], CommandType='Text', CommandTimeout='30']
SELECT s."Id", s."CustomerUserId", s0."Tier"
FROM "Subscriptions" AS s
INNER JOIN "SubscriptionPlans" AS s0 ON s."PlanId" = s0."Id"
WHERE s."Id" = ANY (@__subscriptionIds_0) AND s."Status" = 'Active'
[13:39:55 INF] Executed DbCommand (1ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT o."Type" AS "OrderType", (
    SELECT COALESCE(sum(o0."FinalAmount"), 0.0)
    FROM "Orders" AS o0
    WHERE o0."Status" = 1 AND o0."UpdatedAt" >= @__thirtyDaysAgo_0 AND o."Type" = o0."Type") AS "TotalAmount"
FROM "Orders" AS o
WHERE o."Status" = 1 AND o."UpdatedAt" >= @__thirtyDaysAgo_0
GROUP BY o."Type"
[13:39:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM "Orders" AS o
WHERE o."CreatedAt" >= @__thirtyDaysAgo_0
[13:39:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM "Orders" AS o
WHERE o."CreatedAt" >= @__thirtyDaysAgo_0 AND o."Status" = 1
[13:39:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT o."CustomerUserId"
    FROM "Orders" AS o
    WHERE o."Status" = 1 AND o."UpdatedAt" >= @__thirtyDaysAgo_0
) AS o0
[13:39:55 INF] Business metrics collected successfully
[13:40:10 INF] Starting payment timeout check job
[13:40:10 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT o."Id", o."CouponCode", o."CouponId", o."CreatedAt", o."CustomerUserId", o."DeletedAt", o."ExpireAt", o."IsDeleted", o."Metadata", o."OrderNo", o."PaidAt", o."PaymentMethod", o."ProductId", o."ProductName", o."RefundedAt", o."Remark", o."Status", o."Type", o."UpdatedAt", o."UpdatedBy", o."Amount", o."AmountCurrency", o."DiscountAmount", o."DiscountCurrency", o."FinalAmount", o."FinalCurrency", o."PayableAmount", o."PayableCurrency"
FROM "Orders" AS o
WHERE o."Status" = 0 AND o."ExpireAt" < now()
[13:40:10 INF] Executed DbCommand (0ms) [Parameters=[@__expiryTime_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT p."Id", p."CompletedAt", p."CreatedAt", p."ExpireAt", p."FailReason", p."FailedAt", p."OrderId", p."PayerAccount", p."PayerName", p."PaymentMethod", p."PaymentNo", p."RawData", p."Status", p."TransactionId", p."UpdatedAt", p."UpdatedBy", p.amount, p.currency
FROM "PaymentTransactions" AS p
WHERE p."Status" = 0 AND p."CreatedAt" < @__expiryTime_0
[13:40:10 INF] Payment timeout check job completed. Cancelled orders: 0, Errors: 0
[13:40:49 INF] Performance metrics collected: CPU=0.1%, Memory=327MB, Threads=81
[13:40:55 INF] Executed DbCommand (1ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT c.user_id
    FROM conversations AS c
    WHERE c.updated_at >= @__today_0
) AS c0
[13:40:55 INF] Executed DbCommand (1ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT c.user_id
    FROM conversations AS c
    WHERE c.updated_at >= @__thirtyDaysAgo_0
) AS c0
[13:40:55 INF] Executed DbCommand (0ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM customer_users AS c
WHERE c.created_at >= @__today_0
[13:40:55 INF] Executed DbCommand (1ms) [Parameters=[@__yesterday_0='?' (DbType = DateTime), @__today_1='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT DISTINCT c.user_id
FROM conversations AS c
WHERE c.updated_at >= @__yesterday_0 AND c.updated_at < @__today_1
[13:40:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
[13:40:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
WHERE a.status = 'Published'
[13:40:55 INF] Executed DbCommand (0ms) [Parameters=[@__oneHourAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT c.agent_id AS "AgentId", count(*)::int AS "Count"
FROM conversations AS c
WHERE c.created_at >= @__oneHourAgo_0 AND c.agent_id <> '00000000-0000-0000-0000-000000000000'
GROUP BY c.agent_id
[13:40:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM conversations AS c
[13:40:55 INF] Executed DbCommand (0ms) [Parameters=[@__oneDayAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM conversations AS c
WHERE c.updated_at >= @__oneDayAgo_0
[13:40:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT c.id AS "Id", c.message_count AS "MessageCount"
FROM conversations AS c
[13:40:55 INF] Executed DbCommand (0ms) [Parameters=[@__AddHours_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT c.created_at AS "CreatedAt", c.updated_at AS "UpdatedAt"
FROM conversations AS c
WHERE c.updated_at < @__AddHours_0
[13:40:55 INF] Executed DbCommand (0ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT t."Model", t."SubscriptionId", COALESCE(sum(t."Tokens"), 0)::int AS "TotalTokens"
FROM "TokenUsages" AS t
WHERE t."CreatedAt" >= @__today_0
GROUP BY t."Model", t."SubscriptionId"
[13:40:55 INF] Executed DbCommand (0ms) [Parameters=[@__subscriptionIds_0='?' (DbType = Object)], CommandType='Text', CommandTimeout='30']
SELECT s."Id", s."CustomerUserId", s0."Tier"
FROM "Subscriptions" AS s
INNER JOIN "SubscriptionPlans" AS s0 ON s."PlanId" = s0."Id"
WHERE s."Id" = ANY (@__subscriptionIds_0) AND s."Status" = 'Active'
[13:40:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT o."Type" AS "OrderType", (
    SELECT COALESCE(sum(o0."FinalAmount"), 0.0)
    FROM "Orders" AS o0
    WHERE o0."Status" = 1 AND o0."UpdatedAt" >= @__thirtyDaysAgo_0 AND o."Type" = o0."Type") AS "TotalAmount"
FROM "Orders" AS o
WHERE o."Status" = 1 AND o."UpdatedAt" >= @__thirtyDaysAgo_0
GROUP BY o."Type"
[13:40:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM "Orders" AS o
WHERE o."CreatedAt" >= @__thirtyDaysAgo_0
[13:40:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM "Orders" AS o
WHERE o."CreatedAt" >= @__thirtyDaysAgo_0 AND o."Status" = 1
[13:40:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT o."CustomerUserId"
    FROM "Orders" AS o
    WHERE o."Status" = 1 AND o."UpdatedAt" >= @__thirtyDaysAgo_0
) AS o0
[13:40:55 INF] Business metrics collected successfully
[13:41:49 INF] Performance metrics collected: CPU=0.1%, Memory=327MB, Threads=81
[13:41:55 INF] Executed DbCommand (1ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT c.user_id
    FROM conversations AS c
    WHERE c.updated_at >= @__today_0
) AS c0
[13:41:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT c.user_id
    FROM conversations AS c
    WHERE c.updated_at >= @__thirtyDaysAgo_0
) AS c0
[13:41:55 INF] Executed DbCommand (0ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM customer_users AS c
WHERE c.created_at >= @__today_0
[13:41:55 INF] Executed DbCommand (0ms) [Parameters=[@__yesterday_0='?' (DbType = DateTime), @__today_1='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT DISTINCT c.user_id
FROM conversations AS c
WHERE c.updated_at >= @__yesterday_0 AND c.updated_at < @__today_1
[13:41:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
[13:41:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
WHERE a.status = 'Published'
[13:41:55 INF] Executed DbCommand (0ms) [Parameters=[@__oneHourAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT c.agent_id AS "AgentId", count(*)::int AS "Count"
FROM conversations AS c
WHERE c.created_at >= @__oneHourAgo_0 AND c.agent_id <> '00000000-0000-0000-0000-000000000000'
GROUP BY c.agent_id
[13:41:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM conversations AS c
[13:41:55 INF] Executed DbCommand (0ms) [Parameters=[@__oneDayAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM conversations AS c
WHERE c.updated_at >= @__oneDayAgo_0
[13:41:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT c.id AS "Id", c.message_count AS "MessageCount"
FROM conversations AS c
[13:41:55 INF] Executed DbCommand (0ms) [Parameters=[@__AddHours_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT c.created_at AS "CreatedAt", c.updated_at AS "UpdatedAt"
FROM conversations AS c
WHERE c.updated_at < @__AddHours_0
[13:41:55 INF] Executed DbCommand (0ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT t."Model", t."SubscriptionId", COALESCE(sum(t."Tokens"), 0)::int AS "TotalTokens"
FROM "TokenUsages" AS t
WHERE t."CreatedAt" >= @__today_0
GROUP BY t."Model", t."SubscriptionId"
[13:41:55 INF] Executed DbCommand (1ms) [Parameters=[@__subscriptionIds_0='?' (DbType = Object)], CommandType='Text', CommandTimeout='30']
SELECT s."Id", s."CustomerUserId", s0."Tier"
FROM "Subscriptions" AS s
INNER JOIN "SubscriptionPlans" AS s0 ON s."PlanId" = s0."Id"
WHERE s."Id" = ANY (@__subscriptionIds_0) AND s."Status" = 'Active'
[13:41:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT o."Type" AS "OrderType", (
    SELECT COALESCE(sum(o0."FinalAmount"), 0.0)
    FROM "Orders" AS o0
    WHERE o0."Status" = 1 AND o0."UpdatedAt" >= @__thirtyDaysAgo_0 AND o."Type" = o0."Type") AS "TotalAmount"
FROM "Orders" AS o
WHERE o."Status" = 1 AND o."UpdatedAt" >= @__thirtyDaysAgo_0
GROUP BY o."Type"
[13:41:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM "Orders" AS o
WHERE o."CreatedAt" >= @__thirtyDaysAgo_0
[13:41:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM "Orders" AS o
WHERE o."CreatedAt" >= @__thirtyDaysAgo_0 AND o."Status" = 1
[13:41:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT o."CustomerUserId"
    FROM "Orders" AS o
    WHERE o."Status" = 1 AND o."UpdatedAt" >= @__thirtyDaysAgo_0
) AS o0
[13:41:55 INF] Business metrics collected successfully
[13:42:49 INF] Performance metrics collected: CPU=0.1%, Memory=327MB, Threads=81
[13:42:55 INF] Executed DbCommand (3ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT c.user_id
    FROM conversations AS c
    WHERE c.updated_at >= @__today_0
) AS c0
[13:42:55 INF] Executed DbCommand (1ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT c.user_id
    FROM conversations AS c
    WHERE c.updated_at >= @__thirtyDaysAgo_0
) AS c0
[13:42:55 INF] Executed DbCommand (0ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM customer_users AS c
WHERE c.created_at >= @__today_0
[13:42:55 INF] Executed DbCommand (0ms) [Parameters=[@__yesterday_0='?' (DbType = DateTime), @__today_1='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT DISTINCT c.user_id
FROM conversations AS c
WHERE c.updated_at >= @__yesterday_0 AND c.updated_at < @__today_1
[13:42:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
[13:42:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM agents AS a
WHERE a.status = 'Published'
[13:42:55 INF] Executed DbCommand (1ms) [Parameters=[@__oneHourAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT c.agent_id AS "AgentId", count(*)::int AS "Count"
FROM conversations AS c
WHERE c.created_at >= @__oneHourAgo_0 AND c.agent_id <> '00000000-0000-0000-0000-000000000000'
GROUP BY c.agent_id
[13:42:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM conversations AS c
[13:42:55 INF] Executed DbCommand (0ms) [Parameters=[@__oneDayAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM conversations AS c
WHERE c.updated_at >= @__oneDayAgo_0
[13:42:55 INF] Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT c.id AS "Id", c.message_count AS "MessageCount"
FROM conversations AS c
[13:42:55 INF] Executed DbCommand (0ms) [Parameters=[@__AddHours_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT c.created_at AS "CreatedAt", c.updated_at AS "UpdatedAt"
FROM conversations AS c
WHERE c.updated_at < @__AddHours_0
[13:42:55 INF] Executed DbCommand (1ms) [Parameters=[@__today_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT t."Model", t."SubscriptionId", COALESCE(sum(t."Tokens"), 0)::int AS "TotalTokens"
FROM "TokenUsages" AS t
WHERE t."CreatedAt" >= @__today_0
GROUP BY t."Model", t."SubscriptionId"
[13:42:55 INF] Executed DbCommand (1ms) [Parameters=[@__subscriptionIds_0='?' (DbType = Object)], CommandType='Text', CommandTimeout='30']
SELECT s."Id", s."CustomerUserId", s0."Tier"
FROM "Subscriptions" AS s
INNER JOIN "SubscriptionPlans" AS s0 ON s."PlanId" = s0."Id"
WHERE s."Id" = ANY (@__subscriptionIds_0) AND s."Status" = 'Active'
[13:42:55 INF] Executed DbCommand (1ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT o."Type" AS "OrderType", (
    SELECT COALESCE(sum(o0."FinalAmount"), 0.0)
    FROM "Orders" AS o0
    WHERE o0."Status" = 1 AND o0."UpdatedAt" >= @__thirtyDaysAgo_0 AND o."Type" = o0."Type") AS "TotalAmount"
FROM "Orders" AS o
WHERE o."Status" = 1 AND o."UpdatedAt" >= @__thirtyDaysAgo_0
GROUP BY o."Type"
[13:42:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM "Orders" AS o
WHERE o."CreatedAt" >= @__thirtyDaysAgo_0
[13:42:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM "Orders" AS o
WHERE o."CreatedAt" >= @__thirtyDaysAgo_0 AND o."Status" = 1
[13:42:55 INF] Executed DbCommand (0ms) [Parameters=[@__thirtyDaysAgo_0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT count(*)::int
FROM (
    SELECT DISTINCT o."CustomerUserId"
    FROM "Orders" AS o
    WHERE o."Status" = 1 AND o."UpdatedAt" >= @__thirtyDaysAgo_0
) AS o0
[13:42:55 INF] Business metrics collected successfully
[13:43:35 WRN] Connection Failed: rabbitmq://localhost/
RabbitMQ.Client.Exceptions.BrokerUnreachableException: None of the specified endpoints were reachable
 ---> System.AggregateException: One or more errors occurred. (Connection failed)
 ---> RabbitMQ.Client.Exceptions.ConnectFailureException: Connection failed
 ---> System.Net.Sockets.SocketException (61): Connection refused
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Threading.Tasks.ValueTask.ValueTaskSourceAsTask.<>c.<.cctor>b__4_0(Object state)
--- End of stack trace from previous location ---
   at RabbitMQ.Client.TcpClientAdapter.ConnectAsync(String host, Int32 port)
   at RabbitMQ.Client.Impl.TaskExtensions.TimeoutAfter(Task task, TimeSpan timeout)
   at RabbitMQ.Client.Impl.SocketFrameHandler.ConnectOrFail(ITcpClient socket, AmqpTcpEndpoint endpoint, TimeSpan timeout)
   --- End of inner exception stack trace ---
   at RabbitMQ.Client.Impl.SocketFrameHandler.ConnectOrFail(ITcpClient socket, AmqpTcpEndpoint endpoint, TimeSpan timeout)
   at RabbitMQ.Client.Impl.SocketFrameHandler.ConnectUsingAddressFamily(AmqpTcpEndpoint endpoint, Func`2 socketFactory, TimeSpan timeout, AddressFamily family)
   at RabbitMQ.Client.Impl.SocketFrameHandler.ConnectUsingIPv4(AmqpTcpEndpoint endpoint, Func`2 socketFactory, TimeSpan timeout)
   at RabbitMQ.Client.Impl.SocketFrameHandler..ctor(AmqpTcpEndpoint endpoint, Func`2 socketFactory, TimeSpan connectionTimeout, TimeSpan readTimeout, TimeSpan writeTimeout)
   at RabbitMQ.Client.Framing.Impl.IProtocolExtensions.CreateFrameHandler(IProtocol protocol, AmqpTcpEndpoint endpoint, ArrayPool`1 pool, Func`2 socketFactory, TimeSpan connectionTimeout, TimeSpan readTimeout, TimeSpan writeTimeout)
   at RabbitMQ.Client.ConnectionFactory.CreateFrameHandler(AmqpTcpEndpoint endpoint)
   at RabbitMQ.Client.EndpointResolverExtensions.SelectOne[T](IEndpointResolver resolver, Func`2 selector)
   --- End of inner exception stack trace ---
   at RabbitMQ.Client.EndpointResolverExtensions.SelectOne[T](IEndpointResolver resolver, Func`2 selector)
   at RabbitMQ.Client.ConnectionFactory.CreateConnection(IEndpointResolver endpointResolver, String clientProvidedName)
   --- End of inner exception stack trace ---
   at RabbitMQ.Client.ConnectionFactory.CreateConnection(IEndpointResolver endpointResolver, String clientProvidedName)
   at RabbitMQ.Client.ConnectionFactory.CreateConnection(IList`1 hostnames, String clientProvidedName)
   at MassTransit.RabbitMqTransport.ConnectionContextFactory.CreateConnection(ISupervisor supervisor) in /_/src/Transports/MassTransit.RabbitMqTransport/RabbitMqTransport/ConnectionContextFactory.cs:line 85
[13:43:35 WRN] Server whimlabai-lewvandemacbook-pro:96831:bc7ce549 encountered an exception while sending heartbeat
Npgsql.NpgsqlException (0x80004005): Failed to connect to 127.0.0.1:5432
 ---> System.Net.Sockets.SocketException (61): Connection refused
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Hangfire.PostgreSql.PostgreSqlStorage.CreateAndOpenConnection()
   at Hangfire.PostgreSql.PostgreSqlStorage.UseConnection[T](DbConnection dedicatedConnection, Func`2 func)
   at Hangfire.PostgreSql.PostgreSqlConnection.Heartbeat(String serverId)
   at Hangfire.Server.ServerHeartbeatProcess.Execute(BackgroundProcessContext context) in C:\projects\hangfire-525\src\Hangfire.Core\Server\ServerHeartbeatProcess.cs:line 50
[13:43:39 ERR] Failed executing DbCommand (1ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT a."Id", a."AdminUserId", a."City", a."Country", a."CreatedAt", a."DeviceId", a."DeviceName", a."ExpiresAt", a."IpAddress", a."IsActive", a."IsAnomalousLocation", a."LastActivityAt", a."Latitude", a."Longitude", a."MfaVerified", a."MfaVerifiedAt", a."RefreshToken", a."RefreshTokenExpiresAt", a."Region", a."RequiresMfa", a."SessionToken", a."UpdatedAt", a."UpdatedBy", a."UserAgent"
FROM "AdminUserSessions" AS a
WHERE a."IsActive"
[13:43:39 INF] A transient exception occurred during execution. The operation will be retried after 0ms.
Npgsql.NpgsqlException (0x80004005): Exception while reading from stream
 ---> System.IO.EndOfStreamException: Attempted to read past the end of the stream.
   at Npgsql.Internal.NpgsqlReadBuffer.<Ensure>g__EnsureLong|55_0(NpgsqlReadBuffer buffer, Int32 count, Boolean async, Boolean readingNotifications)
   at Npgsql.Internal.NpgsqlReadBuffer.<Ensure>g__EnsureLong|55_0(NpgsqlReadBuffer buffer, Int32 count, Boolean async, Boolean readingNotifications)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
Npgsql.NpgsqlException (0x80004005): Exception while reading from stream
 ---> System.IO.EndOfStreamException: Attempted to read past the end of the stream.
   at Npgsql.Internal.NpgsqlReadBuffer.<Ensure>g__EnsureLong|55_0(NpgsqlReadBuffer buffer, Int32 count, Boolean async, Boolean readingNotifications)
   at Npgsql.Internal.NpgsqlReadBuffer.<Ensure>g__EnsureLong|55_0(NpgsqlReadBuffer buffer, Int32 count, Boolean async, Boolean readingNotifications)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
[13:43:39 ERR] An error occurred using the connection to database 'whimlabai' on server 'tcp://localhost:5432'.
[13:43:39 INF] A transient exception occurred during execution. The operation will be retried after 1086ms.
Npgsql.NpgsqlException (0x80004005): Failed to connect to 127.0.0.1:5432
 ---> System.Net.Sockets.SocketException (61): Connection refused
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at Npgsql.TaskTimeoutAndCancellation.ExecuteAsync(Func`2 getTaskFunc, NpgsqlTimeout timeout, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.ConnectAsync(NpgsqlTimeout timeout, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.ConnectAsync(NpgsqlTimeout timeout, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenInternalAsync(Boolean errorsExpected, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenInternalAsync(Boolean errorsExpected, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenAsync(CancellationToken cancellationToken, Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
Npgsql.NpgsqlException (0x80004005): Failed to connect to 127.0.0.1:5432
 ---> System.Net.Sockets.SocketException (61): Connection refused
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at Npgsql.TaskTimeoutAndCancellation.ExecuteAsync(Func`2 getTaskFunc, NpgsqlTimeout timeout, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.ConnectAsync(NpgsqlTimeout timeout, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.ConnectAsync(NpgsqlTimeout timeout, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenInternalAsync(Boolean errorsExpected, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenInternalAsync(Boolean errorsExpected, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenAsync(CancellationToken cancellationToken, Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
[13:43:40 ERR] An error occurred using the connection to database 'whimlabai' on server 'tcp://localhost:5432'.
[13:43:40 INF] A transient exception occurred during execution. The operation will be retried after 3297ms.
Npgsql.NpgsqlException (0x80004005): Failed to connect to 127.0.0.1:5432
 ---> System.Net.Sockets.SocketException (61): Connection refused
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at Npgsql.TaskTimeoutAndCancellation.ExecuteAsync(Func`2 getTaskFunc, NpgsqlTimeout timeout, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.ConnectAsync(NpgsqlTimeout timeout, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.ConnectAsync(NpgsqlTimeout timeout, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenInternalAsync(Boolean errorsExpected, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenInternalAsync(Boolean errorsExpected, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenAsync(CancellationToken cancellationToken, Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
Npgsql.NpgsqlException (0x80004005): Failed to connect to 127.0.0.1:5432
 ---> System.Net.Sockets.SocketException (61): Connection refused
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at Npgsql.TaskTimeoutAndCancellation.ExecuteAsync(Func`2 getTaskFunc, NpgsqlTimeout timeout, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.ConnectAsync(NpgsqlTimeout timeout, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.ConnectAsync(NpgsqlTimeout timeout, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenInternalAsync(Boolean errorsExpected, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenInternalAsync(Boolean errorsExpected, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenAsync(CancellationToken cancellationToken, Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
[13:43:41 WRN] Connection Failed: rabbitmq://localhost/
RabbitMQ.Client.Exceptions.BrokerUnreachableException: None of the specified endpoints were reachable
 ---> System.AggregateException: One or more errors occurred. (Connection failed)
 ---> RabbitMQ.Client.Exceptions.ConnectFailureException: Connection failed
 ---> System.Net.Sockets.SocketException (61): Connection refused
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Threading.Tasks.ValueTask.ValueTaskSourceAsTask.<>c.<.cctor>b__4_0(Object state)
--- End of stack trace from previous location ---
   at RabbitMQ.Client.TcpClientAdapter.ConnectAsync(String host, Int32 port)
   at RabbitMQ.Client.Impl.TaskExtensions.TimeoutAfter(Task task, TimeSpan timeout)
   at RabbitMQ.Client.Impl.SocketFrameHandler.ConnectOrFail(ITcpClient socket, AmqpTcpEndpoint endpoint, TimeSpan timeout)
   --- End of inner exception stack trace ---
   at RabbitMQ.Client.Impl.SocketFrameHandler.ConnectOrFail(ITcpClient socket, AmqpTcpEndpoint endpoint, TimeSpan timeout)
   at RabbitMQ.Client.Impl.SocketFrameHandler.ConnectUsingAddressFamily(AmqpTcpEndpoint endpoint, Func`2 socketFactory, TimeSpan timeout, AddressFamily family)
   at RabbitMQ.Client.Impl.SocketFrameHandler.ConnectUsingIPv4(AmqpTcpEndpoint endpoint, Func`2 socketFactory, TimeSpan timeout)
   at RabbitMQ.Client.Impl.SocketFrameHandler..ctor(AmqpTcpEndpoint endpoint, Func`2 socketFactory, TimeSpan connectionTimeout, TimeSpan readTimeout, TimeSpan writeTimeout)
   at RabbitMQ.Client.Framing.Impl.IProtocolExtensions.CreateFrameHandler(IProtocol protocol, AmqpTcpEndpoint endpoint, ArrayPool`1 pool, Func`2 socketFactory, TimeSpan connectionTimeout, TimeSpan readTimeout, TimeSpan writeTimeout)
   at RabbitMQ.Client.ConnectionFactory.CreateFrameHandler(AmqpTcpEndpoint endpoint)
   at RabbitMQ.Client.EndpointResolverExtensions.SelectOne[T](IEndpointResolver resolver, Func`2 selector)
   --- End of inner exception stack trace ---
   at RabbitMQ.Client.EndpointResolverExtensions.SelectOne[T](IEndpointResolver resolver, Func`2 selector)
   at RabbitMQ.Client.ConnectionFactory.CreateConnection(IEndpointResolver endpointResolver, String clientProvidedName)
   --- End of inner exception stack trace ---
   at RabbitMQ.Client.ConnectionFactory.CreateConnection(IEndpointResolver endpointResolver, String clientProvidedName)
   at RabbitMQ.Client.ConnectionFactory.CreateConnection(IList`1 hostnames, String clientProvidedName)
   at MassTransit.RabbitMqTransport.ConnectionContextFactory.CreateConnection(ISupervisor supervisor) in /_/src/Transports/MassTransit.RabbitMqTransport/RabbitMqTransport/ConnectionContextFactory.cs:line 85
[13:43:44 ERR] An error occurred using the connection to database 'whimlabai' on server 'tcp://localhost:5432'.
[13:43:44 ERR] An exception occurred while iterating over the results of a query for context type 'WhimLabAI.Infrastructure.Data.WhimLabAIDbContext'.
Microsoft.EntityFrameworkCore.Storage.RetryLimitExceededException: The maximum number of retries (3) was exceeded while executing database operations with 'NpgsqlRetryingExecutionStrategy'. See the inner exception for the most recent failure.
 ---> Npgsql.NpgsqlException (0x80004005): Failed to connect to 127.0.0.1:5432
 ---> System.Net.Sockets.SocketException (61): Connection refused
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at Npgsql.TaskTimeoutAndCancellation.ExecuteAsync(Func`2 getTaskFunc, NpgsqlTimeout timeout, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.ConnectAsync(NpgsqlTimeout timeout, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.ConnectAsync(NpgsqlTimeout timeout, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenInternalAsync(Boolean errorsExpected, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenInternalAsync(Boolean errorsExpected, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenAsync(CancellationToken cancellationToken, Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
Microsoft.EntityFrameworkCore.Storage.RetryLimitExceededException: The maximum number of retries (3) was exceeded while executing database operations with 'NpgsqlRetryingExecutionStrategy'. See the inner exception for the most recent failure.
 ---> Npgsql.NpgsqlException (0x80004005): Failed to connect to 127.0.0.1:5432
 ---> System.Net.Sockets.SocketException (61): Connection refused
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at Npgsql.TaskTimeoutAndCancellation.ExecuteAsync(Func`2 getTaskFunc, NpgsqlTimeout timeout, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.ConnectAsync(NpgsqlTimeout timeout, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.ConnectAsync(NpgsqlTimeout timeout, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenInternalAsync(Boolean errorsExpected, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenInternalAsync(Boolean errorsExpected, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenAsync(CancellationToken cancellationToken, Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
[13:43:44 ERR] 清理超时会话时发生错误
Microsoft.EntityFrameworkCore.Storage.RetryLimitExceededException: The maximum number of retries (3) was exceeded while executing database operations with 'NpgsqlRetryingExecutionStrategy'. See the inner exception for the most recent failure.
 ---> Npgsql.NpgsqlException (0x80004005): Failed to connect to 127.0.0.1:5432
 ---> System.Net.Sockets.SocketException (61): Connection refused
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at Npgsql.TaskTimeoutAndCancellation.ExecuteAsync(Func`2 getTaskFunc, NpgsqlTimeout timeout, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.ConnectAsync(NpgsqlTimeout timeout, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.ConnectAsync(NpgsqlTimeout timeout, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenInternalAsync(Boolean errorsExpected, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenInternalAsync(Boolean errorsExpected, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenAsync(CancellationToken cancellationToken, Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at WhimLabAI.Infrastructure.Data.Repositories.Repository`1.GetAsync(Expression`1 predicate, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/WhimLabAI/BackEnd/WhimLabAI.Infrastructure/Data/Repositories/Repository.cs:line 31
   at WhimLabAI.Infrastructure.BackgroundServices.SessionCleanupService.CleanupExpiredSessionsAsync(CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/WhimLabAI/BackEnd/WhimLabAI.Infrastructure/BackgroundServices/SessionCleanupService.cs:line 76
[13:43:44 ERR] 会话清理过程中发生错误
Microsoft.EntityFrameworkCore.Storage.RetryLimitExceededException: The maximum number of retries (3) was exceeded while executing database operations with 'NpgsqlRetryingExecutionStrategy'. See the inner exception for the most recent failure.
 ---> Npgsql.NpgsqlException (0x80004005): Failed to connect to 127.0.0.1:5432
 ---> System.Net.Sockets.SocketException (61): Connection refused
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at Npgsql.TaskTimeoutAndCancellation.ExecuteAsync(Func`2 getTaskFunc, NpgsqlTimeout timeout, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.ConnectAsync(NpgsqlTimeout timeout, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.ConnectAsync(NpgsqlTimeout timeout, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenInternalAsync(Boolean errorsExpected, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenInternalAsync(Boolean errorsExpected, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenAsync(CancellationToken cancellationToken, Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at WhimLabAI.Infrastructure.Data.Repositories.Repository`1.GetAsync(Expression`1 predicate, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/WhimLabAI/BackEnd/WhimLabAI.Infrastructure/Data/Repositories/Repository.cs:line 31
   at WhimLabAI.Infrastructure.BackgroundServices.SessionCleanupService.CleanupExpiredSessionsAsync(CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/WhimLabAI/BackEnd/WhimLabAI.Infrastructure/BackgroundServices/SessionCleanupService.cs:line 76
   at WhimLabAI.Infrastructure.BackgroundServices.SessionCleanupService.ExecuteAsync(CancellationToken stoppingToken) in /Users/<USER>/RiderProjects/WhimLabAI/BackEnd/WhimLabAI.Infrastructure/BackgroundServices/SessionCleanupService.cs:line 48
[13:43:45 INF] Server whimlabai-lewvandemacbook-pro:96831:bc7ce549 caught stopping signal...
[13:43:45 INF] Application is shutting down...
[13:43:45 INF] Server whimlabai-lewvandemacbook-pro:96831:bc7ce549 All dispatchers stopped
[13:43:45 WRN] Server whimlabai-lewvandemacbook-pro:96831:bc7ce549 there was an exception, server may not be removed
Npgsql.NpgsqlException (0x80004005): Failed to connect to 127.0.0.1:5432
 ---> System.Net.Sockets.SocketException (61): Connection refused
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Hangfire.PostgreSql.PostgreSqlStorage.CreateAndOpenConnection()
   at Hangfire.PostgreSql.PostgreSqlStorage.UseConnection[T](DbConnection dedicatedConnection, Func`2 func)
   at Hangfire.PostgreSql.PostgreSqlConnection.RemoveServer(String serverId)
   at Hangfire.Server.BackgroundServerProcess.ServerDelete(BackgroundServerContext context, Stopwatch stoppedAt) in C:\projects\hangfire-525\src\Hangfire.Core\Server\BackgroundServerProcess.cs:line 214
[13:43:45 INF] Bus stopped: rabbitmq://localhost/
[13:43:45 INF] 会话清理服务已停止
