<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\..\BackEnd\WhimLabAI.Application\WhimLabAI.Application.csproj" />
      <ProjectReference Include="..\..\..\BackEnd\WhimLabAI.Abstractions\WhimLabAI.Abstractions.csproj" />
      <ProjectReference Include="..\..\..\BackEnd\WhimLabAI.Shared\WhimLabAI.Shared.csproj" />
      <ProjectReference Include="..\..\..\BackEnd\WhimLabAI.Infrastructure\WhimLabAI.Infrastructure.csproj" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="$(AspNetCoreVersion)" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="$(JwtBearerVersion)" />
        <PackageReference Include="prometheus-net.AspNetCore" Version="8.2.1" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="$(SwashbuckleVersion)" />
        <PackageReference Include="Serilog.AspNetCore" Version="$(SerilogAspNetCoreVersion)" />
        <PackageReference Include="AspNetCore.HealthChecks.NpgSql" Version="9.0.0" />
        <PackageReference Include="AspNetCore.HealthChecks.Redis" Version="8.0.2" />
        <PackageReference Include="AspNetCore.HealthChecks.RabbitMQ" Version="9.0.0" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning" Version="5.1.0" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning.ApiExplorer" Version="5.1.0" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.7">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Hangfire.Core" Version="1.8.16" />
        <PackageReference Include="Hangfire.AspNetCore" Version="1.8.16" />
        <PackageReference Include="Hangfire.PostgreSql" Version="1.20.12" />
        <PackageReference Include="FluentValidation" Version="$(FluentValidationVersion)" />
    </ItemGroup>

</Project>
