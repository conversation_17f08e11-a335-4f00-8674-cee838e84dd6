从 /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/WebApi/WhimLabAI.WebApi/Properties/launchSettings.json 使用启动设置...
正在生成...
/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/WebApi/WhimLabAI.WebApi/WhimLabAI.WebApi.csproj : warning NU1603: WhimLabAI.WebApi depends on AspNetCore.HealthChecks.Redis (>= 8.0.2) but AspNetCore.HealthChecks.Redis 8.0.2 was not found. AspNetCore.HealthChecks.Redis 9.0.0 was resolved instead.
/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/WebApi/WhimLabAI.WebApi/WhimLabAI.WebApi.csproj : warning NU1603: WhimLabAI.WebApi depends on Serilog.AspNetCore (>= 8.0.5) but Serilog.AspNetCore 8.0.5 was not found. Serilog.AspNetCore 9.0.0 was resolved instead.
/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/WebApi/WhimLabAI.WebApi/WhimLabAI.WebApi.csproj : warning NU1603: WhimLabAI.WebApi depends on AspNetCore.HealthChecks.Redis (>= 8.0.2) but AspNetCore.HealthChecks.Redis 8.0.2 was not found. AspNetCore.HealthChecks.Redis 9.0.0 was resolved instead.
/Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/WebApi/WhimLabAI.WebApi/WhimLabAI.WebApi.csproj : warning NU1603: WhimLabAI.WebApi depends on Serilog.AspNetCore (>= 8.0.5) but Serilog.AspNetCore 8.0.5 was not found. Serilog.AspNetCore 9.0.0 was resolved instead.
[06:35:16 INF] Starting WhimLabAI Web API
[06:35:17 INF] Starting database migration...
[06:35:17 WRN] The property 'Agent.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[06:35:17 WRN] The property 'AgentVersion.KnowledgeBases' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[06:35:17 WRN] The property 'AgentVersion.Plugins' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[06:35:17 WRN] The property 'MessageAttachment.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[06:35:17 WRN] The property 'CustomerProfile.CustomFields' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[06:35:17 WRN] The foreign key property 'ConversationMessage.ConversationId1' was created in shadow state because a conflicting property with the simple name 'ConversationId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
[06:35:17 WRN] The foreign key property 'UsageRecord.SubscriptionId1' was created in shadow state because a conflicting property with the simple name 'SubscriptionId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
[06:35:17 ERR] An error occurred using the connection to database 'whimlabai' on server 'tcp://localhost:5432'.
[06:35:17 ERR] An error occurred during migration or seeding
Npgsql.PostgresException (0x80004005): 28P01: password authentication failed for user "postgres"
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.Internal.NpgsqlConnector.AuthenticateSASL(List`1 mechanisms, String username, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Authenticate(String username, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenInternalAsync(Boolean errorsExpected, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenInternalAsync(Boolean errorsExpected, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenAsync(CancellationToken cancellationToken, Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.HistoryRepository.GetAppliedMigrationsAsync(CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Migrations.Internal.NpgsqlHistoryRepository.GetAppliedMigrationsAsync(CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Migrations.Internal.NpgsqlMigrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at WhimLabAI.WebApi.Extensions.MigrationExtensions.MigrateAndSeedAsync(WebApplication app) in /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/WebApi/WhimLabAI.WebApi/Extensions/MigrationExtensions.cs:line 20
  Exception data:
    Severity: FATAL
    SqlState: 28P01
    MessageText: password authentication failed for user "postgres"
    File: auth.c
    Line: 321
    Routine: auth_failed
[06:35:17 FTL] Application terminated unexpectedly
Npgsql.PostgresException (0x80004005): 28P01: password authentication failed for user "postgres"
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.Internal.NpgsqlConnector.AuthenticateSASL(List`1 mechanisms, String username, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Authenticate(String username, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenInternalAsync(Boolean errorsExpected, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenInternalAsync(Boolean errorsExpected, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenAsync(CancellationToken cancellationToken, Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.HistoryRepository.GetAppliedMigrationsAsync(CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Migrations.Internal.NpgsqlHistoryRepository.GetAppliedMigrationsAsync(CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Migrations.Internal.NpgsqlMigrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at WhimLabAI.WebApi.Extensions.MigrationExtensions.MigrateAndSeedAsync(WebApplication app) in /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/WebApi/WhimLabAI.WebApi/Extensions/MigrationExtensions.cs:line 20
   at Program.<Main>$(String[] args) in /Users/<USER>/RiderProjects/WhimLabAI/FrontEnd/WebApi/WhimLabAI.WebApi/Program.cs:line 240
  Exception data:
    Severity: FATAL
    SqlState: 28P01
    MessageText: password authentication failed for user "postgres"
    File: auth.c
    Line: 321
    Routine: auth_failed
