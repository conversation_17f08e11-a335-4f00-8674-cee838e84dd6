using Microsoft.EntityFrameworkCore;
using WhimLabAI.Infrastructure.Data;
using WhimLabAI.Infrastructure.Data.Migrations;

namespace Microsoft.Extensions.Hosting;

/// <summary>
/// 数据库初始化扩展方法
/// </summary>
public static class DatabaseInitializationExtensions
{
    /// <summary>
    /// 初始化数据库
    /// </summary>
    public static async Task<IHost> InitializeDatabaseAsync(this IHost host)
    {
        using var scope = host.Services.CreateScope();
        var services = scope.ServiceProvider;
        var logger = services.GetRequiredService<ILogger<WhimLabAIDbContext>>();

        try
        {
            logger.LogInformation("Initializing database...");
            
            var context = services.GetRequiredService<WhimLabAIDbContext>();
            
            // 应用迁移和种子数据
            await host.Services.CreateScope().ServiceProvider
                .GetRequiredService<IHost>()
                .MigrateDatabaseAsync();
            
            logger.LogInformation("Database initialization completed successfully");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while initializing the database");
            throw;
        }

        return host;
    }

    /// <summary>
    /// 确保数据库已创建
    /// </summary>
    public static async Task<IHost> EnsureDatabaseCreatedAsync(this IHost host)
    {
        using var scope = host.Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<WhimLabAIDbContext>();
        
        await context.Database.EnsureCreatedAsync();
        
        return host;
    }

    /// <summary>
    /// 删除并重新创建数据库（仅用于开发环境）
    /// </summary>
    public static async Task<IHost> RecreateDatabaseAsync(this IHost host)
    {
        using var scope = host.Services.CreateScope();
        var services = scope.ServiceProvider;
        var env = services.GetRequiredService<IHostEnvironment>();
        
        if (!env.IsDevelopment())
        {
            throw new InvalidOperationException("Database recreation is only allowed in development environment");
        }

        var context = services.GetRequiredService<WhimLabAIDbContext>();
        var logger = services.GetRequiredService<ILogger<WhimLabAIDbContext>>();

        logger.LogWarning("Deleting existing database...");
        await context.Database.EnsureDeletedAsync();
        
        logger.LogInformation("Creating new database...");
        await context.Database.EnsureCreatedAsync();
        
        logger.LogInformation("Running migrations and seed data...");
        await host.MigrateDatabaseAsync();

        return host;
    }
}