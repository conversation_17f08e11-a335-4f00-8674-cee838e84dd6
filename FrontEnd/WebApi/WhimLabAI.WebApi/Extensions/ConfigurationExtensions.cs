using System.Text.RegularExpressions;

namespace WhimLabAI.WebApi.Extensions;

/// <summary>
/// Configuration extensions for handling environment variables
/// </summary>
public static class ConfigurationExtensions
{
    /// <summary>
    /// Replace configuration placeholders with environment variables
    /// </summary>
    public static IConfigurationBuilder AddEnvironmentVariableReplacements(this IConfigurationBuilder builder)
    {
        builder.Add(new EnvironmentVariableReplacementConfigurationSource());
        return builder;
    }
}

/// <summary>
/// Configuration source for environment variable replacements
/// </summary>
public class EnvironmentVariableReplacementConfigurationSource : IConfigurationSource
{
    public IConfigurationProvider Build(IConfigurationBuilder builder)
    {
        var config = builder.Build();
        return new EnvironmentVariableReplacementConfigurationProvider(config);
    }
}

/// <summary>
/// Configuration provider that replaces ${VAR_NAME} placeholders with environment variables
/// </summary>
public class EnvironmentVariableReplacementConfigurationProvider : ConfigurationProvider
{
    private readonly IConfiguration _configuration;
    private static readonly Regex PlaceholderRegex = new(@"\$\{([^}:]+)(?::([^}]*))?\}", RegexOptions.Compiled);

    public EnvironmentVariableReplacementConfigurationProvider(IConfiguration configuration)
    {
        _configuration = configuration;
    }

    public override void Load()
    {
        var data = new Dictionary<string, string?>(StringComparer.OrdinalIgnoreCase);

        foreach (var kvp in _configuration.AsEnumerable())
        {
            if (kvp.Value != null)
            {
                data[kvp.Key] = ReplacePlaceholders(kvp.Value);
            }
            else
            {
                data[kvp.Key] = kvp.Value;
            }
        }

        Data = data;
    }

    private string ReplacePlaceholders(string value)
    {
        return PlaceholderRegex.Replace(value, match =>
        {
            var envVarName = match.Groups[1].Value;
            var defaultValue = match.Groups[2].Value;
            
            var envValue = Environment.GetEnvironmentVariable(envVarName);
            
            if (!string.IsNullOrEmpty(envValue))
            {
                return envValue;
            }
            
            if (!string.IsNullOrEmpty(defaultValue))
            {
                return defaultValue;
            }
            
            // Return the original placeholder if no value found
            return match.Value;
        });
    }
}

/// <summary>
/// Helper class for parsing array values from environment variables
/// </summary>
public static class EnvironmentVariableHelper
{
    /// <summary>
    /// Parse comma-separated string into array
    /// </summary>
    public static string[] ParseArray(string? value)
    {
        if (string.IsNullOrWhiteSpace(value))
            return Array.Empty<string>();

        return value.Split(',', StringSplitOptions.RemoveEmptyEntries)
                   .Select(s => s.Trim())
                   .ToArray();
    }
}