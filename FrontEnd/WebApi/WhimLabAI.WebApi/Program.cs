using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.IdentityModel.Tokens;
using Microsoft.IdentityModel.JsonWebTokens;
using Microsoft.OpenApi.Models;
using Serilog;
using Serilog.Events;
using System.Text;
using WhimLabAI.Application;
using WhimLabAI.Infrastructure;
using WhimLabAI.Infrastructure.Data;
using WhimLabAI.Infrastructure.Security;
using WhimLabAI.Infrastructure.ApiSecurity;
using Microsoft.AspNetCore.RateLimiting;
using WhimLabAI.WebApi.Extensions;
using WhimLabAI.WebApi.Middleware;
using WhimLabAI.WebApi.Filters;
using Prometheus;
using Hangfire;
using Hangfire.PostgreSql;
using WhimLabAI.Infrastructure.PaymentGateways;
using Microsoft.Extensions.Diagnostics.HealthChecks;

// 配置 Serilog
Log.Logger = new LoggerConfiguration()
    .MinimumLevel.Override("Microsoft", LogEventLevel.Information)
    .MinimumLevel.Override("Microsoft.AspNetCore", LogEventLevel.Warning)
    .Enrich.FromLogContext()
    .WriteTo.Console()
    .WriteTo.File("logs/whimlabai-.txt", rollingInterval: RollingInterval.Day)
    .CreateBootstrapLogger();

try
{
    Log.Information("Starting WhimLabAI Web API");
    
    var builder = WebApplication.CreateBuilder(args);

    // 配置文件加载顺序
    builder.Configuration
        .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
        .AddJsonFile($"appsettings.{builder.Environment.EnvironmentName}.json", optional: true, reloadOnChange: true)
        .AddJsonFile("appsettings.QuickDev.json", optional: true, reloadOnChange: true)
        .AddEnvironmentVariables()
        .AddEnvironmentVariableReplacements();

    // 配置 Serilog - using the bootstrap logger already created
    builder.Host.UseSerilog();

    // Add services to the container.
    builder.Services.AddControllers(options =>
    {
        // 添加全局CSRF保护（仅对需要认证的端点）
        if (!builder.Environment.IsDevelopment())
        {
            options.Filters.Add<WhimLabAI.WebApi.Filters.GlobalCsrfProtectionFilter>();
        }
    });
    
    // 配置 CORS
    builder.Services.AddCors(options =>
    {
        options.AddPolicy("AllowFrontends", policy =>
        {
            policy.WithOrigins(
                "https://localhost:7216", // Admin HTTPS
                "http://localhost:5139",  // Admin HTTP
                "https://localhost:7040", // Customer HTTPS
                "http://localhost:5076"   // Customer HTTP
            )
            .AllowAnyMethod()
            .AllowAnyHeader()
            .AllowCredentials()
            .WithExposedHeaders("Token-Expired", "X-Correlation-ID");
        });
        
        options.AddPolicy("AllowFrontend", policy =>
        {
            var corsSettings = builder.Configuration.GetSection("CorsSettings");
            var allowedOriginsConfig = corsSettings["AllowedOrigins"];
            
            // Support both array format and comma-separated string from environment variables
            string[] allowedOrigins;
            if (!string.IsNullOrEmpty(allowedOriginsConfig) && allowedOriginsConfig.Contains(','))
            {
                allowedOrigins = EnvironmentVariableHelper.ParseArray(allowedOriginsConfig);
            }
            else
            {
                allowedOrigins = corsSettings.GetSection("AllowedOrigins").Get<string[]>() 
                    ?? new[] { "https://localhost:7216", "https://localhost:7040" };
            }
            
            policy.WithOrigins(allowedOrigins)
            .AllowAnyHeader()
            .AllowAnyMethod()
            .AllowCredentials() // 对于 SignalR 必需
            .WithExposedHeaders("Token-Expired", "X-Correlation-ID");
        });
        
        // 开发环境允许所有来源
        if (builder.Environment.IsDevelopment())
        {
            options.AddPolicy("DevelopmentCors", policy =>
            {
                policy.AllowAnyOrigin()
                      .AllowAnyHeader()
                      .AllowAnyMethod();
            });
        }
    });
    
    // 添加 HttpClient
    builder.Services.AddHttpClient();
    
    // 添加内存缓存（会话超时中间件需要）
    builder.Services.AddMemoryCache();
    
    // 添加响应缓存服务
    builder.Services.AddResponseCaching(options =>
    {
        options.MaximumBodySize = 10 * 1024 * 1024; // 10MB max cacheable response size
        options.SizeLimit = 100 * 1024 * 1024; // 100MB total cache size
        options.UseCaseSensitivePaths = false;
    });
    
    // 添加输出缓存（.NET 7+性能更好的替代方案）
    builder.Services.AddOutputCache(options =>
    {
        // 默认策略：缓存5分钟
        options.AddBasePolicy(builder => builder
            .Expire(TimeSpan.FromMinutes(5))
            .SetVaryByQuery("page", "pageSize", "search", "sort", "filter"));
        
        // 为公共数据定义特定策略
        options.AddPolicy("PublicData", builder => builder
            .Expire(TimeSpan.FromMinutes(10))
            .SetVaryByQuery("page", "pageSize", "search", "sort", "filter")
            .Tag("public"));
        
        // 为用户特定数据定义策略
        options.AddPolicy("UserSpecific", builder => builder
            .Expire(TimeSpan.FromMinutes(5))
            .SetVaryByHeader("Authorization")
            .SetVaryByQuery("page", "pageSize"));
        
        // 为静态数据定义长期缓存策略
        options.AddPolicy("StaticData", builder => builder
            .Expire(TimeSpan.FromHours(1))
            .Tag("static"));
    });
    
    // 配置 SignalR
    builder.Services.AddSignalR(options =>
    {
        options.EnableDetailedErrors = builder.Environment.IsDevelopment();
        options.MaximumReceiveMessageSize = 102400; // 100KB
    }).AddJsonProtocol(options =>
    {
        options.PayloadSerializerOptions.PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase;
    });
    
    // 注册 Infrastructure 和 Application 层服务
    builder.Services.AddInfrastructure(builder.Configuration);
    builder.Services.AddApplication(builder.Configuration);
    
    // 注册实时通知服务
    builder.Services.AddScoped<WhimLabAI.Abstractions.Application.IRealtimeNotificationService, 
        WhimLabAI.WebApi.Services.RealtimeNotificationService>();
    
    // 注册安全服务
    WhimLabAI.Infrastructure.Security.SecurityExtensions.AddSecurityServices(builder.Services, builder.Configuration);
    
    // 配置API版本控制
    builder.Services.AddApiVersioningConfiguration();
    
    // 配置输入验证
    builder.Services.AddInputValidation();
    
    // 配置速率限制
    builder.Services.AddRateLimitingConfiguration(builder.Configuration);
    
    // 配置会话超时
    builder.Services.Configure<WhimLabAI.Shared.Options.SessionTimeoutOptions>(
        builder.Configuration.GetSection(WhimLabAI.Shared.Options.SessionTimeoutOptions.SectionName));
    
    // 配置订阅定价
    builder.Services.Configure<WhimLabAI.Shared.Options.SubscriptionPricingOptions>(
        builder.Configuration.GetSection("SubscriptionPricing"));
    
    // 配置外部服务
    builder.Services.Configure<WhimLabAI.Shared.Options.ExternalServiceOptions>(
        builder.Configuration.GetSection("ExternalServices"));
    
    // 配置 API 文档
    builder.Services.AddEndpointsApiExplorer();
    builder.Services.AddSwaggerGen(options =>
    {
        options.SwaggerDoc("v1", new OpenApiInfo
        {
            Title = "WhimLabAI API",
            Version = "v1",
            Description = "Enterprise AI Agent Platform API"
        });
        
        // 解决控制器路由冲突
        options.ResolveConflictingActions(apiDescriptions => apiDescriptions.First());
        
        // 配置自定义 SchemaId 以避免类型名称冲突
        options.CustomSchemaIds(type =>
        {
            // 定义冲突类型集合
            var conflictingTypes = new HashSet<string> 
            { 
                "DeviceListDto", "DeviceDto", "PerformanceAlert", "PerformanceMetrics",
                "PermissionDto", "RoleDto", "UserDto", "AdminListDto", "AdminCustomerListDto"
            };
            
            // 递归函数来生成类型名称，包括处理冲突
            string GetTypeName(Type t)
            {
                if (!t.IsGenericType)
                {
                    // 非泛型类型
                    var name = t.Name;
                    
                    // 检查是否为冲突类型
                    if (conflictingTypes.Contains(name) && t.Namespace != null)
                    {
                        if (t.Namespace.Contains("Admin.Rbac"))
                            return $"Rbac_{name}";
                        else if (t.Namespace.Contains("Admin.Permission"))
                            return $"Permission_{name}";
                        else if (t.Namespace.Contains("Admin.Role"))
                            return $"Role_{name}";
                        else if (t.Namespace.Contains("Admin.User"))
                            return $"AdminUser_{name}";
                        else if (t.Namespace.Contains("Admin.Customer"))
                            return $"AdminCustomer_{name}";
                        else if (t.Namespace.Contains("Customer.Security"))
                            return $"CustomerSecurity_{name}";
                        else if (t.Namespace.Contains("Device") && !t.Namespace.Contains("Customer"))
                            return $"Device_{name}";
                        else if (t.Namespace.Contains("Performance"))
                            return $"Performance_{name}";
                        else
                        {
                            var namespaceParts = t.Namespace.Split('.');
                            if (namespaceParts.Length >= 2)
                            {
                                var prefix = namespaceParts[^2];
                                return $"{prefix}_{name}";
                            }
                        }
                    }
                    
                    return name;
                }
                else
                {
                    // 泛型类型
                    var genericTypeName = t.GetGenericTypeDefinition().Name.Split('`')[0];
                    var genericArguments = t.GetGenericArguments();
                    
                    // 递归处理泛型参数
                    var genericNames = genericArguments.Select(arg => GetTypeName(arg));
                    
                    return $"{genericTypeName}Of{string.Join("And", genericNames)}";
                }
            }
            
            return GetTypeName(type);
        });
        
        // 添加 JWT 认证
        options.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
        {
            Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
            Name = "Authorization",
            In = ParameterLocation.Header,
            Type = SecuritySchemeType.ApiKey,
            Scheme = "Bearer"
        });
        
        // 添加 API Key 认证
        options.AddSecurityDefinition("ApiKey", new OpenApiSecurityScheme
        {
            Description = "API Key Authorization. Example: \"X-API-Key: {api_key}\"",
            Name = "X-API-Key",
            In = ParameterLocation.Header,
            Type = SecuritySchemeType.ApiKey
        });
        
        options.AddSecurityRequirement(new OpenApiSecurityRequirement
        {
            {
                new OpenApiSecurityScheme
                {
                    Reference = new OpenApiReference
                    {
                        Type = ReferenceType.SecurityScheme,
                        Id = "Bearer"
                    }
                },
                Array.Empty<string>()
            },
            {
                new OpenApiSecurityScheme
                {
                    Reference = new OpenApiReference
                    {
                        Type = ReferenceType.SecurityScheme,
                        Id = "ApiKey"
                    }
                },
                Array.Empty<string>()
            }
        });
    });


    // 配置健康检查
    // Configure health checks
    var healthChecksBuilder = builder.Services.AddHealthChecks()
        .AddNpgSql(
            builder.Configuration.GetConnectionString("DefaultConnection")!, 
            name: "postgresql",
            tags: new[] { "db", "sql", "postgresql" })
        .AddRedis(
            builder.Configuration.GetConnectionString("Redis")!, 
            name: "redis",
            tags: new[] { "cache", "redis" });
    
    // Add RabbitMQ health check
    var rabbitMQConfig = builder.Configuration.GetSection("RabbitMQ");
    if (rabbitMQConfig.Exists())
    {
        var rabbitMQConnectionString = $"amqp://{rabbitMQConfig["Username"]}:{rabbitMQConfig["Password"]}@{rabbitMQConfig["Host"]}:{rabbitMQConfig["Port"]}/{rabbitMQConfig["VirtualHost"]}";
        healthChecksBuilder.AddRabbitMQ(
            name: "rabbitmq",
            tags: new[] { "messaging", "rabbitmq" });
    }
    
    // Add custom MinIO health check
    healthChecksBuilder.AddTypeActivatedCheck<WhimLabAI.WebApi.HealthChecks.MinioHealthCheck>(
        "minio", HealthStatus.Unhealthy,
        tags: new[] { "storage", "minio" });
    
    // Add business logic health check
    healthChecksBuilder.AddTypeActivatedCheck<WhimLabAI.WebApi.HealthChecks.BusinessHealthCheck>(
        "business", HealthStatus.Unhealthy,
        tags: new[] { "business", "ready" });

    // 配置响应压缩
    builder.Services.AddResponseCompression(options =>
    {
        options.EnableForHttps = true;
    });
    
    // 注册API安全服务
    builder.Services.AddApiSecurityServices(builder.Configuration);
    
    // 配置 Hangfire - defer connection string resolution
    builder.Services.AddHangfire((serviceProvider, configuration) =>
    {
        var config = serviceProvider.GetRequiredService<IConfiguration>();
        var connectionString = config.GetConnectionString("DefaultConnection");
        configuration
            .SetDataCompatibilityLevel(CompatibilityLevel.Version_180)
            .UseSimpleAssemblyNameTypeSerializer()
            .UseRecommendedSerializerSettings()
            .UsePostgreSqlStorage(options => options
                .UseNpgsqlConnection(connectionString));
    });
    
    // 添加 Hangfire 服务器
    builder.Services.AddHangfireServer(options =>
    {
        options.WorkerCount = Environment.ProcessorCount * 2;
        options.Queues = new[] { "critical", "default", "low" };
        options.ServerName = $"WhimLabAI-{Environment.MachineName}";
    });
    
    // 清除默认的JWT Claim类型映射，保持claim类型的一致性
    System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler.DefaultInboundClaimTypeMap.Clear();
    System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler.DefaultOutboundClaimTypeMap.Clear();
    
    // 配置 JWT 认证
    var jwtSettings = builder.Configuration.GetSection("Jwt");
    var logger = builder.Services.BuildServiceProvider().GetRequiredService<ILogger<Program>>();
    var secretKey = WhimLabAI.WebApi.Configuration.JwtSecurityKeyGenerator.GetOrGenerateSecretKey(builder.Configuration, logger);
    var key = Encoding.UTF8.GetBytes(secretKey);
    
    builder.Services.AddAuthentication(options =>
    {
        options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
        options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
        options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
    })
    .AddJwtBearer(options =>
    {
        options.RequireHttpsMetadata = !builder.Environment.IsDevelopment();
        options.SaveToken = true;
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuerSigningKey = true,
            IssuerSigningKey = new SymmetricSecurityKey(key),
            ValidateIssuer = true,
            ValidIssuer = jwtSettings["Issuer"],
            ValidateAudience = true,
            ValidAudience = jwtSettings["Audience"],
            ValidateLifetime = true,
            ClockSkew = TimeSpan.Zero
        };
        
        // SignalR JWT 配置和会话验证
        options.Events = new JwtBearerEvents
        {
            OnMessageReceived = context =>
            {
                var accessToken = context.Request.Query["access_token"];
                var path = context.HttpContext.Request.Path;
                
                if (!string.IsNullOrEmpty(accessToken) && path.StartsWithSegments("/hubs"))
                {
                    context.Token = accessToken;
                }
                
                return Task.CompletedTask;
            },
            OnTokenValidated = async context =>
            {
                var sessionValidationHandler = context.HttpContext.RequestServices
                    .GetRequiredService<WhimLabAI.WebApi.Authentication.JwtSessionValidationHandler>();
                await sessionValidationHandler.OnTokenValidated(context);
            },
            OnAuthenticationFailed = async context =>
            {
                var sessionValidationHandler = context.HttpContext.RequestServices
                    .GetRequiredService<WhimLabAI.WebApi.Authentication.JwtSessionValidationHandler>();
                await sessionValidationHandler.OnAuthenticationFailed(context);
            },
            OnChallenge = async context =>
            {
                // Prevent the default behavior of JWT Bearer which returns empty body
                context.HandleResponse();
                
                var sessionValidationHandler = context.HttpContext.RequestServices
                    .GetRequiredService<WhimLabAI.WebApi.Authentication.JwtSessionValidationHandler>();
                await sessionValidationHandler.OnChallenge(context);
                
                // Write custom error response
                context.Response.StatusCode = 401;
                context.Response.ContentType = "application/json";
                
                var error = new
                {
                    success = false,
                    message = context.ErrorDescription ?? "未授权访问",
                    errorCode = "UNAUTHORIZED"
                };
                
                await context.Response.WriteAsync(System.Text.Json.JsonSerializer.Serialize(error));
            }
        };
    })
    .AddJwtBearer("AdminJwt", options =>
    {
        options.RequireHttpsMetadata = !builder.Environment.IsDevelopment();
        options.SaveToken = true;
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuerSigningKey = true,
            IssuerSigningKey = new SymmetricSecurityKey(key),
            ValidateIssuer = true,
            ValidIssuer = jwtSettings["Issuer"],
            ValidateAudience = true,
            ValidAudience = jwtSettings["Audience"],
            ValidateLifetime = true,
            ClockSkew = TimeSpan.Zero,
            RoleClaimType = "role" // Ensure role claims are recognized
        };
        
        options.Events = new JwtBearerEvents
        {
            OnTokenValidated = async context =>
            {
                var sessionValidationHandler = context.HttpContext.RequestServices
                    .GetRequiredService<WhimLabAI.WebApi.Authentication.JwtSessionValidationHandler>();
                await sessionValidationHandler.OnTokenValidated(context);
            },
            OnAuthenticationFailed = async context =>
            {
                var sessionValidationHandler = context.HttpContext.RequestServices
                    .GetRequiredService<WhimLabAI.WebApi.Authentication.JwtSessionValidationHandler>();
                await sessionValidationHandler.OnAuthenticationFailed(context);
            },
            OnChallenge = async context =>
            {
                // Prevent the default behavior of JWT Bearer which returns empty body
                context.HandleResponse();
                
                var sessionValidationHandler = context.HttpContext.RequestServices
                    .GetRequiredService<WhimLabAI.WebApi.Authentication.JwtSessionValidationHandler>();
                await sessionValidationHandler.OnChallenge(context);
                
                // Write custom error response
                context.Response.StatusCode = 401;
                context.Response.ContentType = "application/json";
                
                var error = new
                {
                    success = false,
                    message = context.ErrorDescription ?? "未授权访问",
                    errorCode = "UNAUTHORIZED"
                };
                
                await context.Response.WriteAsync(System.Text.Json.JsonSerializer.Serialize(error));
            }
        };
    });
    
    builder.Services.AddAuthorization(options =>
    {
        // Add Admin policy
        options.AddPolicy("Admin", policy =>
        {
            policy.RequireAuthenticatedUser();
            policy.RequireRole("Admin", "SuperAdmin");
        });
        
        // Add SuperAdmin policy
        options.AddPolicy("SuperAdmin", policy =>
        {
            policy.RequireAuthenticatedUser();
            policy.RequireRole("SuperAdmin");
        });
        
        // Add Customer policy
        options.AddPolicy("Customer", policy =>
        {
            policy.RequireAuthenticatedUser();
            policy.RequireClaim("UserType", "Customer");
        });
    });
    
    // 添加权限授权处理器
    builder.Services.AddSingleton<IAuthorizationPolicyProvider, WhimLabAI.WebApi.Authorization.PermissionPolicyProvider>();
    builder.Services.AddScoped<IAuthorizationHandler, WhimLabAI.WebApi.Authorization.PermissionAuthorizationHandler>();
    
    // 注册JWT会话验证处理器
    builder.Services.AddScoped<WhimLabAI.WebApi.Authentication.JwtSessionValidationHandler>();

    var app = builder.Build();

    // Configure the HTTP request pipeline.
    app.UseSerilogRequestLogging();

    // Use security headers middleware - should be early in the pipeline
    app.UseSecurityHeaders();

    // Use global exception handler
    app.UseGlobalExceptionHandler();

    // Add HTTPS redirection
    app.UseHttpsRedirection();
    
    // 启用 CORS - 必须在认证和路由之前
    if (app.Environment.IsDevelopment())
    {
        app.UseCors("AllowFrontends");
    }
    else
    {
        app.UseCors("AllowFrontends");
    }

    // Enable Swagger in Development
    if (app.Environment.IsDevelopment())
    {
        app.UseSwagger();
        app.UseSwaggerUI(c =>
        {
            c.SwaggerEndpoint("/swagger/v1/swagger.json", "WhimLabAI API v1");
            c.RoutePrefix = "swagger";
        });
        
        // Redirect root to Swagger
        app.MapGet("/", () => Results.Redirect("/swagger")).ExcludeFromDescription();
    }

    // 使用安全中间件
    WhimLabAI.Infrastructure.Security.SecurityExtensions.UseSecurityMiddleware(app, builder.Configuration);
    
    // 使用速率限制
    app.UseRateLimiter();
    
    // 使用响应缓存（必须在UseResponseCompression之前）
    app.UseResponseCaching();
    
    // 使用输出缓存（更现代的缓存方案）
    app.UseOutputCache();
    
    // 使用ETag支持（必须在响应压缩之前）
    app.UseETags();
    
    app.UseResponseCompression();
    
    // 配置静态文件服务（用于提供上传的文件）
    var uploadsPath = Path.Combine(builder.Environment.ContentRootPath, "wwwroot", "uploads");
    
    // 确保上传目录存在
    if (!Directory.Exists(uploadsPath))
    {
        Directory.CreateDirectory(uploadsPath);
        Log.Information("Created uploads directory at: {UploadsPath}", uploadsPath);
    }
    
    app.UseStaticFiles(new StaticFileOptions
    {
        FileProvider = new Microsoft.Extensions.FileProviders.PhysicalFileProvider(uploadsPath),
        RequestPath = "/uploads"
    });

    // 使用API安全中间件
    app.UseApiSecurity();
    
    // 配置 Hangfire Dashboard - only in non-container environment or after initialization
    if (app.Environment.IsDevelopment() || Environment.GetEnvironmentVariable("DOTNET_RUNNING_IN_CONTAINER") != "true")
    {
        app.UseHangfireDashboard("/hangfire", new DashboardOptions
        {
            DashboardTitle = "WhimLabAI Background Jobs",
            Authorization = new[] { new WhimLabAI.WebApi.Authorization.HangfireAuthorizationFilter() },
            DisplayStorageConnectionString = false
        });
    }
    
    app.UseAuthentication();
    
    // 使用会话活动跟踪中间件
    app.UseMiddleware<WhimLabAI.WebApi.Middleware.SessionActivityMiddleware>();
    
    app.UseAuthorization();
    
    // 使用支付签名验证中间件（必须在支付安全中间件之前）
    app.UsePaymentCallbackSignatureVerification();
    
    // 使用支付安全中间件
    app.UsePaymentSecurity();
    
    // 使用审计日志中间件
    app.UseMiddleware<WhimLabAI.Infrastructure.Auditing.AuditLogMiddleware>();
    
    // 使用领域事件分发中间件
    app.UseMiddleware<WhimLabAI.Infrastructure.Middleware.DomainEventDispatcherMiddleware>();
    
    // 使用指标收集中间件
    app.UseMiddleware<WhimLabAI.WebApi.Middleware.MetricsMiddleware>();
    
    // 配置Prometheus端点
    app.UseHttpMetrics(); // 自动收集HTTP指标
    
    app.MapControllers();
    
    // 映射Prometheus指标端点
    app.MapMetrics();
    
    // 映射 SignalR Hub
    app.MapHub<WhimLabAI.WebApi.Hubs.ConversationHub>("/hubs/conversation");
    app.MapHub<WhimLabAI.WebApi.Hubs.NotificationHub>("/hubs/notification");
    app.MapHub<WhimLabAI.WebApi.Hubs.QRCodeHub>("/hubs/qrcode");
    
    // 健康检查端点
    app.MapHealthChecks("/health", new Microsoft.AspNetCore.Diagnostics.HealthChecks.HealthCheckOptions
    {
        ResponseWriter = WhimLabAI.WebApi.HealthChecks.HealthCheckResponseWriter.WriteResponse,
        AllowCachingResponses = false
    });
    
    app.MapHealthChecks("/health/ready", new Microsoft.AspNetCore.Diagnostics.HealthChecks.HealthCheckOptions
    {
        Predicate = check => check.Tags.Contains("db") || check.Tags.Contains("cache") || check.Tags.Contains("messaging") || check.Tags.Contains("storage"),
        ResponseWriter = WhimLabAI.WebApi.HealthChecks.HealthCheckResponseWriter.WriteResponse,
        AllowCachingResponses = false
    });
    
    app.MapHealthChecks("/health/live", new Microsoft.AspNetCore.Diagnostics.HealthChecks.HealthCheckOptions
    {
        Predicate = _ => false,
        ResponseWriter = WhimLabAI.WebApi.HealthChecks.HealthCheckResponseWriter.WriteResponse,
        AllowCachingResponses = false
    });

    // 初始化数据库
    if (app.Environment.IsDevelopment())
    {
        await app.MigrateAndSeedAsync();
    }
    
    // Validate JWT configuration on startup
    var startupLogger = app.Services.GetRequiredService<ILogger<Program>>();
    var jwtConfig = app.Configuration.GetSection("Jwt");
    var jwtSecretKey = jwtConfig["SecretKey"];
    
    if (!WhimLabAI.WebApi.Configuration.JwtSecurityKeyGenerator.IsSecureKey(jwtSecretKey))
    {
        if (app.Environment.IsProduction())
        {
            startupLogger.LogError("JWT secret key does not meet security requirements for production environment");
            throw new InvalidOperationException("JWT configuration is not secure for production");
        }
        else
        {
            startupLogger.LogWarning("JWT secret key does not meet security requirements. This is acceptable for development only.");
        }
    }
    
    // 配置 Hangfire 定时任务
    var recurringJobManager = app.Services.GetRequiredService<IRecurringJobManager>();
    
    // 订阅自动续费任务 - 每天凌晨2点执行
    recurringJobManager.AddOrUpdate<WhimLabAI.Application.BackgroundJobs.SubscriptionRenewalJob>(
        "subscription-renewal",
        job => job.ProcessAutoRenewalsAsync(CancellationToken.None),
        "0 2 * * *", // CRON表达式：每天凌晨2点
        new RecurringJobOptions
        {
            TimeZone = TimeZoneInfo.FindSystemTimeZoneById("China Standard Time")
            // Queue = "critical" // Queue property not available in RecurringJobOptions
        });
    
    // 配额月度重置任务 - 每月1日凌晨0点执行
    recurringJobManager.AddOrUpdate<WhimLabAI.Application.BackgroundJobs.QuotaResetJob>(
        "quota-monthly-reset",
        job => job.ResetMonthlyQuotasAsync(CancellationToken.None),
        "0 0 1 * *", // CRON表达式：每月1日凌晨0点
        new RecurringJobOptions
        {
            TimeZone = TimeZoneInfo.FindSystemTimeZoneById("China Standard Time")
            // Queue = "critical" // Queue property not available in RecurringJobOptions
        });
    
    // 支付超时检查任务 - 每5分钟执行一次
    recurringJobManager.AddOrUpdate<WhimLabAI.Application.BackgroundJobs.PaymentTimeoutJob>(
        "payment-timeout-check",
        job => job.CheckPaymentTimeoutsAsync(CancellationToken.None),
        "*/5 * * * *", // CRON表达式：每5分钟
        new RecurringJobOptions
        {
            // Queue = "default" // Queue property not available in RecurringJobOptions
        });
    
    // 清理过期数据任务 - 每天凌晨3点执行
    recurringJobManager.AddOrUpdate<WhimLabAI.Application.BackgroundJobs.DataCleanupJob>(
        "data-cleanup",
        job => job.CleanupExpiredDataAsync(CancellationToken.None),
        "0 3 * * *", // CRON表达式：每天凌晨3点
        new RecurringJobOptions
        {
            TimeZone = TimeZoneInfo.FindSystemTimeZoneById("China Standard Time")
            // Queue = "low" // Queue property not available in RecurringJobOptions
        });
    
    Log.Information("Hangfire recurring jobs configured");
    
    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "Application terminated unexpectedly");
}
finally
{
    Log.CloseAndFlush();
}

// 使Program类对集成测试可见
public partial class Program { }