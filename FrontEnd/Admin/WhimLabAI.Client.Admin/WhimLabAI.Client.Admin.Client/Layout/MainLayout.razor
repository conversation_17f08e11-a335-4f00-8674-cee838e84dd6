﻿@inherits LayoutComponentBase
@using Microsoft.AspNetCore.Components.Authorization
@using WhimLabAI.Client.Admin.Client.Services
@using WhimLabAI.Client.Admin.Client.Components
@using WhimLabAI.Client.Admin.Client.Components.Loading
@inject AuthenticationStateProvider AuthStateProvider
@inject NavigationManager Navigation
@inject ISnackbar Snackbar

<MudThemeProvider Theme="@_theme" IsDarkMode="_isDarkMode" />
<MudPopoverProvider />
<MudDialogProvider />
<MudSnackbarProvider />

<NetworkStatusIndicator />
<ProgressBar />

<MudLayout>
    <MudAppBar Elevation="1">
        <MudIconButton Icon="@Icons.Material.Filled.Menu" Color="Color.Inherit" Edge="Edge.Start" OnClick="@((e) => DrawerToggle())" />
        <MudText Typo="Typo.h5" Class="ml-3">WhimLabAI 管理后台</MudText>
        <MudSpacer />
        <MudIconButton Icon="@(DarkLightModeButtonIcon)" Color="Color.Inherit" OnClick="@DarkModeToggle" />
        <AuthorizeView>
            <Authorized>
                <MudMenu Icon="@Icons.Material.Filled.AccountCircle" Color="Color.Inherit" AnchorOrigin="Origin.BottomRight" TransformOrigin="Origin.TopRight">
                    <MudMenuItem Disabled="true">
                        <div class="d-flex align-center">
                            <MudAvatar Size="Size.Small" Class="mr-2">
                                <MudIcon Icon="@Icons.Material.Filled.Person" />
                            </MudAvatar>
                            <div>
                                <MudText Typo="Typo.body2">@context.User.Identity?.Name</MudText>
                                <MudText Typo="Typo.caption" >管理员</MudText>
                            </div>
                        </div>
                    </MudMenuItem>
                    <MudDivider />
                    <MudMenuItem Icon="@Icons.Material.Filled.Person" OnClick="GoToProfile">个人信息</MudMenuItem>
                    <MudMenuItem Icon="@Icons.Material.Filled.Settings" OnClick="GoToSettings">系统设置</MudMenuItem>
                    <MudDivider />
                    <MudMenuItem Icon="@Icons.Material.Filled.Logout" OnClick="Logout">退出登录</MudMenuItem>
                </MudMenu>
            </Authorized>
            <NotAuthorized>
                <MudIconButton Icon="@Icons.Material.Filled.Login" Color="Color.Inherit" OnClick="GoToLogin" />
            </NotAuthorized>
        </AuthorizeView>
    </MudAppBar>
    <MudDrawer id="nav-drawer" @bind-Open="_drawerOpen" ClipMode="DrawerClipMode.Always" Elevation="2">
        <NavMenu />
    </MudDrawer>
    <MudMainContent Class="pt-16 pa-4">
        <PageTransition>
            @Body
        </PageTransition>
    </MudMainContent>
</MudLayout>


<div id="blazor-error-ui" data-nosnippet>
    An unhandled error has occurred.
    <a href="." class="reload">Reload</a>
    <span class="dismiss">🗙</span>
</div>

@code {
    [CascadingParameter]
    private Task<AuthenticationState>? AuthenticationState { get; set; }

    private bool _drawerOpen = true;
    private bool _isDarkMode = true;
    private MudTheme? _theme = null;

    protected override void OnInitialized()
    {
        base.OnInitialized();

        _theme = new()
        {
            PaletteLight = _lightPalette,
            PaletteDark = _darkPalette,
            LayoutProperties = new LayoutProperties()
        };
    }

    private void DrawerToggle()
    {
        _drawerOpen = !_drawerOpen;
    }

    private void DarkModeToggle()
    {
        _isDarkMode = !_isDarkMode;
    }

    private readonly PaletteLight _lightPalette = new()
    {
        Black = "#110e2d",
        AppbarText = "#424242",
        AppbarBackground = "rgba(255,255,255,0.8)",
        DrawerBackground = "#ffffff",
        GrayLight = "#e8e8e8",
        GrayLighter = "#f9f9f9",
    };

    private readonly PaletteDark _darkPalette = new()
    {
        Primary = "#7e6fff",
        Surface = "#1e1e2d",
        Background = "#1a1a27",
        BackgroundGray = "#151521",
        AppbarText = "#92929f",
        AppbarBackground = "rgba(26,26,39,0.8)",
        DrawerBackground = "#1a1a27",
        ActionDefault = "#74718e",
        ActionDisabled = "#9999994d",
        ActionDisabledBackground = "#605f6d4d",
        TextPrimary = "#b2b0bf",
        TextSecondary = "#92929f",
        TextDisabled = "#ffffff33",
        DrawerIcon = "#92929f",
        DrawerText = "#92929f",
        GrayLight = "#2a2833",
        GrayLighter = "#1e1e2d",
        Info = "#4a86ff",
        Success = "#3dcb6c",
        Warning = "#ffb545",
        Error = "#ff3f5f",
        LinesDefault = "#33323e",
        TableLines = "#33323e",
        Divider = "#292838",
        OverlayLight = "#1e1e2d80",
    };

    public string DarkLightModeButtonIcon => _isDarkMode switch
    {
        true => Icons.Material.Rounded.AutoMode,
        false => Icons.Material.Outlined.DarkMode,
    };
    
    private async Task Logout()
    {
        try
        {
            await ((ClientAuthStateProvider)AuthStateProvider).MarkUserAsLoggedOut();
            Snackbar.Add("已成功退出登录", Severity.Success);
            Navigation.NavigateTo("/login", forceLoad: true);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"退出登录失败：{ex.Message}", Severity.Error);
        }
    }
    
    private void GoToLogin()
    {
        Navigation.NavigateTo("/login");
    }
    
    private void GoToProfile()
    {
        Navigation.NavigateTo("/profile");
    }
    
    private void GoToSettings()
    {
        Navigation.NavigateTo("/settings");
    }
}


