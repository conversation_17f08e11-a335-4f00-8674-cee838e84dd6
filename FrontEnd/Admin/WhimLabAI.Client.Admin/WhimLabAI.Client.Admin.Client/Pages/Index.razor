@page "/"
@using Microsoft.AspNetCore.Authorization
@using WhimLabAI.Client.Admin.Client.Services
@using WhimLabAI.Client.Admin.Client.DTOs
@inject DashboardService DashboardService
@attribute [Authorize]

<PageTitle>仪表盘 - WhimLabAI 管理后台</PageTitle>

<MudText Typo="Typo.h3" Class="mb-4">仪表盘</MudText>

@if (_isLoading)
{
    <MudProgressLinear Color="Color.Primary" Indeterminate="true" Class="mb-4" />
}

<MudGrid>
    <MudItem xs="12" sm="6" md="3">
        <MudPaper Elevation="2" Class="pa-4">
            <div class="d-flex align-center">
                <MudIcon Icon="@Icons.Material.Filled.People" Size="Size.Large" Color="Color.Primary" />
                <div class="ml-3">
                    <MudText Typo="Typo.body2">总用户数</MudText>
                    @if (_stats != null)
                    {
                        <MudText Typo="Typo.h5">@_stats.TotalUsers.ToString("N0")</MudText>
                        <MudText Typo="Typo.caption" Color="Color.Success">今日新增: +@_stats.TodayNewUsers</MudText>
                    }
                    else
                    {
                        <MudSkeleton Width="60px" Height="32px" />
                    }
                </div>
            </div>
        </MudPaper>
    </MudItem>
    
    <MudItem xs="12" sm="6" md="3">
        <MudPaper Elevation="2" Class="pa-4">
            <div class="d-flex align-center">
                <MudIcon Icon="@Icons.Material.Filled.SmartToy" Size="Size.Large" Color="Color.Success" />
                <div class="ml-3">
                    <MudText Typo="Typo.body2">AI代理数</MudText>
                    @if (_stats != null)
                    {
                        <MudText Typo="Typo.h5">@_stats.TotalAgents.ToString("N0")</MudText>
                        <MudText Typo="Typo.caption">活跃对话: @_stats.ActiveConversations</MudText>
                    }
                    else
                    {
                        <MudSkeleton Width="60px" Height="32px" />
                    }
                </div>
            </div>
        </MudPaper>
    </MudItem>
    
    <MudItem xs="12" sm="6" md="3">
        <MudPaper Elevation="2" Class="pa-4">
            <div class="d-flex align-center">
                <MudIcon Icon="@Icons.Material.Filled.ShoppingCart" Size="Size.Large" Color="Color.Warning" />
                <div class="ml-3">
                    <MudText Typo="Typo.body2">今日订单</MudText>
                    @if (_stats != null)
                    {
                        <MudText Typo="Typo.h5">@_stats.TodayOrders.ToString("N0")</MudText>
                        <MudText Typo="Typo.caption">付费用户: @_stats.PaidUsers</MudText>
                    }
                    else
                    {
                        <MudSkeleton Width="60px" Height="32px" />
                    }
                </div>
            </div>
        </MudPaper>
    </MudItem>
    
    <MudItem xs="12" sm="6" md="3">
        <MudPaper Elevation="2" Class="pa-4">
            <div class="d-flex align-center">
                <MudIcon Icon="@Icons.Material.Filled.AttachMoney" Size="Size.Large" Color="Color.Error" />
                <div class="ml-3">
                    <MudText Typo="Typo.body2">今日收入</MudText>
                    @if (_stats != null)
                    {
                        <MudText Typo="Typo.h5">¥@_stats.TodayRevenue.ToString("N2")</MudText>
                        <MudText Typo="Typo.caption">总收入: ¥@_stats.TotalRevenue.ToString("N2")</MudText>
                    }
                    else
                    {
                        <MudSkeleton Width="60px" Height="32px" />
                    }
                </div>
            </div>
        </MudPaper>
    </MudItem>
</MudGrid>

<MudGrid Class="mt-4">
    <MudItem xs="12" md="8">
        <MudPaper Elevation="2" Class="pa-4">
            <MudText Typo="Typo.h6" Class="mb-3">用户增长趋势</MudText>
            @if (_userGrowthData.Any())
            {
                <MudChart ChartType="ChartType.Line" ChartSeries="@_userGrowthSeries" XAxisLabels="@_userGrowthLabels" Width="100%" Height="300px" />
            }
            else
            {
                <MudSkeleton SkeletonType="SkeletonType.Rectangle" Height="300px" />
            }
        </MudPaper>
    </MudItem>
    
    <MudItem xs="12" md="4">
        <MudPaper Elevation="2" Class="pa-4">
            <MudText Typo="Typo.h6" Class="mb-3">订阅分布</MudText>
            @if (_subscriptionData.Any())
            {
                <MudChart ChartType="ChartType.Donut" ChartSeries="@_subscriptionSeries" Width="100%" Height="300px" />
            }
            else
            {
                <MudSkeleton SkeletonType="SkeletonType.Circle" Width="300px" Height="300px" />
            }
        </MudPaper>
    </MudItem>
</MudGrid>

<MudGrid Class="mt-4">
    <MudItem xs="12">
        <MudPaper Elevation="2" Class="pa-4">
            <MudText Typo="Typo.h6" Class="mb-3">快速操作</MudText>
            <MudGrid>
                <MudItem xs="12" sm="6" md="3">
                    <MudButton Variant="Variant.Outlined" Color="Color.Primary" FullWidth="true" StartIcon="@Icons.Material.Filled.PersonAdd" Href="/users">
                        用户管理
                    </MudButton>
                </MudItem>
                <MudItem xs="12" sm="6" md="3">
                    <MudButton Variant="Variant.Outlined" Color="Color.Success" FullWidth="true" StartIcon="@Icons.Material.Filled.SmartToy" Href="/agents">
                        AI代理管理
                    </MudButton>
                </MudItem>
                <MudItem xs="12" sm="6" md="3">
                    <MudButton Variant="Variant.Outlined" Color="Color.Warning" FullWidth="true" StartIcon="@Icons.Material.Filled.Receipt" Href="/orders">
                        订单管理
                    </MudButton>
                </MudItem>
                <MudItem xs="12" sm="6" md="3">
                    <MudButton Variant="Variant.Outlined" Color="Color.Info" FullWidth="true" StartIcon="@Icons.Material.Filled.Analytics" Href="/analytics/revenue">
                        数据分析
                    </MudButton>
                </MudItem>
            </MudGrid>
        </MudPaper>
    </MudItem>
</MudGrid>

@code {
    private bool _isLoading = true;
    private DashboardStatsDto? _stats;
    private List<UserGrowthDataDto> _userGrowthData = new();
    private List<SubscriptionDistributionDto> _subscriptionData = new();
    
    private List<ChartSeries> _userGrowthSeries = new();
    private string[] _userGrowthLabels = Array.Empty<string>();
    private List<ChartSeries> _subscriptionSeries = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadDashboardData();
    }

    private async Task LoadDashboardData()
    {
        try
        {
            _isLoading = true;
            
            // Load dashboard stats
            _stats = await DashboardService.GetDashboardStatsAsync();
            
            // Load user growth data
            _userGrowthData = await DashboardService.GetUserGrowthDataAsync(30);
            if (_userGrowthData.Any())
            {
                _userGrowthLabels = _userGrowthData.Select(d => d.Date.ToString("MM/dd")).ToArray();
                _userGrowthSeries = new List<ChartSeries>
                {
                    new ChartSeries
                    {
                        Name = "用户数",
                        Data = _userGrowthData.Select(d => (double)d.Count).ToArray()
                    }
                };
            }
            
            // Load subscription distribution
            _subscriptionData = await DashboardService.GetSubscriptionDistributionAsync();
            if (_subscriptionData.Any())
            {
                _subscriptionSeries = _subscriptionData.Select(s => new ChartSeries
                {
                    Name = s.PlanName,
                    Data = new[] { (double)s.Count }
                }).ToList();
            }
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }
}