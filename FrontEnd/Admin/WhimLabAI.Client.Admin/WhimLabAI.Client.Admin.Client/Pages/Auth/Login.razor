@page "/login"
@rendermode InteractiveAuto
@layout EmptyLayout
@using WhimLabAI.Client.Admin.Client.Services
@using System.ComponentModel.DataAnnotations
@using Microsoft.AspNetCore.Components.Authorization
@using WhimLabAI.Client.Admin.Client.Layout
@using Microsoft.Extensions.Logging
@* Use the appropriate service based on render mode *@
@inject WhimLabAI.Client.Admin.Client.Services.ClientAuthService AuthService
@inject NavigationManager Navigation
@inject ISnackbar Snackbar
@inject AuthenticationStateProvider AuthStateProvider
@inject ILogger<Login> Logger
@inject ITokenStorageService TokenStorage

<PageTitle>管理员登录 - WhimLabAI</PageTitle>

<MudContainer MaxWidth="MaxWidth.Small" Class="d-flex align-center" Style="height: 100vh;">
    <MudPaper Elevation="3" Class="pa-8" Style="width: 100%;">
        <MudGrid>
            <MudItem xs="12" Class="text-center mb-4">
                <MudIcon Icon="@Icons.Material.Filled.AdminPanelSettings" Size="Size.Large" Color="Color.Primary" />
                <MudText Typo="Typo.h4" Class="mt-2">管理员登录</MudText>
                <MudText Typo="Typo.body2" >欢迎回到 WhimLabAI 管理后台</MudText>
            </MudItem>
            
            <MudItem xs="12">
                <EditForm Model="@_loginModel" OnValidSubmit="HandleLogin">
                    <DataAnnotationsValidator />
                    
                    <MudTextField 
                        @bind-Value="_loginModel.UserName" 
                        Label="用户名/邮箱/手机号" 
                        Variant="Variant.Outlined" 
                        FullWidth="true"
                        InputType="InputType.Text"
                        AdornmentIcon="@Icons.Material.Filled.Person"
                        Adornment="Adornment.Start"
                        For="@(() => _loginModel.UserName)"
                        Class="mb-4" />
                    
                    <MudTextField 
                        @bind-Value="_loginModel.Password" 
                        Label="密码" 
                        Variant="Variant.Outlined" 
                        FullWidth="true"
                        InputType="@_passwordInputType"
                        AdornmentIcon="@Icons.Material.Filled.Lock"
                        Adornment="Adornment.Start"
                        For="@(() => _loginModel.Password)"
                        Class="mb-4" />
                    
                    <MudGrid Class="mb-4">
                        <MudItem xs="8">
                            <MudTextField 
                                @bind-Value="_loginModel.CaptchaCode" 
                                Label="验证码" 
                                Variant="Variant.Outlined" 
                                FullWidth="true"
                                InputType="InputType.Text"
                                AdornmentIcon="@Icons.Material.Filled.Security"
                                Adornment="Adornment.Start"
                                For="@(() => _loginModel.CaptchaCode)" />
                        </MudItem>
                        <MudItem xs="4" Class="d-flex align-center">
                            @if (!string.IsNullOrEmpty(_captchaImage))
                            {
                                <MudImage Src="@_captchaImage" Alt="验证码" Style="cursor: pointer; height: 56px;" @onclick="RefreshCaptcha" />
                            }
                            else
                            {
                                <MudButton Variant="Variant.Outlined" Size="Size.Small" @onclick="RefreshCaptcha">
                                    点击加载验证码
                                </MudButton>
                            }
                        </MudItem>
                    </MudGrid>
                    
                    <div class="d-flex justify-space-between align-center mb-4">
                        <MudCheckBox @bind-Value="_loginModel.RememberMe" Label="记住我" Color="Color.Primary" />
                        <MudLink Href="/forgot-password" Typo="Typo.body2">忘记密码？</MudLink>
                    </div>
                    
                    <MudButton 
                        ButtonType="ButtonType.Submit" 
                        Variant="Variant.Filled" 
                        Color="Color.Primary" 
                        FullWidth="true"
                        Disabled="@_isLoading"
                        Class="mb-4">
                        @if (_isLoading)
                        {
                            <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="mr-2" />
                            <span>登录中...</span>
                        }
                        else
                        {
                            <span>登录</span>
                        }
                    </MudButton>
                    
                    <MudDivider Class="mb-4" />
                    
                    <div class="text-center">
                        <MudText Typo="Typo.body2" >
                            需要帮助？请联系系统管理员
                        </MudText>
                    </div>
                </EditForm>
            </MudItem>
        </MudGrid>
    </MudPaper>
</MudContainer>

@code {
    [Parameter] [SupplyParameterFromQuery] public string? ReturnUrl { get; set; }
    
    private readonly LoginModel _loginModel = new();
    private bool _isLoading;
    private bool _passwordVisible;
    private InputType _passwordInputType = InputType.Password;
    private string _passwordVisibilityIcon = Icons.Material.Filled.VisibilityOff;
    private string _captchaImage = string.Empty;
    private string _captchaId = string.Empty;
    
    public class LoginModel
    {
        [Required(ErrorMessage = "请输入用户名/邮箱/手机号")]
        public string UserName { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "请输入密码")]
        public string Password { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "请输入验证码")]
        public string CaptchaCode { get; set; } = string.Empty;
        
        public bool RememberMe { get; set; }
    }
    
    protected override async Task OnInitializedAsync()
    {
        // Don't fetch captcha during prerendering
    }
    
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            // Auto-load captcha on first render
            await RefreshCaptcha();
            StateHasChanged();
        }
    }
    
    private async Task HandleLogin()
    {
        if (_isLoading) return;
        
        _isLoading = true;
        
        try
        {
            var response = await AuthService.LoginAsync(
                _loginModel.UserName, 
                _loginModel.Password, 
                _loginModel.CaptchaCode,
                _captchaId,
                _loginModel.RememberMe);
            
            if (response.Success)
            {
                // Save tokens based on render mode
                var isClientSide = OperatingSystem.IsBrowser();
                
                if (isClientSide)
                {
                    // Client-side: Save to in-memory storage and update auth state
                    // Note: This is temporary and will be lost on page refresh
                    await TokenStorage.SetTokenAsync("adminToken", response.Data.Token);
                    if (!string.IsNullOrEmpty(response.Data.RefreshToken))
                    {
                        await TokenStorage.SetTokenAsync("adminRefreshToken", response.Data.RefreshToken);
                    }
                    
                    // Update authentication state if we have the client provider
                    if (AuthStateProvider is ClientAuthStateProvider clientProvider)
                    {
                        await clientProvider.NotifyUserAuthentication(response.Data.Token);
                    }
                }
                else
                {
                    // Server-side: Database session has been created by the service
                    // The forceLoad navigation will cause the server to re-evaluate auth state
                }
                
                Snackbar.Add("登录成功！", Severity.Success);
                
                // Always force a full page reload to ensure auth state is properly updated
                var targetUrl = !string.IsNullOrEmpty(ReturnUrl) ? ReturnUrl : "/";
                Navigation.NavigateTo(targetUrl, forceLoad: true);
            }
            else
            {
                Snackbar.Add(response.Message ?? "登录失败，请检查用户名和密码", Severity.Error);
                await RefreshCaptcha();
                _loginModel.CaptchaCode = string.Empty;
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"登录失败：{ex.Message}", Severity.Error);
            await RefreshCaptcha();
            _loginModel.CaptchaCode = string.Empty;
        }
        finally
        {
            _isLoading = false;
        }
    }
    
    private async Task RefreshCaptcha()
    {
        try
        {
            Logger.LogInformation("RefreshCaptcha called");
            var captchaData = await AuthService.GetCaptchaAsync();
            if (captchaData != null)
            {
                _captchaId = captchaData.CaptchaId;
                // Ensure proper base64 image format
                if (!captchaData.ImageBase64.StartsWith("data:image"))
                {
                    _captchaImage = $"data:image/png;base64,{captchaData.ImageBase64}";
                }
                else
                {
                    _captchaImage = captchaData.ImageBase64;
                }
                Logger.LogInformation($"Captcha loaded successfully: {_captchaId}");
            }
            else
            {
                Logger.LogWarning("GetCaptchaAsync returned null");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to get captcha");
            Snackbar.Add($"获取验证码失败：{ex.Message}", Severity.Error);
        }
    }
    
    private void TogglePasswordVisibility()
    {
        _passwordVisible = !_passwordVisible;
        _passwordInputType = _passwordVisible ? InputType.Text : InputType.Password;
        _passwordVisibilityIcon = _passwordVisible ? Icons.Material.Filled.Visibility : Icons.Material.Filled.VisibilityOff;
    }
}