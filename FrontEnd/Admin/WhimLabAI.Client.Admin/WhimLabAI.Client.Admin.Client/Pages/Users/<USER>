@page "/users/{userId:guid}"
@using Microsoft.AspNetCore.Authorization
@using WhimLabAI.Client.Admin.Client.Services
@using WhimLabAI.Client.Admin.Client.DTOs
@using WhimLabAI.Shared.Enums
@inject CustomerUserService CustomerUserService
@inject NavigationManager Navigation
@inject ISnackbar Snackbar
@inject IDialogService DialogService
@attribute [Authorize]

<PageTitle>用户详情 - WhimLabAI 管理后台</PageTitle>

<MudText Typo="Typo.h3" Class="mb-4">
    <MudIconButton Icon="@Icons.Material.Filled.ArrowBack" OnClick="@(() => Navigation.NavigateTo("/users"))" />
    用户详情
</MudText>

@if (_loading)
{
    <MudProgressLinear Color="Color.Primary" Indeterminate="true" Class="mb-4" />
}
else if (_user == null)
{
    <MudAlert Severity="Severity.Error">用户不存在或无法加载</MudAlert>
}
else
{
    <MudGrid>
        <MudItem xs="12" md="8">
            <MudPaper Elevation="2" Class="pa-4 mb-4">
                <MudText Typo="Typo.h6" Class="mb-3">基本信息</MudText>
                <MudGrid>
                    <MudItem xs="12" sm="6">
                        <MudField Label="用户名" Variant="Variant.Text">@_user.UserName</MudField>
                    </MudItem>
                    <MudItem xs="12" sm="6">
                        <MudField Label="昵称" Variant="Variant.Text">@(_user.NickName ?? "-")</MudField>
                    </MudItem>
                    <MudItem xs="12" sm="6">
                        <MudField Label="邮箱" Variant="Variant.Text">
                            @_user.Email
                            @if (_user.EmailVerified)
                            {
                                <MudIcon Icon="@Icons.Material.Filled.Verified" Color="Color.Success" Size="Size.Small" />
                            }
                        </MudField>
                    </MudItem>
                    <MudItem xs="12" sm="6">
                        <MudField Label="手机号" Variant="Variant.Text">
                            @(_user.PhoneNumber ?? "-")
                            @if (_user.PhoneVerified)
                            {
                                <MudIcon Icon="@Icons.Material.Filled.Verified" Color="Color.Success" Size="Size.Small" />
                            }
                        </MudField>
                    </MudItem>
                    <MudItem xs="12" sm="6">
                        <MudField Label="性别" Variant="Variant.Text">@GetGenderDisplay(_user.Gender)</MudField>
                    </MudItem>
                    <MudItem xs="12" sm="6">
                        <MudField Label="生日" Variant="Variant.Text">@(_user.Birthday?.ToString("yyyy-MM-dd") ?? "-")</MudField>
                    </MudItem>
                    <MudItem xs="12">
                        <MudField Label="个人简介" Variant="Variant.Text">@(_user.Bio ?? "-")</MudField>
                    </MudItem>
                </MudGrid>
            </MudPaper>

            <MudPaper Elevation="2" Class="pa-4 mb-4">
                <MudText Typo="Typo.h6" Class="mb-3">职业信息</MudText>
                <MudGrid>
                    <MudItem xs="12" sm="4">
                        <MudField Label="地区" Variant="Variant.Text">@(_user.Region ?? "-")</MudField>
                    </MudItem>
                    <MudItem xs="12" sm="4">
                        <MudField Label="行业" Variant="Variant.Text">@(_user.Industry ?? "-")</MudField>
                    </MudItem>
                    <MudItem xs="12" sm="4">
                        <MudField Label="职位" Variant="Variant.Text">@(_user.Position ?? "-")</MudField>
                    </MudItem>
                </MudGrid>
            </MudPaper>

            <MudPaper Elevation="2" Class="pa-4 mb-4">
                <MudText Typo="Typo.h6" Class="mb-3">订阅信息</MudText>
                <MudGrid>
                    <MudItem xs="12" sm="6">
                        <MudField Label="当前套餐" Variant="Variant.Text">
                            <MudChip T="string" Color="@GetTierColor(_user.CurrentTier)" Size="Size.Small">
                                @GetTierDisplayName(_user.CurrentTier)
                            </MudChip>
                        </MudField>
                    </MudItem>
                    <MudItem xs="12" sm="6">
                        <MudField Label="到期时间" Variant="Variant.Text">
                            @(_user.SubscriptionExpiresAt?.ToString("yyyy-MM-dd HH:mm") ?? "无期限")
                        </MudField>
                    </MudItem>
                    <MudItem xs="12" sm="6">
                        <MudField Label="剩余Token" Variant="Variant.Text">@_user.RemainingTokens.ToString("N0")</MudField>
                    </MudItem>
                    <MudItem xs="12" sm="6">
                        <MudField Label="月配额" Variant="Variant.Text">@_user.MonthlyTokenQuota.ToString("N0")</MudField>
                    </MudItem>
                </MudGrid>
            </MudPaper>

            <MudPaper Elevation="2" Class="pa-4">
                <MudText Typo="Typo.h6" Class="mb-3">第三方绑定</MudText>
                @if (_user.OAuthBindings.Any())
                {
                    <MudSimpleTable>
                        <thead>
                            <tr>
                                <th>平台</th>
                                <th>显示名称</th>
                                <th>绑定时间</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var binding in _user.OAuthBindings)
                            {
                                <tr>
                                    <td>@GetProviderDisplay(binding.Provider)</td>
                                    <td>@(binding.DisplayName ?? "-")</td>
                                    <td>@binding.BindAt.ToString("yyyy-MM-dd HH:mm")</td>
                                </tr>
                            }
                        </tbody>
                    </MudSimpleTable>
                }
                else
                {
                    <MudText Typo="Typo.body2" Color="Color.Default">暂无第三方账号绑定</MudText>
                }
            </MudPaper>
        </MudItem>

        <MudItem xs="12" md="4">
            <MudPaper Elevation="2" Class="pa-4 mb-4">
                <MudText Typo="Typo.h6" Class="mb-3">账号状态</MudText>
                <MudField Label="状态" Variant="Variant.Text" Class="mb-3">
                    <MudChip T="string" Color="@GetStatusColor(_user.Status)" Size="Size.Small">
                        @GetStatusDisplayName(_user.Status)
                    </MudChip>
                </MudField>
                <MudField Label="双因素认证" Variant="Variant.Text" Class="mb-3">
                    @if (_user.TwoFactorEnabled)
                    {
                        <MudChip T="string" Color="Color.Success" Size="Size.Small">已启用</MudChip>
                    }
                    else
                    {
                        <MudChip T="string" Color="Color.Default" Size="Size.Small">未启用</MudChip>
                    }
                </MudField>
                <MudField Label="注册时间" Variant="Variant.Text" Class="mb-3">
                    @_user.RegisteredAt.ToString("yyyy-MM-dd HH:mm")
                </MudField>
                <MudField Label="最后登录" Variant="Variant.Text" Class="mb-3">
                    @_user.LastLoginAt.ToString("yyyy-MM-dd HH:mm")
                </MudField>
                <MudField Label="登录IP" Variant="Variant.Text" Class="mb-3">
                    @(_user.LastLoginIp ?? "-")
                </MudField>
                <MudField Label="登录地点" Variant="Variant.Text">
                    @(_user.LastLoginLocation ?? "-")
                </MudField>
            </MudPaper>

            <MudPaper Elevation="2" Class="pa-4 mb-4">
                <MudText Typo="Typo.h6" Class="mb-3">统计数据</MudText>
                <MudField Label="总对话数" Variant="Variant.Text" Class="mb-3">
                    @_user.TotalConversations.ToString("N0")
                </MudField>
                <MudField Label="总订单数" Variant="Variant.Text" Class="mb-3">
                    @_user.TotalOrders.ToString("N0")
                </MudField>
                <MudField Label="累计消费" Variant="Variant.Text">
                    ¥@_user.TotalSpent.ToString("N2")
                </MudField>
            </MudPaper>

            <MudPaper Elevation="2" Class="pa-4">
                <MudText Typo="Typo.h6" Class="mb-3">操作</MudText>
                <MudStack Spacing="2">
                    @if (_user.Status == UserStatus.Active)
                    {
                        <MudButton Variant="Variant.Outlined" Color="Color.Warning" FullWidth="true"
                                   StartIcon="@Icons.Material.Filled.Lock"
                                   OnClick="@(() => UpdateUserStatus(UserStatus.Suspended))">
                            暂停账号
                        </MudButton>
                    }
                    else if (_user.Status == UserStatus.Suspended)
                    {
                        <MudButton Variant="Variant.Outlined" Color="Color.Success" FullWidth="true"
                                   StartIcon="@Icons.Material.Filled.LockOpen"
                                   OnClick="@(() => UpdateUserStatus(UserStatus.Active))">
                            恢复账号
                        </MudButton>
                    }
                    <MudButton Variant="Variant.Outlined" Color="Color.Primary" FullWidth="true"
                               StartIcon="@Icons.Material.Filled.Token"
                               OnClick="AdjustUserQuota">
                        调整配额
                    </MudButton>
                    <MudButton Variant="Variant.Outlined" Color="Color.Info" FullWidth="true"
                               StartIcon="@Icons.Material.Filled.Key"
                               OnClick="ResetUserPassword">
                        重置密码
                    </MudButton>
                    <MudButton Variant="Variant.Outlined" Color="Color.Default" FullWidth="true"
                               StartIcon="@Icons.Material.Filled.History"
                               OnClick="ViewLoginHistory">
                        登录历史
                    </MudButton>
                </MudStack>
            </MudPaper>
        </MudItem>
    </MudGrid>
}

@code {
    [Parameter] public Guid UserId { get; set; }
    
    private CustomerUserDetailDto? _user;
    private bool _loading = true;

    protected override async Task OnParametersSetAsync()
    {
        await LoadUserDetail();
    }

    private async Task LoadUserDetail()
    {
        _loading = true;
        StateHasChanged();

        _user = await CustomerUserService.GetCustomerUserDetailAsync(UserId);
        
        _loading = false;
        StateHasChanged();
    }

    private async Task UpdateUserStatus(UserStatus newStatus)
    {
        if (_user == null) return;

        var confirmed = await DialogService.ShowMessageBox(
            "确认操作",
            $"确定要{(newStatus == UserStatus.Suspended ? "暂停" : "恢复")}用户 {_user.UserName} 吗？",
            yesText: "确定", cancelText: "取消");

        if (confirmed != true) return;

        var parameters = new DialogParameters<StatusReasonDialog>
        {
            { x => x.Title, newStatus == UserStatus.Suspended ? "暂停用户" : "恢复用户" },
            { x => x.Message, "请输入操作原因：" }
        };

        var dialog = await DialogService.ShowAsync<StatusReasonDialog>("操作原因", parameters);
        var dialogResult = await dialog.Result;

        if (!dialogResult.Canceled && dialogResult.Data is string reason && !string.IsNullOrWhiteSpace(reason))
        {
            var request = new UpdateCustomerStatusRequest
            {
                UserId = _user.Id,
                Status = newStatus,
                Reason = reason
            };

            var result = await CustomerUserService.UpdateCustomerStatusAsync(request);
            if (result.Success)
            {
                Snackbar.Add($"用户状态已更新", Severity.Success);
                await LoadUserDetail();
            }
            else
            {
                Snackbar.Add(result.Message ?? "操作失败", Severity.Error);
            }
        }
    }

    private async Task AdjustUserQuota()
    {
        if (_user == null) return;

        var parameters = new DialogParameters<QuotaAdjustDialog>
        {
            { x => x.UserName, _user.UserName },
            { x => x.UserId, _user.Id }
        };

        var dialog = await DialogService.ShowAsync<QuotaAdjustDialog>("调整配额", parameters);
        var result = await dialog.Result;

        if (!result.Canceled && result.Data is QuotaAdjustResult adjustResult)
        {
            var apiResult = await CustomerUserService.AdjustCustomerQuotaAsync(
                _user.Id, adjustResult.AdditionalTokens, adjustResult.Reason);
            
            if (apiResult.Success)
            {
                Snackbar.Add("配额调整成功", Severity.Success);
                await LoadUserDetail();
            }
            else
            {
                Snackbar.Add(apiResult.Message ?? "配额调整失败", Severity.Error);
            }
        }
    }

    private async Task ResetUserPassword()
    {
        if (_user == null) return;

        var confirmed = await DialogService.ShowMessageBox(
            "重置密码",
            $"确定要重置用户 {_user.UserName} 的密码吗？新密码将发送到用户的邮箱。",
            yesText: "确定", cancelText: "取消");

        if (confirmed == true)
        {
            var result = await CustomerUserService.ResetCustomerPasswordAsync(_user.Id);
            if (result.Success)
            {
                Snackbar.Add("密码重置成功，新密码已发送到用户邮箱", Severity.Success);
            }
            else
            {
                Snackbar.Add(result.Message ?? "密码重置失败", Severity.Error);
            }
        }
    }

    private void ViewLoginHistory()
    {
        Navigation.NavigateTo($"/users/{UserId}/login-history");
    }

    private string GetGenderDisplay(Gender? gender) => gender switch
    {
        Gender.Male => "男",
        Gender.Female => "女",
        Gender.Other => "其他",
        _ => "-"
    };

    private string GetProviderDisplay(string provider) => provider switch
    {
        "WeChat" => "微信",
        "GitHub" => "GitHub",
        "Google" => "谷歌",
        "Microsoft" => "微软",
        _ => provider
    };

    private Color GetTierColor(SubscriptionTier tier) => tier switch
    {
        SubscriptionTier.Free => Color.Default,
        SubscriptionTier.Basic => Color.Info,
        SubscriptionTier.Pro => Color.Primary,
        SubscriptionTier.Ultra => Color.Secondary,
        _ => Color.Default
    };

    private string GetTierDisplayName(SubscriptionTier tier) => tier switch
    {
        SubscriptionTier.Free => "免费版",
        SubscriptionTier.Basic => "基础版",
        SubscriptionTier.Pro => "专业版",
        SubscriptionTier.Ultra => "旗舰版",
        _ => "未知"
    };

    private Color GetStatusColor(UserStatus status) => status switch
    {
        UserStatus.Active => Color.Success,
        UserStatus.Inactive => Color.Warning,
        UserStatus.Suspended => Color.Error,
        UserStatus.Deleted => Color.Dark,
        _ => Color.Default
    };

    private string GetStatusDisplayName(UserStatus status) => status switch
    {
        UserStatus.Active => "正常",
        UserStatus.Inactive => "未激活",
        UserStatus.Suspended => "已暂停",
        UserStatus.Deleted => "已删除",
        _ => "未知"
    };
}