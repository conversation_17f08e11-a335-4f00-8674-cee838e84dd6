namespace WhimLabAI.Client.Admin.Client.DTOs;

public class DashboardStatsDto
{
    public int TotalUsers { get; set; }
    public int TodayNewUsers { get; set; }
    public int ActiveUsers { get; set; }
    public int PaidUsers { get; set; }
    public decimal TotalRevenue { get; set; }
    public decimal TodayRevenue { get; set; }
    public long TodayTokenUsage { get; set; }
    public int TotalAgents { get; set; }
    public int TodayOrders { get; set; }
    public int ActiveConversations { get; set; }
}

public class UserGrowthDataDto
{
    public DateTime Date { get; set; }
    public int Count { get; set; }
}

public class RevenueDataDto
{
    public DateTime Date { get; set; }
    public decimal Amount { get; set; }
}

public class SubscriptionDistributionDto
{
    public string PlanName { get; set; } = string.Empty;
    public int Count { get; set; }
    public decimal Revenue { get; set; }
}