using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Client.Admin.Client.DTOs;

public class CustomerUserListDto
{
    public Guid Id { get; set; }
    public string UserName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string? PhoneNumber { get; set; }
    public string? NickName { get; set; }
    public UserStatus Status { get; set; }
    public SubscriptionTier CurrentTier { get; set; }
    public DateTime RegisteredAt { get; set; }
    public DateTime LastLoginAt { get; set; }
    public int TotalOrders { get; set; }
    public decimal TotalSpent { get; set; }
}

public class CustomerUserDetailDto
{
    public Guid Id { get; set; }
    public string UserName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string? PhoneNumber { get; set; }
    public string? NickName { get; set; }
    public string? Avatar { get; set; }
    public Gender? Gender { get; set; }
    public DateTime? Birthday { get; set; }
    public string? Bio { get; set; }
    public string? Region { get; set; }
    public string? Industry { get; set; }
    public string? Position { get; set; }
    public UserStatus Status { get; set; }
    public bool EmailVerified { get; set; }
    public bool PhoneVerified { get; set; }
    public bool TwoFactorEnabled { get; set; }
    
    // Subscription info
    public SubscriptionTier CurrentTier { get; set; }
    public DateTime? SubscriptionExpiresAt { get; set; }
    public long RemainingTokens { get; set; }
    public long MonthlyTokenQuota { get; set; }
    
    // Statistics
    public DateTime RegisteredAt { get; set; }
    public DateTime LastLoginAt { get; set; }
    public string? LastLoginIp { get; set; }
    public string? LastLoginLocation { get; set; }
    public int TotalConversations { get; set; }
    public int TotalOrders { get; set; }
    public decimal TotalSpent { get; set; }
    
    // OAuth bindings
    public List<OAuthBindingDto> OAuthBindings { get; set; } = new();
}

public class OAuthBindingDto
{
    public string Provider { get; set; } = string.Empty;
    public string? DisplayName { get; set; }
    public DateTime BindAt { get; set; }
}

public class CustomerUserSearchRequest
{
    public string? Keyword { get; set; }
    public UserStatus? Status { get; set; }
    public SubscriptionTier? Tier { get; set; }
    public DateTime? RegisteredFrom { get; set; }
    public DateTime? RegisteredTo { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 20;
    public string? OrderBy { get; set; }
    public bool Descending { get; set; } = true;
}

public class UpdateCustomerStatusRequest
{
    public Guid UserId { get; set; }
    public UserStatus Status { get; set; }
    public string Reason { get; set; } = string.Empty;
}

public class QuotaAdjustResult
{
    public long AdditionalTokens { get; set; }
    public string Reason { get; set; } = string.Empty;
}