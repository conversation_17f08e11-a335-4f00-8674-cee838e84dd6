using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using WhimLabAI.Client.Admin.Client.DTOs;
using WhimLabAI.Shared.Dtos;
using WhimLabAI.Shared.Results;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Client.Admin.Client.Services;

public class CustomerUserService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<CustomerUserService> _logger;
    private static readonly JsonSerializerOptions JsonOptions = new()
    {
        PropertyNameCaseInsensitive = true
    };

    public CustomerUserService(HttpClient httpClient, ILogger<CustomerUserService> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
    }

    public async Task<PagedResult<CustomerUserListDto>?> GetCustomerUsersAsync(CustomerUserSearchRequest request)
    {
        try
        {
            var queryParams = new List<string>
            {
                $"page={request.Page}",
                $"pageSize={request.PageSize}"
            };

            if (!string.IsNullOrWhiteSpace(request.Keyword))
                queryParams.Add($"keyword={Uri.EscapeDataString(request.Keyword)}");
            
            if (request.Status.HasValue)
                queryParams.Add($"status={request.Status.Value}");
            
            if (request.Tier.HasValue)
                queryParams.Add($"tier={request.Tier.Value}");
            
            if (request.RegisteredFrom.HasValue)
                queryParams.Add($"registeredFrom={request.RegisteredFrom.Value:yyyy-MM-dd}");
            
            if (request.RegisteredTo.HasValue)
                queryParams.Add($"registeredTo={request.RegisteredTo.Value:yyyy-MM-dd}");
            
            if (!string.IsNullOrWhiteSpace(request.OrderBy))
                queryParams.Add($"orderBy={request.OrderBy}");
            
            queryParams.Add($"descending={request.Descending}");

            var queryString = string.Join("&", queryParams);
            var response = await _httpClient.GetFromJsonAsync<ApiResponse<PagedResult<CustomerUserListDto>>>(
                $"api/v1/admin/customer-users?{queryString}", JsonOptions);
            
            return response?.Success == true ? response.Data : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching customer users");
            return null;
        }
    }

    public async Task<CustomerUserDetailDto?> GetCustomerUserDetailAsync(Guid userId)
    {
        try
        {
            var response = await _httpClient.GetFromJsonAsync<ApiResponse<CustomerUserDetailDto>>(
                $"api/v1/admin/customer-users/{userId}", JsonOptions);
            
            return response?.Success == true ? response.Data : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching customer user detail for {UserId}", userId);
            return null;
        }
    }

    public async Task<ApiResponse<bool>> UpdateCustomerStatusAsync(UpdateCustomerStatusRequest request)
    {
        try
        {
            var response = await _httpClient.PutAsJsonAsync(
                $"api/v1/admin/customer-users/{request.UserId}/status", request, JsonOptions);
            
            var result = await response.Content.ReadFromJsonAsync<ApiResponse<bool>>(JsonOptions);
            return result ?? new ApiResponse<bool> { Success = false, Message = "Failed to update status" };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating customer status for {UserId}", request.UserId);
            return new ApiResponse<bool> { Success = false, Message = $"Error: {ex.Message}" };
        }
    }

    public async Task<ApiResponse<bool>> ResetCustomerPasswordAsync(Guid userId)
    {
        try
        {
            var response = await _httpClient.PostAsync(
                $"api/v1/admin/customer-users/{userId}/reset-password", null);
            
            var result = await response.Content.ReadFromJsonAsync<ApiResponse<bool>>(JsonOptions);
            return result ?? new ApiResponse<bool> { Success = false, Message = "Failed to reset password" };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resetting password for {UserId}", userId);
            return new ApiResponse<bool> { Success = false, Message = $"Error: {ex.Message}" };
        }
    }

    public async Task<ApiResponse<bool>> AdjustCustomerQuotaAsync(Guid userId, long additionalTokens, string reason)
    {
        try
        {
            var request = new { AdditionalTokens = additionalTokens, Reason = reason };
            var response = await _httpClient.PostAsJsonAsync(
                $"api/v1/admin/customer-users/{userId}/adjust-quota", request, JsonOptions);
            
            var result = await response.Content.ReadFromJsonAsync<ApiResponse<bool>>(JsonOptions);
            return result ?? new ApiResponse<bool> { Success = false, Message = "Failed to adjust quota" };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adjusting quota for {UserId}", userId);
            return new ApiResponse<bool> { Success = false, Message = $"Error: {ex.Message}" };
        }
    }

    public async Task<List<LoginHistoryDto>> GetLoginHistoryAsync(Guid userId, int days = 30)
    {
        try
        {
            var response = await _httpClient.GetFromJsonAsync<ApiResponse<List<LoginHistoryDto>>>(
                $"api/v1/admin/customer-users/{userId}/login-history?days={days}", JsonOptions);
            
            return response?.Success == true && response.Data != null ? response.Data : new List<LoginHistoryDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching login history for {UserId}", userId);
            return new List<LoginHistoryDto>();
        }
    }
}

public class LoginHistoryDto
{
    public DateTime LoginTime { get; set; }
    public string IpAddress { get; set; } = string.Empty;
    public string? Location { get; set; }
    public string Device { get; set; } = string.Empty;
    public LoginMethod LoginMethod { get; set; }
    public bool Success { get; set; }
}