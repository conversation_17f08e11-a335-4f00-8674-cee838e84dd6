using System.Net.Http.Json;
using WhimLabAI.Client.Admin.Client.DTOs;
using WhimLabAI.Shared.Dtos;

namespace WhimLabAI.Client.Admin.Client.Services;

public class DashboardService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<DashboardService> _logger;

    public DashboardService(HttpClient httpClient, ILogger<DashboardService> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
    }

    public async Task<DashboardStatsDto?> GetDashboardStatsAsync()
    {
        try
        {
            var response = await _httpClient.GetFromJsonAsync<ApiResponse<DashboardStatsDto>>("api/v1/admin/dashboard/stats");
            return response?.Success == true ? response.Data : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching dashboard stats");
            return null;
        }
    }

    public async Task<List<UserGrowthDataDto>> GetUserGrowthDataAsync(int days = 30)
    {
        try
        {
            var response = await _httpClient.GetFromJsonAsync<ApiResponse<List<UserGrowthDataDto>>>($"api/v1/admin/dashboard/user-growth?days={days}");
            return response?.Success == true && response.Data != null ? response.Data : new List<UserGrowthDataDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching user growth data");
            return new List<UserGrowthDataDto>();
        }
    }

    public async Task<List<RevenueDataDto>> GetRevenueDataAsync(int days = 30)
    {
        try
        {
            var response = await _httpClient.GetFromJsonAsync<ApiResponse<List<RevenueDataDto>>>($"api/v1/admin/dashboard/revenue?days={days}");
            return response?.Success == true && response.Data != null ? response.Data : new List<RevenueDataDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching revenue data");
            return new List<RevenueDataDto>();
        }
    }

    public async Task<List<SubscriptionDistributionDto>> GetSubscriptionDistributionAsync()
    {
        try
        {
            var response = await _httpClient.GetFromJsonAsync<ApiResponse<List<SubscriptionDistributionDto>>>("api/v1/admin/dashboard/subscription-distribution");
            return response?.Success == true && response.Data != null ? response.Data : new List<SubscriptionDistributionDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching subscription distribution");
            return new List<SubscriptionDistributionDto>();
        }
    }
}