using Microsoft.AspNetCore.Components.Authorization;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using Microsoft.IdentityModel.Tokens;

namespace WhimLabAI.Client.Admin.Client.Services;

public class ClientAuthStateProvider : AuthenticationStateProvider
{
    private readonly ITokenStorageService _tokenStorage;
    private readonly HttpClient _httpClient;
    
    private static readonly AuthenticationState Anonymous = 
        new(new ClaimsPrincipal(new ClaimsIdentity()));

    public ClientAuthStateProvider(ITokenStorageService tokenStorage, HttpClient httpClient)
    {
        _tokenStorage = tokenStorage;
        _httpClient = httpClient;
    }

    public override async Task<AuthenticationState> GetAuthenticationStateAsync()
    {
        try
        {
            var token = await _tokenStorage.GetTokenAsync("adminToken");
            if (string.IsNullOrEmpty(token))
            {
                return Anonymous;
            }

            var principal = ValidateToken(token);
            
            // 设置默认请求头
            _httpClient.DefaultRequestHeaders.Authorization = 
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            
            return new AuthenticationState(principal);
        }
        catch
        {
            return Anonymous;
        }
    }

    public async Task NotifyUserAuthentication(string token)
    {
        await MarkUserAsAuthenticated(token);
    }
    
    public async Task MarkUserAsAuthenticated(string token)
    {
        await _tokenStorage.SetTokenAsync("adminToken", token);
        
        var principal = ValidateToken(token);
        
        // 设置默认请求头
        _httpClient.DefaultRequestHeaders.Authorization = 
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
        
        NotifyAuthenticationStateChanged(Task.FromResult(new AuthenticationState(principal)));
    }

    public async Task MarkUserAsLoggedOut()
    {
        await _tokenStorage.RemoveTokenAsync("adminToken");
        await _tokenStorage.RemoveTokenAsync("adminRefreshToken");
        
        // 清除请求头
        _httpClient.DefaultRequestHeaders.Authorization = null;
        
        NotifyAuthenticationStateChanged(Task.FromResult(Anonymous));
    }

    public async Task LogoutAsync()
    {
        await MarkUserAsLoggedOut();
    }

    public async Task<string?> GetTokenAsync()
    {
        try
        {
            return await _tokenStorage.GetTokenAsync("adminToken");
        }
        catch
        {
            return null;
        }
    }

    public async Task<bool> TryRefreshTokenAsync()
    {
        try
        {
            var refreshToken = await _tokenStorage.GetTokenAsync("adminRefreshToken");
            if (string.IsNullOrEmpty(refreshToken))
            {
                return false;
            }

            // TODO: Call refresh token endpoint
            // For now, return false
            return false;
        }
        catch
        {
            return false;
        }
    }

    private ClaimsPrincipal ValidateToken(string token)
    {
        var handler = new JwtSecurityTokenHandler();
        
        try
        {
            var jwt = handler.ReadJwtToken(token);
            
            // 检查token是否过期
            if (jwt.ValidTo < DateTime.UtcNow)
            {
                throw new SecurityTokenExpiredException("Token has expired");
            }
            
            // 创建身份
            var identity = new ClaimsIdentity(jwt.Claims, "jwt");
            
            // 验证是否为管理员用户
            var roleClaims = jwt.Claims.Where(c => c.Type == ClaimTypes.Role).Select(c => c.Value.ToLower());
            if (!roleClaims.Any(r => r.Contains("admin")))
            {
                throw new UnauthorizedAccessException("Invalid admin token");
            }
            
            return new ClaimsPrincipal(identity);
        }
        catch (Exception ex)
        {
            throw new UnauthorizedAccessException("Invalid token", ex);
        }
    }
}