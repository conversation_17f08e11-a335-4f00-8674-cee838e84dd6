using Microsoft.AspNetCore.Components;
using System.Net.NetworkInformation;

namespace WhimLabAI.Client.Admin.Client.Services;

/// <summary>
/// Service for monitoring network connectivity status.
/// Since Blazor Server has built-in reconnection handling, this service is simplified
/// for WebAssembly scenarios where we might need custom network status handling.
/// </summary>
public class NetworkStatusService : IDisposable
{
    private readonly ILogger<NetworkStatusService> _logger;
    private Timer? _pingTimer;
    private readonly HttpClient _httpClient;
    
    public event Action<bool>? NetworkStatusChanged;
    public bool IsOnline { get; private set; } = true;

    public NetworkStatusService(ILogger<NetworkStatusService> logger, HttpClient httpClient)
    {
        _logger = logger;
        _httpClient = httpClient;
    }

    public async Task InitializeAsync()
    {
        try
        {
            // Initial check
            await CheckNetworkStatus();
            
            // Start periodic checking every 30 seconds
            _pingTimer = new Timer(async _ => await CheckNetworkStatus(), null, 
                TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize network status monitoring");
        }
    }

    private async Task CheckNetworkStatus()
    {
        try
        {
            // Try to ping the API endpoint
            using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(5));
            var response = await _httpClient.GetAsync("/api/health", cts.Token);
            
            var newStatus = response.IsSuccessStatusCode;
            
            if (newStatus != IsOnline)
            {
                IsOnline = newStatus;
                _logger.LogInformation($"Network status changed: {(IsOnline ? "Online" : "Offline")}");
                NetworkStatusChanged?.Invoke(IsOnline);
            }
        }
        catch (Exception ex)
        {
            // If we can't reach the server, we're offline
            if (IsOnline)
            {
                IsOnline = false;
                _logger.LogWarning(ex, "Network check failed, marking as offline");
                NetworkStatusChanged?.Invoke(false);
            }
        }
    }

    public void Dispose()
    {
        _pingTimer?.Dispose();
    }
}