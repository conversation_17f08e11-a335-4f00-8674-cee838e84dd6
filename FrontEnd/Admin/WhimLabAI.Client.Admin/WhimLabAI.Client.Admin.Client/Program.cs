using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
using Microsoft.AspNetCore.Components.Authorization;
using MudBlazor.Services;
using WhimLabAI.Client.Admin.Client.Services;
using Microsoft.Extensions.Configuration;

var builder = WebAssemblyHostBuilder.CreateDefault(args);

// Add MudBlazor services
builder.Services.AddMudServices();

// Add Authorization
builder.Services.AddAuthorizationCore();

// Add Token Storage Service
builder.Services.AddScoped<ITokenStorageService, ClientTokenStorageService>();

// Add HttpClient with error handling
builder.Services.AddScoped<ClientAuthStateProvider>();
builder.Services.AddScoped<ErrorHandlingHttpMessageHandler>();

builder.Services.AddScoped(sp => 
{
    var errorHandler = sp.GetRequiredService<ErrorHandlingHttpMessageHandler>();
    var loadingHandler = sp.GetRequiredService<LoadingHttpMessageHandler>();
    
    // Chain handlers: Loading -> Error -> HttpClient
    loadingHandler.InnerHandler = errorHandler;
    errorHandler.InnerHandler = new HttpClientHandler();
    
    var httpClient = new HttpClient(loadingHandler) 
    { 
        BaseAddress = new Uri("https://localhost:5001/"),
        Timeout = TimeSpan.FromSeconds(30)
    };
    
    return httpClient;
});

// Add Authentication State Provider
builder.Services.AddScoped<AuthenticationStateProvider>(provider => 
    provider.GetRequiredService<ClientAuthStateProvider>());

// Add Cascading Authentication State
builder.Services.AddCascadingAuthenticationState();

// Add Error Handling Services
builder.Services.AddScoped<HttpInterceptorService>();
builder.Services.AddScoped<ErrorLoggingService>();
builder.Services.AddScoped<NetworkStatusService>();

// Add Loading State Services
builder.Services.AddSingleton<ILoadingStateService, LoadingStateService>();
builder.Services.AddScoped<LoadingHttpMessageHandler>();

// Configure HttpClient for services
var apiBaseUrl = "https://localhost:5001/";

// Add ClientAuthService
builder.Services.AddHttpClient<ClientAuthService>(client =>
{
    client.BaseAddress = new Uri(apiBaseUrl);
});

// Add DashboardService
builder.Services.AddHttpClient<DashboardService>(client =>
{
    client.BaseAddress = new Uri(apiBaseUrl);
});

// Add CustomerUserService
builder.Services.AddHttpClient<CustomerUserService>(client =>
{
    client.BaseAddress = new Uri(apiBaseUrl);
});

// Add AdminUserService
builder.Services.AddHttpClient<AdminUserService>(client =>
{
    client.BaseAddress = new Uri(apiBaseUrl);
});

// Add RoleService
builder.Services.AddHttpClient<RoleService>(client =>
{
    client.BaseAddress = new Uri(apiBaseUrl);
});

// Add AgentService
builder.Services.AddHttpClient<AgentService>(client =>
{
    client.BaseAddress = new Uri(apiBaseUrl);
});

// Add ConversationService
builder.Services.AddHttpClient<ConversationService>(client =>
{
    client.BaseAddress = new Uri(apiBaseUrl);
});

// Add SubscriptionService
builder.Services.AddHttpClient<SubscriptionService>(client =>
{
    client.BaseAddress = new Uri(apiBaseUrl);
});

// Add OrderService
builder.Services.AddHttpClient<OrderService>(client =>
{
    client.BaseAddress = new Uri(apiBaseUrl);
});

// Add CouponService
builder.Services.AddHttpClient<CouponService>(client =>
{
    client.BaseAddress = new Uri(apiBaseUrl);
});

// Add AnalyticsService
builder.Services.AddHttpClient<AnalyticsService>(client =>
{
    client.BaseAddress = new Uri(apiBaseUrl);
});

// Add SystemSettingsService
builder.Services.AddHttpClient<SystemSettingsService>(client =>
{
    client.BaseAddress = new Uri(apiBaseUrl);
});

// Add LogService
builder.Services.AddHttpClient<LogService>(client =>
{
    client.BaseAddress = new Uri(apiBaseUrl);
});

// Add MonitoringService
builder.Services.AddHttpClient<MonitoringService>(client =>
{
    client.BaseAddress = new Uri(apiBaseUrl);
});

// Add ProfileService
builder.Services.AddHttpClient<ProfileService>(client =>
{
    client.BaseAddress = new Uri(apiBaseUrl);
});

// Add Logging
builder.Logging.SetMinimumLevel(LogLevel.Information);

await builder.Build().RunAsync();
