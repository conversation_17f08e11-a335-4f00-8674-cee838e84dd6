using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Components.Server.ProtectedBrowserStorage;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using Microsoft.IdentityModel.Tokens;
using Microsoft.AspNetCore.Http;

namespace WhimLabAI.Client.Admin.Services;

public class ServerAuthStateProvider : AuthenticationStateProvider
{
    private readonly ProtectedLocalStorage _localStorage;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ILogger<ServerAuthStateProvider> _logger;
    
    private static readonly AuthenticationState Anonymous = 
        new(new ClaimsPrincipal(new ClaimsIdentity()));
    
    private ClaimsPrincipal? _cachedPrincipal;

    public ServerAuthStateProvider(
        ProtectedLocalStorage localStorage,
        IHttpContextAccessor httpContextAccessor,
        ILogger<ServerAuthStateProvider> logger)
    {
        _localStorage = localStorage;
        _httpContextAccessor = httpContextAccessor;
        _logger = logger;
    }

    public override async Task<AuthenticationState> GetAuthenticationStateAsync()
    {
        try
        {
            // Check if we're in prerendering mode
            if (_httpContextAccessor.HttpContext != null)
            {
                var response = _httpContextAccessor.HttpContext.Response;
                // During prerendering, response hasn't started yet
                if (!response.HasStarted)
                {
                    // Try to get token from cookie during prerendering
                    var tokenFromCookie = GetTokenFromCookie();
                    if (!string.IsNullOrEmpty(tokenFromCookie))
                    {
                        try
                        {
                            var cookiePrincipal = ValidateToken(tokenFromCookie);
                            _cachedPrincipal = cookiePrincipal;
                            return new AuthenticationState(cookiePrincipal);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Cookie中的token验证失败");
                        }
                    }
                    // During prerendering, return anonymous if no valid cookie
                    return Anonymous;
                }
                
                // Not in prerendering, check cookie first
                var tokenFromCookieAfterRender = GetTokenFromCookie();
                if (!string.IsNullOrEmpty(tokenFromCookieAfterRender))
                {
                    try
                    {
                        var cookiePrincipal = ValidateToken(tokenFromCookieAfterRender);
                        _cachedPrincipal = cookiePrincipal;
                        return new AuthenticationState(cookiePrincipal);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Cookie中的token验证失败");
                        // 清除无效的cookie
                        ClearAuthCookies();
                    }
                }
            }

            // 如果有缓存的Principal，直接返回
            if (_cachedPrincipal != null)
            {
                return new AuthenticationState(_cachedPrincipal);
            }

            // 检查是否在预渲染阶段
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext != null && !httpContext.Response.HasStarted)
            {
                // 在预渲染阶段，返回匿名用户
                return Anonymous;
            }

            try
            {
                // 从localStorage获取token
                var result = await _localStorage.GetAsync<string>("adminToken");
                if (!result.Success || string.IsNullOrEmpty(result.Value))
                {
                    return Anonymous;
                }

                var principal = ValidateToken(result.Value);
                _cachedPrincipal = principal;
                return new AuthenticationState(principal);
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("JavaScript interop"))
            {
                // 如果仍然在预渲染阶段，返回匿名用户
                _logger.LogDebug("JavaScript interop not available during prerendering, returning anonymous user");
                return Anonymous;
            }

            return Anonymous;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取认证状态时出错");
            return Anonymous;
        }
    }

    public async Task MarkUserAsAuthenticated(string token)
    {
        try
        {
            await _localStorage.SetAsync("adminToken", token);
            
            // 同时设置Cookie以支持SSR
            SetAuthCookie(token);
            
            var principal = ValidateToken(token);
            _cachedPrincipal = principal;
            NotifyAuthenticationStateChanged(Task.FromResult(new AuthenticationState(principal)));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "标记用户为已认证时出错");
            throw;
        }
    }

    public async Task NotifyUserAuthentication(string token, object user)
    {
        await MarkUserAsAuthenticated(token);
    }

    public async Task NotifyUserLogout()
    {
        await MarkUserAsLoggedOut();
    }

    public async Task MarkUserAsLoggedOut()
    {
        await _localStorage.DeleteAsync("adminToken");
        await _localStorage.DeleteAsync("adminRefreshToken");
        
        // 清除Cookies
        ClearAuthCookies();
        
        _cachedPrincipal = null;
        NotifyAuthenticationStateChanged(Task.FromResult(Anonymous));
    }

    /// <summary>
    /// 获取访问令牌
    /// </summary>
    public string? GetAccessToken()
    {
        try
        {
            // 在SSR期间，优先从Cookie获取
            if (_httpContextAccessor.HttpContext != null)
            {
                var tokenFromCookie = GetTokenFromCookie();
                if (!string.IsNullOrEmpty(tokenFromCookie))
                {
                    return tokenFromCookie;
                }
                
                // During prerendering, don't try to access localStorage
                if (!_httpContextAccessor.HttpContext.Response.HasStarted)
                {
                    return null;
                }
            }
            
            var result = _localStorage.GetAsync<string>("adminToken").Result;
            return result.Success ? result.Value : null;
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// 获取刷新令牌
    /// </summary>
    public string? GetRefreshToken()
    {
        try
        {
            // 在SSR期间，优先从Cookie获取
            if (_httpContextAccessor.HttpContext != null)
            {
                var tokenFromCookie = GetRefreshTokenFromCookie();
                if (!string.IsNullOrEmpty(tokenFromCookie))
                {
                    return tokenFromCookie;
                }
                
                // During prerendering, don't try to access localStorage
                if (!_httpContextAccessor.HttpContext.Response.HasStarted)
                {
                    return null;
                }
            }
            
            var result = _localStorage.GetAsync<string>("adminRefreshToken").Result;
            return result.Success ? result.Value : null;
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// 更新令牌
    /// </summary>
    public async Task UpdateTokenAsync(string accessToken, string? refreshToken)
    {
        await _localStorage.SetAsync("adminToken", accessToken);
        if (!string.IsNullOrEmpty(refreshToken))
        {
            await _localStorage.SetAsync("adminRefreshToken", refreshToken);
            SetRefreshTokenCookie(refreshToken);
        }
        
        // 更新Cookie
        SetAuthCookie(accessToken);
        
        var principal = ValidateToken(accessToken);
        _cachedPrincipal = principal;
        NotifyAuthenticationStateChanged(Task.FromResult(new AuthenticationState(principal)));
    }

    /// <summary>
    /// 注销
    /// </summary>
    public async Task LogoutAsync()
    {
        await MarkUserAsLoggedOut();
    }

    private ClaimsPrincipal ValidateToken(string token)
    {
        var handler = new JwtSecurityTokenHandler();
        
        try
        {
            var jwt = handler.ReadJwtToken(token);
            
            // 检查token是否过期
            if (jwt.ValidTo < DateTime.UtcNow)
            {
                throw new SecurityTokenExpiredException("Token has expired");
            }
            
            // 创建身份
            var identity = new ClaimsIdentity(jwt.Claims, "jwt");
            
            // 验证是否为管理员用户
            var roleClaims = jwt.Claims.Where(c => c.Type == ClaimTypes.Role).Select(c => c.Value.ToLower());
            if (!roleClaims.Any(r => r.Contains("admin")))
            {
                throw new UnauthorizedAccessException("Invalid admin token");
            }
            
            return new ClaimsPrincipal(identity);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Token validation failed");
            throw new UnauthorizedAccessException("Invalid token", ex);
        }
    }
    
    private string? GetTokenFromCookie()
    {
        var httpContext = _httpContextAccessor.HttpContext;
        if (httpContext?.Request.Cookies.TryGetValue("admin-auth-token", out var token) == true)
        {
            return token;
        }
        return null;
    }
    
    private string? GetRefreshTokenFromCookie()
    {
        var httpContext = _httpContextAccessor.HttpContext;
        if (httpContext?.Request.Cookies.TryGetValue("admin-refresh-token", out var token) == true)
        {
            return token;
        }
        return null;
    }
    
    private void SetAuthCookie(string token)
    {
        var httpContext = _httpContextAccessor.HttpContext;
        if (httpContext != null)
        {
            var cookieOptions = new CookieOptions
            {
                HttpOnly = true,
                Secure = true,
                SameSite = SameSiteMode.Strict,
                Expires = DateTimeOffset.UtcNow.AddHours(1) // Token有效期
            };
            
            httpContext.Response.Cookies.Append("admin-auth-token", token, cookieOptions);
        }
    }
    
    private void SetRefreshTokenCookie(string refreshToken)
    {
        var httpContext = _httpContextAccessor.HttpContext;
        if (httpContext != null)
        {
            var cookieOptions = new CookieOptions
            {
                HttpOnly = true,
                Secure = true,
                SameSite = SameSiteMode.Strict,
                Expires = DateTimeOffset.UtcNow.AddDays(7) // Refresh token有效期更长
            };
            
            httpContext.Response.Cookies.Append("admin-refresh-token", refreshToken, cookieOptions);
        }
    }
    
    private void ClearAuthCookies()
    {
        var httpContext = _httpContextAccessor.HttpContext;
        if (httpContext != null)
        {
            httpContext.Response.Cookies.Delete("admin-auth-token");
            httpContext.Response.Cookies.Delete("admin-refresh-token");
        }
    }
}