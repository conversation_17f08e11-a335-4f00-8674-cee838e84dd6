using Microsoft.AspNetCore.Components.Authorization;
using System.Net.Http.Json;
using WhimLabAI.Shared.Dtos;
using WhimLabAI.Client.Admin.Client.Services;
using System.Text.Json;

namespace WhimLabAI.Client.Admin.Services;

/// <summary>
/// Server-side authentication service for SSR mode
/// </summary>
public class ClientAuthService
{
    private readonly HttpClient _httpClient;
    private readonly AuthenticationStateProvider _authStateProvider;
    private readonly ILogger<ClientAuthService> _logger;

    public ClientAuthService(
        IHttpClientFactory httpClientFactory,
        AuthenticationStateProvider authStateProvider,
        ILogger<ClientAuthService> logger)
    {
        _httpClient = httpClientFactory.CreateClient("API");
        _authStateProvider = authStateProvider;
        _logger = logger;
    }

    public async Task<ApiResponse<LoginResponse>> LoginAsync(string userName, string password, string captchaCode, string captchaId, bool rememberMe)
    {
        try
        {
            var loginRequest = new
            {
                Account = userName,
                Password = password,
                LoginMethod = 0,
                CaptchaCode = captchaCode,
                CaptchaId = captchaId
            };

            _logger.LogInformation($"Sending login request to {_httpClient.BaseAddress}api/v1/admin/auth/login");

            var response = await _httpClient.PostAsJsonAsync("api/v1/admin/auth/login", loginRequest);
            var content = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                var result = JsonSerializer.Deserialize<ApiResponse<AdminAuthResponseDto>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (result != null && result.Data != null)
                {
                    // Map AdminAuthResponseDto to LoginResponse
                    var loginResponse = new LoginResponse
                    {
                        AccessToken = result.Data.AccessToken,
                        RefreshToken = result.Data.RefreshToken,
                        ExpiresIn = result.Data.ExpiresIn,
                        User = new AdminUserDto
                        {
                            Id = result.Data.User.Id,
                            Username = result.Data.User.Username,
                            Email = result.Data.User.Email,
                            Nickname = result.Data.User.Nickname,
                            Avatar = result.Data.User.Avatar,
                            IsSuper = result.Data.User.IsSuper,
                            LastLoginAt = result.Data.User.LastLoginAt,
                            LastLoginIp = result.Data.User.LastLoginIp,
                            Roles = result.Data.User.Roles,
                            Permissions = result.Data.User.Permissions
                        },
                        RequiresTwoFactor = result.Data.RequiresTwoFactor,
                        TwoFactorChallengeToken = result.Data.TwoFactorChallengeToken,
                        RequirePasswordChange = result.Data.RequirePasswordChange,
                        PasswordChangeToken = result.Data.PasswordChangeToken,
                        Message = result.Data.Message
                    };

                    // Update authentication state using database sessions
                    if (_authStateProvider is DatabaseAuthStateProvider databaseProvider)
                    {
                        // Generate a secure session token
                        var sessionToken = Guid.NewGuid().ToString("N");
                        await databaseProvider.MarkUserAsAuthenticated(sessionToken, loginResponse.User.Id);
                    }

                    return new ApiResponse<LoginResponse>
                    {
                        Success = result.Success,
                        Message = result.Message,
                        Data = loginResponse
                    };
                }

                return new ApiResponse<LoginResponse>
                {
                    Success = false,
                    Message = "Invalid response format"
                };
            }
            else
            {
                _logger.LogError($"Login failed with status {response.StatusCode}. Response content: {content}");
                
                var errorResult = JsonSerializer.Deserialize<ApiResponse<object>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                return new ApiResponse<LoginResponse>
                {
                    Success = false,
                    Message = errorResult?.Message ?? "Login failed"
                };
            }
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "Network error during login");
            return new ApiResponse<LoginResponse>
            {
                Success = false,
                Message = "网络连接失败，请检查网络设置"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during login");
            return new ApiResponse<LoginResponse>
            {
                Success = false,
                Message = "登录失败，请稍后重试"
            };
        }
    }

    public async Task<CaptchaResponse?> GetCaptchaAsync()
    {
        try
        {
            _logger.LogInformation($"GetCaptchaAsync called, HttpClient BaseAddress: {_httpClient.BaseAddress}");
            var response = await _httpClient.GetAsync("api/v1/admin/auth/captcha");
            _logger.LogInformation($"Captcha API response status: {response.StatusCode}");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                _logger.LogInformation($"Captcha API response content length: {content.Length}");
                var result = JsonSerializer.Deserialize<ApiResponse<CaptchaResponse>>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
                
                return result?.Data;
            }
            
            _logger.LogWarning($"Captcha API returned {response.StatusCode}");
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting captcha");
            return null;
        }
    }

    public async Task LogoutAsync()
    {
        try
        {
            await _httpClient.PostAsync("api/v1/admin/auth/logout", null);
            
            if (_authStateProvider is DatabaseAuthStateProvider databaseProvider)
                await databaseProvider.MarkUserAsLoggedOut();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during logout");
        }
    }
}