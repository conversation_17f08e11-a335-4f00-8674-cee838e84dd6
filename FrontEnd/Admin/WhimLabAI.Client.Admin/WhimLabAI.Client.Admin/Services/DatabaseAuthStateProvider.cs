using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Components.Server.ProtectedBrowserStorage;
using System.Security.Claims;
using System.IdentityModel.Tokens.Jwt;
using WhimLabAI.Domain.Repositories;
using Microsoft.Extensions.Caching.Memory;

namespace WhimLabAI.Client.Admin.Services;

/// <summary>
/// Server-side authentication state provider that uses database sessions
/// </summary>
public class DatabaseAuthStateProvider : AuthenticationStateProvider
{
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IAdminSessionRepository _sessionRepository;
    private readonly IMemoryCache _memoryCache;
    private readonly ILogger<DatabaseAuthStateProvider> _logger;
    
    private const string SessionCookieName = "WhimLabAI.Admin.SessionId";
    private const string CacheKeyPrefix = "admin_session_";
    private static readonly TimeSpan CacheDuration = TimeSpan.FromMinutes(5);
    
    private static readonly AuthenticationState Anonymous = 
        new(new ClaimsPrincipal(new ClaimsIdentity()));

    public DatabaseAuthStateProvider(
        IHttpContextAccessor httpContextAccessor,
        IAdminSessionRepository sessionRepository,
        IMemoryCache memoryCache,
        ILogger<DatabaseAuthStateProvider> logger)
    {
        _httpContextAccessor = httpContextAccessor;
        _sessionRepository = sessionRepository;
        _memoryCache = memoryCache;
        _logger = logger;
    }

    public override async Task<AuthenticationState> GetAuthenticationStateAsync()
    {
        try
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext == null)
            {
                return Anonymous;
            }

            // Get session ID from HTTP-only cookie
            var sessionId = httpContext.Request.Cookies[SessionCookieName];
            if (string.IsNullOrEmpty(sessionId))
            {
                return Anonymous;
            }

            // Try to get session from cache first
            var cacheKey = $"{CacheKeyPrefix}{sessionId}";
            if (_memoryCache.TryGetValue<ClaimsPrincipal>(cacheKey, out var cachedPrincipal))
            {
                return new AuthenticationState(cachedPrincipal);
            }

            // Get session from database
            var session = await _sessionRepository.GetByTokenAsync(sessionId);
            if (session == null || session.IsExpired() || !session.IsFullyAuthenticated())
            {
                return Anonymous;
            }

            // Update last activity
            session.UpdateActivity();
            // The repository doesn't have UpdateAsync, so we'll skip updating here
            // The activity is tracked in the session object

            // Create claims principal
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, session.AdminUserId.ToString()),
                new Claim("SessionId", session.Id.ToString()),
                new Claim("SessionToken", session.SessionToken)
            };

            // Add role claims (you might need to fetch these from AdminUser)
            claims.Add(new Claim(ClaimTypes.Role, "Admin"));

            var identity = new ClaimsIdentity(claims, "DatabaseSession");
            var principal = new ClaimsPrincipal(identity);

            // Cache the principal
            _memoryCache.Set(cacheKey, principal, CacheDuration);

            return new AuthenticationState(principal);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting authentication state");
            return Anonymous;
        }
    }

    public async Task MarkUserAsAuthenticated(string sessionToken, Guid adminUserId)
    {
        try
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext == null)
            {
                throw new InvalidOperationException("HttpContext is not available");
            }

            // Create session
            var session = new Domain.Entities.System.AdminUserSession(
                adminUserId: adminUserId,
                sessionToken: sessionToken,
                ipAddress: httpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown",
                userAgent: httpContext.Request.Headers["User-Agent"].ToString(),
                expiresAt: DateTime.UtcNow.AddHours(8), // 8 hour session
                requiresMfa: false
            );

            await _sessionRepository.AddAsync(session);

            // Set HTTP-only cookie
            httpContext.Response.Cookies.Append(SessionCookieName, sessionToken, new CookieOptions
            {
                HttpOnly = true,
                Secure = true,
                SameSite = SameSiteMode.Strict,
                Expires = session.ExpiresAt,
                Path = "/"
            });

            // Create claims
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, adminUserId.ToString()),
                new Claim("SessionId", session.Id.ToString()),
                new Claim("SessionToken", sessionToken),
                new Claim(ClaimTypes.Role, "Admin")
            };

            var identity = new ClaimsIdentity(claims, "DatabaseSession");
            var principal = new ClaimsPrincipal(identity);

            // Cache the principal
            var cacheKey = $"{CacheKeyPrefix}{sessionToken}";
            _memoryCache.Set(cacheKey, principal, CacheDuration);

            NotifyAuthenticationStateChanged(Task.FromResult(new AuthenticationState(principal)));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error marking user as authenticated");
            throw;
        }
    }

    public async Task MarkUserAsLoggedOut()
    {
        try
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext == null)
            {
                return;
            }

            // Get session ID from cookie
            var sessionId = httpContext.Request.Cookies[SessionCookieName];
            if (!string.IsNullOrEmpty(sessionId))
            {
                // Invalidate session in database
                await _sessionRepository.InvalidateSessionAsync(sessionId);

                // Remove from cache
                var cacheKey = $"{CacheKeyPrefix}{sessionId}";
                _memoryCache.Remove(cacheKey);

                // Delete cookie
                httpContext.Response.Cookies.Delete(SessionCookieName);
            }

            NotifyAuthenticationStateChanged(Task.FromResult(Anonymous));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error marking user as logged out");
        }
    }
}