<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>


  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.7" />
    <PackageReference Include="Microsoft.AspNetCore.Components.Authorization" Version="9.0.7" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer.NetTopologySuite" Version="9.0.7" />
    <ProjectReference Include="..\WhimLabAI.Client.Admin.Client\WhimLabAI.Client.Admin.Client.csproj" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Server" Version="9.*" />
  </ItemGroup>


  <ItemGroup>
    <ProjectReference Include="..\..\..\..\BackEnd\WhimLabAI.Shared\WhimLabAI.Shared.csproj" />
    <ProjectReference Include="..\..\..\..\BackEnd\WhimLabAI.Infrastructure\WhimLabAI.Infrastructure.csproj" />
    <ProjectReference Include="..\..\..\..\BackEnd\WhimLabAI.Domain\WhimLabAI.Domain.csproj" />
  </ItemGroup>

</Project>