namespace WhimLabAI.Shared.Options;

/// <summary>
/// 外部服务配置选项
/// </summary>
public class ExternalServiceOptions
{
    /// <summary>
    /// 配置节名称
    /// </summary>
    public const string SectionName = "ExternalServices";
    
    /// <summary>
    /// 阿里云短信服务配置
    /// </summary>
    public AliyunSmsOptions AliyunSms { get; set; } = new();
    
    /// <summary>
    /// Twilio短信服务配置
    /// </summary>
    public TwilioOptions Twilio { get; set; } = new();
    
    /// <summary>
    /// 性能基准测试配置
    /// </summary>
    public BenchmarkOptions Benchmark { get; set; } = new();
}

/// <summary>
/// 阿里云短信配置
/// </summary>
public class AliyunSmsOptions
{
    public string ApiUrl { get; set; } = "https://dysmsapi.aliyuncs.com";
}

/// <summary>
/// Twilio配置
/// </summary>
public class TwilioOptions
{
    public string ApiUrl { get; set; } = "https://api.twilio.com/2010-04-01/Accounts";
}

/// <summary>
/// 性能基准测试配置
/// </summary>
public class BenchmarkOptions
{
    public string BaseUrl { get; set; } = "https://localhost:5001";
    public int MaxKeysToAnalyze { get; set; } = 5000;
}