namespace WhimLabAI.Shared.Constants;

/// <summary>
/// 权限常量定义
/// </summary>
public static class PermissionConstants
{
    /// <summary>
    /// 用户管理模块
    /// </summary>
    public static class User
    {
        public const string Module = "user";
        
        /// <summary>
        /// 客户用户管理
        /// </summary>
        public static class Customer
        {
            public const string View = "user.customer.view";
            public const string Create = "user.customer.create";
            public const string Edit = "user.customer.edit";
            public const string Delete = "user.customer.delete";
            public const string Export = "user.customer.export";
        }
        
        /// <summary>
        /// 管理员管理
        /// </summary>
        public static class Admin
        {
            public const string View = "user.admin.view";
            public const string Create = "user.admin.create";
            public const string Edit = "user.admin.edit";
            public const string Delete = "user.admin.delete";
            public const string AssignRole = "user.admin.assign_role";
        }
    }
    
    /// <summary>
    /// 权限管理模块
    /// </summary>
    public static class Permission
    {
        public const string Module = "permission";
        
        /// <summary>
        /// 角色管理
        /// </summary>
        public static class Role
        {
            public const string View = "permission.role.view";
            public const string Create = "permission.role.create";
            public const string Edit = "permission.role.edit";
            public const string Delete = "permission.role.delete";
            public const string AssignPermission = "permission.role.assign_permission";
        }
        
        /// <summary>
        /// 权限管理
        /// </summary>
        public static class Perm
        {
            public const string View = "permission.perm.view";
            public const string Edit = "permission.perm.edit";
        }
    }
    
    /// <summary>
    /// 智能体管理模块
    /// </summary>
    public static class Agent
    {
        public const string Module = "agent";
        
        public const string View = "agent.view";
        public const string Create = "agent.create";
        public const string Edit = "agent.edit";
        public const string Delete = "agent.delete";
        public const string Publish = "agent.publish";
        public const string Archive = "agent.archive";
        public const string Version = "agent.version";
        public const string Config = "agent.config";
    }
    
    /// <summary>
    /// 对话管理模块
    /// </summary>
    public static class Conversation
    {
        public const string Module = "conversation";
        
        public const string View = "conversation.view";
        public const string Export = "conversation.export";
        public const string Delete = "conversation.delete";
        public const string Monitor = "conversation.monitor";
    }
    
    /// <summary>
    /// 订阅管理模块
    /// </summary>
    public static class Subscription
    {
        public const string Module = "subscription";
        
        public const string View = "subscription.view";
        public const string Create = "subscription.create";
        public const string Edit = "subscription.edit";
        public const string Cancel = "subscription.cancel";
        public const string Renew = "subscription.renew";
        
        /// <summary>
        /// 套餐管理
        /// </summary>
        public static class Plan
        {
            public const string View = "subscription.plan.view";
            public const string Create = "subscription.plan.create";
            public const string Edit = "subscription.plan.edit";
            public const string Delete = "subscription.plan.delete";
        }
    }
    
    /// <summary>
    /// 支付管理模块
    /// </summary>
    public static class Payment
    {
        public const string Module = "payment";
        
        public const string View = "payment.view";
        public const string Export = "payment.export";
        public const string Refund = "payment.refund";
        public const string Reconciliation = "payment.reconciliation";
        
        /// <summary>
        /// 优惠券管理
        /// </summary>
        public static class Coupon
        {
            public const string View = "payment.coupon.view";
            public const string Create = "payment.coupon.create";
            public const string Edit = "payment.coupon.edit";
            public const string Delete = "payment.coupon.delete";
            public const string Issue = "payment.coupon.issue";
        }
    }
    
    /// <summary>
    /// 系统管理模块
    /// </summary>
    public static class System
    {
        public const string Module = "system";
        
        public const string Config = "system.config";
        public const string Log = "system.log";
        public const string Monitor = "system.monitor";
        public const string Backup = "system.backup";
        public const string Maintenance = "system.maintenance";
    }
    
    /// <summary>
    /// 数据分析模块
    /// </summary>
    public static class Analytics
    {
        public const string Module = "analytics";
        
        public const string Dashboard = "analytics.dashboard";
        public const string UserReport = "analytics.user_report";
        public const string RevenueReport = "analytics.revenue_report";
        public const string AgentReport = "analytics.agent_report";
        public const string Export = "analytics.export";
    }
}