using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Shared.Dtos.Invoice;

/// <summary>
/// 创建发票DTO
/// </summary>
public class CreateInvoiceDto
{
    /// <summary>
    /// 客户用户ID
    /// </summary>
    public Guid CustomerUserId { get; set; }
    
    /// <summary>
    /// 关联的订阅ID（可选）
    /// </summary>
    public Guid? SubscriptionId { get; set; }
    
    /// <summary>
    /// 关联的订单ID（可选）
    /// </summary>
    public Guid? OrderId { get; set; }
    
    /// <summary>
    /// 到期天数（默认30天）
    /// </summary>
    public int DueDays { get; set; } = 30;
    
    /// <summary>
    /// 发票项目列表
    /// </summary>
    public List<CreateInvoiceItemDto> Items { get; set; } = new();
    
    /// <summary>
    /// 折扣金额
    /// </summary>
    public decimal? DiscountAmount { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public string? Notes { get; set; }
    
    /// <summary>
    /// 公司信息
    /// </summary>
    public CompanyInfoDto? CompanyInfo { get; set; }
    
    /// <summary>
    /// 是否立即发出
    /// </summary>
    public bool IssueImmediately { get; set; } = false;
}

/// <summary>
/// 创建发票项目DTO
/// </summary>
public class CreateInvoiceItemDto
{
    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; } = string.Empty;
    
    /// <summary>
    /// 数量
    /// </summary>
    public decimal Quantity { get; set; }
    
    /// <summary>
    /// 单价
    /// </summary>
    public decimal UnitPrice { get; set; }
    
    /// <summary>
    /// 税率（默认6%）
    /// </summary>
    public decimal TaxRate { get; set; } = 0.06m;
}

/// <summary>
/// 更新发票DTO
/// </summary>
public class UpdateInvoiceDto
{
    /// <summary>
    /// 到期日期
    /// </summary>
    public DateTime? DueDate { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public string? Notes { get; set; }
    
    /// <summary>
    /// 公司信息
    /// </summary>
    public CompanyInfoDto? CompanyInfo { get; set; }
}

/// <summary>
/// 标记发票已支付DTO
/// </summary>
public class MarkInvoicePaidDto
{
    /// <summary>
    /// 支付方式
    /// </summary>
    public string PaymentMethod { get; set; } = string.Empty;
    
    /// <summary>
    /// 支付交易ID
    /// </summary>
    public string TransactionId { get; set; } = string.Empty;
    
    /// <summary>
    /// 支付时间
    /// </summary>
    public DateTime? PaidDate { get; set; }
}

/// <summary>
/// 公司信息DTO
/// </summary>
public class CompanyInfoDto
{
    /// <summary>
    /// 公司名称
    /// </summary>
    public string CompanyName { get; set; } = string.Empty;
    
    /// <summary>
    /// 税号
    /// </summary>
    public string TaxId { get; set; } = string.Empty;
    
    /// <summary>
    /// 地址
    /// </summary>
    public string? Address { get; set; }
    
    /// <summary>
    /// 电话
    /// </summary>
    public string? Phone { get; set; }
    
    /// <summary>
    /// 开户银行
    /// </summary>
    public string? BankName { get; set; }
    
    /// <summary>
    /// 银行账号
    /// </summary>
    public string? BankAccount { get; set; }
}

/// <summary>
/// 发票项目DTO
/// </summary>
public class InvoiceItemDto
{
    /// <summary>
    /// ID
    /// </summary>
    public Guid Id { get; set; }
    
    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; } = string.Empty;
    
    /// <summary>
    /// 数量
    /// </summary>
    public decimal Quantity { get; set; }
    
    /// <summary>
    /// 单价
    /// </summary>
    public decimal UnitPrice { get; set; }
    
    /// <summary>
    /// 税率
    /// </summary>
    public decimal TaxRate { get; set; }
    
    /// <summary>
    /// 金额
    /// </summary>
    public decimal Amount { get; set; }
    
    /// <summary>
    /// 税额
    /// </summary>
    public decimal TaxAmount { get; set; }
    
    /// <summary>
    /// 总金额
    /// </summary>
    public decimal TotalAmount { get; set; }
}

/// <summary>
/// 发票统计DTO
/// </summary>
public class InvoiceStatisticsDto
{
    /// <summary>
    /// 总发票数
    /// </summary>
    public int TotalInvoices { get; set; }
    
    /// <summary>
    /// 已支付发票数
    /// </summary>
    public int PaidInvoices { get; set; }
    
    /// <summary>
    /// 待支付发票数
    /// </summary>
    public int PendingInvoices { get; set; }
    
    /// <summary>
    /// 逾期发票数
    /// </summary>
    public int OverdueInvoices { get; set; }
    
    /// <summary>
    /// 总金额
    /// </summary>
    public decimal TotalAmount { get; set; }
    
    /// <summary>
    /// 已支付金额
    /// </summary>
    public decimal PaidAmount { get; set; }
    
    /// <summary>
    /// 待支付金额
    /// </summary>
    public decimal PendingAmount { get; set; }
    
    /// <summary>
    /// 逾期金额
    /// </summary>
    public decimal OverdueAmount { get; set; }
    
    /// <summary>
    /// 月度统计
    /// </summary>
    public List<MonthlyInvoiceStatDto> MonthlyStats { get; set; } = new();
}

/// <summary>
/// 月度发票统计DTO
/// </summary>
public class MonthlyInvoiceStatDto
{
    /// <summary>
    /// 年月（格式：YYYY-MM）
    /// </summary>
    public string Month { get; set; } = string.Empty;
    
    /// <summary>
    /// 发票数量
    /// </summary>
    public int Count { get; set; }
    
    /// <summary>
    /// 总金额
    /// </summary>
    public decimal Amount { get; set; }
}

/// <summary>
/// 发票DTO
/// </summary>
public class InvoiceDto
{
    public Guid Id { get; set; }
    public string InvoiceNumber { get; set; } = string.Empty;
    public Guid CustomerUserId { get; set; }
    public string CustomerName { get; set; } = string.Empty;
    public string CustomerEmail { get; set; } = string.Empty;
    public Guid? SubscriptionId { get; set; }
    public Guid? OrderId { get; set; }
    public DateTime IssueDate { get; set; }
    public DateTime DueDate { get; set; }
    public DateTime? PaidDate { get; set; }
    public decimal SubtotalAmount { get; set; }
    public decimal? DiscountAmount { get; set; }
    public decimal TaxAmount { get; set; }
    public decimal TotalAmount { get; set; }
    public InvoiceStatus Status { get; set; }
    public string? PaymentMethod { get; set; }
    public string? TransactionId { get; set; }
    public string? Notes { get; set; }
    public string? CancellationReason { get; set; }
    public decimal? RefundAmount { get; set; }
    public string? RefundReason { get; set; }
    public DateTime? RefundedAt { get; set; }
    public CompanyInfoDto? CompanyInfo { get; set; }
    public List<InvoiceItemDto> Items { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

/// <summary>
/// 发票批量操作结果DTO
/// </summary>
public class InvoiceBatchOperationResultDto
{
    public int SuccessCount { get; set; }
    public int FailureCount { get; set; }
    public List<InvoiceOperationResultDto> Results { get; set; } = new();
}

/// <summary>
/// 发票操作结果DTO
/// </summary>
public class InvoiceOperationResultDto
{
    public Guid? InvoiceId { get; set; }
    public string? InvoiceNumber { get; set; }
    public bool Success { get; set; }
    public string? Error { get; set; }
}

/// <summary>
/// 客户发票统计DTO
/// </summary>
public class CustomerInvoiceStatisticsDto
{
    public Guid CustomerUserId { get; set; }
    public string CustomerName { get; set; } = string.Empty;
    public int TotalInvoices { get; set; }
    public decimal TotalAmount { get; set; }
    public decimal PaidAmount { get; set; }
    public decimal UnpaidAmount { get; set; }
    public decimal OverdueAmount { get; set; }
    public Dictionary<InvoiceStatus, int> StatusCounts { get; set; } = new();
    public List<MonthlyInvoiceSummaryDto> MonthlyBreakdown { get; set; } = new();
}

/// <summary>
/// 月度发票摘要DTO
/// </summary>
public class MonthlyInvoiceSummaryDto
{
    public string Month { get; set; } = string.Empty;
    public int InvoiceCount { get; set; }
    public decimal TotalAmount { get; set; }
    public decimal PaidAmount { get; set; }
    public decimal UnpaidAmount { get; set; }
}

/// <summary>
/// 取消发票DTO
/// </summary>
public class CancelInvoiceDto
{
    public string Reason { get; set; } = string.Empty;
}

/// <summary>
/// 退款发票DTO
/// </summary>
public class RefundInvoiceDto
{
    public decimal RefundAmount { get; set; }
    public string Reason { get; set; } = string.Empty;
}

/// <summary>
/// 发送发票邮件DTO
/// </summary>
public class SendInvoiceEmailDto
{
    public string? Email { get; set; }
}

/// <summary>
/// 批量发送发票邮件DTO
/// </summary>
public class BatchSendInvoiceEmailDto
{
    public List<Guid> InvoiceIds { get; set; } = new();
}