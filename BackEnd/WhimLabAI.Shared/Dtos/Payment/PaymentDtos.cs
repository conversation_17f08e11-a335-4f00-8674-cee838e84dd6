using System;
using System.Collections.Generic;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Shared.Dtos.Payment;

/// <summary>
/// 支付请求
/// </summary>
public class PaymentRequest
{
    public string OrderId { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public string Currency { get; set; } = "CNY";
    public PaymentMethod Method { get; set; }
    public string Subject { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string ReturnUrl { get; set; } = string.Empty;
    public string NotifyUrl { get; set; } = string.Empty;
    public Dictionary<string, string> Metadata { get; set; } = new();
}

/// <summary>
/// 支付结果
/// </summary>
public class PaymentResult
{
    public bool Success { get; set; }
    public string TransactionId { get; set; } = string.Empty;
    public string PaymentUrl { get; set; } = string.Empty;
    public string QrCode { get; set; } = string.Empty;
    public PaymentStatus Status { get; set; }
    public string Message { get; set; } = string.Empty;
    public Dictionary<string, string> RawResponse { get; set; } = new();
}

/// <summary>
/// 退款请求
/// </summary>
public class RefundRequest
{
    public string TransactionId { get; set; } = string.Empty;
    public string RefundId { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public string Reason { get; set; } = string.Empty;
    public string NotifyUrl { get; set; } = string.Empty;
    public Dictionary<string, string> Metadata { get; set; } = new();
}

/// <summary>
/// 退款结果
/// </summary>
public class RefundResult
{
    public bool Success { get; set; }
    public string RefundId { get; set; } = string.Empty;
    public string RefundTransactionId { get; set; } = string.Empty;
    public RefundStatus Status { get; set; }
    public decimal RefundAmount { get; set; }
    public string Message { get; set; } = string.Empty;
    public DateTime? RefundTime { get; set; }
    public Dictionary<string, string> RawResponse { get; set; } = new();
}

/// <summary>
/// 创建支付订单DTO
/// </summary>
public class CreatePaymentOrderDto
{
    public Guid PlanId { get; set; }
    public string PaymentMethod { get; set; } = string.Empty;
    public string? PromoCode { get; set; }
    public string BillingCycle { get; set; } = "monthly"; // monthly, yearly
}

/// <summary>
/// 创建支付订单响应DTO
/// </summary>
public class CreatePaymentOrderResponseDto
{
    public string OrderNo { get; set; } = string.Empty;
    public string? QrCodeUrl { get; set; }
    public string? PayUrl { get; set; }
    public DateTime ExpireTime { get; set; }
}

/// <summary>
/// 支付订单状态DTO
/// </summary>
public class PaymentOrderStatusDto
{
    public string Status { get; set; } = string.Empty; // pending, success, failed, expired, cancelled, refunded, processing
    public string? Message { get; set; }
    public string OrderNumber { get; set; } = string.Empty; // 订单号
    public decimal Amount { get; set; } // 支付金额
    public string PaymentMethod { get; set; } = string.Empty; // 支付方式
    public DateTime? PaidAt { get; set; }
}

/// <summary>
/// 支付历史DTO
/// </summary>
public class PaymentHistoryDto
{
    public Guid Id { get; set; }
    public string OrderNo { get; set; } = string.Empty;
    public string OrderNumber => OrderNo; // Alias for compatibility
    public decimal Amount { get; set; }
    public decimal OriginalAmount { get; set; } // 原始金额（折扣前）
    public string PaymentMethod { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string ProductName { get; set; } = string.Empty; // 商品名称
    public string? TransactionId { get; set; } // 交易号
    public string? InvoiceUrl { get; set; } // 发票URL
    public bool CanRefund { get; set; } // 是否可退款
    public DateTime CreatedAt { get; set; }
    public DateTime? PaidAt { get; set; }
}

/// <summary>
/// 支付历史查询DTO
/// </summary>
public class PaymentHistoryQueryDto
{
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public string? PaymentMethod { get; set; }
    public string? Status { get; set; }
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 20;
}

/// <summary>
/// 支付详情DTO
/// </summary>
public class PaymentDetailDto
{
    public Guid Id { get; set; }
    public string OrderNo { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public string PaymentMethod { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string? TransactionId { get; set; }
    public string? PromoCode { get; set; }
    public decimal? DiscountAmount { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? PaidAt { get; set; }
    public DateTime? ExpiredAt { get; set; }
}

/// <summary>
/// 申请退款DTO
/// </summary>
public class RequestRefundDto
{
    public Guid PaymentId { get; set; }
    public string Reason { get; set; } = string.Empty;
    public decimal? Amount { get; set; } // 部分退款金额，null表示全额退款
}

/// <summary>
/// 退款状态DTO
/// </summary>
public class RefundStatusDto
{
    public string RefundNo { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty; // pending, processing, success, failed
    public decimal RefundAmount { get; set; }
    public string? Message { get; set; }
    public DateTime? RefundedAt { get; set; }
}

/// <summary>
/// 支付方式DTO
/// </summary>
public class PaymentMethodDto
{
    public string Code { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Icon { get; set; }
    public bool IsEnabled { get; set; }
    public string? Description { get; set; }
}

/// <summary>
/// 验证支付DTO
/// </summary>
public class VerifyPaymentDto
{
    public string OrderNo { get; set; } = string.Empty;
    public string TransactionId { get; set; } = string.Empty;
}