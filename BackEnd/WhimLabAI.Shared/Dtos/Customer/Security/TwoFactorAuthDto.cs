using System.ComponentModel.DataAnnotations;

namespace WhimLabAI.Shared.Dtos.Customer.Security;

/// <summary>
/// 启用双因素认证DTO
/// </summary>
public class EnableTwoFactorDto
{
    /// <summary>
    /// 密码（用于验证身份）
    /// </summary>
    [Required(ErrorMessage = "密码不能为空")]
    public string Password { get; set; } = string.Empty;
}

/// <summary>
/// 双因素认证设置响应DTO
/// </summary>
public class TwoFactorSetupDto
{
    /// <summary>
    /// 密钥
    /// </summary>
    public string Secret { get; set; } = string.Empty;
    
    /// <summary>
    /// 二维码URI（用于Google Authenticator等）
    /// </summary>
    public string QrCodeUri { get; set; } = string.Empty;
    
    /// <summary>
    /// 手动输入密钥（格式化后的）
    /// </summary>
    public string ManualEntryKey { get; set; } = string.Empty;
}

/// <summary>
/// 确认双因素认证DTO
/// </summary>
public class ConfirmTwoFactorDto
{
    /// <summary>
    /// 验证码
    /// </summary>
    [Required(ErrorMessage = "验证码不能为空")]
    [StringLength(6, MinimumLength = 6, ErrorMessage = "验证码必须是6位数字")]
    public string Code { get; set; } = string.Empty;
}

/// <summary>
/// 禁用双因素认证DTO
/// </summary>
public class DisableTwoFactorDto
{
    /// <summary>
    /// 密码（用于验证身份）
    /// </summary>
    [Required(ErrorMessage = "密码不能为空")]
    public string Password { get; set; } = string.Empty;
    
    /// <summary>
    /// 当前的双因素验证码
    /// </summary>
    [Required(ErrorMessage = "验证码不能为空")]
    [StringLength(6, MinimumLength = 6, ErrorMessage = "验证码必须是6位数字")]
    public string Code { get; set; } = string.Empty;
}