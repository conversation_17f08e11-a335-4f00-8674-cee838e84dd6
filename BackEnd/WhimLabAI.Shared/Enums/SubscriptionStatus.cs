using System.ComponentModel;

namespace WhimLabAI.Shared.Enums;

/// <summary>
/// 订阅状态
/// </summary>
public enum SubscriptionStatus
{
    /// <summary>
    /// 待激活
    /// </summary>
    [Description("待激活")]
    Pending = 0,

    /// <summary>
    /// 激活
    /// </summary>
    [Description("激活")]
    Active = 1,

    /// <summary>
    /// 已过期
    /// </summary>
    [Description("已过期")]
    Expired = 2,

    /// <summary>
    /// 已取消
    /// </summary>
    [Description("已取消")]
    Cancelled = 3
}