using System.ComponentModel;

namespace WhimLabAI.Shared.Enums;

/// <summary>
/// 退款状态
/// </summary>
public enum RefundStatus
{
    /// <summary>
    /// 待处理
    /// </summary>
    [Description("待处理")]
    Pending = 0,

    /// <summary>
    /// 处理中
    /// </summary>
    [Description("处理中")]
    Processing = 1,

    /// <summary>
    /// 已完成
    /// </summary>
    [Description("已完成")]
    Completed = 2,

    /// <summary>
    /// 失败
    /// </summary>
    [Description("失败")]
    Failed = 3,

    /// <summary>
    /// 已取消
    /// </summary>
    [Description("已取消")]
    Cancelled = 4
}