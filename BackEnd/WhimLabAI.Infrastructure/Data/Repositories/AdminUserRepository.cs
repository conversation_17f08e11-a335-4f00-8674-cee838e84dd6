using Microsoft.EntityFrameworkCore;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Domain.Entities.User;
using WhimLabAI.Domain.Entities.Auth;
using WhimLabAI.Domain.ValueObjects;

namespace WhimLabAI.Infrastructure.Data.Repositories;

public class AdminUserRepository : Repository<AdminUser>, IAdminUserRepository
{
    public AdminUserRepository(WhimLabAIDbContext context) : base(context)
    {
    }

    public IQueryable<AdminUser> GetQueryable()
    {
        return _dbSet.AsQueryable();
    }

    public async Task<AdminUser?> GetByUsernameAsync(string username, CancellationToken cancellationToken = default)
    {
        // 用户名不区分大小写
        // 先查询用户基本信息，避免复杂的Include
        var user = await _dbSet
            .FirstOrDefaultAsync(u => u.Username.ToLower() == username.ToLower(), cancellationToken);
            
        if (user == null)
            return null;
            
        // 分开加载相关数据
        await _context.Entry(user)
            .Collection(u => u.UserRoles)
            .Query()
            .Include(ur => ur.Role)
            .LoadAsync(cancellationToken);
            
        return user;
    }

    public async Task<AdminUser?> GetByEmailAsync(string email, CancellationToken cancellationToken = default)
    {
        var emailVO = WhimLabAI.Domain.ValueObjects.Email.CreateOrNull(email);
        if (emailVO == null)
            return null;
            
        // 暂时简化查询，避免深层Include导致的问题
        return await _dbSet
            .Include(u => u.UserRoles)
                .ThenInclude(ur => ur.Role)
            .AsSplitQuery() // 使用分割查询避免复杂JOIN
            .FirstOrDefaultAsync(u => u.Email == emailVO, cancellationToken);
    }

    public async Task<AdminUser?> GetByPhoneAsync(string phone, CancellationToken cancellationToken = default)
    {
        // 清理输入的手机号
        phone = phone.Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", "");
        
        // 处理国家码前缀
        if (phone.StartsWith("+86"))
            phone = phone.Substring(3);
        else if (phone.StartsWith("86") && phone.Length == 13)
            phone = phone.Substring(2);
            
        // 暂时简化查询，避免深层Include导致的问题
        // 对于值对象的查询，直接比较其属性
        return await _dbSet
            .Include(u => u.UserRoles)
                .ThenInclude(ur => ur.Role)
            .AsSplitQuery() // 使用分割查询避免复杂JOIN
            .FirstOrDefaultAsync(u => u.Phone != null && u.Phone.Value == phone, cancellationToken);
    }

    public async Task<bool> IsUsernameExistsAsync(string username, CancellationToken cancellationToken = default)
    {
        // 用户名不区分大小写
        return await _dbSet.AnyAsync(u => u.Username.ToLower() == username.ToLower(), cancellationToken);
    }

    public async Task<bool> IsEmailExistsAsync(string email, CancellationToken cancellationToken = default)
    {
        var emailVO = WhimLabAI.Domain.ValueObjects.Email.Create(email);
        return await _dbSet.AnyAsync(u => u.Email == emailVO, cancellationToken);
    }

    public async Task<AdminUser?> GetWithRolesAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(u => u.UserRoles)
                .ThenInclude(ur => ur.Role)
                    .ThenInclude(r => r.Permissions)
                        .ThenInclude(rp => rp.Permission)
            .AsSplitQuery() // 使用分割查询避免复杂JOIN
            .FirstOrDefaultAsync(u => u.Id == userId, cancellationToken);
    }

    public async Task<IReadOnlyList<AdminUser>> GetActiveUsersAsync(int pageNumber, int pageSize, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(u => u.Status == Shared.Enums.UserStatus.Active)
            .OrderByDescending(u => u.CreatedAt)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);
    }

    public override async Task<AdminUser?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(u => u.UserRoles)
                .ThenInclude(ur => ur.Role)
            .FirstOrDefaultAsync(u => u.Id == id, cancellationToken);
    }

    public async Task<List<Permission>> GetUserPermissionsAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        // First check if user exists and is super admin
        var userInfo = await _dbSet
            .Where(u => u.Id == userId)
            .Select(u => new { u.IsSuperAdmin })
            .FirstOrDefaultAsync(cancellationToken);
            
        if (userInfo == null)
        {
            return new List<Permission>();
        }

        // 如果是超级管理员，返回所有权限
        if (userInfo.IsSuperAdmin)
        {
            return await _context.Set<Permission>()
                .Where(p => p.IsEnabled)
                .AsNoTracking()
                .ToListAsync(cancellationToken);
        }

        // Use database query instead of in-memory operations
        var permissions = await _context.Set<AdminUserRole>()
            .Where(ur => ur.AdminUserId == userId && ur.Role != null && ur.Role.IsEnabled)
            .SelectMany(ur => ur.Role.Permissions)
            .Where(rp => rp.Permission != null && rp.Permission.IsEnabled)
            .Select(rp => rp.Permission)
            .Distinct()
            .AsNoTracking()
            .ToListAsync(cancellationToken);

        return permissions;
    }

    public async Task<bool> HasPermissionAsync(Guid userId, string permissionCode, CancellationToken cancellationToken = default)
    {
        // First check user status and super admin flag
        var userInfo = await _dbSet
            .Where(u => u.Id == userId)
            .Select(u => new { u.IsActive, u.IsSuperAdmin })
            .FirstOrDefaultAsync(cancellationToken);
            
        if (userInfo == null || !userInfo.IsActive)
        {
            return false;
        }

        // 超级管理员拥有所有权限
        if (userInfo.IsSuperAdmin)
        {
            return true;
        }

        // Use database query to check permission directly
        return await _context.Set<AdminUserRole>()
            .Where(ur => ur.AdminUserId == userId && ur.Role != null && ur.Role.IsEnabled)
            .SelectMany(ur => ur.Role.Permissions)
            .AnyAsync(rp => rp.Permission != null && 
                           rp.Permission.Code == permissionCode && 
                           rp.Permission.IsEnabled, 
                     cancellationToken);
    }
}