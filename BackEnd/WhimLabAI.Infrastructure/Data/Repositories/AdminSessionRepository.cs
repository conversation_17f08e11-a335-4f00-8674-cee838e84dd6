using Microsoft.EntityFrameworkCore;
using WhimLabAI.Domain.Entities.System;
using WhimLabAI.Domain.Repositories;
using System.Linq.Expressions;

namespace WhimLabAI.Infrastructure.Data.Repositories;

public class AdminSessionRepository : Repository<AdminUserSession>, IAdminSessionRepository
{
    public AdminSessionRepository(WhimLabAIDbContext context) : base(context)
    {
    }

    public async Task<AdminUserSession?> GetByTokenAsync(
        string token, 
        CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .FirstOrDefaultAsync(s => s.SessionToken == token, cancellationToken);
    }

    public async Task<IEnumerable<AdminUserSession>> GetActiveSessionsAsync(
        Guid adminUserId, 
        CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(s => s.AdminUserId == adminUserId && s.IsActive && s.ExpiresAt > DateTime.UtcNow)
            .OrderByDescending(s => s.LastActivityAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<bool> InvalidateSessionAsync(
        string token, 
        CancellationToken cancellationToken = default)
    {
        var session = await GetByTokenAsync(token, cancellationToken);
        if (session == null)
            return false;

        session.InvalidateSession();
        _dbSet.Update(session);
        return true;
    }

    public async Task<bool> InvalidateAllAdminSessionsAsync(
        Guid adminUserId, 
        CancellationToken cancellationToken = default)
    {
        var sessions = await _dbSet
            .Where(s => s.AdminUserId == adminUserId && s.IsActive)
            .ToListAsync(cancellationToken);

        foreach (var session in sessions)
        {
            session.InvalidateSession();
        }

        _dbSet.UpdateRange(sessions);
        return sessions.Any();
    }

    public async Task<IEnumerable<AdminUserSession>> GetExpiredSessionsAsync(
        int days, 
        CancellationToken cancellationToken = default)
    {
        var cutoffDate = DateTime.UtcNow.AddDays(-days);
        return await _dbSet
            .Where(s => s.ExpiresAt <= DateTime.UtcNow || s.LastActivityAt <= cutoffDate)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<AdminUserSession>> GetSessionsRequiringMfaAsync(
        CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(s => s.IsActive && s.RequiresMfa && !s.MfaVerified && s.ExpiresAt > DateTime.UtcNow)
            .ToListAsync(cancellationToken);
    }
    
    public async Task<(IEnumerable<AdminUserSession> items, int totalCount)> GetLoginHistoryAsync(
        Expression<Func<AdminUserSession, bool>>? predicate,
        int pageNumber,
        int pageSize,
        string sortBy,
        bool isDescending,
        CancellationToken cancellationToken = default)
    {
        var query = _dbSet.AsQueryable();
        
        if (predicate != null)
        {
            query = query.Where(predicate);
        }
        
        var totalCount = await query.CountAsync(cancellationToken);
        
        // 动态排序
        query = sortBy.ToLower() switch
        {
            "logintime" or "createdat" => isDescending ? query.OrderByDescending(s => s.CreatedAt) : query.OrderBy(s => s.CreatedAt),
            "ipaddress" => isDescending ? query.OrderByDescending(s => s.IpAddress) : query.OrderBy(s => s.IpAddress),
            "city" => isDescending ? query.OrderByDescending(s => s.City) : query.OrderBy(s => s.City),
            "lastactivity" => isDescending ? query.OrderByDescending(s => s.LastActivityAt) : query.OrderBy(s => s.LastActivityAt),
            _ => query.OrderByDescending(s => s.CreatedAt)
        };
        
        var items = await query
            .Skip(pageNumber * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);
        
        return (items, totalCount);
    }
    
    public async Task<Dictionary<string, int>> GetLoginLocationStatisticsAsync(
        Guid adminUserId, 
        int days = 30, 
        CancellationToken cancellationToken = default)
    {
        var cutoffDate = DateTime.UtcNow.AddDays(-days);
        
        var locations = await _dbSet
            .Where(s => s.AdminUserId == adminUserId && s.CreatedAt >= cutoffDate && !string.IsNullOrEmpty(s.City))
            .GroupBy(s => s.City + ", " + s.Country)
            .Select(g => new { Location = g.Key, Count = g.Count() })
            .OrderByDescending(x => x.Count)
            .Take(10)
            .ToDictionaryAsync(x => x.Location ?? "Unknown", x => x.Count, cancellationToken);
        
        return locations;
    }
    
    public async Task<bool> InvalidateSessionByIdAsync(
        Guid sessionId, 
        CancellationToken cancellationToken = default)
    {
        var session = await _dbSet.FirstOrDefaultAsync(s => s.Id == sessionId, cancellationToken);
        if (session == null)
            return false;
        
        session.InvalidateSession();
        _dbSet.Update(session);
        return true;
    }
    
    public async Task<bool> InvalidateAllAdminSessionsExceptAsync(
        Guid adminUserId, 
        Guid exceptSessionId, 
        CancellationToken cancellationToken = default)
    {
        var sessions = await _dbSet
            .Where(s => s.AdminUserId == adminUserId && s.IsActive && s.Id != exceptSessionId)
            .ToListAsync(cancellationToken);
        
        foreach (var session in sessions)
        {
            session.InvalidateSession();
        }
        
        if (sessions.Any())
        {
            _dbSet.UpdateRange(sessions);
        }
        
        return sessions.Any();
    }
}