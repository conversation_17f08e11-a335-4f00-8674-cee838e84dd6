using System.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Domain.Common;

namespace WhimLabAI.Infrastructure.Data;

public class UnitOfWork : IUnitOfWork
{
    private readonly WhimLabAIDbContext _context;
    private readonly Dictionary<Type, object> _repositories = new();
    private bool _disposed;
    private IDbContextTransaction? _currentTransaction;
    
    // Repository fields
    private readonly ICustomerUserRepository _customerUsers;
    private readonly IAdminUserRepository _adminUsers;
    private readonly IAgentRepository _agents;
    private readonly IConversationRepository _conversations;
    private readonly ISubscriptionRepository _subscriptions;
    private readonly ISubscriptionPlanRepository _subscriptionPlans;
    private readonly IUsageRecordRepository _usageRecords;
    private readonly IOrderRepository _orders;
    private readonly IPaymentRepository _payments;
    private readonly IRefundRepository _refunds;
    private readonly IRefundRecordRepository _refundRecords;
    private readonly IRoleRepository _roles;
    private readonly IPermissionRepository _permissions;
    private readonly ITokenUsageRepository _tokenUsages;
    private readonly IDifyConversationMappingRepository _difyConversationMappings;
    private readonly IKnowledgeBaseRepository _knowledgeBases;
    private readonly IDocumentRepository _documents;
    private readonly IDocumentChunkRepository _documentChunks;
    private readonly IAuditLogRepository _auditLogs;
    private readonly INotificationRepository _notifications;
    private readonly ISystemEventRepository _systemEvents;
    private readonly ICustomerSessionRepository _customerSessions;
    private readonly IAdminSessionRepository _adminSessions;
    private readonly IFileRepository _files;
    private readonly IVerificationCodeRepository _verificationCodes;
    private readonly IAgentLikeRepository _agentLikes;
    private readonly IAgentCategoryRepository _agentCategories;
    private readonly IAgentRatingRepository _agentRatings;
    private readonly IRatingHelpfulnessRepository _ratingHelpfulnesses;
    private readonly IInvoiceRepository _invoices;
    private readonly ICouponRepository _coupons;
    private readonly IQRCodeSessionRepository _qrCodeSessions;

    public UnitOfWork(
        WhimLabAIDbContext context,
        ICustomerUserRepository customerUsers,
        IAdminUserRepository adminUsers,
        IAgentRepository agents,
        IConversationRepository conversations,
        ISubscriptionRepository subscriptions,
        ISubscriptionPlanRepository subscriptionPlans,
        IUsageRecordRepository usageRecords,
        IOrderRepository orders,
        IPaymentRepository payments,
        IRefundRepository refunds,
        IRefundRecordRepository refundRecords,
        IRoleRepository roles,
        IPermissionRepository permissions,
        ITokenUsageRepository tokenUsages,
        IDifyConversationMappingRepository difyConversationMappings,
        IKnowledgeBaseRepository knowledgeBases,
        IDocumentRepository documents,
        IDocumentChunkRepository documentChunks,
        IAuditLogRepository auditLogs,
        INotificationRepository notifications,
        ISystemEventRepository systemEvents,
        ICustomerSessionRepository customerSessions,
        IAdminSessionRepository adminSessions,
        IFileRepository files,
        IVerificationCodeRepository verificationCodes,
        IAgentLikeRepository agentLikes,
        IAgentCategoryRepository agentCategories,
        IAgentRatingRepository agentRatings,
        IRatingHelpfulnessRepository ratingHelpfulnesses,
        IInvoiceRepository invoices,
        ICouponRepository coupons,
        IQRCodeSessionRepository qrCodeSessions)
    {
        _context = context;
        _customerUsers = customerUsers;
        _adminUsers = adminUsers;
        _agents = agents;
        _conversations = conversations;
        _subscriptions = subscriptions;
        _subscriptionPlans = subscriptionPlans;
        _usageRecords = usageRecords;
        _orders = orders;
        _payments = payments;
        _refunds = refunds;
        _refundRecords = refundRecords;
        _roles = roles;
        _permissions = permissions;
        _tokenUsages = tokenUsages;
        _difyConversationMappings = difyConversationMappings;
        _knowledgeBases = knowledgeBases;
        _documents = documents;
        _documentChunks = documentChunks;
        _auditLogs = auditLogs;
        _notifications = notifications;
        _systemEvents = systemEvents;
        _customerSessions = customerSessions;
        _adminSessions = adminSessions;
        _files = files;
        _verificationCodes = verificationCodes;
        _agentLikes = agentLikes;
        _agentCategories = agentCategories;
        _agentRatings = agentRatings;
        _ratingHelpfulnesses = ratingHelpfulnesses;
        _invoices = invoices;
        _coupons = coupons;
        _qrCodeSessions = qrCodeSessions;
    }

    public IRepository<T> Repository<T>() where T : Entity
    {
        var type = typeof(T);
        if (!_repositories.ContainsKey(type))
        {
            var repositoryType = typeof(Repositories.Repository<>);
            var repositoryInstance = Activator.CreateInstance(repositoryType.MakeGenericType(type), _context);
            _repositories[type] = repositoryInstance!;
        }

        return (IRepository<T>)_repositories[type];
    }

    public async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            // Handle domain events before saving
            var aggregateRoots = _context.ChangeTracker
                .Entries<AggregateRoot>()
                .Where(e => e.Entity.DomainEvents.Any())
                .Select(e => e.Entity)
                .ToList();

            // Clear domain events after processing
            // Domain events are handled by application services using MediatR
            foreach (var aggregate in aggregateRoots)
            {
                aggregate.ClearDomainEvents();
            }

            return await _context.SaveChangesAsync(cancellationToken);
        }
        catch (DbUpdateConcurrencyException ex)
        {
            throw new InvalidOperationException("并发冲突，请重试", ex);
        }
        catch (DbUpdateException ex)
        {
            throw new InvalidOperationException("数据库更新失败", ex);
        }
    }

    public async Task<IDbTransaction> BeginTransactionAsync(IsolationLevel isolationLevel = IsolationLevel.ReadCommitted, CancellationToken cancellationToken = default)
    {
        if (_currentTransaction == null)
        {
            _currentTransaction = await _context.Database.BeginTransactionAsync(isolationLevel, cancellationToken);
        }
        return _currentTransaction.GetDbTransaction();
    }

    public async Task CommitAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            await SaveChangesAsync(cancellationToken);
            
            if (_currentTransaction != null)
            {
                await _currentTransaction.CommitAsync(cancellationToken);
                _currentTransaction.Dispose();
                _currentTransaction = null;
            }
        }
        catch
        {
            await RollbackAsync(cancellationToken);
            throw;
        }
    }

    public async Task RollbackAsync(CancellationToken cancellationToken = default)
    {
        if (_currentTransaction != null)
        {
            await _currentTransaction.RollbackAsync(cancellationToken);
            _currentTransaction.Dispose();
            _currentTransaction = null;
        }
    }

    // Specific repository properties
    public ICustomerUserRepository CustomerUsers => _customerUsers;
    public IAdminUserRepository AdminUsers => _adminUsers;
    public IAgentRepository Agents => _agents;
    public IConversationRepository Conversations => _conversations;
    public ISubscriptionRepository Subscriptions => _subscriptions;
    public ISubscriptionPlanRepository SubscriptionPlans => _subscriptionPlans;
    public IUsageRecordRepository UsageRecords => _usageRecords;
    public IOrderRepository Orders => _orders;
    public IPaymentRepository Payments => _payments;
    public IRefundRepository Refunds => _refunds;
    public IRefundRecordRepository RefundRecords => _refundRecords;
    public IRoleRepository Roles => _roles;
    public IPermissionRepository Permissions => _permissions;
    public ITokenUsageRepository TokenUsages => _tokenUsages;
    public IDifyConversationMappingRepository DifyConversationMappings => _difyConversationMappings;
    public IKnowledgeBaseRepository KnowledgeBases => _knowledgeBases;
    public IDocumentRepository Documents => _documents;
    public IDocumentChunkRepository DocumentChunks => _documentChunks;
    public IAuditLogRepository AuditLogs => _auditLogs;
    public INotificationRepository Notifications => _notifications;
    public ISystemEventRepository SystemEvents => _systemEvents;
    public ICustomerSessionRepository CustomerSessions => _customerSessions;
    public IAdminSessionRepository AdminSessions => _adminSessions;
    public IFileRepository Files => _files;
    public IVerificationCodeRepository VerificationCodes => _verificationCodes;
    public IAgentLikeRepository AgentLikes => _agentLikes;
    public IAgentCategoryRepository AgentCategories => _agentCategories;
    public IAgentRatingRepository AgentRatings => _agentRatings;
    public IRatingHelpfulnessRepository RatingHelpfulnesses => _ratingHelpfulnesses;
    public IInvoiceRepository Invoices => _invoices;
    public ICouponRepository Coupons => _coupons;
    public IQRCodeSessionRepository QRCodeSessions => _qrCodeSessions;

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            _currentTransaction?.Dispose();
            _context.Dispose();
        }
        _disposed = true;
    }
}