using Microsoft.EntityFrameworkCore;
using WhimLabAI.Domain.Entities.User;
using WhimLabAI.Domain.Entities.Auth;
using WhimLabAI.Domain.Entities.Agent;
using WhimLabAI.Domain.Entities.Conversation;
using WhimLabAI.Domain.Entities.Subscription;
using WhimLabAI.Domain.Entities.Payment;
using WhimLabAI.Domain.Entities.Audit;
using WhimLabAI.Domain.Entities.ApiKey;
using WhimLabAI.Domain.Entities.Compliance;
using WhimLabAI.Domain.Entities.KnowledgeBase;
using WhimLabAI.Domain.Entities.Notification;
using WhimLabAI.Domain.Entities.Monitoring;
using WhimLabAI.Domain.Entities.System;
using WhimLabAI.Domain.Entities.EventSourcing;
using WhimLabAI.Domain.Entities.Storage;
using WhimLabAI.Infrastructure.Data.ValueConverters;
using WhimLabAI.Infrastructure.Security;

namespace WhimLabAI.Infrastructure.Data;

public class WhimLabAIDbContext : DbContext
{
    private readonly IDataEncryptionService? _encryptionService;
    private readonly EncryptedValueConverterFactory? _converterFactory;

    public WhimLabAIDbContext(DbContextOptions<WhimLabAIDbContext> options) : base(options)
    {
    }

    public WhimLabAIDbContext(
        DbContextOptions<WhimLabAIDbContext> options,
        IDataEncryptionService encryptionService) : base(options)
    {
        _encryptionService = encryptionService;
        _converterFactory = new EncryptedValueConverterFactory(encryptionService);
    }

    // User domain
    public DbSet<CustomerUser> CustomerUsers { get; set; } = null!;
    public DbSet<LoginHistory> LoginHistories { get; set; } = null!;
    public DbSet<DeviceAuthorization> DeviceAuthorizations { get; set; } = null!;
    public DbSet<NotificationSetting> NotificationSettings { get; set; } = null!;
    public DbSet<OAuthBinding> OAuthBindings { get; set; } = null!;
    public DbSet<QRCodeSession> QRCodeSessions { get; set; } = null!;
    public DbSet<AdminUser> AdminUsers { get; set; } = null!;
    public DbSet<Role> Roles { get; set; } = null!;
    public DbSet<Permission> Permissions { get; set; } = null!;
    public DbSet<RolePermission> RolePermissions { get; set; } = null!;
    public DbSet<RecoveryCode> RecoveryCodes { get; set; } = null!;
    
    // Session domain
    public DbSet<CustomerUserSession> CustomerUserSessions { get; set; } = null!;
    public DbSet<AdminUserSession> AdminUserSessions { get; set; } = null!;

    // Agent domain
    public DbSet<Agent> Agents { get; set; } = null!;
    public DbSet<AgentVersion> AgentVersions { get; set; } = null!;
    public DbSet<AgentCategory> AgentCategories { get; set; } = null!;
    // AgentTag is a value object stored as JSON in Agent entity
    public DbSet<AgentRating> AgentRatings { get; set; } = null!;
    public DbSet<AgentVersionReview> AgentVersionReviews { get; set; } = null!;
    public DbSet<AgentVersionUsageStats> AgentVersionUsageStats { get; set; } = null!;

    // Conversation domain
    public DbSet<Conversation> Conversations { get; set; } = null!;
    public DbSet<ConversationMessage> ConversationMessages { get; set; } = null!;
    public DbSet<MessageAttachment> MessageAttachments { get; set; } = null!;

    // Subscription domain
    public DbSet<Subscription> Subscriptions { get; set; } = null!;
    public DbSet<SubscriptionPlan> SubscriptionPlans { get; set; } = null!;
    public DbSet<TokenUsage> TokenUsages { get; set; } = null!;
    public DbSet<UsageRecord> UsageRecords { get; set; } = null!;
    public DbSet<QuotaAlert> QuotaAlerts { get; set; } = null!;

    // Payment domain
    public DbSet<Order> Orders { get; set; } = null!;
    public DbSet<PaymentTransaction> PaymentTransactions { get; set; } = null!;
    public DbSet<RefundRecord> RefundRecords { get; set; } = null!;
    public DbSet<Coupon> Coupons { get; set; } = null!;
    public DbSet<CouponUsage> CouponUsages { get; set; } = null!;
    
    // Audit domain
    public DbSet<AuditLog> AuditLogs { get; set; } = null!;
    
    // API Key domain
    public DbSet<ApiKey> ApiKeys { get; set; } = null!;
    public DbSet<ApiKeyUsage> ApiKeyUsages { get; set; } = null!;
    
    // Compliance domain
    public DbSet<UserConsent> UserConsents { get; set; } = null!;
    public DbSet<PrivacySettings> PrivacySettings { get; set; } = null!;
    public DbSet<DataRetentionPolicy> DataRetentionPolicies { get; set; } = null!;
    public DbSet<DataExportRequest> DataExportRequests { get; set; } = null!;
    
    // Knowledge Base domain
    public DbSet<KnowledgeBase> KnowledgeBases { get; set; } = null!;
    public DbSet<Document> Documents { get; set; } = null!;
    public DbSet<DocumentChunk> DocumentChunks { get; set; } = null!;
    
    // Notification domain
    public DbSet<Notification> Notifications { get; set; } = null!;
    
    // Monitoring domain
    public DbSet<AlertHistoryEntity> AlertHistories { get; set; } = null!;
    
    // Storage domain
    public DbSet<FileEntity> FileEntities { get; set; } = null!;
    
    // System domain
    public DbSet<SystemEvent> SystemEvents { get; set; } = null!;
    public DbSet<VerificationCode> VerificationCodes { get; set; } = null!;
    
    // Event Sourcing domain
    public DbSet<EventStore> EventStores { get; set; } = null!;
    public DbSet<Snapshot> Snapshots { get; set; } = null!;

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        base.OnConfiguring(optionsBuilder);
        
        // Suppress the pending model changes warning for now
        // This is a temporary fix to allow the application to start
        optionsBuilder.ConfigureWarnings(warnings => 
            warnings.Ignore(Microsoft.EntityFrameworkCore.Diagnostics.RelationalEventId.PendingModelChangesWarning));
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Enable pgvector extension
        modelBuilder.HasPostgresExtension("vector");

        // Apply all entity configurations from this assembly
        modelBuilder.ApplyConfigurationsFromAssembly(typeof(WhimLabAIDbContext).Assembly);

        // Configure JSON columns for PostgreSQL
        // Agent.Metadata is configured in AgentConfiguration

        modelBuilder.Entity<Conversation>()
            .Property(e => e.Metadata)
            .HasColumnType("jsonb");
            
        // Configure Conversation-ConversationMessage relationship
        modelBuilder.Entity<Conversation>()
            .HasMany(c => c.Messages)
            .WithOne(m => m.Conversation)
            .HasForeignKey(m => m.ConversationId)
            .OnDelete(DeleteBehavior.Cascade);

        modelBuilder.Entity<Order>()
            .Property(e => e.Metadata)
            .HasColumnType("jsonb")
            .HasColumnType("jsonb");

        modelBuilder.Entity<TokenUsage>()
            .Property(e => e.Metadata)
            .HasColumnType("jsonb");

        // Configure ApiKey entity
        modelBuilder.Entity<ApiKey>()
            .Property(e => e.Scopes)
            .HasColumnType("jsonb");

        modelBuilder.Entity<ApiKey>()
            .Property(e => e.IpWhitelist)
            .HasColumnType("jsonb");

        modelBuilder.Entity<ApiKey>()
            .Property(e => e.AllowedDomains)
            .HasColumnType("jsonb");

        modelBuilder.Entity<ApiKey>()
            .Property(e => e.Metadata)
            .HasColumnType("jsonb");

        modelBuilder.Entity<ApiKey>()
            .HasIndex(e => new { e.KeyPrefix, e.KeySuffix, e.IsActive });

        // Create indexes for both CustomerUserId and AdminUserId
        modelBuilder.Entity<ApiKey>()
            .HasIndex(e => e.CustomerUserId)
            .HasFilter("\"CustomerUserId\" IS NOT NULL");

        modelBuilder.Entity<ApiKey>()
            .HasIndex(e => e.AdminUserId)
            .HasFilter("\"AdminUserId\" IS NOT NULL");

        // Configure ApiKeyUsage entity
        modelBuilder.Entity<ApiKeyUsage>()
            .HasIndex(e => e.ApiKeyId);

        modelBuilder.Entity<ApiKeyUsage>()
            .HasIndex(e => e.UsedAt);

        modelBuilder.Entity<ApiKeyUsage>()
            .HasOne(e => e.ApiKey)
            .WithMany()
            .HasForeignKey(e => e.ApiKeyId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configure System entities
        modelBuilder.Entity<SystemEvent>()
            .Property(e => e.EventData)
            .HasColumnType("jsonb");

        // Configure Compliance entities
        // Create composite indexes for both CustomerUserId and AdminUserId with ConsentType
        modelBuilder.Entity<UserConsent>()
            .HasIndex(e => new { e.CustomerUserId, e.ConsentType })
            .HasFilter("\"CustomerUserId\" IS NOT NULL");
            
        modelBuilder.Entity<UserConsent>()
            .HasIndex(e => new { e.AdminUserId, e.ConsentType })
            .HasFilter("\"AdminUserId\" IS NOT NULL");

        modelBuilder.Entity<UserConsent>()
            .Property(e => e.Metadata)
            .HasColumnType("jsonb");

        modelBuilder.Entity<PrivacySettings>()
            .HasIndex(e => e.UserId)
            .IsUnique();

        modelBuilder.Entity<DataRetentionPolicy>()
            .HasIndex(e => e.DataType);

        modelBuilder.Entity<DataExportRequest>()
            .HasIndex(e => e.UserId);

        modelBuilder.Entity<DataExportRequest>()
            .HasIndex(e => e.Status);

        modelBuilder.Entity<DataExportRequest>()
            .Property(e => e.IncludedDataTypes)
            .HasColumnType("jsonb");

        // Configure KnowledgeBase entities
        modelBuilder.Entity<KnowledgeBase>()
            .Property(e => e.VectorDbConfig)
            .HasColumnType("jsonb");

        modelBuilder.Entity<KnowledgeBase>()
            .Property(e => e.ChunkingConfig)
            .HasColumnType("jsonb");

        modelBuilder.Entity<KnowledgeBase>()
            .Property(e => e.Metadata)
            .HasColumnType("jsonb");

        modelBuilder.Entity<KnowledgeBase>()
            .HasIndex(e => new { e.OwnerId, e.OwnerType, e.IsDeleted });

        modelBuilder.Entity<KnowledgeBase>()
            .HasIndex(e => e.Status);

        modelBuilder.Entity<Document>()
            .Property(e => e.Metadata)
            .HasColumnType("jsonb");

        modelBuilder.Entity<Document>()
            .HasIndex(e => new { e.KnowledgeBaseId, e.IsDeleted });

        modelBuilder.Entity<Document>()
            .HasIndex(e => e.Status);

        modelBuilder.Entity<Document>()
            .HasIndex(e => new { e.FileHash, e.KnowledgeBaseId });

        modelBuilder.Entity<DocumentChunk>()
            .Property(e => e.Metadata)
            .HasColumnType("jsonb");

        modelBuilder.Entity<DocumentChunk>()
            .HasIndex(e => e.DocumentId);

        modelBuilder.Entity<DocumentChunk>()
            .HasIndex(e => e.KnowledgeBaseId);

        modelBuilder.Entity<DocumentChunk>()
            .HasIndex(e => e.VectorId);

        modelBuilder.Entity<DocumentChunk>()
            .HasIndex(e => new { e.KnowledgeBaseId, e.IsEmbedded });

        // Configure relationships
        modelBuilder.Entity<KnowledgeBase>()
            .HasMany(kb => kb.Documents)
            .WithOne(d => d.KnowledgeBase)
            .HasForeignKey(d => d.KnowledgeBaseId)
            .OnDelete(DeleteBehavior.Cascade);

        modelBuilder.Entity<Document>()
            .HasMany(d => d.Chunks)
            .WithOne(c => c.Document)
            .HasForeignKey(c => c.DocumentId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configure value object conversions
        ConfigureValueObjectConversions(modelBuilder);
        
        // Configure encryption for sensitive fields
        if (_converterFactory != null)
        {
            ConfigureEncryption(modelBuilder);
        }
    }

    private void ConfigureValueObjectConversions(ModelBuilder modelBuilder)
    {
        // Email value object
        // CustomerUser.Email is configured as owned type in CustomerUserConfiguration
        
        modelBuilder.Entity<AdminUser>()
            .Property(e => e.Email)
            .HasConversion(
                v => v.Value,
                v => Domain.ValueObjects.Email.Create(v))
            .HasMaxLength(255);

        // PhoneNumber value object
        // CustomerUser.Phone is configured as owned type in CustomerUserConfiguration
        // AdminUser.Phone is configured as owned type in AdminUserConfiguration

        // Password value object
        // CustomerUser.PasswordHash is configured as owned type in CustomerUserConfiguration
        
        modelBuilder.Entity<AdminUser>()
            .Property(e => e.PasswordHash)
            .HasConversion(
                v => v.Hash,
                v => Domain.ValueObjects.Password.CreateFromHash(v))
            .HasMaxLength(255);

        // Money value object
        modelBuilder.Entity<Subscription>()
            .OwnsOne(e => e.PaidAmount, money =>
            {
                money.Property(m => m.Amount).HasColumnName("PaidAmount").HasPrecision(18, 2);
                money.Property(m => m.Currency).HasColumnName("PaidCurrency").HasMaxLength(3);
            });

        modelBuilder.Entity<Order>()
            .OwnsOne(e => e.Amount, money =>
            {
                money.Property(m => m.Amount).HasColumnName("Amount").HasPrecision(18, 2);
                money.Property(m => m.Currency).HasColumnName("AmountCurrency").HasMaxLength(3);
            });

        modelBuilder.Entity<Order>()
            .OwnsOne(e => e.FinalAmount, money =>
            {
                money.Property(m => m.Amount).HasColumnName("FinalAmount").HasPrecision(18, 2);
                money.Property(m => m.Currency).HasColumnName("FinalCurrency").HasMaxLength(3);
            });

        modelBuilder.Entity<Order>()
            .OwnsOne(e => e.DiscountAmount, money =>
            {
                money.Property(m => m.Amount).HasColumnName("DiscountAmount").HasPrecision(18, 2);
                money.Property(m => m.Currency).HasColumnName("DiscountCurrency").HasMaxLength(3);
            });

        modelBuilder.Entity<Order>()
            .OwnsOne(e => e.PayableAmount, money =>
            {
                money.Property(m => m.Amount).HasColumnName("PayableAmount").HasPrecision(18, 2);
                money.Property(m => m.Currency).HasColumnName("PayableCurrency").HasMaxLength(3);
            });

        // Coupon and CouponUsage configurations are now handled by CouponConfiguration.cs and CouponUsageConfiguration.cs

        // Configure PaymentTransaction Money value object
        modelBuilder.Entity<PaymentTransaction>()
            .OwnsOne(e => e.Amount, money =>
            {
                money.Property(m => m.Amount).HasColumnName("amount").HasPrecision(18, 2);
                money.Property(m => m.Currency).HasColumnName("currency").HasMaxLength(3);
            });

        // Configure RefundRecord Money value object
        modelBuilder.Entity<RefundRecord>()
            .OwnsOne(e => e.RefundAmount, money =>
            {
                money.Property(m => m.Amount).HasColumnName("refund_amount").HasPrecision(18, 2);
                money.Property(m => m.Currency).HasColumnName("refund_currency").HasMaxLength(3);
            });

        // Configure SubscriptionPlan Money value object
        modelBuilder.Entity<SubscriptionPlan>()
            .OwnsOne(e => e.Price, money =>
            {
                money.Property(m => m.Amount).HasColumnName("price").HasPrecision(18, 2);
                money.Property(m => m.Currency).HasColumnName("currency").HasMaxLength(3);
            });

        // TokenQuota value object
        modelBuilder.Entity<Subscription>()
            .OwnsOne(e => e.TokenQuota, quota =>
            {
                quota.Property(q => q.Total).HasColumnName("TokenQuotaTotal");
                quota.Property(q => q.Used).HasColumnName("TokenQuotaUsed");
            });

        // ModelConfiguration value object
        // AgentVersion.ModelConfig is configured as JSON property in AgentVersionConfiguration
    }

    public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        UpdateTimestamps();
        return base.SaveChangesAsync(cancellationToken);
    }

    private void UpdateTimestamps()
    {
        var entries = ChangeTracker.Entries()
            .Where(e => e.Entity is Domain.Common.Entity && 
                (e.State == EntityState.Added || e.State == EntityState.Modified));

        foreach (var entry in entries)
        {
            var entity = (Domain.Common.Entity)entry.Entity;
            
            // CreatedAt is automatically set in Entity constructor
            // No need to set it here
            
            entity.UpdateTimestamp();
        }
    }

    private void ConfigureEncryption(ModelBuilder modelBuilder)
    {
        // 加密用户敏感信息
        // CustomerUser doesn't have RealName property
        // modelBuilder.Entity<CustomerUser>()
        //     .Property(e => e.RealName)
        //     .HasConversion(_converterFactory!.CreateNullableStringConverter("CustomerUser_RealName"));

        // AdminUser doesn't have RealName property
        // modelBuilder.Entity<AdminUser>()
        //     .Property(e => e.RealName)
        //     .HasConversion(_converterFactory!.CreateStringConverter("AdminUser_RealName"));

        // 加密支付相关信息
        modelBuilder.Entity<PaymentTransaction>()
            .Property(e => e.PayerAccount)
            .HasConversion(_converterFactory!.CreateNullableStringConverter("PaymentTransaction_PayerAccount"));

        // RefundRecord doesn't have RefundAccountNumber property
        // modelBuilder.Entity<RefundRecord>()
        //     .Property(e => e.RefundAccountNumber)
        //     .HasConversion(_converterFactory!.CreateNullableStringConverter("RefundRecord_AccountNumber"));

        // AgentVersion doesn't have ApiKey property - removed incorrect configuration

        // DeviceAuthorization doesn't have DeviceFingerprint property - removed incorrect configuration

        modelBuilder.Entity<LoginHistory>()
            .Property(e => e.LoginIp)
            .HasConversion(_converterFactory!.CreateStringConverter("LoginHistory_LoginIp"));

        // 加密OAuth绑定的访问令牌
        modelBuilder.Entity<OAuthBinding>()
            .Property(e => e.AccessToken)
            .HasConversion(_converterFactory!.CreateNullableStringConverter("OAuthBinding_AccessToken"));

        modelBuilder.Entity<OAuthBinding>()
            .Property(e => e.RefreshToken)
            .HasConversion(_converterFactory!.CreateNullableStringConverter("OAuthBinding_RefreshToken"));

        // NotificationSetting doesn't have PushToken property - removed incorrect configuration
    }
}