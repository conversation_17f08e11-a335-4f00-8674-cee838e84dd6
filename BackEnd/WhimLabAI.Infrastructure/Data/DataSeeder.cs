using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using WhimLabAI.Infrastructure.Data.Seeding;

namespace WhimLabAI.Infrastructure.Data;

/// <summary>
/// 数据库初始化扩展方法
/// </summary>
public static class DataSeeder
{
    public static async Task InitializeDatabaseAsync(this IHost host)
    {
        using var scope = host.Services.CreateScope();
        var services = scope.ServiceProvider;
        
        try
        {
            var context = services.GetRequiredService<WhimLabAIDbContext>();
            var logger = services.GetRequiredService<ILogger<WhimLabAIDbContext>>();
            
            logger.LogInformation("开始初始化数据库...");
            
            // 应用迁移
            if (context.Database.GetPendingMigrations().Any())
            {
                logger.LogInformation("应用数据库迁移...");
                await context.Database.MigrateAsync();
            }
            
            // 使用UnifiedDataSeeder进行种子数据初始化
            logger.LogInformation("开始种子数据初始化...");
            var unifiedSeeder = new UnifiedDataSeeder(
                services,
                services.GetRequiredService<ILogger<UnifiedDataSeeder>>()
            );
            
            // 在生产环境不包含示例数据
            var includeSampleData = !host.Services.GetRequiredService<IHostEnvironment>().IsProduction();
            await unifiedSeeder.SeedAllAsync(includeSampleData);
            
            logger.LogInformation("数据库初始化完成");
        }
        catch (Exception ex)
        {
            var logger = services.GetRequiredService<ILogger<WhimLabAIDbContext>>();
            logger.LogError(ex, "数据库初始化失败");
            throw;
        }
    }
}