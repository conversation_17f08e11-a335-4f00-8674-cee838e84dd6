using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using WhimLabAI.Domain.Entities.Subscription;
using WhimLabAI.Domain.ValueObjects;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Infrastructure.Data.Seeding.SampleData;

/// <summary>
/// 示例订阅数据种子
/// </summary>
public class SampleSubscriptionSeeder : EntitySeederBase<Subscription>
{
    public SampleSubscriptionSeeder(WhimLabAIDbContext context, ILogger<SampleSubscriptionSeeder> logger) 
        : base(context, logger)
    {
    }
    
    public override async Task SeedAsync()
    {
        // 仅在开发环境中运行
        var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
        if (environment != "Development")
        {
            return;
        }

        // 检查是否已有订阅
        if (await Context.Subscriptions.AnyAsync())
        {
            LogSeedingSkipped("Subscription");
            return;
        }

        // 获取订阅计划 - 确保加载价格信息
        var freePlan = await Context.SubscriptionPlans
            .FirstOrDefaultAsync(p => p.Name == "免费版");
        var basicPlan = await Context.SubscriptionPlans
            .FirstOrDefaultAsync(p => p.Name == "基础版");
        var proPlan = await Context.SubscriptionPlans
            .FirstOrDefaultAsync(p => p.Name == "专业版");
        var ultraPlan = await Context.SubscriptionPlans
            .FirstOrDefaultAsync(p => p.Name == "旗舰版");

        if (freePlan == null || basicPlan == null || proPlan == null || ultraPlan == null)
        {
            throw new InvalidOperationException("Subscription plans must be seeded before subscriptions");
        }
        
        // 验证价格信息
        if (freePlan.Price == null || basicPlan.Price == null || proPlan.Price == null || ultraPlan.Price == null)
        {
            throw new InvalidOperationException("Subscription plan prices are not loaded properly");
        }
        
        // Log plan prices for debugging
        Logger.LogInformation("Free plan price: {Amount} {Currency}", freePlan.Price.Amount, freePlan.Price.Currency);
        Logger.LogInformation("Basic plan price: {Amount} {Currency}", basicPlan.Price.Amount, basicPlan.Price.Currency);
        Logger.LogInformation("Pro plan price: {Amount} {Currency}", proPlan.Price.Amount, proPlan.Price.Currency);
        Logger.LogInformation("Ultra plan price: {Amount} {Currency}", ultraPlan.Price.Amount, ultraPlan.Price.Currency);

        var subscriptions = new List<Subscription>();
        
        // Demo用户 - Pro订阅（活跃）
        var sub1 = new Subscription(
            customerUserId: Guid.Parse("d1111111-1111-1111-1111-111111111111"), // demo_user
            planId: proPlan.Id,
            startDate: DateTime.UtcNow.AddDays(-25),
            endDate: DateTime.UtcNow.AddDays(5),
            paymentMethod: "Alipay",
            autoRenew: true
        );
        SetEntityId(sub1, Guid.Parse("f1111111-1111-1111-1111-111111111111"));
        SetEntityTimestamps(sub1, DateTime.UtcNow.AddDays(-30));
        // Create new Money instance to ensure proper tracking
        var proPrice = Money.Create(proPlan.Price.Amount, proPlan.Price.Currency);
        sub1.SetPlanInfo(proPlan.MonthlyTokens, proPrice);
        sub1.Activate();
        sub1.UseTokens(45000); // Simulate usage
        
        // Alice - Basic订阅（活跃）
        var sub2 = new Subscription(
            customerUserId: Guid.Parse("d222**************-2222-************"), // alice_wang
            planId: basicPlan.Id,
            startDate: DateTime.UtcNow.AddDays(-45),
            endDate: DateTime.UtcNow.AddDays(15),
            paymentMethod: "WeChatPay",
            autoRenew: true
        );
        SetEntityId(sub2, Guid.Parse("f222**************-2222-************"));
        SetEntityTimestamps(sub2, DateTime.UtcNow.AddDays(-60));
        var basicPrice = Money.Create(basicPlan.Price.Amount, basicPlan.Price.Currency);
        sub2.SetPlanInfo(basicPlan.MonthlyTokens, basicPrice);
        sub2.Activate();
        sub2.UseTokens(23000); // Simulate usage
        subscriptions.Add(sub2);
        
        // Bob - Ultra订阅（活跃）
        var sub3 = new Subscription(
            customerUserId: Guid.Parse("d333**************-3333-************"), // bob_chen
            planId: ultraPlan.Id,
            startDate: DateTime.UtcNow.AddDays(-60),
            endDate: DateTime.UtcNow.AddDays(30),
            paymentMethod: "Alipay",
            autoRenew: true
        );
        SetEntityId(sub3, Guid.Parse("f333**************-3333-************"));
        SetEntityTimestamps(sub3, DateTime.UtcNow.AddDays(-90));
        var ultraPrice = Money.Create(ultraPlan.Price.Amount, ultraPlan.Price.Currency);
        sub3.SetPlanInfo(ultraPlan.MonthlyTokens, ultraPrice);
        sub3.Activate();
        sub3.UseTokens(125000); // Simulate usage
        subscriptions.Add(sub3);
        
        // 免费测试用户 - Free订阅（活跃）
        var sub4 = new Subscription(
            customerUserId: Guid.Parse("d444**************-4444-************"), // test_free
            planId: freePlan.Id,
            startDate: DateTime.UtcNow.AddDays(-7),
            endDate: DateTime.UtcNow.AddYears(100), // Free plan - set far future date
            autoRenew: false
        );
        SetEntityId(sub4, Guid.Parse("f444**************-4444-************"));
        SetEntityTimestamps(sub4, DateTime.UtcNow.AddDays(-7));
        sub4.SetPlanInfo(freePlan.MonthlyTokens, Money.Create(0, "CNY")); // Use Money.Create instead of Money.Zero
        sub4.Activate();
        sub4.UseTokens(2500); // Simulate usage
        subscriptions.Add(sub4);
        
        // 专业版测试用户 - Pro订阅（已取消）
        var sub5 = new Subscription(
            customerUserId: Guid.Parse("d555**************-5555-************"), // test_pro
            planId: proPlan.Id,
            startDate: DateTime.UtcNow.AddDays(-45),
            endDate: DateTime.UtcNow.AddDays(15), // Still active to allow token usage
            paymentMethod: "Alipay",
            autoRenew: false
        );
        SetEntityId(sub5, Guid.Parse("f555**************-5555-************"));
        SetEntityTimestamps(sub5, DateTime.UtcNow.AddDays(-45));
        var proPrice2 = Money.Create(proPlan.Price.Amount, proPlan.Price.Currency);
        sub5.SetPlanInfo(proPlan.MonthlyTokens, proPrice2);
        sub5.Activate();
        sub5.UseTokens(180000); // Simulate usage
        sub5.Cancel("用户主动取消订阅"); // Cancel the subscription
        subscriptions.Add(sub5);
        
        // 过期订阅示例
        var sub6 = new Subscription(
            customerUserId: Guid.Parse("d666**************-6666-************"), // inactive_user
            planId: basicPlan.Id,
            startDate: DateTime.UtcNow.AddDays(-120),
            endDate: DateTime.UtcNow.AddDays(-30),
            paymentMethod: "WeChatPay",
            autoRenew: false
        );
        SetEntityId(sub6, Guid.Parse("f666**************-6666-************"));
        SetEntityTimestamps(sub6, DateTime.UtcNow.AddDays(-120));
        var basicPrice2 = Money.Create(basicPlan.Price.Amount, basicPlan.Price.Currency);
        sub6.SetPlanInfo(basicPlan.MonthlyTokens, basicPrice2);
        sub6.Activate();
        // Skip token usage for expired subscription
        sub6.Expire(); // Mark as expired
        subscriptions.Add(sub6);

        // Add subscriptions to context
        await Context.Subscriptions.AddRangeAsync(subscriptions);
        
        // Force EF Core to detect changes to owned types
        Context.ChangeTracker.DetectChanges();
        
        // Save changes
        await Context.SaveChangesAsync();
        
        LogSeeding("Subscription", subscriptions.Count);
        
        // Note: SubscriptionHistory entity doesn't exist in the domain model
        // Domain events would typically handle history tracking
    }
}