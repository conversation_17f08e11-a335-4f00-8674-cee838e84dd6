using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using WhimLabAI.Domain.Entities.Agent;
using WhimLabAI.Domain.ValueObjects;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Infrastructure.Data.Seeding.SampleData;

/// <summary>
/// 示例AI智能体种子数据
/// </summary>
public class SampleAgentSeeder : EntitySeederBase<Agent>
{
    public SampleAgentSeeder(WhimLabAIDbContext context, ILogger<SampleAgentSeeder> logger) 
        : base(context, logger)
    {
    }
    
    public override async Task SeedAsync()
    {
        // 仅在开发环境中运行
        var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
        if (environment != "Development")
        {
            return;
        }

        // 检查是否已有智能体
        if (await Context.Agents.AnyAsync())
        {
            LogSeedingSkipped("Agent");
            return;
        }

        var agents = new List<Agent>();
        
        // Agent 1: 通用助手
        var agent1 = new Agent(
            name: "通用助手",
            creatorId: Guid.Parse("c1111111-1111-1111-1111-111111111111"),
            uniqueKey: "general-assistant",
            description: "一个功能全面的AI助手，可以帮助您处理各种日常任务，包括问答、写作、翻译等。"
        );
        SetEntityId(agent1, Guid.Parse("e1111111-1111-1111-1111-111111111111"));
        SetEntityTimestamps(agent1, DateTime.UtcNow.AddDays(-90));
        agent1.Update(icon: "/images/agents/general-assistant.png");
        
        
        // Add tags
        agent1.AddTag("通用");
        agent1.AddTag("助手");
        agent1.AddTag("问答");
        agent1.AddTag("写作");
        
        
        agents.Add(agent1);
        
        // Agent 2: 代码助手
        var agent2 = new Agent(
            name: "代码助手",
            creatorId: Guid.Parse("c1111111-1111-1111-1111-111111111111"),
            uniqueKey: "code-assistant",
            description: "专业的编程助手，精通多种编程语言，可以帮助您编写代码、调试问题、优化性能等。"
        );
        SetEntityId(agent2, Guid.Parse("e222**************-2222-************"));
        SetEntityTimestamps(agent2, DateTime.UtcNow.AddDays(-80));
        agent2.Update(icon: "/images/agents/code-assistant.png");
        
        
        // Add tags
        agent2.AddTag("编程");
        agent2.AddTag("代码");
        agent2.AddTag("调试");
        agent2.AddTag("开发");
        
        
        agents.Add(agent2);
        
        // Agent 3: 写作助手
        var agent3 = new Agent(
            name: "写作助手",
            creatorId: Guid.Parse("c222**************-2222-************"),
            uniqueKey: "writing-assistant",
            description: "专业的写作助手，可以帮助您创作文章、故事、诗歌、营销文案等各类文字内容。"
        );
        SetEntityId(agent3, Guid.Parse("e333**************-3333-************"));
        SetEntityTimestamps(agent3, DateTime.UtcNow.AddDays(-60));
        agent3.Update(icon: "/images/agents/writing-assistant.png");
        
        
        // Add tags
        agent3.AddTag("写作");
        agent3.AddTag("创意");
        agent3.AddTag("文案");
        agent3.AddTag("内容创作");
        
        
        agents.Add(agent3);
        
        // Agent 4: 数据分析师
        var agent4 = new Agent(
            name: "数据分析师",
            creatorId: Guid.Parse("c1111111-1111-1111-1111-111111111111"),
            uniqueKey: "data-analyst",
            description: "专业的数据分析助手，可以帮助您分析数据、生成报表、提供业务洞察。"
        );
        SetEntityId(agent4, Guid.Parse("e444**************-4444-************"));
        SetEntityTimestamps(agent4, DateTime.UtcNow.AddDays(-45));
        agent4.Update(icon: "/images/agents/data-analyst.png");
        
        
        // Add tags
        agent4.AddTag("数据分析");
        agent4.AddTag("统计");
        agent4.AddTag("报表");
        agent4.AddTag("商业智能");
        
        
        agents.Add(agent4);
        
        // Agent 5: 教育导师
        var agent5 = new Agent(
            name: "教育导师",
            creatorId: Guid.Parse("c222**************-2222-************"),
            uniqueKey: "education-tutor",
            description: "个性化的教育助手，可以帮助学生学习各科知识，提供解题思路和学习建议。"
        );
        SetEntityId(agent5, Guid.Parse("e555**************-5555-************"));
        SetEntityTimestamps(agent5, DateTime.UtcNow.AddDays(-40));
        agent5.Update(icon: "/images/agents/education-tutor.png");
        
        
        // Add tags
        agent5.AddTag("教育");
        agent5.AddTag("学习");
        agent5.AddTag("辅导");
        agent5.AddTag("知识");
        
        
        agents.Add(agent5);
        
        // Agent 6: 翻译专家
        var agent6 = new Agent(
            name: "翻译专家",
            creatorId: Guid.Parse("c1111111-1111-1111-1111-111111111111"),
            uniqueKey: "translator",
            description: "精通多国语言的翻译助手，提供准确、地道的翻译服务。"
        );
        SetEntityId(agent6, Guid.Parse("e666**************-6666-************"));
        SetEntityTimestamps(agent6, DateTime.UtcNow.AddDays(-35));
        agent6.Update(icon: "/images/agents/translator.png");
        
        
        // Add tags
        agent6.AddTag("翻译");
        agent6.AddTag("语言");
        agent6.AddTag("多语言");
        agent6.AddTag("本地化");
        
        
        agents.Add(agent6);
        
        // Agent 7: 健康顾问
        var agent7 = new Agent(
            name: "健康顾问",
            creatorId: Guid.Parse("c222**************-2222-************"),
            uniqueKey: "health-advisor",
            description: "提供健康建议、营养指导和生活方式改善建议的智能助手。"
        );
        SetEntityId(agent7, Guid.Parse("e777**************-7777-************"));
        SetEntityTimestamps(agent7, DateTime.UtcNow.AddDays(-25));
        agent7.Update(icon: "/images/agents/health-advisor.png");
        
        
        // Add tags
        agent7.AddTag("健康");
        agent7.AddTag("营养");
        agent7.AddTag("健身");
        agent7.AddTag("生活方式");
        
        
        agents.Add(agent7);
        
        // Agent 8: Dify集成助手
        var agent8 = new Agent(
            name: "Dify集成助手",
            creatorId: Guid.Parse("c1111111-1111-1111-1111-111111111111"),
            uniqueKey: "dify-assistant",
            description: "通过Dify平台集成的高级AI助手，支持复杂的工作流和多模型协作。"
        );
        SetEntityId(agent8, Guid.Parse("e888**************-8888-************"));
        SetEntityTimestamps(agent8, DateTime.UtcNow.AddDays(-20));
        agent8.Update(icon: "/images/agents/dify-assistant.png");
        
        
        // Add tags
        agent8.AddTag("Dify");
        agent8.AddTag("工作流");
        agent8.AddTag("自动化");
        agent8.AddTag("集成");
        
        // Add Dify API key
        agent8.AddDifyApiKey("sk_dify_test_key", "app_123456");
        
        agents.Add(agent8);
        
        // Agent 9: 测试草稿助手
        var agent9 = new Agent(
            name: "测试草稿助手",
            creatorId: Guid.Parse("c222**************-2222-************"),
            uniqueKey: "test-draft",
            description: "这是一个尚未发布的测试助手，仅供内部测试使用。"
        );
        SetEntityId(agent9, Guid.Parse("e999**************-9999-************"));
        SetEntityTimestamps(agent9, DateTime.UtcNow.AddDays(-5));
        agent9.Update(icon: "/images/agents/draft-assistant.png");
        
        
        // Add tags
        agent9.AddTag("测试");
        agent9.AddTag("草稿");
        
        // Note: Not publishing this agent (keeping it as draft)
        // agent9 remains in Draft status with no usage count or ratings
        
        agents.Add(agent9);

        await Context.Agents.AddRangeAsync(agents);
        await Context.SaveChangesAsync();
        
        // Set initial versions after saving to avoid circular dependency
        foreach (var agent in agents)
        {
            agent.SetInitialVersion();
        }
        
        // Configure versions and publish agents after initial save
        await ConfigureAndPublishSampleAgents(agents);
        
        await Context.SaveChangesAsync();
        
        LogSeeding("Agent", agents.Count);
    }
    
    /// <summary>
    /// 配置和发布示例智能体（在初始保存后执行）
    /// </summary>
    private async Task ConfigureAndPublishSampleAgents(List<Agent> agents)
    {
        // Agent 1: 通用助手
        var agent1 = agents[0];
        var version1 = agent1.Versions.First();
        var modelConfig1 = ModelConfiguration.Create(
            modelType: "OpenAI",
            modelName: "gpt-3.5-turbo",
            temperature: 0.7,
            maxTokens: 2000
        );
        version1.UpdateConfiguration(
            modelConfig: modelConfig1,
            systemPrompt: @"你是WhimLab通用助手，一个友好、专业的AI助手。你的职责是：
1. 准确理解用户的需求并提供有帮助的回答
2. 保持友好和专业的语气
3. 如果不确定答案，诚实地说明
4. 提供结构化和易于理解的回答"
        );
        agent1.Publish();
        for (int i = 0; i < 1000; i++) agent1.IncrementUsageCount();
        var random1 = new Random(1);
        for (int i = 0; i < 150; i++)
        {
            var score = random1.Next(4, 6); // 4 or 5
            agent1.AddRating(Guid.NewGuid(), score);
        }
        
        // Agent 2: 代码助手
        var agent2 = agents[1];
        var version2 = agent2.CreateNewVersion();
        var modelConfig2 = ModelConfiguration.Create(
            modelType: "OpenAI",
            modelName: "gpt-4",
            temperature: 0.3,
            maxTokens: 4000
        );
        version2.UpdateConfiguration(
            modelConfig: modelConfig2,
            systemPrompt: @"你是一个专业的编程助手。你精通多种编程语言包括但不限于：
- Python, JavaScript, TypeScript, Java, C#, C++, Go, Rust
- Web开发技术：HTML, CSS, React, Vue, Angular
- 后端技术：Node.js, Django, Spring, ASP.NET
- 数据库：SQL, NoSQL, Redis
- DevOps：Docker, Kubernetes, CI/CD

请提供清晰、高效、符合最佳实践的代码解决方案。"
        );
        agent2.Publish();
        for (int i = 0; i < 2500; i++) agent2.IncrementUsageCount();
        var random2 = new Random(2);
        for (int i = 0; i < 320; i++)
        {
            var score = random2.Next(4, 6); // 4 or 5
            agent2.AddRating(Guid.NewGuid(), score);
        }
        
        // Agent 3: 写作助手
        var agent3 = agents[2];
        var version3 = agent3.Versions.First();
        var modelConfig3 = ModelConfiguration.Create(
            modelType: "Anthropic",
            modelName: "claude-2.1",
            temperature: 0.8,
            maxTokens: 3000
        );
        version3.UpdateConfiguration(
            modelConfig: modelConfig3,
            systemPrompt: @"你是一位才华横溢的写作助手，擅长各种文体的创作：
- 创意写作：小说、散文、诗歌
- 商务写作：报告、提案、邮件
- 营销文案：广告语、产品描述、社交媒体内容
- 学术写作：论文、摘要、文献综述

请根据用户需求，创作高质量、有创意的文字内容。"
        );
        agent3.Publish();
        for (int i = 0; i < 1800; i++) agent3.IncrementUsageCount();
        var random3 = new Random(3);
        for (int i = 0; i < 200; i++)
        {
            var score = random3.Next(4, 6); // 4 or 5
            agent3.AddRating(Guid.NewGuid(), score);
        }
        
        // Agent 4: 数据分析师
        var agent4 = agents[3];
        var version4 = agent4.Versions.First();
        var modelConfig4 = ModelConfiguration.Create(
            modelType: "OpenAI",
            modelName: "gpt-4",
            temperature: 0.2,
            maxTokens: 2500
        );
        version4.UpdateConfiguration(
            modelConfig: modelConfig4,
            systemPrompt: @"你是一位经验丰富的数据分析专家，擅长：
- 数据清洗和预处理
- 统计分析和假设检验
- 数据可视化建议
- 业务指标分析
- 预测建模建议
- SQL查询优化

请提供专业、准确的数据分析建议。"
        );
        agent4.Publish();
        for (int i = 0; i < 800; i++) agent4.IncrementUsageCount();
        var random4 = new Random(4);
        for (int i = 0; i < 90; i++)
        {
            var score = random4.Next(4, 6); // 4 or 5
            agent4.AddRating(Guid.NewGuid(), score);
        }
        
        // Agent 5: 教育导师
        var agent5 = agents[4];
        var version5 = agent5.Versions.First();
        var modelConfig5 = ModelConfiguration.Create(
            modelType: "OpenAI",
            modelName: "gpt-3.5-turbo",
            temperature: 0.5,
            maxTokens: 2000
        );
        version5.UpdateConfiguration(
            modelConfig: modelConfig5,
            systemPrompt: @"你是一位耐心、专业的教育导师。你的教学理念是：
- 循序渐进，因材施教
- 启发式教学，培养思维能力
- 鼓励提问，激发学习兴趣
- 结合实例，便于理解

请根据学生的水平提供适合的教学内容。"
        );
        agent5.Publish();
        for (int i = 0; i < 1200; i++) agent5.IncrementUsageCount();
        var random5 = new Random(5);
        for (int i = 0; i < 150; i++)
        {
            var score = random5.Next(4, 6); // 4 or 5
            agent5.AddRating(Guid.NewGuid(), score);
        }
        
        // Agent 6: 翻译专家
        var agent6 = agents[5];
        var version6 = agent6.Versions.First();
        var modelConfig6 = ModelConfiguration.Create(
            modelType: "OpenAI",
            modelName: "gpt-3.5-turbo",
            temperature: 0.3,
            maxTokens: 2000
        );
        version6.UpdateConfiguration(
            modelConfig: modelConfig6,
            systemPrompt: @"你是一位专业的翻译专家，精通以下语言：
- 中文（简体/繁体）
- 英语
- 日语
- 韩语
- 法语
- 德语
- 西班牙语
- 俄语

翻译原则：
1. 准确传达原文含义
2. 符合目标语言的表达习惯
3. 保持原文的语气和风格
4. 必要时提供文化背景说明"
        );
        agent6.Publish();
        for (int i = 0; i < 1500; i++) agent6.IncrementUsageCount();
        var random6 = new Random(6);
        for (int i = 0; i < 180; i++)
        {
            var score = random6.Next(4, 6); // 4 or 5
            agent6.AddRating(Guid.NewGuid(), score);
        }
        
        // Agent 7: 健康顾问
        var agent7 = agents[6];
        var version7 = agent7.Versions.First();
        var modelConfig7 = ModelConfiguration.Create(
            modelType: "Anthropic",
            modelName: "claude-instant-1.2",
            temperature: 0.4,
            maxTokens: 2000
        );
        version7.UpdateConfiguration(
            modelConfig: modelConfig7,
            systemPrompt: @"你是一位专业的健康顾问。请注意：
- 你提供的是一般性健康建议，不能替代专业医疗诊断
- 遇到紧急医疗情况，建议立即就医
- 基于科学研究提供建议
- 考虑个体差异，给出个性化建议

你可以提供以下方面的建议：
- 营养饮食
- 运动健身
- 睡眠改善
- 压力管理
- 健康生活习惯"
        );
        agent7.Publish();
        for (int i = 0; i < 600; i++) agent7.IncrementUsageCount();
        var random7 = new Random(7);
        for (int i = 0; i < 70; i++)
        {
            var score = random7.Next(4, 6); // 4 or 5
            agent7.AddRating(Guid.NewGuid(), score);
        }
        
        // Agent 8: Dify集成助手
        var agent8 = agents[7];
        var version8 = agent8.Versions.First();
        var modelConfig8 = ModelConfiguration.Create(
            modelType: "Dify",
            modelName: "dify-app-001",
            temperature: 0.7,
            maxTokens: 2000
        );
        // Add Dify-specific settings
        modelConfig8 = modelConfig8
            .WithAdditionalSetting("difyAppId", "app_123456")
            .WithAdditionalSetting("enableStreaming", true)
            .WithAdditionalSetting("conversationMode", "continuous");
            
        version8.UpdateConfiguration(
            modelConfig: modelConfig8,
            systemPrompt: "你是一个基于Dify平台的智能工作流助手。请根据用户需求，利用Dify平台的能力来执行任务。" // 为Dify助手提供基础prompt
        );
        agent8.Publish();
        for (int i = 0; i < 300; i++) agent8.IncrementUsageCount();
        var random8 = new Random(8);
        for (int i = 0; i < 35; i++)
        {
            var score = random8.Next(4, 6); // 4 or 5
            agent8.AddRating(Guid.NewGuid(), score);
        }
        
        // Agent 9: 测试草稿助手
        var agent9 = agents[8];
        var version9 = agent9.Versions.First();
        var modelConfig9 = ModelConfiguration.Create(
            modelType: "OpenAI",
            modelName: "gpt-3.5-turbo",
            temperature: 0.7,
            maxTokens: 1000
        );
        version9.UpdateConfiguration(
            modelConfig: modelConfig9,
            systemPrompt: "你是一个测试助手。"
        );
        // Note: Not publishing this agent (keeping it as draft)
        // agent9 remains in Draft status with no usage count or ratings
    }
}