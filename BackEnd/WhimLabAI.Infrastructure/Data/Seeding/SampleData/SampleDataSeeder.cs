using System;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;

namespace WhimLabAI.Infrastructure.Data.Seeding.SampleData;

/// <summary>
/// 示例数据主入口
/// </summary>
public class SampleDataSeeder : IDataSeeder
{
    private readonly ILogger<SampleDataSeeder> _logger;

    public SampleDataSeeder(ILogger<SampleDataSeeder> logger = null!)
    {
        _logger = logger ?? Microsoft.Extensions.Logging.Abstractions.NullLogger<SampleDataSeeder>.Instance;
    }

    public async Task SeedAsync(WhimLabAIDbContext context)
    {
        // 仅在开发环境中运行
        var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
        if (environment != "Development")
        {
            _logger.LogInformation("Skipping sample data seeding in {Environment} environment", environment);
            return;
        }

        _logger.LogInformation("Starting sample data seeding...");

        // 按照依赖顺序执行种子数据
        // Create loggers for each seeder type
        var customerUserLogger = _logger as ILogger<SampleCustomerUserSeeder> ?? NullLogger<SampleCustomerUserSeeder>.Instance;
        var agentLogger = _logger as ILogger<SampleAgentSeeder> ?? NullLogger<SampleAgentSeeder>.Instance;
        var subscriptionLogger = _logger as ILogger<SampleSubscriptionSeeder> ?? NullLogger<SampleSubscriptionSeeder>.Instance;
        var conversationLogger = _logger as ILogger<SampleConversationSeeder> ?? NullLogger<SampleConversationSeeder>.Instance;
        var usageRecordLogger = _logger as ILogger<SampleUsageRecordSeeder> ?? NullLogger<SampleUsageRecordSeeder>.Instance;
        
        var seeders = new IDataSeeder[]
        {
            new SampleCustomerUserSeeder(context, customerUserLogger),
            new SampleAgentSeeder(context, agentLogger),
            new SampleSubscriptionSeeder(context, subscriptionLogger),
            new SampleConversationSeeder(context, conversationLogger),
            new SampleUsageRecordSeeder(context, usageRecordLogger)
        };

        foreach (var seeder in seeders)
        {
            try
            {
                _logger.LogInformation("Running {SeederName}...", seeder.GetType().Name);
                await seeder.SeedAsync(context);
                await context.SaveChangesAsync();
                _logger.LogInformation("{SeederName} completed successfully", seeder.GetType().Name);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error running {SeederName}", seeder.GetType().Name);
                throw;
            }
        }

        _logger.LogInformation("Sample data seeding completed successfully");
    }
}

/// <summary>
/// 示例数据统计
/// </summary>
public class SampleDataStatistics
{
    public int CustomerUsers { get; set; }
    public int Agents { get; set; }
    public int Subscriptions { get; set; }
    public int Conversations { get; set; }
    public int Messages { get; set; }
    public int UsageRecords { get; set; }

    public static async Task<SampleDataStatistics> GetStatisticsAsync(WhimLabAIDbContext context)
    {
        return new SampleDataStatistics
        {
            CustomerUsers = await context.CustomerUsers.CountAsync(),
            Agents = await context.Agents.CountAsync(),
            Subscriptions = await context.Subscriptions.CountAsync(),
            Conversations = await context.Conversations.CountAsync(),
            Messages = await context.ConversationMessages.CountAsync(),
            UsageRecords = await context.UsageRecords.CountAsync()
        };
    }

    public override string ToString()
    {
        return $@"Sample Data Statistics:
- Customer Users: {CustomerUsers}
- AI Agents: {Agents}
- Subscriptions: {Subscriptions}
- Conversations: {Conversations}
- Messages: {Messages}
- Usage Records: {UsageRecords}";
    }
}