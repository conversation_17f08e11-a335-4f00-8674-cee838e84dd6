using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using WhimLabAI.Domain.Entities.User;
using WhimLabAI.Shared.Enums;
using BC = BCrypt.Net.BCrypt;

namespace WhimLabAI.Infrastructure.Data.Seeding.SampleData;

/// <summary>
/// 示例客户用户种子数据
/// </summary>
public class SampleCustomerUserSeeder : EntitySeederBase<CustomerUser>
{
    public SampleCustomerUserSeeder(WhimLabAIDbContext context, ILogger<SampleCustomerUserSeeder> logger) 
        : base(context, logger)
    {
    }
    
    public override async Task SeedAsync()
    {
        // 仅在开发环境中运行
        var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
        if (environment != "Development")
        {
            return;
        }

        // 检查是否已有客户用户
        if (await Context.CustomerUsers.AnyAsync())
        {
            LogSeedingSkipped("CustomerUser");
            return;
        }

        var users = new List<CustomerUser>();
        
        // User 1: demo_user
        var user1 = new CustomerUser(
            username: "demo_user",
            password: "Demo@123",
            email: "<EMAIL>",
            phone: "13800138000",
            registerIp: "127.0.0.1"
        );
        SetEntityId(user1, Guid.Parse("d1111111-1111-1111-1111-111111111111"));
        user1.UpdateProfile(
            nickname: "演示用户",
            gender: Gender.Unknown,
            birthday: new DateTime(1990, 1, 1, 0, 0, 0, DateTimeKind.Utc)
        );
        user1.VerifyEmail();
        user1.VerifyPhone();
        user1.RecordLogin("127.0.0.1");
        // 最后设置时间戳，避免并发冲突
        SetEntityTimestamps(user1, DateTime.UtcNow.AddDays(-30));
        users.Add(user1);
        
        // User 2: alice_wang
        var user2 = new CustomerUser(
            username: "alice_wang",
            password: "Alice@123",
            email: "<EMAIL>",
            phone: "13900139001",
            registerIp: "*************"
        );
        SetEntityId(user2, Guid.Parse("d222**************-2222-************"));
        user2.UpdateProfile(
            nickname: "Alice",
            gender: Gender.Female,
            birthday: new DateTime(1995, 5, 15, 0, 0, 0, DateTimeKind.Utc),
            region: "上海"
        );
        user2.VerifyEmail();
        user2.VerifyPhone();
        user2.RecordLogin("*************");
        // 最后设置时间戳，避免并发冲突
        SetEntityTimestamps(user2, DateTime.UtcNow.AddDays(-60));
        users.Add(user2);
        
        // User 3: bob_chen
        var user3 = new CustomerUser(
            username: "bob_chen",
            password: "Bob@1234",
            email: "<EMAIL>",
            phone: "13700137002",
            registerIp: "*********"
        );
        SetEntityId(user3, Guid.Parse("d333**************-3333-************"));
        user3.UpdateProfile(
            nickname: "Bob",
            gender: Gender.Male,
            birthday: new DateTime(1988, 12, 20, 0, 0, 0, DateTimeKind.Utc),
            region: "北京",
            position: "数据分析师"
        );
        user3.VerifyEmail();
        user3.VerifyPhone();
        user3.RecordLogin("*********");
        // 最后设置时间戳，避免并发冲突
        SetEntityTimestamps(user3, DateTime.UtcNow.AddDays(-90));
        users.Add(user3);
        
        // User 4: test_free
        var user4 = new CustomerUser(
            username: "test_free",
            password: "Test@123",
            email: "<EMAIL>",
            registerIp: "127.0.0.1"
        );
        SetEntityId(user4, Guid.Parse("d444**************-4444-************"));
        user4.UpdateProfile(
            nickname: "免费测试用户",
            gender: Gender.Unknown
        );
        user4.VerifyEmail();
        // 最后设置时间戳，避免并发冲突
        SetEntityTimestamps(user4, DateTime.UtcNow.AddDays(-7));
        users.Add(user4);
        
        // User 5: test_pro
        var user5 = new CustomerUser(
            username: "test_pro",
            password: "Test@123",
            email: "<EMAIL>",
            registerIp: "127.0.0.1"
        );
        SetEntityId(user5, Guid.Parse("d555**************-5555-************"));
        user5.UpdateProfile(
            nickname: "专业版测试用户",
            gender: Gender.Unknown
        );
        user5.VerifyEmail();
        // 最后设置时间戳，避免并发冲突
        SetEntityTimestamps(user5, DateTime.UtcNow.AddDays(-45));
        users.Add(user5);
        
        // User 6: inactive_user
        var user6 = new CustomerUser(
            username: "inactive_user",
            password: "Inactive@123",
            email: "<EMAIL>",
            registerIp: "127.0.0.1"
        );
        SetEntityId(user6, Guid.Parse("d666**************-6666-************"));
        user6.UpdateProfile(
            nickname: "停用用户",
            gender: Gender.Unknown
        );
        user6.Deactivate(); // Make user inactive
        // 最后设置时间戳，避免并发冲突
        SetEntityTimestamps(user6, DateTime.UtcNow.AddDays(-120));
        users.Add(user6);

        await Context.CustomerUsers.AddRangeAsync(users);
        await Context.SaveChangesAsync();
        
        LogSeeding("CustomerUser", users.Count);
    }
}