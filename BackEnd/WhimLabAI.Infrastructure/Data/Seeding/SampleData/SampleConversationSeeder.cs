using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using WhimLabAI.Domain.Entities.Conversation;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Infrastructure.Data.Seeding.SampleData;

/// <summary>
/// 示例对话数据种子
/// </summary>
public class SampleConversationSeeder : EntitySeederBase<Conversation>
{
    public SampleConversationSeeder(WhimLabAIDbContext context, ILogger<SampleConversationSeeder> logger) 
        : base(context, logger)
    {
    }
    
    public override async Task SeedAsync()
    {
        // 仅在开发环境中运行
        var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
        if (environment != "Development")
        {
            return;
        }

        // 检查是否已有对话
        if (await Context.Conversations.AnyAsync())
        {
            LogSeedingSkipped("Conversation");
            return;
        }

        // Demo用户与通用助手的对话
        var conv1 = new Conversation(
            customerUserId: Guid.Parse("d1111111-1111-1111-1111-111111111111"), // demo_user
            agentId: Guid.Parse("e1111111-1111-1111-1111-111111111111"), // 通用助手
            title: "日常问答对话",
            model: "gpt-3.5-turbo"
        );
        SetEntityId(conv1, Guid.Parse("f1111111-1111-1111-1111-111111111111"));
        SetEntityTimestamps(conv1, DateTime.UtcNow.AddDays(-5));
        
        // 添加消息
        conv1.AddMessage("user", "你好，请介绍一下你自己", 10);
        conv1.AddMessage("assistant", "你好！我是WhimLab通用助手，一个友好、专业的AI助手。我可以帮助您处理各种日常任务，包括：\n\n1. **问答咨询** - 回答您的各种问题\n2. **写作辅助** - 帮助您撰写文章、邮件等\n3. **翻译服务** - 支持多语言翻译\n4. **信息整理** - 帮您总结和整理信息\n5. **创意激发** - 提供创意和建议\n\n有什么我可以帮助您的吗？", 120);
        conv1.AddMessage("user", "帮我写一个关于环保的标语", 12);
        conv1.AddMessage("assistant", "这里有几个关于环保的标语供您参考：\n\n1. **\"绿色地球，美好家园\"**\n   简洁有力，强调环保与家园的关系\n\n2. **\"今天的行动，明天的希望\"**\n   突出行动的重要性和对未来的影响\n\n3. **\"小举动，大改变\"**\n   鼓励每个人从小事做起\n\n4. **\"保护环境，就是保护我们自己\"**\n   强调环保与个人利益的直接关系\n\n5. **\"地球不需要我们，但我们需要地球\"**\n   引发思考，强调人与自然的依存关系\n\n您可以根据使用场景选择合适的标语，或者告诉我具体的使用场景，我可以为您定制更合适的标语。", 180);

        // Demo用户与代码助手的对话
        var conv2 = new Conversation(
            customerUserId: Guid.Parse("d1111111-1111-1111-1111-111111111111"), // demo_user
            agentId: Guid.Parse("e222**************-2222-************"), // 代码助手
            title: "Python编程问题",
            model: "gpt-4"
        );
        SetEntityId(conv2, Guid.Parse("f222**************-2222-************"));
        SetEntityTimestamps(conv2, DateTime.UtcNow.AddDays(-3));
        
        // Alice与写作助手的对话
        var conv3 = new Conversation(
            customerUserId: Guid.Parse("d222**************-2222-************"), // alice_wang
            agentId: Guid.Parse("e333**************-3333-************"), // 写作助手
            title: "营销文案创作",
            model: "gpt-3.5-turbo"
        );
        SetEntityId(conv3, Guid.Parse("f333**************-3333-************"));
        SetEntityTimestamps(conv3, DateTime.UtcNow.AddDays(-7));
        
        // Bob与数据分析师的对话
        var conv4 = new Conversation(
            customerUserId: Guid.Parse("d333**************-3333-************"), // bob_chen
            agentId: Guid.Parse("e444**************-4444-************"), // 数据分析师
            title: "销售数据分析",
            model: "gpt-4"
        );
        SetEntityId(conv4, Guid.Parse("f444**************-4444-************"));
        SetEntityTimestamps(conv4, DateTime.UtcNow.AddDays(-2));
        
        // 已完成的对话
        var conv5 = new Conversation(
            customerUserId: Guid.Parse("d1111111-1111-1111-1111-111111111111"), // demo_user
            agentId: Guid.Parse("e666**************-6666-************"), // 翻译专家
            title: "英文文档翻译",
            model: "gpt-3.5-turbo"
        );
        SetEntityId(conv5, Guid.Parse("f555**************-5555-************"));
        SetEntityTimestamps(conv5, DateTime.UtcNow.AddDays(-10));
        
        // 已归档的对话
        var conv6 = new Conversation(
            customerUserId: Guid.Parse("d222**************-2222-************"), // alice_wang
            agentId: Guid.Parse("e555**************-5555-************"), // 教育导师
            title: "数学学习辅导",
            model: "gpt-3.5-turbo"
        );
        SetEntityId(conv6, Guid.Parse("f666**************-6666-************"));
        // 注意：先添加消息，再归档，最后设置时间戳
        
        // 为代码助手对话添加消息
        conv2.AddMessage("user", "如何在Python中实现一个简单的缓存装饰器？", 18);
        conv2.AddMessage("assistant", @"我来为您展示如何实现一个简单的缓存装饰器。这里有几种不同的实现方式：

## 1. 基础版本 - 简单缓存装饰器

```python
def simple_cache(func):
    cache = {}
    
    def wrapper(*args, **kwargs):
        # 创建缓存键
        key = str(args) + str(kwargs)
        
        # 检查缓存
        if key in cache:
            print(f'Cache hit for {func.__name__}{args}')
            return cache[key]
        
        # 计算结果并缓存
        result = func(*args, **kwargs)
        cache[key] = result
        print(f'Cache miss for {func.__name__}{args}')
        return result
    
    return wrapper
```

这是最简单的实现方式。", 220);
        
        // 为写作助手对话添加消息
        conv3.AddMessage("user", "帮我写一篇关于新产品发布的营销文案", 20);
        conv3.AddMessage("assistant", "好的，我来帮您撰写一篇引人注目的新产品发布营销文案...", 150);
        
        // 为数据分析师对话添加消息
        conv4.AddMessage("user", "帮我分析一下上个季度的销售数据", 15);
        conv4.AddMessage("assistant", "我来为您分析上个季度的销售数据...", 200);
        
        // 为翻译专家对话添加消息
        conv5.AddMessage("user", "请帮我翻译这段英文文档", 10);
        conv5.AddMessage("assistant", "好的，我来为您翻译这段文档...", 100);
        
        // 为教育导师对话添加消息
        conv6.AddMessage("user", "我想学习一次函数的相关知识", 12);
        conv6.AddMessage("assistant", "好的，我来为您讲解一次函数的相关知识...", 180);

        // 归档对话（必须在添加消息后）
        conv6.Archive();

        // 最后设置归档对话的时间戳，避免并发冲突
        SetEntityTimestamps(conv6, DateTime.UtcNow.AddDays(-35));

        var conversations = new List<Conversation> { conv1, conv2, conv3, conv4, conv5, conv6 };
        await Context.Conversations.AddRangeAsync(conversations);
        await Context.SaveChangesAsync();
        
        LogSeeding("Conversation", conversations.Count);
    }
}