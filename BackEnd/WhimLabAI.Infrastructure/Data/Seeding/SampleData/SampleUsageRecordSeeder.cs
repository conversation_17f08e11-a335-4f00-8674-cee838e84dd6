using WhimLabAI.Domain.Entities.Subscription;
using WhimLabAI.Infrastructure.Data.Seeding;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Infrastructure.Data.Seeding.SampleData;

public class SampleUsageRecordSeeder : EntitySeederBase<UsageRecord>
{
    public SampleUsageRecordSeeder(WhimLabAIDbContext context, ILogger<SampleUsageRecordSeeder> logger) 
        : base(context, logger)
    {
    }
    
    public override async Task SeedAsync()
    {
        // 仅在开发环境中运行
        var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
        if (environment != "Development")
        {
            return;
        }
        
        // 检查是否已有数据
        if (await Context.UsageRecords.AnyAsync())
        {
            LogSeedingSkipped("UsageRecord");
            return;
        }

        var random = new Random();
        var usageRecords = new List<UsageRecord>();

        // 获取活跃用户和智能体 - 使用实际种子数据中的ID
        var activeUserIds = new[]
        {
            Guid.Parse("d1111111-1111-1111-1111-111111111111"), // demo_user
            Guid.Parse("d222**************-2222-************"), // alice_wang
            Guid.Parse("d333**************-3333-************"), // bob_chen
        };

        var agentIds = new[]
        {
            Guid.Parse("e1111111-1111-1111-1111-111111111111"), // 通用助手
            Guid.Parse("e222**************-2222-************"), // 代码助手
            Guid.Parse("e333**************-3333-************"), // 写作助手
            Guid.Parse("e444**************-4444-************"), // 数据分析师
            Guid.Parse("e666**************-6666-************"), // 翻译专家
        };

        var modelNames = new[] { "gpt-3.5-turbo", "gpt-4", "claude-instant-1.2", "claude-2.1" };

        // 生成最近30天的使用记录
        var startDate = DateTime.UtcNow.AddDays(-30);
        
        for (int day = 0; day < 30; day++)
        {
            var date = startDate.AddDays(day);
            
            // 每天为每个活跃用户生成一些使用记录
            foreach (var userId in activeUserIds)
            {
                // 每天生成2-8条记录
                var recordCount = random.Next(2, 9);
                
                for (int i = 0; i < recordCount; i++)
                {
                    var hour = random.Next(0, 24);
                    var minute = random.Next(0, 60);
                    var agentId = agentIds[random.Next(agentIds.Length)];
                    var modelName = modelNames[random.Next(modelNames.Length)];
                    var inputTokens = random.Next(50, 500);
                    var outputTokens = random.Next(100, 1000);
                    var (inputCost, outputCost) = CalculateCost(modelName, inputTokens, outputTokens);

                    // 需要一个有效的订阅ID
                    var subscription = await Context.Subscriptions
                        .FirstOrDefaultAsync(s => s.CustomerUserId == userId && s.Status == SubscriptionStatus.Active);
                    
                    if (subscription == null)
                        continue;
                    
                    var conversationId = Guid.NewGuid(); // 生成对话ID
                    var recordDateTime = date.AddHours(hour).AddMinutes(minute);
                    
                    var usageRecord = new UsageRecord(
                        subscriptionId: subscription.Id,
                        userId: userId,
                        conversationId: conversationId,
                        agentId: agentId,
                        modelName: modelName,
                        modelProvider: GetModelProvider(modelName),
                        tokensUsed: inputTokens + outputTokens,
                        costAmount: inputCost + outputCost
                    );
                    
                    // 使用EntitySeederBase提供的方法设置时间戳
                    SetEntityTimestamps(usageRecord, recordDateTime);
                    
                    // 添加元数据
                    usageRecord.AddMetadata("inputTokens", inputTokens);
                    usageRecord.AddMetadata("outputTokens", outputTokens);
                    usageRecord.AddMetadata("inputCost", inputCost);
                    usageRecord.AddMetadata("outputCost", outputCost);
                    usageRecord.AddMetadata("responseTime", random.Next(500, 3000));
                    usageRecord.AddMetadata("success", true);
                    usageRecord.AddMetadata("billingCycle", date.ToString("yyyy-MM"));
                    
                    usageRecords.Add(usageRecord);
                }
            }
        }

        await Context.UsageRecords.AddRangeAsync(usageRecords);
        await Context.SaveChangesAsync();
        
        LogSeeding("UsageRecord", usageRecords.Count);
    }
    
    private string GetModelProvider(string modelName)
    {
        if (modelName.StartsWith("gpt")) return "OpenAI";
        if (modelName.StartsWith("claude")) return "Anthropic";
        return "Unknown";
    }

    private (decimal inputCost, decimal outputCost) CalculateCost(string modelName, int inputTokens, int outputTokens)
    {
        // 成本计算（每1000个token的价格）
        return modelName switch
        {
            "gpt-3.5-turbo" => (inputTokens * 0.0015m / 1000, outputTokens * 0.002m / 1000),
            "gpt-4" => (inputTokens * 0.03m / 1000, outputTokens * 0.06m / 1000),
            "claude-instant-1.2" => (inputTokens * 0.00163m / 1000, outputTokens * 0.00551m / 1000),
            "claude-2.1" => (inputTokens * 0.008m / 1000, outputTokens * 0.024m / 1000),
            _ => (inputTokens * 0.001m / 1000, outputTokens * 0.001m / 1000)
        };
    }
}