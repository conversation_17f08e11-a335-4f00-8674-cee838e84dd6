using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;

namespace WhimLabAI.Infrastructure.Data.Seeding;

/// <summary>
/// 系统配置种子数据
/// </summary>
public class SystemConfigurationSeeder : IDataSeeder
{
    public async Task SeedAsync(WhimLabAIDbContext context)
    {
        // 使用原始SQL创建系统配置表（如果不存在）
        var createTableSql = @"
            CREATE TABLE IF NOT EXISTS SystemConfigurations (
                Id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                Category VARCHAR(100) NOT NULL,
                Key VARCHAR(200) NOT NULL,
                Value TEXT NOT NULL,
                DataType VARCHAR(50) NOT NULL,
                Description TEXT,
                IsEncrypted BOOLEAN NOT NULL DEFAULT FALSE,
                IsPublic BOOLEAN NOT NULL DEFAULT FALSE,
                LastModifiedBy VARCHAR(100),
                LastModifiedAt TIMESTAMP,
                CreatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(Category, Key)
            );";

        await context.Database.ExecuteSqlRawAsync(createTableSql);

        // 检查是否已有配置
        var hasConfigs = await context.Database
            .ExecuteSqlRawAsync("SELECT 1 FROM SystemConfigurations LIMIT 1") > 0;

        if (hasConfigs)
        {
            return;
        }

        // 插入系统配置
        var configurations = GetDefaultConfigurations();
        
        foreach (var config in configurations)
        {
            var sql = @"
                INSERT INTO SystemConfigurations (Id, Category, Key, Value, DataType, Description, IsEncrypted, IsPublic, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8)
                ON CONFLICT (Category, Key) DO NOTHING";

            await context.Database.ExecuteSqlRawAsync(sql,
                config.Id,
                config.Category,
                config.Key,
                config.Value,
                config.DataType,
                config.Description,
                config.IsEncrypted,
                config.IsPublic,
                config.CreatedAt);
        }
    }

    private List<SystemConfiguration> GetDefaultConfigurations()
    {
        return new List<SystemConfiguration>
        {
            // 系统基本配置
            new SystemConfiguration
            {
                Category = "System",
                Key = "SiteName",
                Value = "WhimLab AI",
                DataType = "String",
                Description = "网站名称",
                IsPublic = true
            },
            new SystemConfiguration
            {
                Category = "System",
                Key = "SiteUrl",
                Value = "https://whimlabai.com",
                DataType = "String",
                Description = "网站URL",
                IsPublic = true
            },
            new SystemConfiguration
            {
                Category = "System",
                Key = "Version",
                Value = "1.0.0",
                DataType = "String",
                Description = "系统版本",
                IsPublic = true
            },
            new SystemConfiguration
            {
                Category = "System",
                Key = "MaintenanceMode",
                Value = "false",
                DataType = "Boolean",
                Description = "维护模式",
                IsPublic = true
            },

            // 安全配置
            new SystemConfiguration
            {
                Category = "Security",
                Key = "PasswordMinLength",
                Value = "8",
                DataType = "Integer",
                Description = "密码最小长度",
                IsPublic = false
            },
            new SystemConfiguration
            {
                Category = "Security",
                Key = "PasswordRequireUppercase",
                Value = "true",
                DataType = "Boolean",
                Description = "密码需要大写字母",
                IsPublic = false
            },
            new SystemConfiguration
            {
                Category = "Security",
                Key = "PasswordRequireLowercase",
                Value = "true",
                DataType = "Boolean",
                Description = "密码需要小写字母",
                IsPublic = false
            },
            new SystemConfiguration
            {
                Category = "Security",
                Key = "PasswordRequireDigit",
                Value = "true",
                DataType = "Boolean",
                Description = "密码需要数字",
                IsPublic = false
            },
            new SystemConfiguration
            {
                Category = "Security",
                Key = "PasswordRequireSpecialChar",
                Value = "true",
                DataType = "Boolean",
                Description = "密码需要特殊字符",
                IsPublic = false
            },
            new SystemConfiguration
            {
                Category = "Security",
                Key = "MaxLoginAttempts",
                Value = "5",
                DataType = "Integer",
                Description = "最大登录尝试次数",
                IsPublic = false
            },
            new SystemConfiguration
            {
                Category = "Security",
                Key = "AccountLockoutDuration",
                Value = "30",
                DataType = "Integer",
                Description = "账户锁定时长（分钟）",
                IsPublic = false
            },

            // 邮件配置
            new SystemConfiguration
            {
                Category = "Email",
                Key = "SmtpHost",
                Value = "smtp.example.com",
                DataType = "String",
                Description = "SMTP服务器地址",
                IsPublic = false
            },
            new SystemConfiguration
            {
                Category = "Email",
                Key = "SmtpPort",
                Value = "587",
                DataType = "Integer",
                Description = "SMTP端口",
                IsPublic = false
            },
            new SystemConfiguration
            {
                Category = "Email",
                Key = "SmtpUsername",
                Value = "<EMAIL>",
                DataType = "String",
                Description = "SMTP用户名",
                IsPublic = false
            },
            new SystemConfiguration
            {
                Category = "Email",
                Key = "SmtpPassword",
                Value = "",
                DataType = "String",
                Description = "SMTP密码",
                IsEncrypted = true,
                IsPublic = false
            },
            new SystemConfiguration
            {
                Category = "Email",
                Key = "SmtpEnableSsl",
                Value = "true",
                DataType = "Boolean",
                Description = "启用SSL",
                IsPublic = false
            },
            new SystemConfiguration
            {
                Category = "Email",
                Key = "FromEmail",
                Value = "<EMAIL>",
                DataType = "String",
                Description = "发件人邮箱",
                IsPublic = false
            },
            new SystemConfiguration
            {
                Category = "Email",
                Key = "FromName",
                Value = "WhimLab AI",
                DataType = "String",
                Description = "发件人名称",
                IsPublic = false
            },

            // 存储配置
            new SystemConfiguration
            {
                Category = "Storage",
                Key = "Provider",
                Value = "Local",
                DataType = "String",
                Description = "存储提供商（Local/S3/Azure/Aliyun）",
                IsPublic = false
            },
            new SystemConfiguration
            {
                Category = "Storage",
                Key = "LocalPath",
                Value = "/app/storage",
                DataType = "String",
                Description = "本地存储路径",
                IsPublic = false
            },
            new SystemConfiguration
            {
                Category = "Storage",
                Key = "MaxFileSize",
                Value = "10485760",
                DataType = "Long",
                Description = "最大文件大小（字节）",
                IsPublic = false
            },
            new SystemConfiguration
            {
                Category = "Storage",
                Key = "AllowedFileTypes",
                Value = ".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx,.txt",
                DataType = "String",
                Description = "允许的文件类型",
                IsPublic = false
            },

            // AI配置
            new SystemConfiguration
            {
                Category = "AI",
                Key = "DefaultProvider",
                Value = "OpenAI",
                DataType = "String",
                Description = "默认AI提供商",
                IsPublic = false
            },
            new SystemConfiguration
            {
                Category = "AI",
                Key = "MaxTokensPerRequest",
                Value = "4000",
                DataType = "Integer",
                Description = "每次请求最大Token数",
                IsPublic = false
            },
            new SystemConfiguration
            {
                Category = "AI",
                Key = "DefaultTemperature",
                Value = "0.7",
                DataType = "Double",
                Description = "默认温度参数",
                IsPublic = false
            },
            new SystemConfiguration
            {
                Category = "AI",
                Key = "StreamingEnabled",
                Value = "true",
                DataType = "Boolean",
                Description = "启用流式响应",
                IsPublic = false
            },

            // 支付配置
            new SystemConfiguration
            {
                Category = "Payment",
                Key = "EnabledGateways",
                Value = "Alipay,WeChatPay",
                DataType = "String",
                Description = "启用的支付网关",
                IsPublic = false
            },
            new SystemConfiguration
            {
                Category = "Payment",
                Key = "Currency",
                Value = "CNY",
                DataType = "String",
                Description = "默认货币",
                IsPublic = true
            },
            new SystemConfiguration
            {
                Category = "Payment",
                Key = "RefundEnabled",
                Value = "true",
                DataType = "Boolean",
                Description = "启用退款",
                IsPublic = false
            },
            new SystemConfiguration
            {
                Category = "Payment",
                Key = "RefundDays",
                Value = "7",
                DataType = "Integer",
                Description = "退款天数限制",
                IsPublic = false
            },

            // 限制配置
            new SystemConfiguration
            {
                Category = "Limits",
                Key = "FreeUserDailyRequests",
                Value = "100",
                DataType = "Integer",
                Description = "免费用户每日请求限制",
                IsPublic = false
            },
            new SystemConfiguration
            {
                Category = "Limits",
                Key = "MaxConversationLength",
                Value = "1000",
                DataType = "Integer",
                Description = "最大对话长度",
                IsPublic = false
            },
            new SystemConfiguration
            {
                Category = "Limits",
                Key = "MessageRetentionDays",
                Value = "90",
                DataType = "Integer",
                Description = "消息保留天数",
                IsPublic = false
            },

            // 通知配置
            new SystemConfiguration
            {
                Category = "Notification",
                Key = "EnableEmailNotifications",
                Value = "true",
                DataType = "Boolean",
                Description = "启用邮件通知",
                IsPublic = false
            },
            new SystemConfiguration
            {
                Category = "Notification",
                Key = "EnableSmsNotifications",
                Value = "false",
                DataType = "Boolean",
                Description = "启用短信通知",
                IsPublic = false
            },
            new SystemConfiguration
            {
                Category = "Notification",
                Key = "EnablePushNotifications",
                Value = "true",
                DataType = "Boolean",
                Description = "启用推送通知",
                IsPublic = false
            }
        };
    }

    private class SystemConfiguration
    {
        public Guid Id { get; set; } = Guid.NewGuid();
        public string Category { get; set; } = string.Empty;
        public string Key { get; set; } = string.Empty;
        public string Value { get; set; } = string.Empty;
        public string DataType { get; set; } = string.Empty;
        public string? Description { get; set; }
        public bool IsEncrypted { get; set; }
        public bool IsPublic { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    }
}