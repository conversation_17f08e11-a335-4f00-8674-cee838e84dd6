using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using WhimLabAI.Domain.Entities.Subscription;
using WhimLabAI.Domain.ValueObjects;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Constants;

namespace WhimLabAI.Infrastructure.Data.Seeding;

/// <summary>
/// 订阅计划种子数据
/// </summary>
public class SubscriptionPlanSeeder : EntitySeederBase<SubscriptionPlan>
{
    public SubscriptionPlanSeeder(WhimLabAIDbContext context, ILogger<SubscriptionPlanSeeder> logger) 
        : base(context, logger)
    {
    }

    public override async Task SeedAsync()
    {
        // 检查是否已有订阅计划
        if (await Context.SubscriptionPlans.AnyAsync())
        {
            LogSeedingSkipped("SubscriptionPlan");
            return;
        }

        var plans = new List<SubscriptionPlan>();

        // Free plan
        var freePlan = new SubscriptionPlan(
            name: "免费版",
            tier: SubscriptionTier.Free,
            price: Money.Create(0, "CNY"),
            tokenQuota: BusinessConstants.DefaultTokenQuota.Free,
            billingCycle: WhimLabAI.Shared.Enums.BillingCycle.Monthly,
            description: "适合个人用户体验"
        );
        SetEntityId(freePlan, Guid.Parse("b1111111-1111-1111-1111-111111111111"));
        freePlan.SetFeatures(new List<string> 
        { 
            "每月 5,000 tokens",
            "基础对话功能",
            "3个预置智能体",
            "社区支持"
        });
        freePlan.SetAdvancedLimits(
            maxAgents: 3,
            maxConversationsPerDay: 10,
            allowCustomAgents: false,
            allowPlugins: false,
            allowKnowledgeBase: false
        );
        freePlan.SetSortOrder(1);
        plans.Add(freePlan);

        // Basic plan
        var basicPlan = new SubscriptionPlan(
            name: "基础版",
            tier: SubscriptionTier.Basic,
            price: Money.Create(99, "CNY"),
            tokenQuota: BusinessConstants.DefaultTokenQuota.Basic,
            billingCycle: WhimLabAI.Shared.Enums.BillingCycle.Monthly,
            description: "适合个人专业用户和小团队"
        );
        SetEntityId(basicPlan, Guid.Parse("b222**************-2222-************"));
        basicPlan.SetFeatures(new List<string> 
        { 
            "每月 50,000 tokens",
            "高级对话功能",
            "10个预置智能体",
            "创建5个自定义智能体",
            "邮件支持",
            "API访问（100次/天）"
        });
        basicPlan.SetAdvancedLimits(
            maxAgents: 10,
            maxConversationsPerDay: 50,
            allowCustomAgents: true,
            allowPlugins: false,
            allowKnowledgeBase: false
        );
        basicPlan.SetSortOrder(2);
        basicPlan.SetLimit("ApiRateLimit", 100);
        basicPlan.SetLimit("DataRetentionDays", 90);
        plans.Add(basicPlan);

        // Pro plan
        var proPlan = new SubscriptionPlan(
            name: "专业版",
            tier: SubscriptionTier.Pro,
            price: Money.Create(199, "CNY"),
            tokenQuota: BusinessConstants.DefaultTokenQuota.Pro,
            billingCycle: WhimLabAI.Shared.Enums.BillingCycle.Monthly,
            description: "适合专业团队和中型企业"
        );
        SetEntityId(proPlan, Guid.Parse("b333**************-3333-************"));
        proPlan.SetFeatures(new List<string> 
        { 
            "每月 200,000 tokens",
            "所有对话功能",
            "所有预置智能体",
            "创建20个自定义智能体",
            "优先邮件支持",
            "API访问（1000次/天）",
            "插件支持",
            "知识库功能"
        });
        proPlan.SetAdvancedLimits(
            maxAgents: 50,
            maxConversationsPerDay: 200,
            allowCustomAgents: true,
            allowPlugins: true,
            allowKnowledgeBase: true
        );
        proPlan.SetSortOrder(3);
        proPlan.SetLimit("ApiRateLimit", 1000);
        proPlan.SetLimit("DataRetentionDays", 180);
        proPlan.SetLimit("TeamMembers", 10);
        plans.Add(proPlan);

        // Ultra plan
        var ultraPlan = new SubscriptionPlan(
            name: "旗舰版",
            tier: SubscriptionTier.Ultra,
            price: Money.Create(299, "CNY"),
            tokenQuota: BusinessConstants.DefaultTokenQuota.Ultra,
            billingCycle: WhimLabAI.Shared.Enums.BillingCycle.Monthly,
            description: "适合大型企业，享受所有高级功能"
        );
        SetEntityId(ultraPlan, Guid.Parse("b444**************-4444-************"));
        ultraPlan.SetFeatures(new List<string> 
        { 
            "每月 500,000 tokens",
            "所有功能无限制",
            "无限自定义智能体",
            "专属客服支持",
            "API访问（无限制）",
            "所有插件",
            "高级知识库",
            "私有部署支持",
            "SSO单点登录",
            "审计日志"
        });
        ultraPlan.SetAdvancedLimits(
            maxAgents: -1, // Unlimited
            maxConversationsPerDay: -1, // Unlimited
            allowCustomAgents: true,
            allowPlugins: true,
            allowKnowledgeBase: true
        );
        ultraPlan.SetSortOrder(4);
        ultraPlan.MarkAsPopular(); // Mark Ultra as popular/recommended
        ultraPlan.SetLimit("ApiRateLimit", -1); // Unlimited
        ultraPlan.SetLimit("DataRetentionDays", 365);
        ultraPlan.SetLimit("TeamMembers", -1); // Unlimited
        ultraPlan.SetLimit("CustomDomain", true);
        ultraPlan.SetLimit("WhiteLabel", true);
        plans.Add(ultraPlan);

        // Add quarterly and annual versions of paid plans
        plans.AddRange(CreateBillingVariants(basicPlan));
        plans.AddRange(CreateBillingVariants(proPlan));
        plans.AddRange(CreateBillingVariants(ultraPlan));

        LogSeeding("SubscriptionPlan", plans.Count);
        await Context.SubscriptionPlans.AddRangeAsync(plans);
        await Context.SaveChangesAsync();
        LogSeedingComplete("SubscriptionPlan");
    }

    private List<SubscriptionPlan> CreateBillingVariants(SubscriptionPlan monthlyPlan)
    {
        var variants = new List<SubscriptionPlan>();

        // Quarterly plan (10% discount)
        var quarterlyPlan = new SubscriptionPlan(
            name: $"{monthlyPlan.Name} - 季付",
            tier: monthlyPlan.Tier,
            price: monthlyPlan.Price.ApplyDiscount(10),
            tokenQuota: monthlyPlan.TokenQuota * 3,
            billingCycle: WhimLabAI.Shared.Enums.BillingCycle.Quarterly,
            description: $"{monthlyPlan.Description} (季付享9折优惠)"
        );
        CopyPlanSettings(monthlyPlan, quarterlyPlan);
        quarterlyPlan.SetSortOrder(monthlyPlan.SortOrder + 10);
        variants.Add(quarterlyPlan);

        // Annual plan (20% discount)
        var annualPlan = new SubscriptionPlan(
            name: $"{monthlyPlan.Name} - 年付",
            tier: monthlyPlan.Tier,
            price: monthlyPlan.Price.ApplyDiscount(20),
            tokenQuota: monthlyPlan.TokenQuota * 12,
            billingCycle: WhimLabAI.Shared.Enums.BillingCycle.Annual,
            description: $"{monthlyPlan.Description} (年付享8折优惠)"
        );
        CopyPlanSettings(monthlyPlan, annualPlan);
        annualPlan.SetSortOrder(monthlyPlan.SortOrder + 20);
        variants.Add(annualPlan);

        return variants;
    }

    private void CopyPlanSettings(SubscriptionPlan source, SubscriptionPlan target)
    {
        target.SetFeatures(source.Features.ToList());
        target.SetAdvancedLimits(
            source.MaxAgents,
            source.MaxConversationsPerDay,
            source.AllowCustomAgents,
            source.AllowPlugins,
            source.AllowKnowledgeBase
        );
        
        foreach (var limit in source.Limits)
        {
            target.SetLimit(limit.Key, limit.Value);
        }

        if (source.IsPopular)
        {
            target.MarkAsPopular();
        }
    }
}