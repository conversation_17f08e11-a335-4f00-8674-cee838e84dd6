using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;

namespace WhimLabAI.Infrastructure.Data.Seeding;

/// <summary>
/// AI提供商种子数据
/// </summary>
public class AIProviderSeeder : IDataSeeder
{
    public async Task SeedAsync(WhimLabAIDbContext context)
    {
        // 使用原始SQL创建AI提供商配置表（如果不存在）
        var createTableSql = @"
            CREATE TABLE IF NOT EXISTS AIProviderConfigurations (
                Id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                ProviderName VARCHAR(100) NOT NULL UNIQUE,
                DisplayName VARCHAR(200) NOT NULL,
                Description TEXT,
                ProviderType VARCHAR(50) NOT NULL,
                Configuration JSONB NOT NULL,
                IsActive BOOLEAN NOT NULL DEFAULT TRUE,
                SortOrder INTEGER NOT NULL DEFAULT 0,
                CreatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                UpdatedAt TIMESTAMP
            );";

        await context.Database.ExecuteSqlRawAsync(createTableSql);

        // 检查是否已有配置
        var hasProviders = await context.Database
            .ExecuteSqlRawAsync("SELECT 1 FROM AIProviderConfigurations LIMIT 1") > 0;

        if (hasProviders)
        {
            return;
        }

        // 插入AI提供商配置
        var providers = GetDefaultProviders();
        
        foreach (var provider in providers)
        {
            var sql = @"
                INSERT INTO AIProviderConfigurations 
                (Id, ProviderName, DisplayName, Description, ProviderType, Configuration, IsActive, SortOrder, CreatedAt)
                VALUES (@p0, @p1, @p2, @p3, @p4, @p5::jsonb, @p6, @p7, @p8)
                ON CONFLICT (ProviderName) DO NOTHING";

            await context.Database.ExecuteSqlRawAsync(sql,
                provider.Id,
                provider.ProviderName,
                provider.DisplayName,
                provider.Description,
                provider.ProviderType,
                provider.Configuration,
                provider.IsActive,
                provider.SortOrder,
                provider.CreatedAt);
        }
    }

    private List<AIProviderConfiguration> GetDefaultProviders()
    {
        return new List<AIProviderConfiguration>
        {
            new AIProviderConfiguration
            {
                ProviderName = "OpenAI",
                DisplayName = "OpenAI",
                Description = "OpenAI GPT系列模型",
                ProviderType = "SemanticKernel",
                Configuration = System.Text.Json.JsonSerializer.Serialize(new
                {
                    ApiKey = "",
                    BaseUrl = "https://api.openai.com/v1",
                    Organization = "",
                    Models = new[]
                    {
                        new
                        {
                            Name = "gpt-3.5-turbo",
                            DisplayName = "GPT-3.5 Turbo",
                            MaxTokens = 4096,
                            InputCostPer1k = 0.0015m,
                            OutputCostPer1k = 0.002m,
                            IsActive = true
                        },
                        new
                        {
                            Name = "gpt-4",
                            DisplayName = "GPT-4",
                            MaxTokens = 8192,
                            InputCostPer1k = 0.03m,
                            OutputCostPer1k = 0.06m,
                            IsActive = true
                        },
                        new
                        {
                            Name = "gpt-4-turbo",
                            DisplayName = "GPT-4 Turbo",
                            MaxTokens = 128000,
                            InputCostPer1k = 0.01m,
                            OutputCostPer1k = 0.03m,
                            IsActive = true
                        }
                    },
                    DefaultParameters = new
                    {
                        Temperature = 0.7,
                        TopP = 1.0,
                        FrequencyPenalty = 0,
                        PresencePenalty = 0
                    }
                }),
                IsActive = true,
                SortOrder = 1
            },
            new AIProviderConfiguration
            {
                ProviderName = "Anthropic",
                DisplayName = "Anthropic Claude",
                Description = "Anthropic Claude系列模型",
                ProviderType = "SemanticKernel",
                Configuration = System.Text.Json.JsonSerializer.Serialize(new
                {
                    ApiKey = "",
                    BaseUrl = "https://api.anthropic.com",
                    Models = new[]
                    {
                        new
                        {
                            Name = "claude-instant-1.2",
                            DisplayName = "Claude Instant",
                            MaxTokens = 100000,
                            InputCostPer1k = 0.00163m,
                            OutputCostPer1k = 0.00551m,
                            IsActive = true
                        },
                        new
                        {
                            Name = "claude-2.1",
                            DisplayName = "Claude 2.1",
                            MaxTokens = 200000,
                            InputCostPer1k = 0.008m,
                            OutputCostPer1k = 0.024m,
                            IsActive = true
                        },
                        new
                        {
                            Name = "claude-3-sonnet-20240229",
                            DisplayName = "Claude 3 Sonnet",
                            MaxTokens = 200000,
                            InputCostPer1k = 0.003m,
                            OutputCostPer1k = 0.015m,
                            IsActive = true
                        }
                    },
                    DefaultParameters = new
                    {
                        Temperature = 0.7,
                        TopP = 1.0,
                        TopK = 0,
                        MaxTokens = 4096
                    }
                }),
                IsActive = true,
                SortOrder = 2
            },
            new AIProviderConfiguration
            {
                ProviderName = "Azure",
                DisplayName = "Azure OpenAI",
                Description = "Microsoft Azure OpenAI服务",
                ProviderType = "SemanticKernel",
                Configuration = System.Text.Json.JsonSerializer.Serialize(new
                {
                    ApiKey = "",
                    Endpoint = "",
                    DeploymentNames = new Dictionary<string, string>
                    {
                        ["gpt-35-turbo"] = "",
                        ["gpt-4"] = ""
                    },
                    ApiVersion = "2023-12-01-preview",
                    Models = new[]
                    {
                        new
                        {
                            Name = "gpt-35-turbo",
                            DisplayName = "GPT-3.5 Turbo (Azure)",
                            MaxTokens = 4096,
                            IsActive = true
                        },
                        new
                        {
                            Name = "gpt-4",
                            DisplayName = "GPT-4 (Azure)",
                            MaxTokens = 8192,
                            IsActive = true
                        }
                    }
                }),
                IsActive = false,
                SortOrder = 3
            },
            new AIProviderConfiguration
            {
                ProviderName = "Dify",
                DisplayName = "Dify平台",
                Description = "Dify AI应用开发平台",
                ProviderType = "Dify",
                Configuration = System.Text.Json.JsonSerializer.Serialize(new
                {
                    BaseUrl = "https://api.dify.ai/v1",
                    DefaultApiKey = "",
                    Apps = new[]
                    {
                        new
                        {
                            AppId = "",
                            AppName = "示例应用",
                            ApiKey = "",
                            Description = "Dify平台示例应用",
                            IsActive = false
                        }
                    },
                    DefaultParameters = new
                    {
                        ConversationId = "",
                        User = "default_user"
                    }
                }),
                IsActive = true,
                SortOrder = 4
            },
            new AIProviderConfiguration
            {
                ProviderName = "HuggingFace",
                DisplayName = "HuggingFace",
                Description = "HuggingFace开源模型",
                ProviderType = "SemanticKernel",
                Configuration = System.Text.Json.JsonSerializer.Serialize(new
                {
                    ApiKey = "",
                    BaseUrl = "https://api-inference.huggingface.co/models",
                    Models = new[]
                    {
                        new
                        {
                            Name = "microsoft/DialoGPT-medium",
                            DisplayName = "DialoGPT Medium",
                            MaxTokens = 1024,
                            IsActive = false
                        },
                        new
                        {
                            Name = "google/flan-t5-xxl",
                            DisplayName = "FLAN-T5 XXL",
                            MaxTokens = 512,
                            IsActive = false
                        }
                    }
                }),
                IsActive = false,
                SortOrder = 5
            },
            new AIProviderConfiguration
            {
                ProviderName = "LocalLLM",
                DisplayName = "本地大模型",
                Description = "本地部署的大语言模型",
                ProviderType = "SemanticKernel",
                Configuration = System.Text.Json.JsonSerializer.Serialize(new
                {
                    BaseUrl = "http://localhost:8000/v1",
                    Models = new[]
                    {
                        new
                        {
                            Name = "llama-2-7b",
                            DisplayName = "Llama 2 7B",
                            MaxTokens = 4096,
                            IsActive = false
                        },
                        new
                        {
                            Name = "chatglm3-6b",
                            DisplayName = "ChatGLM3 6B",
                            MaxTokens = 8192,
                            IsActive = false
                        }
                    },
                    DefaultParameters = new
                    {
                        Temperature = 0.7,
                        TopP = 0.9,
                        MaxTokens = 2048
                    }
                }),
                IsActive = false,
                SortOrder = 6
            }
        };
    }

    private class AIProviderConfiguration
    {
        public Guid Id { get; set; } = Guid.NewGuid();
        public string ProviderName { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string ProviderType { get; set; } = string.Empty;
        public string Configuration { get; set; } = "{}";
        public bool IsActive { get; set; }
        public int SortOrder { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    }
}