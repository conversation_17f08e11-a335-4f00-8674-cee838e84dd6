using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace WhimLabAI.Infrastructure.Data.Seeding;

/// <summary>
/// 统一的数据种子管理器
/// 负责管理所有种子数据的执行顺序和依赖关系
/// </summary>
public class UnifiedDataSeeder
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<UnifiedDataSeeder> _logger;

    // 定义seeder的执行优先级
    private enum SeederPriority
    {
        Core = 100,        // 核心数据（角色、权限等）
        Configuration = 200, // 配置数据
        Master = 300,      // 主数据（订阅计划、AI提供商等）
        User = 400,        // 用户数据
        Business = 500,    // 业务数据（Agent等）
        Sample = 1000      // 示例数据
    }

    // Seeder注册信息
    private class SeederInfo
    {
        public Type SeederType { get; set; }
        public SeederPriority Priority { get; set; }
        public string Description { get; set; }
        public Type[] Dependencies { get; set; }

        public SeederInfo(Type seederType, SeederPriority priority, string description, params Type[] dependencies)
        {
            SeederType = seederType;
            Priority = priority;
            Description = description;
            Dependencies = dependencies;
        }
    }

    // 注册所有的seeders
    private readonly List<SeederInfo> _seeders = new()
    {
        // Core seeders - 必须最先执行
        new SeederInfo(typeof(RoleSeeder), SeederPriority.Core, "角色基础数据"),
        new SeederInfo(typeof(PermissionSeeder), SeederPriority.Core, "权限基础数据", typeof(RoleSeeder)),

        // Configuration seeders
        new SeederInfo(typeof(SystemConfigurationSeeder), SeederPriority.Configuration, "系统配置"),
        new SeederInfo(typeof(AIProviderSeeder), SeederPriority.Configuration, "AI提供商配置"),

        // Master data seeders
        new SeederInfo(typeof(SubscriptionPlanSeeder), SeederPriority.Master, "订阅套餐"),
        new SeederInfo(typeof(TokenPackageSeeder), SeederPriority.Master, "Token包"),
        new SeederInfo(typeof(AgentCategorySeeder), SeederPriority.Master, "智能体分类"),

        // User seeders - 依赖角色
        new SeederInfo(typeof(AdminUserSeeder), SeederPriority.User, "管理员用户", typeof(RoleSeeder), typeof(PermissionSeeder)),

        // Business seeders - 依赖分类和用户
        new SeederInfo(typeof(AgentSeeder), SeederPriority.Business, "系统预置智能体", typeof(AgentCategorySeeder), typeof(AdminUserSeeder))
    };

    // Sample data seeders - 仅在开发环境执行
    private readonly List<SeederInfo> _sampleSeeders = new()
    {
        new SeederInfo(typeof(SampleData.SampleCustomerUserSeeder), SeederPriority.Sample, "示例客户用户"),
        new SeederInfo(typeof(SampleData.SampleAgentSeeder), SeederPriority.Sample, "示例智能体",
            typeof(AgentCategorySeeder), typeof(AdminUserSeeder)),
        new SeederInfo(typeof(SampleData.SampleSubscriptionSeeder), SeederPriority.Sample, "示例订阅",
            typeof(SampleData.SampleCustomerUserSeeder), typeof(SubscriptionPlanSeeder)),
        new SeederInfo(typeof(SampleData.SampleConversationSeeder), SeederPriority.Sample, "示例对话",
            typeof(SampleData.SampleCustomerUserSeeder), typeof(SampleData.SampleAgentSeeder))
    };

    private readonly HashSet<Type> _executedSeeders = new();

    public UnifiedDataSeeder(IServiceProvider serviceProvider, ILogger<UnifiedDataSeeder> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    /// <summary>
    /// 执行所有种子数据
    /// </summary>
    public async Task SeedAllAsync(bool includeSampleData = false)
    {
        using var scope = _serviceProvider.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<WhimLabAIDbContext>();

        _logger.LogInformation("开始执行数据种子...");

        // 使用执行策略来处理可能的并发冲突
        var executionStrategy = context.Database.CreateExecutionStrategy();

        await executionStrategy.ExecuteAsync(async () =>
        {
            try
            {
                // 执行核心种子数据
                await ExecuteSeedersByPriorityAsync(scope, _seeders);

                // 在开发环境执行示例数据
                if (includeSampleData)
                {
                    _logger.LogInformation("开始执行示例数据种子...");
                    await ExecuteSeedersByPriorityAsync(scope, _sampleSeeders);
                }

                _logger.LogInformation("数据种子执行完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行数据种子时发生错误");
                throw;
            }
        });
    }

    /// <summary>
    /// 按优先级执行seeders
    /// </summary>
    private async Task ExecuteSeedersByPriorityAsync(IServiceScope scope, List<SeederInfo> seeders)
    {
        var context = scope.ServiceProvider.GetRequiredService<WhimLabAIDbContext>();

        // 按优先级分组并排序
        var priorityGroups = seeders
            .GroupBy(s => s.Priority)
            .OrderBy(g => g.Key);

        foreach (var group in priorityGroups)
        {
            _logger.LogInformation("执行优先级 {Priority} 的种子数据...", group.Key);

            // 在同一优先级内，考虑依赖关系
            var orderedSeeders = TopologicalSort(group.ToList());

            foreach (var seederInfo in orderedSeeders)
            {
                // 执行每个 seeder
                await ExecuteSeederAsync(scope, seederInfo);
            }
        }
    }

    /// <summary>
    /// 执行单个seeder
    /// </summary>
    private async Task ExecuteSeederAsync(IServiceScope scope, SeederInfo seederInfo)
    {
        if (_executedSeeders.Contains(seederInfo.SeederType))
        {
            _logger.LogDebug("Seeder {SeederType} 已执行，跳过", seederInfo.SeederType.Name);
            return;
        }

        // 先执行依赖
        foreach (var dependency in seederInfo.Dependencies)
        {
            var dependencyInfo = _seeders.Concat(_sampleSeeders)
                .FirstOrDefault(s => s.SeederType == dependency);

            if (dependencyInfo != null && !_executedSeeders.Contains(dependency))
            {
                // 递归执行依赖项
                await ExecuteSeederAsync(scope, dependencyInfo);
            }
        }

        _logger.LogInformation("执行 {SeederType}: {Description}", seederInfo.SeederType.Name, seederInfo.Description);

        try
        {
            var context = scope.ServiceProvider.GetRequiredService<WhimLabAIDbContext>();
            var seeder = CreateSeeder(scope, seederInfo.SeederType);

            if (seeder != null)
            {
                await seeder.SeedAsync(context);  // 执行种子数据插入
                _executedSeeders.Add(seederInfo.SeederType);  // 标记为已执行
                _logger.LogInformation("{SeederType} 执行成功", seederInfo.SeederType.Name);
            }
            else
            {
                _logger.LogWarning("无法创建 {SeederType} 实例，跳过执行", seederInfo.SeederType.Name);
            }
        }
        catch (Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException ex)
        {
            _logger.LogWarning(ex, "执行 {SeederType} 时发生并发冲突，这通常是正常的（数据可能已存在）", seederInfo.SeederType.Name);
            // 对于并发冲突，我们标记为已执行，因为数据可能已经存在
            _executedSeeders.Add(seederInfo.SeederType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "执行 {SeederType} 时发生错误", seederInfo.SeederType.Name);
            throw;
        }
    }

    /// <summary>
    /// 创建seeder实例
    /// </summary>
   private IDataSeeder? CreateSeeder(IServiceScope scope, Type seederType)
   {
       try
       {
           // 尝试从 DI 容器获取 seeder 实例
           var seeder = scope.ServiceProvider.GetService(seederType) as IDataSeeder;
           if (seeder != null) return seeder;

           // 手动创建实例
           var context = scope.ServiceProvider.GetRequiredService<WhimLabAIDbContext>();
           var loggerType = typeof(ILogger<>).MakeGenericType(seederType);
           var logger = scope.ServiceProvider.GetRequiredService(loggerType);

           // 使用反射创建 seeder 实例
           var constructor = seederType.GetConstructors().FirstOrDefault();
           if (constructor != null)
           {
               var parameters = constructor.GetParameters();
               var args = new List<object>();

               foreach (var param in parameters)
               {
                   if (param.ParameterType == typeof(WhimLabAIDbContext))
                       args.Add(context);
                   else if (param.ParameterType.IsAssignableFrom(loggerType))
                       args.Add(logger);
                   else
                       args.Add(scope.ServiceProvider.GetRequiredService(param.ParameterType));
               }

               return Activator.CreateInstance(seederType, args.ToArray()) as IDataSeeder;
           }
       }
       catch (Exception ex)
       {
           _logger.LogError(ex, "创建 {SeederType} 实例时发生错误", seederType.Name);
       }

       return null;
   }

    /// <summary>
    /// 拓扑排序，处理依赖关系
    /// </summary>
    private List<SeederInfo> TopologicalSort(List<SeederInfo> seeders)
    {
        var sorted = new List<SeederInfo>();
        var visited = new HashSet<Type>();
        var visiting = new HashSet<Type>();

        foreach (var seeder in seeders)
        {
            Visit(seeder, seeders, sorted, visited, visiting);
        }

        return sorted;
    }


    private void Visit(SeederInfo seeder, List<SeederInfo> allSeeders, List<SeederInfo> sorted,
        HashSet<Type> visited, HashSet<Type> visiting)
    {
        if (visited.Contains(seeder.SeederType))
            return;

        if (visiting.Contains(seeder.SeederType))
            throw new InvalidOperationException($"检测到循环依赖: {seeder.SeederType.Name}");

        visiting.Add(seeder.SeederType);

        foreach (var dependency in seeder.Dependencies)
        {
            var dependencySeeder = allSeeders.FirstOrDefault(s => s.SeederType == dependency);
            if (dependencySeeder != null)
            {
                Visit(dependencySeeder, allSeeders, sorted, visited, visiting);
            }
        }

        visiting.Remove(seeder.SeederType);
        visited.Add(seeder.SeederType);
        sorted.Add(seeder);
    }
}
