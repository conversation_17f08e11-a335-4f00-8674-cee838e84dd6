using Microsoft.Extensions.Logging;
using WhimLabAI.Domain.Common;

namespace WhimLabAI.Infrastructure.Data.Seeding;

/// <summary>
/// Base class for entity seeders that respects DDD principles
/// </summary>
public abstract class EntitySeederBase<TEntity> : IDataSeeder where TEntity : Entity
{
    protected readonly WhimLabAIDbContext Context;
    protected readonly ILogger Logger;
    
    protected EntitySeederBase(WhimLabAIDbContext context, ILogger logger)
    {
        Context = context ?? throw new ArgumentNullException(nameof(context));
        Logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }
    
    public abstract Task SeedAsync();
    
    /// <summary>
    /// Implementation of IDataSeeder interface
    /// </summary>
    public Task SeedAsync(WhimLabAIDbContext context)
    {
        // The context is already provided in the constructor,
        // so we just call the parameterless SeedAsync method
        return SeedAsync();
    }
    
    /// <summary>
    /// Sets the entity ID using internal method for seeding purposes only
    /// </summary>
    protected void SetEntityId(TEntity entity, Guid id)
    {
        entity.SetId(id);
    }
    
    /// <summary>
    /// Sets the entity timestamps using internal method for seeding purposes only
    /// </summary>
    protected void SetEntityTimestamps(TEntity entity, DateTime? createdAt = null, DateTime? updatedAt = null)
    {
        if (createdAt.HasValue)
        {
            entity.SetCreatedAt(createdAt.Value.Kind == DateTimeKind.Utc ? createdAt.Value : DateTime.SpecifyKind(createdAt.Value, DateTimeKind.Utc));
        }
        
        if (updatedAt.HasValue)
        {
            entity.SetUpdatedAt(updatedAt.Value.Kind == DateTimeKind.Utc ? updatedAt.Value : DateTime.SpecifyKind(updatedAt.Value, DateTimeKind.Utc));
        }
    }
    
    /// <summary>
    /// Logs seeding progress
    /// </summary>
    protected void LogSeeding(string entityType, int count)
    {
        Logger.LogInformation("Seeding {Count} {EntityType} entities", count, entityType);
    }
    
    /// <summary>
    /// Logs seeding completion
    /// </summary>
    protected void LogSeedingComplete(string entityType)
    {
        Logger.LogInformation("{EntityType} seeding completed successfully", entityType);
    }
    
    /// <summary>
    /// Logs seeding skip when data already exists
    /// </summary>
    protected void LogSeedingSkipped(string entityType)
    {
        Logger.LogInformation("{EntityType} seeding skipped - data already exists", entityType);
    }
}