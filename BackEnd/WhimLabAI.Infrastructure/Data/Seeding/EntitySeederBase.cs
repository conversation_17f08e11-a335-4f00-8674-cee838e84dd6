using Microsoft.Extensions.Logging;
using WhimLabAI.Domain.Common;

namespace WhimLabAI.Infrastructure.Data.Seeding;

/// <summary>
/// Base class for entity seeders that respects DDD principles
/// </summary>
public abstract class EntitySeederBase<TEntity> : IDataSeeder where TEntity : Entity
{
    protected readonly WhimLabAIDbContext Context;
    protected readonly ILogger Logger;
    
    protected EntitySeederBase(WhimLabAIDbContext context, ILogger logger)
    {
        Context = context ?? throw new ArgumentNullException(nameof(context));
        Logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }
    
    public abstract Task SeedAsync();
    
    /// <summary>
    /// Implementation of IDataSeeder interface
    /// </summary>
    public Task SeedAsync(WhimLabAIDbContext context)
    {
        // The context is already provided in the constructor,
        // so we just call the parameterless SeedAsync method
        return SeedAsync();
    }
    
    /// <summary>
    /// Sets the entity ID using internal method for seeding purposes only
    /// </summary>
    protected void SetEntityId(TEntity entity, Guid id)
    {
        entity.SetId(id);
    }
    
    /// <summary>
    /// Sets the entity timestamps using internal method for seeding purposes only
    /// </summary>
    protected void SetEntityTimestamps(TEntity entity, DateTime? createdAt = null, DateTime? updatedAt = null)
    {
        if (createdAt.HasValue)
        {
            entity.SetCreatedAt(createdAt.Value.Kind == DateTimeKind.Utc ? createdAt.Value : DateTime.SpecifyKind(createdAt.Value, DateTimeKind.Utc));
        }
        
        if (updatedAt.HasValue)
        {
            entity.SetUpdatedAt(updatedAt.Value.Kind == DateTimeKind.Utc ? updatedAt.Value : DateTime.SpecifyKind(updatedAt.Value, DateTimeKind.Utc));
        }
    }

    /// <summary>
    /// 专门用于种子数据的保存方法，避免并发冲突
    /// </summary>
    protected async Task SaveSeedDataAsync(IEnumerable<TEntity> entities)
    {
        var entityList = entities.ToList();
        if (!entityList.Any()) return;

        var originalAutoDetectChanges = Context.ChangeTracker.AutoDetectChangesEnabled;
        try
        {
            // 禁用自动变更检测以避免并发冲突
            Context.ChangeTracker.AutoDetectChangesEnabled = false;

            // 逐个添加实体，避免批量操作的并发问题
            foreach (var entity in entityList)
            {
                // 检查实体是否已存在
                var existingEntity = await Context.Set<TEntity>().FindAsync(entity.Id);
                if (existingEntity == null)
                {
                    // 直接添加到上下文，不触发时间戳更新
                    Context.Set<TEntity>().Add(entity);
                }
                else
                {
                    Logger.LogDebug("Entity {EntityId} already exists, skipping", entity.Id);
                }
            }

            // 使用专门的种子数据保存方法，跳过时间戳自动更新
            await SaveSeedChangesAsync();

            LogSeeding(typeof(TEntity).Name, entityList.Count(e => Context.Entry(e).State == Microsoft.EntityFrameworkCore.EntityState.Added));
        }
        catch (Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException ex)
        {
            Logger.LogWarning(ex, "Concurrency conflict occurred while seeding {EntityType}, this is usually normal (data may already exist)", typeof(TEntity).Name);
            // 对于种子数据，并发冲突通常意味着数据已存在，这是正常的
        }
        finally
        {
            // 恢复原始的变更检测设置
            Context.ChangeTracker.AutoDetectChangesEnabled = originalAutoDetectChanges;
        }
    }

    /// <summary>
    /// 专门用于种子数据的保存方法，跳过自动时间戳更新
    /// </summary>
    private async Task SaveSeedChangesAsync()
    {
        // 标记所有实体为种子数据，避免时间戳被覆盖
        var entries = Context.ChangeTracker.Entries<Domain.Common.Entity>()
            .Where(e => e.State == Microsoft.EntityFrameworkCore.EntityState.Added);

        var originalTimestamps = new Dictionary<Domain.Common.Entity, (DateTime CreatedAt, DateTime UpdatedAt)>();

        // 保存原始时间戳
        foreach (var entry in entries)
        {
            originalTimestamps[entry.Entity] = (entry.Entity.CreatedAt, entry.Entity.UpdatedAt);
        }

        try
        {
            // 保存更改
            await Context.SaveChangesAsync();
        }
        catch (Microsoft.EntityFrameworkCore.DbUpdateConcurrencyException)
        {
            // 对于种子数据，忽略并发冲突异常
            Logger.LogDebug("Concurrency conflict ignored for seed data");
        }

        // 恢复原始时间戳（如果需要）
        foreach (var kvp in originalTimestamps)
        {
            var entity = kvp.Key;
            var timestamps = kvp.Value;

            // 如果时间戳被 DbContext 覆盖，恢复原始值
            if (entity.CreatedAt != timestamps.CreatedAt || entity.UpdatedAt != timestamps.UpdatedAt)
            {
                entity.SetCreatedAt(timestamps.CreatedAt);
                entity.SetUpdatedAt(timestamps.UpdatedAt);
            }
        }
    }

    /// <summary>
    /// Logs seeding progress
    /// </summary>
    protected void LogSeeding(string entityType, int count)
    {
        Logger.LogInformation("Seeding {Count} {EntityType} entities", count, entityType);
    }
    
    /// <summary>
    /// Logs seeding completion
    /// </summary>
    protected void LogSeedingComplete(string entityType)
    {
        Logger.LogInformation("{EntityType} seeding completed successfully", entityType);
    }
    
    /// <summary>
    /// Logs seeding skip when data already exists
    /// </summary>
    protected void LogSeedingSkipped(string entityType)
    {
        Logger.LogInformation("{EntityType} seeding skipped - data already exists", entityType);
    }
}