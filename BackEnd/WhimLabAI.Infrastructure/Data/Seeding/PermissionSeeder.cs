using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using WhimLabAI.Domain.Entities.Auth;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Constants;

namespace WhimLabAI.Infrastructure.Data.Seeding;

/// <summary>
/// 权限种子数据
/// </summary>
public class PermissionSeeder : EntitySeederBase<Permission>
{
    public PermissionSeeder(WhimLabAIDbContext context, ILogger<PermissionSeeder> logger)
        : base(context, logger)
    {
    }

    public override async Task SeedAsync()
    {
        // 检查是否已有权限
        if (await Context.Permissions.AnyAsync())
        {
            LogSeedingSkipped("Permission");
            return;
        }

        var permissions = new List<Permission>();

        // 用户管理权限
        permissions.AddRange(CreatePermissionGroup("user", "用户管理", "UserManagement", new[]
        {
            ("view", "查看用户"),
            ("create", "创建用户"),
            ("update", "更新用户"),
            ("delete", "删除用户"),
            ("export", "导出用户"),
            ("import", "导入用户")
        }));

        // 角色管理权限
        permissions.AddRange(CreatePermissionGroup("role", "角色管理", "RoleManagement", new[]
        {
            ("view", "查看角色"),
            ("create", "创建角色"),
            ("update", "更新角色"),
            ("delete", "删除角色"),
            ("assign", "分配角色")
        }));

        // 权限管理权限
        permissions.AddRange(CreatePermissionGroup("permission", "权限管理", "PermissionManagement", new[]
        {
            ("view", "查看权限"),
            ("assign", "分配权限")
        }));

        // AI智能体管理权限
        permissions.AddRange(CreatePermissionGroup("agent", "智能体管理", "AgentManagement", new[]
        {
            ("view", "查看智能体"),
            ("create", "创建智能体"),
            ("update", "更新智能体"),
            ("delete", "删除智能体"),
            ("publish", "发布智能体"),
            ("unpublish", "下架智能体"),
            ("test", "测试智能体"),
            ("export", "导出智能体"),
            ("import", "导入智能体")
        }));

        // 对话管理权限
        permissions.AddRange(CreatePermissionGroup("conversation", "对话管理", "ConversationManagement", new[]
        {
            ("view", "查看对话"),
            ("export", "导出对话"),
            ("delete", "删除对话"),
            ("monitor", "监控对话")
        }));

        // 订阅管理权限
        permissions.AddRange(CreatePermissionGroup("subscription", "订阅管理", "SubscriptionManagement", new[]
        {
            ("view", "查看订阅"),
            ("create", "创建订阅"),
            ("update", "更新订阅"),
            ("cancel", "取消订阅"),
            ("refund", "退款")
        }));

        // 订阅计划管理权限
        permissions.AddRange(CreatePermissionGroup("subscriptionplan", "订阅计划管理", "SubscriptionPlanManagement", new[]
        {
            ("view", "查看计划"),
            ("create", "创建计划"),
            ("update", "更新计划"),
            ("delete", "删除计划")
        }));

        // 支付管理权限
        permissions.AddRange(CreatePermissionGroup("payment", "支付管理", "PaymentManagement", new[]
        {
            ("view", "查看支付"),
            ("refund", "退款"),
            ("export", "导出支付记录")
        }));

        // 系统配置权限
        permissions.AddRange(CreatePermissionGroup("system", "系统管理", "SystemManagement", new[]
        {
            ("view", "查看配置"),
            ("update", "更新配置"),
            ("maintenance", "系统维护"),
            ("backup", "备份系统"),
            ("restore", "恢复系统")
        }));

        // 审计日志权限
        permissions.AddRange(CreatePermissionGroup("audit", "审计管理", "AuditManagement", new[]
        {
            ("view", "查看审计日志"),
            ("export", "导出审计日志"),
            ("delete", "删除审计日志")
        }));

        // 报表权限
        permissions.AddRange(CreatePermissionGroup("report", "报表管理", "ReportManagement", new[]
        {
            ("view", "查看报表"),
            ("create", "创建报表"),
            ("export", "导出报表")
        }));

        // 数据库管理权限
        permissions.AddRange(CreatePermissionGroup("database", "数据库管理", "DatabaseManagement", new[]
        {
            ("view", "查看数据库"),
            ("analyze", "分析数据库"),
            ("optimize", "优化数据库"),
            ("cleanup", "清理数据库"),
            ("monitor", "监控数据库"),
            ("reports", "数据库报表")
        }));

        // 缓存管理权限
        permissions.AddRange(CreatePermissionGroup("cache", "缓存管理", "CacheManagement", new[]
        {
            ("view", "查看缓存"),
            ("analyze", "分析缓存"),
            ("optimize", "优化缓存"),
            ("cleanup", "清理缓存"),
            ("warm", "预热缓存"),
            ("monitor", "监控缓存"),
            ("validate", "验证缓存"),
            ("reports", "缓存报表")
        }));

        // 性能测试权限
        permissions.AddRange(CreatePermissionGroup("performance", "性能管理", "PerformanceManagement", new[]
        {
            ("view", "查看性能"),
            ("test", "执行测试"),
            ("analyze", "分析性能"),
            ("reports", "性能报表")
        }));

        // 负载测试权限
        permissions.AddRange(CreatePermissionGroup("loadtest", "负载测试", "LoadTestManagement", new[]
        {
            ("view", "查看测试"),
            ("run", "运行测试"),
            ("manage", "管理测试"),
            ("reports", "测试报表")
        }));

        // API管理权限
        permissions.AddRange(CreatePermissionGroup("api", "API管理", "ApiManagement", new[]
        {
            ("view", "查看API"),
            ("create", "创建API密钥"),
            ("update", "更新API密钥"),
            ("delete", "删除API密钥"),
            ("monitor", "监控API")
        }));

        LogSeeding("Permission", permissions.Count);

        // 获取执行策略
        var executionStrategy = Context.Database.CreateExecutionStrategy();
        // 使用执行策略来确保操作可以重试
        await executionStrategy.ExecuteAsync(async () =>
        {
            // 开始数据库事务，确保数据一致性
            using (var transaction = await Context.Database.BeginTransactionAsync())
            {
                try
                {
                    // 添加权限数据
                    await Context.Permissions.AddRangeAsync(permissions);
                    await Context.SaveChangesAsync();  // 保存变更
                    await transaction.CommitAsync();   // 提交事务
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync();  // 如果发生异常，回滚事务
                    throw new Exception("数据种子插入失败", ex);
                }
            }
        });

        // 为角色分配权限
        await AssignPermissionsToRoles(permissions);

        LogSeedingComplete("Permission");
    }

    private async Task AssignPermissionsToRoles(List<Permission> permissions)
    {
        // 确保角色存在
        var roles = await Context.Roles.Include(r => r.Permissions).ToListAsync();

        // 为超级管理员分配所有权限
        var superAdminRole = roles.FirstOrDefault(r => r.Code == "super_admin");
        if (superAdminRole != null)
        {
            foreach (var permission in permissions)
            {
                if (!superAdminRole.Permissions.Any(p => p.Id == permission.Id)) // 防止重复分配
                {
                    superAdminRole.AssignPermission(permission.Id);
                }
            }
            await Context.SaveChangesAsync();
        }

        // 为管理员分配大部分权限（排除系统级权限）
        var adminRole = roles.FirstOrDefault(r => r.Code == "admin");
        if (adminRole != null)
        {
            var adminPermissions = permissions.Where(p =>
                !p.Code.StartsWith("system:") &&
                !p.Code.StartsWith("database:") &&
                !p.Code.StartsWith("cache:") &&
                !p.Code.StartsWith("performance:") &&
                !p.Code.StartsWith("loadtest:")).ToList();

            foreach (var permission in adminPermissions)
            {
                if (!adminRole.Permissions.Any(p => p.Id == permission.Id))
                {
                    adminRole.AssignPermission(permission.Id);
                }
            }
            await Context.SaveChangesAsync();
        }

        // 为客服人员分配基本查看权限
        var supportRole = roles.FirstOrDefault(r => r.Code == "support");
        if (supportRole != null)
        {
            var supportPermissions = permissions.Where(p =>
                p.Code == "user:view" ||
                p.Code == "conversation:view" ||
                p.Code == "subscription:view" ||
                p.Code == "payment:view").ToList();

            foreach (var permission in supportPermissions)
            {
                if (!supportRole.Permissions.Any(p => p.Id == permission.Id))
                {
                    supportRole.AssignPermission(permission.Id);
                }
            }
            await Context.SaveChangesAsync();
        }

        // 为审计员分配审计和报表权限
        var auditorRole = roles.FirstOrDefault(r => r.Code == "auditor");
        if (auditorRole != null)
        {
            var auditorPermissions = permissions.Where(p =>
                p.Code.StartsWith("audit.") ||
                p.Code.StartsWith("report:") ||
                p.Code.EndsWith(":view")).ToList();

            foreach (var permission in auditorPermissions)
            {
                if (!auditorRole.Permissions.Any(p => p.Id == permission.Id))
                {
                    auditorRole.AssignPermission(permission.Id);
                }
            }
            await Context.SaveChangesAsync();
        }
    }

    private List<Permission> CreatePermissionGroup(string resource, string categoryDisplayName, string module, (string action, string displayName)[] actions)
    {
        var sortOrder = 0;
        return actions.Select(a =>
        {
            var permission = new Permission(
                code: $"{resource}:{a.action}",
                name: $"{categoryDisplayName} - {a.displayName}",
                category: categoryDisplayName,
                description: $"允许{a.displayName}",
                parentId: null,
                isSystem: true
            );

            return permission;
        }).ToList();
    }
}
