using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using WhimLabAI.Domain.Entities.Auth;
using WhimLabAI.Shared.Constants;

namespace WhimLabAI.Infrastructure.Data.Seeding;

/// <summary>
/// 角色种子数据
/// </summary>
public class RoleSeeder : EntitySeederBase<Role>
{
    public RoleSeeder(WhimLabAIDbContext context, ILogger<RoleSeeder> logger) 
        : base(context, logger)
    {
    }

    public override async Task SeedAsync()
    {
        // 检查是否已有角色
        if (await Context.Roles.AnyAsync())
        {
            LogSeedingSkipped("Role");
            return;
        }

        var roles = new List<Role>();

        // Create SuperAdmin role
        var superAdminRole = new Role(
            "超级管理员",
            "super_admin",
            "系统超级管理员，拥有所有权限",
            isSystem: true
        );
        SetEntityId(superAdminRole, Guid.Parse("a1111111-1111-1111-1111-111111111111"));
        roles.Add(superAdminRole);

        // Create Admin role
        var adminRole = new Role(
            "管理员",
            "admin",
            "系统管理员，拥有大部分管理权限",
            isSystem: true
        );
        SetEntityId(adminRole, Guid.Parse("a222**************-2222-************"));
        roles.Add(adminRole);

        // Operator role removed - not defined in BusinessConstants

        // Create Support role
        var supportRole = new Role(
            "客服人员",
            "support",
            "客服人员，负责用户支持",
            isSystem: true
        );
        SetEntityId(supportRole, Guid.Parse("a444**************-4444-************"));
        roles.Add(supportRole);

        // Create Auditor role
        var auditorRole = new Role(
            "审计员",
            "auditor",
            "审计员，负责系统审计和合规检查",
            isSystem: true
        );
        SetEntityId(auditorRole, Guid.Parse("a555**************-5555-************"));
        roles.Add(auditorRole);

        // Create Customer role (for regular users)
        var customerRole = new Role(
            "客户",
            "customer",
            "普通客户用户",
            isSystem: true
        );
        SetEntityId(customerRole, Guid.Parse("a666**************-6666-************"));
        roles.Add(customerRole);

        LogSeeding("Role", roles.Count);
        await Context.Roles.AddRangeAsync(roles);
        await Context.SaveChangesAsync();
        LogSeedingComplete("Role");
    }
}