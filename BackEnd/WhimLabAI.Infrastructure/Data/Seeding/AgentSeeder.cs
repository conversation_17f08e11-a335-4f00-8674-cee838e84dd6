using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using WhimLabAI.Domain.Entities.Agent;
using WhimLabAI.Domain.ValueObjects;

namespace WhimLabAI.Infrastructure.Data.Seeding;

/// <summary>
/// 系统预置智能体种子数据
/// </summary>
public class AgentSeeder : EntitySeederBase<Agent>
{
    public AgentSeeder(WhimLabAIDbContext context, ILogger<AgentSeeder> logger) 
        : base(context, logger)
    {
    }

    public override async Task SeedAsync()
    {
        // 检查是否已有智能体数据
        if (await Context.Agents.AnyAsync())
        {
            LogSeedingSkipped("Agent");
            return;
        }

        // 获取必要的依赖数据
        var categories = await Context.AgentCategories.ToListAsync();
        var superAdmin = await Context.AdminUsers.FirstOrDefaultAsync(u => u.IsSuperAdmin);
        
        if (!categories.Any() || superAdmin == null)
        {
            Logger.LogWarning("缺少必要的依赖数据（分类或超级管理员），跳过智能体种子数据");
            return;
        }

        var agents = new List<Agent>();

        // 1. 通用对话助手
        var generalAgent = CreateGeneralChatAgent(superAdmin.Id, categories);
        if (generalAgent != null) agents.Add(generalAgent);

        // 2. 代码助手
        var codeAgent = CreateCodeAssistantAgent(superAdmin.Id, categories);
        if (codeAgent != null) agents.Add(codeAgent);

        // 3. 写作助手
        var writingAgent = CreateWritingAssistantAgent(superAdmin.Id, categories);
        if (writingAgent != null) agents.Add(writingAgent);

        // 4. 翻译专家
        var translationAgent = CreateTranslationAgent(superAdmin.Id, categories);
        if (translationAgent != null) agents.Add(translationAgent);

        LogSeeding("Agent", agents.Count);
        await Context.Agents.AddRangeAsync(agents);
        await Context.SaveChangesAsync();
        
        // Set initial versions and configure agents after saving to avoid circular dependency
        await ConfigureAndPublishAgents(agents, categories);
        
        LogSeedingComplete("Agent");
    }

    private Agent? CreateGeneralChatAgent(Guid creatorId, List<AgentCategory> categories)
    {
        var category = categories.FirstOrDefault(c => c.Name == "others");
        if (category == null) return null;

        var agent = new Agent(
            name: "通用对话助手",
            creatorId: creatorId,
            uniqueKey: "general-chat",
            description: "友好的AI对话伙伴，可以聊天、回答问题、提供建议",
            categoryId: category.Id
        );
        
        SetEntityId(agent, Guid.Parse("a1111111-1111-1111-1111-111111111111"));
        SetEntityTimestamps(agent, DateTime.UtcNow);
        
        agent.Update(
            detailedIntro: "我是您的AI对话伙伴，可以：\n" +
                          "• 回答各种问题\n" +
                          "• 进行有趣的对话\n" +
                          "• 提供生活建议\n" +
                          "• 陪伴聊天解闷",
            icon: "/images/agents/general-chat.png"
        );
        
        agent.AddTag("通用");
        agent.AddTag("对话");
        agent.AddTag("聊天");
        
        // Don't configure version or publish yet - will be done after initial save
        
        return agent;
    }

    private Agent? CreateCodeAssistantAgent(Guid creatorId, List<AgentCategory> categories)
    {
        var category = categories.FirstOrDefault(c => c.Name == "programming-development");
        if (category == null) return null;

        var agent = new Agent(
            name: "代码助手",
            creatorId: creatorId,
            uniqueKey: "code-assistant",
            description: "专业的编程助手，支持多种编程语言",
            categoryId: category.Id
        );
        
        SetEntityId(agent, Guid.Parse("a222**************-2222-************"));
        SetEntityTimestamps(agent, DateTime.UtcNow);
        
        agent.Update(
            detailedIntro: "我是您的编程伙伴，精通多种编程语言，可以帮助您：\n" +
                          "• 编写高质量代码\n" +
                          "• 调试和优化程序\n" +
                          "• 解答技术问题\n" +
                          "• 设计系统架构\n" +
                          "• 代码审查和重构\n" +
                          "支持语言：Python, JavaScript, Java, C#, Go, Rust等",
            icon: "/images/agents/code-assistant.png"
        );
        
        agent.AddTag("编程");
        agent.AddTag("代码");
        agent.AddTag("开发");
        agent.AddTag("技术");
        
        // Version configuration and publishing will be done after save
        
        return agent;
    }

    private Agent? CreateWritingAssistantAgent(Guid creatorId, List<AgentCategory> categories)
    {
        var category = categories.FirstOrDefault(c => c.Name == "creative-writing");
        if (category == null) return null;

        var agent = new Agent(
            name: "写作助手",
            creatorId: creatorId,
            uniqueKey: "writing-assistant",
            description: "专业的中文写作助手，助您妙笔生花",
            categoryId: category.Id
        );
        
        SetEntityId(agent, Guid.Parse("a333**************-3333-************"));
        SetEntityTimestamps(agent, DateTime.UtcNow);
        
        agent.Update(
            detailedIntro: "我是专业的写作助手，可以帮助您：\n" +
                          "• 撰写各类文章（新闻稿、软文、评论等）\n" +
                          "• 创作营销文案\n" +
                          "• 优化文章结构和语言\n" +
                          "• 润色和校对文本\n" +
                          "• 提供写作建议和灵感",
            icon: "/images/agents/writing-assistant.png"
        );
        
        agent.AddTag("写作");
        agent.AddTag("文案");
        agent.AddTag("创作");
        agent.AddTag("中文");
        
        // Version configuration and publishing will be done after save
        
        return agent;
    }

    private Agent? CreateTranslationAgent(Guid creatorId, List<AgentCategory> categories)
    {
        var category = categories.FirstOrDefault(c => c.Name == "office-assistant");
        if (category == null) return null;

        var agent = new Agent(
            name: "翻译专家",
            creatorId: creatorId,
            uniqueKey: "translation-expert",
            description: "精通中英文互译，让语言不再是障碍",
            categoryId: category.Id
        );
        
        SetEntityId(agent, Guid.Parse("a444**************-4444-************"));
        SetEntityTimestamps(agent, DateTime.UtcNow);
        
        agent.Update(
            detailedIntro: "我是专业的翻译助手，提供高质量的翻译服务：\n" +
                          "• 中英文互译\n" +
                          "• 技术文档翻译\n" +
                          "• 商务文件翻译\n" +
                          "• 日常对话翻译\n" +
                          "• 专业术语解释\n" +
                          "确保译文准确、地道、符合语境",
            icon: "/images/agents/translation-expert.png"
        );
        
        agent.AddTag("翻译");
        agent.AddTag("中英文");
        agent.AddTag("语言");
        
        // Version configuration and publishing will be done after save
        
        return agent;
    }
    
    private async Task ConfigureAndPublishAgents(List<Agent> agents, List<AgentCategory> categories)
    {
        foreach (var agent in agents)
        {
            // Set initial version
            agent.SetInitialVersion();
            
            // Configure version based on agent type
            var version = agent.Versions.First();
            
            switch (agent.UniqueKey)
            {
                case "general-chat":
                    var modelConfig1 = ModelConfiguration.Create("OpenAI", "gpt-3.5-turbo", 0.7, 2048);
                    version.UpdateConfiguration(
                        modelConfig1,
                        systemPrompt: "你是一个友好、知识渊博的AI助手。请用轻松、亲切的语气与用户对话，" +
                                     "提供准确有用的信息，必要时给出建议。保持积极乐观的态度。",
                        userPrompt: "请根据用户的问题或话题，给出合适的回应。"
                    );
                    break;
                    
                case "code-assistant":
                    var modelConfig2 = ModelConfiguration.Create("OpenAI", "gpt-4", 0.2, 4096);
                    version.UpdateConfiguration(
                        modelConfig2,
                        systemPrompt: "你是一位经验丰富的高级程序员和架构师。请提供清晰、高效、可维护的代码解决方案。" +
                                     "遵循最佳实践和设计模式。解释代码时要详细但不冗长。",
                        userPrompt: "请根据用户的编程需求提供帮助。如果是代码问题，请给出完整的示例代码。"
                    );
                    break;
                    
                case "writing-assistant":
                    var modelConfig3 = ModelConfiguration.Create("OpenAI", "gpt-4", 0.8, 3000);
                    version.UpdateConfiguration(
                        modelConfig3,
                        systemPrompt: "你是一位专业的中文写作专家，精通各种文体和写作技巧。" +
                                     "请根据用户需求创作高质量的中文内容，注意语言的准确性、流畅性和感染力。",
                        userPrompt: "请根据用户的写作需求，创作或优化相应的文本内容。"
                    );
                    break;
                    
                case "translation-expert":
                    var modelConfig4 = ModelConfiguration.Create("OpenAI", "gpt-4", 0.3, 3000);
                    version.UpdateConfiguration(
                        modelConfig4,
                        systemPrompt: "你是一位专业的中英文翻译专家。请提供准确、地道、符合语境的翻译。" +
                                     "保持原文的语气和风格，必要时提供多个翻译选项或解释文化差异。",
                        userPrompt: "请翻译用户提供的文本，如果没有明确指定，根据内容自动判断翻译方向。"
                    );
                    break;
            }
            
            // Publish agent
            agent.Publish();
        }
        
        await Context.SaveChangesAsync();
    }
}