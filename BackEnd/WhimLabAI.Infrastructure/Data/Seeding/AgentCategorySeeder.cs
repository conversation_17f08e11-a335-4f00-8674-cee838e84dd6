using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using WhimLabAI.Domain.Entities.Agent;

namespace WhimLabAI.Infrastructure.Data.Seeding;

/// <summary>
/// 智能体分类种子数据
/// </summary>
public class AgentCategorySeeder : EntitySeederBase<AgentCategory>
{
    public AgentCategorySeeder(WhimLabAIDbContext context, ILogger<AgentCategorySeeder> logger) 
        : base(context, logger)
    {
    }

    public override async Task SeedAsync()
    {
        // 检查是否已有分类数据
        if (await Context.AgentCategories.AnyAsync())
        {
            LogSeedingSkipped("AgentCategory");
            return;
        }

        var categories = new List<AgentCategory>();

        // Office Assistant
        var officeCategory = new AgentCategory(
            name: "office-assistant",
            displayName: "办公助手",
            description: "协助日常办公任务，提高工作效率",
            icon: "mdi-briefcase"
        );
        SetEntityId(officeCategory, Guid.Parse("f1111111-1111-1111-1111-111111111111"));
        SetEntityTimestamps(officeCategory, DateTime.UtcNow);
        officeCategory.SetSortOrder(1);
        categories.Add(officeCategory);

        // Creative Writing
        var writingCategory = new AgentCategory(
            name: "creative-writing",
            displayName: "创意写作",
            description: "文案创作、内容生成和创意表达",
            icon: "mdi-pen"
        );
        SetEntityId(writingCategory, Guid.Parse("f222**************-2222-************"));
        SetEntityTimestamps(writingCategory, DateTime.UtcNow);
        writingCategory.SetSortOrder(2);
        categories.Add(writingCategory);

        // Programming Development
        var codingCategory = new AgentCategory(
            name: "programming-development",
            displayName: "编程开发",
            description: "代码生成、调试和技术方案支持",
            icon: "mdi-code-braces"
        );
        SetEntityId(codingCategory, Guid.Parse("f333**************-3333-************"));
        SetEntityTimestamps(codingCategory, DateTime.UtcNow);
        codingCategory.SetSortOrder(3);
        categories.Add(codingCategory);

        // Data Analysis
        var dataCategory = new AgentCategory(
            name: "data-analysis",
            displayName: "数据分析",
            description: "数据处理、分析和可视化",
            icon: "mdi-chart-line"
        );
        SetEntityId(dataCategory, Guid.Parse("f444**************-4444-************"));
        SetEntityTimestamps(dataCategory, DateTime.UtcNow);
        dataCategory.SetSortOrder(4);
        categories.Add(dataCategory);

        // Education Learning
        var educationCategory = new AgentCategory(
            name: "education-learning",
            displayName: "教育学习",
            description: "学习辅导、知识问答和教学支持",
            icon: "mdi-school"
        );
        SetEntityId(educationCategory, Guid.Parse("f555**************-5555-************"));
        SetEntityTimestamps(educationCategory, DateTime.UtcNow);
        educationCategory.SetSortOrder(5);
        categories.Add(educationCategory);

        // Life Entertainment
        var entertainmentCategory = new AgentCategory(
            name: "life-entertainment",
            displayName: "生活娱乐",
            description: "日常生活助手和娱乐互动",
            icon: "mdi-gamepad-variant"
        );
        SetEntityId(entertainmentCategory, Guid.Parse("f666**************-6666-************"));
        SetEntityTimestamps(entertainmentCategory, DateTime.UtcNow);
        entertainmentCategory.SetSortOrder(6);
        categories.Add(entertainmentCategory);

        // Professional Consulting
        var consultingCategory = new AgentCategory(
            name: "professional-consulting",
            displayName: "专业咨询",
            description: "专业领域咨询和建议服务",
            icon: "mdi-account-tie"
        );
        SetEntityId(consultingCategory, Guid.Parse("f777**************-7777-************"));
        SetEntityTimestamps(consultingCategory, DateTime.UtcNow);
        consultingCategory.SetSortOrder(7);
        categories.Add(consultingCategory);

        // Others
        var othersCategory = new AgentCategory(
            name: "others",
            displayName: "其他",
            description: "其他类型的智能助手",
            icon: "mdi-dots-horizontal"
        );
        SetEntityId(othersCategory, Guid.Parse("f999**************-9999-************"));
        SetEntityTimestamps(othersCategory, DateTime.UtcNow);
        othersCategory.SetSortOrder(99);
        categories.Add(othersCategory);

        LogSeeding("AgentCategory", categories.Count);
        await Context.AgentCategories.AddRangeAsync(categories);
        await Context.SaveChangesAsync();
        LogSeedingComplete("AgentCategory");
    }
}