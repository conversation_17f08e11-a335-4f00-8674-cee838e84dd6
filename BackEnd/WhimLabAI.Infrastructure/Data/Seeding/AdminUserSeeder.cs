using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using WhimLabAI.Domain.Entities.User;
using WhimLabAI.Shared.Constants;

namespace WhimLabAI.Infrastructure.Data.Seeding;

/// <summary>
/// 管理员用户种子数据
/// </summary>
public class AdminUserSeeder : EntitySeederBase<AdminUser>
{
    private readonly IConfiguration _configuration;
    
    public AdminUserSeeder(WhimLabAIDbContext context, ILogger<AdminUserSeeder> logger, IConfiguration configuration) 
        : base(context, logger)
    {
        _configuration = configuration;
    }

    public override async Task SeedAsync()
    {
        // 检查是否已有管理员用户
        if (await Context.AdminUsers.AnyAsync())
        {
            LogSeedingSkipped("AdminUser");
            return;
        }

        // 获取角色
        var superAdminRole = await Context.Roles.FirstOrDefaultAsync(r => r.Code == "super_admin");
        var adminRole = await Context.Roles.FirstOrDefaultAsync(r => r.Code == "admin");

        if (superAdminRole == null || adminRole == null)
        {
            throw new InvalidOperationException("Roles must be seeded before admin users");
        }

        var adminUsers = new List<AdminUser>();

        // Create super admin user
        var superAdmin = new AdminUser(
            username: _configuration["Seeding:AdminUsers:SuperAdmin:Username"] ?? "superadmin",
            email: _configuration["Seeding:AdminUsers:SuperAdmin:Email"] ?? "<EMAIL>",
            password: _configuration["Seeding:AdminUsers:SuperAdmin:Password"] ?? "SuperAdmin@123",
            phone: _configuration["Seeding:AdminUsers:SuperAdmin:Phone"] ?? "10000000001",
            isSuperAdmin: true
        );
        SetEntityId(superAdmin, Guid.Parse("c1111111-1111-1111-1111-111111111111"));
        superAdmin.UpdateProfile(nickname: "系统超级管理员");
        superAdmin.AssignRole(superAdminRole.Id);
        superAdmin.RecordSuccessfulLogin("127.0.0.1"); // Mark as logged in for initial setup
        adminUsers.Add(superAdmin);

        // Create regular admin user
        var regularAdmin = new AdminUser(
            username: _configuration["Seeding:AdminUsers:Admin:Username"] ?? "admin",
            email: _configuration["Seeding:AdminUsers:Admin:Email"] ?? "<EMAIL>",
            password: _configuration["Seeding:AdminUsers:Admin:Password"] ?? "Admin@123",
            phone: _configuration["Seeding:AdminUsers:Admin:Phone"] ?? "10000000002",
            isSuperAdmin: false
        );
        SetEntityId(regularAdmin, Guid.Parse("c222**************-2222-************"));
        regularAdmin.UpdateProfile(nickname: "系统管理员");
        regularAdmin.AssignRole(adminRole.Id);
        adminUsers.Add(regularAdmin);

        // Create operator user
        var operatorRole = await Context.Roles.FirstOrDefaultAsync(r => r.Code == "operator");
        if (operatorRole != null)
        {
            var operatorUser = new AdminUser(
                username: _configuration["Seeding:AdminUsers:Operator:Username"] ?? "operator",
                email: _configuration["Seeding:AdminUsers:Operator:Email"] ?? "<EMAIL>",
                password: _configuration["Seeding:AdminUsers:Operator:Password"] ?? "Operator@123",
                phone: _configuration["Seeding:AdminUsers:Operator:Phone"] ?? "10000000003",
                isSuperAdmin: false
            );
            SetEntityId(operatorUser, Guid.Parse("c333**************-3333-************"));
            operatorUser.UpdateProfile(nickname: "运营人员");
            operatorUser.AssignRole(operatorRole.Id);
            adminUsers.Add(operatorUser);
        }

        // Create support user
        var supportRole = await Context.Roles.FirstOrDefaultAsync(r => r.Code == "support");
        if (supportRole != null)
        {
            var supportUser = new AdminUser(
                username: _configuration["Seeding:AdminUsers:Support:Username"] ?? "support",
                email: _configuration["Seeding:AdminUsers:Support:Email"] ?? "<EMAIL>",
                password: _configuration["Seeding:AdminUsers:Support:Password"] ?? "Support@123",
                phone: _configuration["Seeding:AdminUsers:Support:Phone"] ?? "10000000004",
                isSuperAdmin: false
            );
            SetEntityId(supportUser, Guid.Parse("c444**************-4444-************"));
            supportUser.UpdateProfile(nickname: "客服人员");
            supportUser.AssignRole(supportRole.Id);
            adminUsers.Add(supportUser);
        }

        // Create auditor user
        var auditorRole = await Context.Roles.FirstOrDefaultAsync(r => r.Code == "auditor");
        if (auditorRole != null)
        {
            var auditorUser = new AdminUser(
                username: _configuration["Seeding:AdminUsers:Auditor:Username"] ?? "auditor",
                email: _configuration["Seeding:AdminUsers:Auditor:Email"] ?? "<EMAIL>",
                password: _configuration["Seeding:AdminUsers:Auditor:Password"] ?? "Auditor@123",
                phone: _configuration["Seeding:AdminUsers:Auditor:Phone"] ?? "10000000005",
                isSuperAdmin: false
            );
            SetEntityId(auditorUser, Guid.Parse("c555**************-5555-************"));
            auditorUser.UpdateProfile(nickname: "审计人员");
            auditorUser.AssignRole(auditorRole.Id);
            adminUsers.Add(auditorUser);
        }

        LogSeeding("AdminUser", adminUsers.Count);
        await Context.AdminUsers.AddRangeAsync(adminUsers);
        await Context.SaveChangesAsync();
        LogSeedingComplete("AdminUser");
    }
}