using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using System;

namespace WhimLabAI.Infrastructure.Data;

/// <summary>
/// Custom implementation of IDbContextFactory to resolve lifetime issues
/// </summary>
public class WhimLabAIDbContextFactory : IDbContextFactory<WhimLabAIDbContext>
{
    private readonly IServiceProvider _serviceProvider;
    private readonly DbContextOptions<WhimLabAIDbContext> _options;

    public WhimLabAIDbContextFactory(IServiceProvider serviceProvider, DbContextOptions<WhimLabAIDbContext> options)
    {
        _serviceProvider = serviceProvider;
        _options = options;
    }

    public WhimLabAIDbContext CreateDbContext()
    {
        // Try to get encryption service from the service provider
        var encryptionService = _serviceProvider.GetService<Security.IDataEncryptionService>();
        
        if (encryptionService != null)
        {
            return new WhimLabAIDbContext(_options, encryptionService);
        }
        
        return new WhimLabAIDbContext(_options);
    }
}