using Microsoft.Extensions.Logging;
using System.Text.Json;
using WhimLabAI.Abstractions.Infrastructure;

namespace WhimLabAI.Infrastructure.Messaging;

/// <summary>
/// 消息队列服务实现（暂时使用内存实现）
/// </summary>
public class MessageQueueService : IMessageQueueService
{
    private readonly ILogger<MessageQueueService> _logger;
    private readonly Dictionary<string, List<Func<string, Task>>> _handlers = new();

    public MessageQueueService(ILogger<MessageQueueService> logger)
    {
        _logger = logger;
    }

    public async Task PublishAsync<T>(string topic, T message, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            var messageJson = JsonSerializer.Serialize(message);
            _logger.LogInformation("Publishing message to topic {Topic}: {Message}", topic, messageJson);

            if (_handlers.TryGetValue(topic, out var handlers))
            {
                foreach (var handler in handlers)
                {
                    await handler(messageJson);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing message to topic {Topic}", topic);
            throw;
        }
    }

    public Task SubscribeAsync<T>(string topic, Func<T, Task> handler, CancellationToken cancellationToken = default) where T : class
    {
        if (!_handlers.ContainsKey(topic))
        {
            _handlers[topic] = new List<Func<string, Task>>();
        }

        _handlers[topic].Add(async (messageJson) =>
        {
            try
            {
                var message = JsonSerializer.Deserialize<T>(messageJson);
                if (message != null)
                {
                    await handler(message);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling message from topic {Topic}", topic);
            }
        });

        _logger.LogInformation("Subscribed to topic {Topic}", topic);
        return Task.CompletedTask;
    }
}