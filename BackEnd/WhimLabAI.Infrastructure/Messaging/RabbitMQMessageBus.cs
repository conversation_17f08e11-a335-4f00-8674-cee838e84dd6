using MassTransit;
using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Infrastructure;

namespace WhimLabAI.Infrastructure.Messaging;

public class RabbitMQMessageBus : IMessageQueueService
{
    private readonly IBus _bus;
    private readonly ILogger<RabbitMQMessageBus> _logger;

    public RabbitMQMessageBus(IBus bus, ILogger<RabbitMQMessageBus> logger)
    {
        _bus = bus;
        _logger = logger;
    }

    public async Task PublishAsync<T>(string topic, T message, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            _logger.LogInformation("Publishing message of type {MessageType} to RabbitMQ", typeof(T).Name);
            
            await _bus.Publish(message, cancellationToken);
            
            _logger.LogInformation("Successfully published message of type {MessageType}", typeof(T).Name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing message of type {MessageType} to RabbitMQ", typeof(T).Name);
            throw;
        }
    }

    public Task SubscribeAsync<T>(string topic, Func<T, Task> handler, CancellationToken cancellationToken = default) where T : class
    {
        // With MassTransit, subscription is handled through consumer registration in DI
        // This method is kept for interface compatibility but actual subscription
        // is done through MassTransit consumer configuration
        _logger.LogWarning("SubscribeAsync called but subscription should be configured through MassTransit consumers");
        return Task.CompletedTask;
    }
}