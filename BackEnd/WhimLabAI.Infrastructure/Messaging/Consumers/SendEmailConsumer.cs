using MassTransit;
using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Shared.Dtos.Notification;

namespace WhimLabAI.Infrastructure.Messaging.Consumers;

public class SendEmailConsumer : BaseEventConsumer<SendEmailDto>
{
    private readonly IEmailService _emailService;

    public SendEmailConsumer(
        ILogger<SendEmailConsumer> logger,
        IEmailService emailService) : base(logger)
    {
        _emailService = emailService;
    }

    protected override async Task HandleEvent(SendEmailDto @event, CancellationToken cancellationToken)
    {
        // Handle template-based emails
        if (!string.IsNullOrEmpty(@event.TemplateId) && @event.Recipients.Count > 0)
        {
            foreach (var recipient in @event.Recipients)
            {
                await _emailService.SendTemplateEmailAsync(
                    recipient, 
                    @event.TemplateId, 
                    @event.TemplateData ?? new Dictionary<string, object>(), 
                    cancellationToken);
            }
        }
        // Handle emails with attachments
        else if (@event.Attachments?.Count > 0 && @event.Recipients.Count > 0)
        {
            foreach (var recipient in @event.Recipients)
            {
                await _emailService.SendEmailWithAttachmentsAsync(
                    recipient,
                    @event.Subject,
                    @event.Body,
                    @event.Attachments,
                    @event.IsHtml,
                    cancellationToken);
            }
        }
        // Handle regular emails
        else if (@event.Recipients.Count > 0)
        {
            await _emailService.SendEmailAsync(
                @event.Recipients,
                @event.Subject,
                @event.Body,
                @event.IsHtml,
                cancellationToken);
        }
    }
}
