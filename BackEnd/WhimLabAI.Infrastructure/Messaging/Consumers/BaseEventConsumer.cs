using MassTransit;
using Microsoft.Extensions.Logging;

namespace WhimLabAI.Infrastructure.Messaging.Consumers;

public abstract class BaseEventConsumer<TEvent> : IConsumer<TEvent> where TEvent : class
{
    protected readonly ILogger<BaseEventConsumer<TEvent>> Logger;

    protected BaseEventConsumer(ILogger<BaseEventConsumer<TEvent>> logger)
    {
        Logger = logger;
    }

    public async Task Consume(ConsumeContext<TEvent> context)
    {
        var eventName = typeof(TEvent).Name;
        Logger.LogInformation("Consuming {EventName} with CorrelationId: {CorrelationId}", 
            eventName, context.CorrelationId);

        try
        {
            await HandleEvent(context.Message, context.CancellationToken);
            
            Logger.LogInformation("Successfully consumed {EventName}", eventName);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error consuming {EventName}", eventName);
            throw;
        }
    }

    protected abstract Task HandleEvent(TEvent @event, CancellationToken cancellationToken);
}