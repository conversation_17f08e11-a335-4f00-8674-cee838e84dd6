using System.Security.Cryptography;
using System.Text;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Infrastructure.OAuth.Configuration;
using WhimLabAI.Infrastructure.OAuth.Providers;
using WhimLabAI.Shared.Results;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Infrastructure.OAuth;

public class OAuthService : IOAuthService
{
    private readonly OAuthOptions _oauthOptions;
    private readonly ICacheService _cacheService;
    private readonly ICustomerUserRepository _customerUserRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IJwtTokenService _jwtTokenService;
    private readonly ILogger<OAuthService> _logger;
    private readonly Dictionary<string, IOAuthProvider> _providers;
    
    public OAuthService(
        IOptions<OAuthOptions> oauthOptions,
        ICacheService cacheService,
        ICustomerUserRepository customerUserRepository,
        IUnitOfWork unitOfWork,
        IJwtTokenService jwtTokenService,
        ILogger<OAuthService> logger)
    {
        _oauthOptions = oauthOptions.Value;
        _cacheService = cacheService;
        _customerUserRepository = customerUserRepository;
        _unitOfWork = unitOfWork;
        _jwtTokenService = jwtTokenService;
        _logger = logger;
        
        // Initialize OAuth providers
        _providers = new Dictionary<string, IOAuthProvider>();
        InitializeProviders();
    }
    
    private void InitializeProviders()
    {
        // Initialize WeChat provider
        var wechatOptions = _oauthOptions.GetProvider("WeChat");
        if (wechatOptions?.Enabled == true)
        {
            _providers["WeChat"] = new WeChatOAuthProvider(wechatOptions, _logger);
        }
        
        // Initialize GitHub provider
        var githubOptions = _oauthOptions.GetProvider("GitHub");
        if (githubOptions?.Enabled == true)
        {
            _providers["GitHub"] = new GitHubOAuthProvider(githubOptions, _logger);
        }
        
        // Initialize Google provider
        var googleOptions = _oauthOptions.GetProvider("Google");
        if (googleOptions?.Enabled == true)
        {
            _providers["Google"] = new GoogleOAuthProvider(googleOptions, _logger);
        }
        
        // Initialize Microsoft provider
        var microsoftOptions = _oauthOptions.GetProvider("Microsoft");
        if (microsoftOptions?.Enabled == true)
        {
            _providers["Microsoft"] = new MicrosoftOAuthProvider(microsoftOptions, _logger);
        }
    }
    
    public async Task<Result<string>> GetAuthorizationUrlAsync(string provider, string state, string? redirectUri = null)
    {
        try
        {
            if (!_providers.TryGetValue(provider, out var oauthProvider))
            {
                return Result<string>.Failure($"OAuth provider '{provider}' is not configured or enabled");
            }
            
            // Generate and store state token for CSRF protection
            var stateToken = GenerateStateToken();
            var cacheKey = $"oauth:state:{stateToken}";
            var stateData = new OAuthStateData
            {
                Provider = provider,
                State = state,
                RedirectUri = redirectUri,
                CreatedAt = DateTime.UtcNow
            };
            
            await _cacheService.SetAsync(cacheKey, stateData, 
                TimeSpan.FromMinutes(_oauthOptions.StateTokenExpirationMinutes));
            
            // Get authorization URL from provider
            var authUrl = oauthProvider.GetAuthorizationUrl(stateToken, redirectUri ?? GetDefaultRedirectUri(provider));
            
            _logger.LogInformation("Generated OAuth authorization URL for provider {Provider}", provider);
            return Result<string>.Success(authUrl);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate authorization URL for provider {Provider}", provider);
            return Result<string>.Failure("生成授权链接失败");
        }
    }
    
    public async Task<Result<OAuthUserInfo>> ExchangeCodeForTokenAsync(string provider, string code, string state, string? redirectUri = null)
    {
        try
        {
            if (!_providers.TryGetValue(provider, out var oauthProvider))
            {
                return Result<OAuthUserInfo>.Failure($"OAuth provider '{provider}' is not configured or enabled");
            }
            
            // Validate state token
            var stateValidation = await ValidateStateAsync(state);
            if (!stateValidation.IsSuccess || !stateValidation.Value)
            {
                return Result<OAuthUserInfo>.Failure("Invalid or expired state token");
            }
            
            // Get state data
            var cacheKey = $"oauth:state:{state}";
            var stateData = await _cacheService.GetAsync<OAuthStateData>(cacheKey);
            if (stateData == null || stateData.Provider != provider)
            {
                return Result<OAuthUserInfo>.Failure("State token mismatch");
            }
            
            // Exchange code for token
            var tokenResult = await oauthProvider.ExchangeCodeForTokenAsync(code, redirectUri ?? stateData.RedirectUri ?? GetDefaultRedirectUri(provider));
            if (!tokenResult.IsSuccess)
            {
                return Result<OAuthUserInfo>.Failure(tokenResult.Error);
            }
            
            // Get user info
            var userInfoResult = await oauthProvider.GetUserInfoAsync(tokenResult.Value.AccessToken);
            if (!userInfoResult.IsSuccess)
            {
                return Result<OAuthUserInfo>.Failure(userInfoResult.Error);
            }
            
            var userInfo = userInfoResult.Value;
            userInfo.AccessToken = tokenResult.Value.AccessToken;
            userInfo.RefreshToken = tokenResult.Value.RefreshToken;
            userInfo.ExpiresAt = tokenResult.Value.ExpiresIn > 0 
                ? DateTime.UtcNow.AddSeconds(tokenResult.Value.ExpiresIn) 
                : null;
            
            // Clean up state token
            await _cacheService.RemoveAsync(cacheKey);
            
            _logger.LogInformation("Successfully exchanged OAuth code for user info, Provider: {Provider}, ProviderId: {ProviderId}", 
                provider, userInfo.ProviderId);
            
            return Result<OAuthUserInfo>.Success(userInfo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to exchange OAuth code for provider {Provider}", provider);
            return Result<OAuthUserInfo>.Failure("获取用户信息失败");
        }
    }
    
    public async Task<Result<OAuthLoginResult>> LoginOrCreateUserAsync(string provider, OAuthUserInfo userInfo)
    {
        try
        {
            // Find existing user by OAuth binding
            var existingUser = await _customerUserRepository.GetByOAuthBindingAsync(provider, userInfo.ProviderId);
            
            if (existingUser != null)
            {
                // Update OAuth binding tokens
                var binding = existingUser.OAuthBindings.FirstOrDefault(b => b.Provider == provider && b.ProviderUserId == userInfo.ProviderId);
                if (binding != null)
                {
                    binding.UpdateTokens(userInfo.AccessToken, userInfo.RefreshToken, userInfo.ExpiresAt);
                    binding.UpdateProfile(userInfo.Name, userInfo.Email, userInfo.Avatar);
                    binding.RecordUsage();
                }
                
                _customerUserRepository.Update(existingUser);
                await _unitOfWork.SaveChangesAsync();
                
                var loginResult = new OAuthLoginResult
                {
                    UserId = existingUser.Id,
                    Username = existingUser.Username,
                    IsNewUser = false,
                    AccessToken = GenerateJwtToken(existingUser),
                    RefreshToken = GenerateRefreshToken()
                };
                
                // Set refresh token
                existingUser.SetRefreshToken(loginResult.RefreshToken, 30);
                _customerUserRepository.Update(existingUser);
                await _unitOfWork.SaveChangesAsync();
                
                _logger.LogInformation("OAuth user logged in successfully, Provider: {Provider}, UserId: {UserId}", 
                    provider, existingUser.Id);
                
                return Result<OAuthLoginResult>.Success(loginResult);
            }
            
            // Check if email is already taken
            if (!string.IsNullOrEmpty(userInfo.Email))
            {
                var userByEmail = await _customerUserRepository.GetByEmailAsync(userInfo.Email);
                if (userByEmail != null)
                {
                    return Result<OAuthLoginResult>.Failure($"邮箱 {userInfo.Email} 已被注册，请使用该账号登录后绑定{provider}账号");
                }
            }
            
            // Create new user
            var username = GenerateUniqueUsername(userInfo);
            var newUser = new Domain.Entities.User.CustomerUser(
                username: username,
                password: GenerateRandomPassword(), // User won't use this password
                email: userInfo.Email,
                phone: null,
                registerIp: null
            );
            
            // Update profile from OAuth info
            newUser.UpdateProfile(
                nickname: userInfo.Name,
                gender: null,
                birthday: null,
                region: null,
                industry: null,
                position: null,
                bio: null
            );
            
            if (!string.IsNullOrEmpty(userInfo.Avatar))
            {
                newUser.UpdateAvatar(userInfo.Avatar);
            }
            
            // Bind OAuth account
            newUser.BindOAuthAccount(provider, userInfo.ProviderId, userInfo.Name);
            
            // Mark email as verified if provided by OAuth
            if (!string.IsNullOrEmpty(userInfo.Email))
            {
                newUser.VerifyEmail();
            }
            
            await _customerUserRepository.AddAsync(newUser);
            await _unitOfWork.SaveChangesAsync();
            
            // Update OAuth binding with tokens
            var newBinding = newUser.OAuthBindings.First(b => b.Provider == provider);
            newBinding.UpdateTokens(userInfo.AccessToken, userInfo.RefreshToken, userInfo.ExpiresAt);
            
            var result = new OAuthLoginResult
            {
                UserId = newUser.Id,
                Username = newUser.Username,
                IsNewUser = true,
                AccessToken = GenerateJwtToken(newUser),
                RefreshToken = GenerateRefreshToken()
            };
            
            // Set refresh token
            newUser.SetRefreshToken(result.RefreshToken, 30);
            _customerUserRepository.Update(newUser);
            await _unitOfWork.SaveChangesAsync();
            
            _logger.LogInformation("New OAuth user created and logged in, Provider: {Provider}, UserId: {UserId}", 
                provider, newUser.Id);
            
            return Result<OAuthLoginResult>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to login or create OAuth user, Provider: {Provider}", provider);
            return Result<OAuthLoginResult>.Failure("登录失败");
        }
    }
    
    public async Task<Result<OAuthTokenInfo>> RefreshTokenAsync(string provider, string refreshToken)
    {
        try
        {
            if (!_providers.TryGetValue(provider, out var oauthProvider))
            {
                return Result<OAuthTokenInfo>.Failure($"OAuth provider '{provider}' is not configured or enabled");
            }
            
            var result = await oauthProvider.RefreshTokenAsync(refreshToken);
            if (!result.IsSuccess)
            {
                return Result<OAuthTokenInfo>.Failure(result.Error);
            }
            
            _logger.LogInformation("OAuth token refreshed successfully for provider {Provider}", provider);
            return Result<OAuthTokenInfo>.Success(result.Value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to refresh OAuth token for provider {Provider}", provider);
            return Result<OAuthTokenInfo>.Failure("刷新令牌失败");
        }
    }
    
    public async Task<Result<bool>> ValidateStateAsync(string state)
    {
        try
        {
            var cacheKey = $"oauth:state:{state}";
            var stateData = await _cacheService.GetAsync<OAuthStateData>(cacheKey);
            
            if (stateData == null)
            {
                return Result<bool>.Success(false);
            }
            
            // Check if state token is expired
            var expirationTime = stateData.CreatedAt.AddMinutes(_oauthOptions.StateTokenExpirationMinutes);
            if (DateTime.UtcNow > expirationTime)
            {
                await _cacheService.RemoveAsync(cacheKey);
                return Result<bool>.Success(false);
            }
            
            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to validate OAuth state token");
            return Result<bool>.Failure("验证状态令牌失败");
        }
    }
    
    private string GenerateStateToken()
    {
        var bytes = new byte[32];
        using (var rng = RandomNumberGenerator.Create())
        {
            rng.GetBytes(bytes);
        }
        return Convert.ToBase64String(bytes).TrimEnd('=').Replace('+', '-').Replace('/', '_');
    }
    
    private string GenerateRandomPassword()
    {
        var bytes = new byte[32];
        using (var rng = RandomNumberGenerator.Create())
        {
            rng.GetBytes(bytes);
        }
        return Convert.ToBase64String(bytes);
    }
    
    private string GenerateRefreshToken()
    {
        return _jwtTokenService.GenerateRefreshToken();
    }
    
    private string GenerateUniqueUsername(OAuthUserInfo userInfo)
    {
        // Try to use name or email prefix as base
        var baseUsername = userInfo.Name;
        if (string.IsNullOrWhiteSpace(baseUsername) && !string.IsNullOrEmpty(userInfo.Email))
        {
            baseUsername = userInfo.Email.Split('@')[0];
        }
        if (string.IsNullOrWhiteSpace(baseUsername))
        {
            baseUsername = "user";
        }
        
        // Clean the username
        baseUsername = System.Text.RegularExpressions.Regex.Replace(baseUsername, @"[^a-zA-Z0-9_]", "").ToLower();
        if (baseUsername.Length > 20)
        {
            baseUsername = baseUsername.Substring(0, 20);
        }
        
        // Add random suffix to ensure uniqueness
        var random = new Random();
        return $"{baseUsername}_{random.Next(1000, 9999)}";
    }
    
    private string GenerateJwtToken(Domain.Entities.User.CustomerUser user)
    {
        return _jwtTokenService.GenerateAccessToken(user.Id, user.Username, UserType.Customer);
    }
    
    private string GetDefaultRedirectUri(string provider)
    {
        return $"{_oauthOptions.BaseUrl}/api/customer/auth/oauth/{provider}/callback";
    }
    
    private class OAuthStateData
    {
        public string Provider { get; set; } = string.Empty;
        public string State { get; set; } = string.Empty;
        public string? RedirectUri { get; set; }
        public DateTime CreatedAt { get; set; }
    }
}