using System.Net.Http.Headers;
using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Infrastructure.OAuth.Configuration;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Infrastructure.OAuth.Providers;

public class MicrosoftOAuthProvider : BaseOAuthProvider
{
    public MicrosoftOAuthProvider(OAuthProviderOptions options, ILogger logger) 
        : base(options, logger)
    {
    }
    
    public override string GetAuthorizationUrl(string state, string redirectUri)
    {
        var tenantId = _options.TenantId ?? "common";
        var authEndpoint = _options.AuthorizationEndpoint?.Replace("{tenant}", tenantId) ?? 
            $"https://login.microsoftonline.com/{tenantId}/oauth2/v2.0/authorize";
        
        var parameters = new Dictionary<string, string>
        {
            ["client_id"] = _options.ClientId ?? throw new InvalidOperationException("Microsoft ClientId is not configured"),
            ["response_type"] = "code",
            ["redirect_uri"] = redirectUri,
            ["response_mode"] = "query",
            ["scope"] = _options.Scope ?? "openid profile email",
            ["state"] = state
        };
        
        var queryString = BuildQueryString(parameters);
        return $"{authEndpoint}?{queryString}";
    }
    
    protected override HttpRequestMessage BuildTokenRequest(string code, string redirectUri)
    {
        var tenantId = _options.TenantId ?? "common";
        var tokenEndpoint = _options.TokenEndpoint?.Replace("{tenant}", tenantId) ?? 
            $"https://login.microsoftonline.com/{tenantId}/oauth2/v2.0/token";
        
        var parameters = new Dictionary<string, string>
        {
            ["client_id"] = _options.ClientId ?? throw new InvalidOperationException("Microsoft ClientId is not configured"),
            ["client_secret"] = _options.ClientSecret ?? throw new InvalidOperationException("Microsoft ClientSecret is not configured"),
            ["code"] = code,
            ["redirect_uri"] = redirectUri,
            ["grant_type"] = "authorization_code",
            ["scope"] = _options.Scope ?? "openid profile email"
        };
        
        var request = new HttpRequestMessage(HttpMethod.Post, tokenEndpoint);
        request.Content = new FormUrlEncodedContent(parameters);
        
        return request;
    }
    
    protected override OAuthTokenInfo? ParseTokenResponse(string response)
    {
        var tokenData = TryParseJson<MicrosoftTokenResponse>(response);
        if (tokenData == null)
        {
            return null;
        }
        
        return new OAuthTokenInfo
        {
            AccessToken = tokenData.AccessToken ?? string.Empty,
            RefreshToken = tokenData.RefreshToken ?? string.Empty,
            ExpiresIn = tokenData.ExpiresIn ?? 3600,
            TokenType = tokenData.TokenType ?? "Bearer"
        };
    }
    
    protected override HttpRequestMessage BuildUserInfoRequest(string accessToken)
    {
        var request = new HttpRequestMessage(HttpMethod.Get, _options.UserInfoEndpoint ?? "https://graph.microsoft.com/v1.0/me");
        request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
        
        return request;
    }
    
    protected override OAuthUserInfo? ParseUserInfoResponse(string response)
    {
        var userData = TryParseJson<MicrosoftUserResponse>(response);
        if (userData == null)
        {
            return null;
        }
        
        return new OAuthUserInfo
        {
            ProviderId = userData.Id ?? string.Empty,
            Email = userData.Mail ?? userData.UserPrincipalName ?? string.Empty,
            Name = userData.DisplayName ?? string.Empty,
            Avatar = string.Empty, // Microsoft Graph doesn't return avatar URL directly
            ExtraData = new Dictionary<string, object>
            {
                ["given_name"] = userData.GivenName ?? string.Empty,
                ["surname"] = userData.Surname ?? string.Empty,
                ["job_title"] = userData.JobTitle ?? string.Empty,
                ["office_location"] = userData.OfficeLocation ?? string.Empty,
                ["mobile_phone"] = userData.MobilePhone ?? string.Empty
            }
        };
    }
    
    public override async Task<Result<OAuthTokenInfo>> RefreshTokenAsync(string refreshToken)
    {
        try
        {
            var tenantId = _options.TenantId ?? "common";
            var tokenEndpoint = _options.TokenEndpoint?.Replace("{tenant}", tenantId) ?? 
                $"https://login.microsoftonline.com/{tenantId}/oauth2/v2.0/token";
            
            var parameters = new Dictionary<string, string>
            {
                ["client_id"] = _options.ClientId ?? throw new InvalidOperationException("Microsoft ClientId is not configured"),
                ["client_secret"] = _options.ClientSecret ?? throw new InvalidOperationException("Microsoft ClientSecret is not configured"),
                ["refresh_token"] = refreshToken,
                ["grant_type"] = "refresh_token",
                ["scope"] = _options.Scope ?? "openid profile email"
            };
            
            var request = new HttpRequestMessage(HttpMethod.Post, tokenEndpoint);
            request.Content = new FormUrlEncodedContent(parameters);
            
            var response = await _httpClient.SendAsync(request);
            
            if (!response.IsSuccessStatusCode)
            {
                var error = await response.Content.ReadAsStringAsync();
                _logger.LogError("Failed to refresh Microsoft token: {StatusCode} {Error}", 
                    response.StatusCode, error);
                return Result<OAuthTokenInfo>.Failure("刷新令牌失败");
            }
            
            var content = await response.Content.ReadAsStringAsync();
            var tokenInfo = ParseTokenResponse(content);
            
            if (tokenInfo == null)
            {
                return Result<OAuthTokenInfo>.Failure("解析令牌响应失败");
            }
            
            return Result<OAuthTokenInfo>.Success(tokenInfo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to refresh Microsoft token");
            return Result<OAuthTokenInfo>.Failure("刷新令牌失败");
        }
    }
    
    private class MicrosoftTokenResponse
    {
        public string? TokenType { get; set; }
        public string? Scope { get; set; }
        public int? ExpiresIn { get; set; }
        public string? AccessToken { get; set; }
        public string? RefreshToken { get; set; }
        public string? IdToken { get; set; }
    }
    
    private class MicrosoftUserResponse
    {
        public string? Id { get; set; }
        public string? DisplayName { get; set; }
        public string? GivenName { get; set; }
        public string? Surname { get; set; }
        public string? UserPrincipalName { get; set; }
        public string? Mail { get; set; }
        public string? JobTitle { get; set; }
        public string? OfficeLocation { get; set; }
        public string? MobilePhone { get; set; }
    }
}