using System.Net.Http.Headers;
using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Infrastructure.OAuth.Configuration;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Infrastructure.OAuth.Providers;

public class GoogleOAuthProvider : BaseOAuthProvider
{
    public GoogleOAuthProvider(OAuthProviderOptions options, ILogger logger) 
        : base(options, logger)
    {
    }
    
    public override string GetAuthorizationUrl(string state, string redirectUri)
    {
        var parameters = new Dictionary<string, string>
        {
            ["client_id"] = _options.ClientId ?? throw new InvalidOperationException("Google ClientId is not configured"),
            ["redirect_uri"] = redirectUri,
            ["response_type"] = "code",
            ["scope"] = _options.Scope ?? "openid profile email",
            ["state"] = state,
            ["access_type"] = "offline",
            ["prompt"] = "consent"
        };
        
        var queryString = BuildQueryString(parameters);
        return $"{_options.AuthorizationEndpoint}?{queryString}";
    }
    
    protected override HttpRequestMessage BuildTokenRequest(string code, string redirectUri)
    {
        var parameters = new Dictionary<string, string>
        {
            ["client_id"] = _options.ClientId ?? throw new InvalidOperationException("Google ClientId is not configured"),
            ["client_secret"] = _options.ClientSecret ?? throw new InvalidOperationException("Google ClientSecret is not configured"),
            ["code"] = code,
            ["redirect_uri"] = redirectUri,
            ["grant_type"] = "authorization_code"
        };
        
        var request = new HttpRequestMessage(HttpMethod.Post, _options.TokenEndpoint);
        request.Content = new FormUrlEncodedContent(parameters);
        
        return request;
    }
    
    protected override OAuthTokenInfo? ParseTokenResponse(string response)
    {
        var tokenData = TryParseJson<GoogleTokenResponse>(response);
        if (tokenData == null)
        {
            return null;
        }
        
        return new OAuthTokenInfo
        {
            AccessToken = tokenData.AccessToken ?? string.Empty,
            RefreshToken = tokenData.RefreshToken ?? string.Empty,
            ExpiresIn = tokenData.ExpiresIn ?? 3600,
            TokenType = tokenData.TokenType ?? "Bearer"
        };
    }
    
    protected override HttpRequestMessage BuildUserInfoRequest(string accessToken)
    {
        var request = new HttpRequestMessage(HttpMethod.Get, _options.UserInfoEndpoint);
        request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
        
        return request;
    }
    
    protected override OAuthUserInfo? ParseUserInfoResponse(string response)
    {
        var userData = TryParseJson<GoogleUserResponse>(response);
        if (userData == null)
        {
            return null;
        }
        
        return new OAuthUserInfo
        {
            ProviderId = userData.Id ?? string.Empty,
            Email = userData.Email ?? string.Empty,
            Name = userData.Name ?? string.Empty,
            Avatar = userData.Picture ?? string.Empty,
            ExtraData = new Dictionary<string, object>
            {
                ["verified_email"] = userData.VerifiedEmail ?? false,
                ["locale"] = userData.Locale ?? string.Empty
            }
        };
    }
    
    public override async Task<Result<OAuthTokenInfo>> RefreshTokenAsync(string refreshToken)
    {
        try
        {
            var parameters = new Dictionary<string, string>
            {
                ["client_id"] = _options.ClientId ?? throw new InvalidOperationException("Google ClientId is not configured"),
                ["client_secret"] = _options.ClientSecret ?? throw new InvalidOperationException("Google ClientSecret is not configured"),
                ["refresh_token"] = refreshToken,
                ["grant_type"] = "refresh_token"
            };
            
            var request = new HttpRequestMessage(HttpMethod.Post, _options.TokenEndpoint);
            request.Content = new FormUrlEncodedContent(parameters);
            
            var response = await _httpClient.SendAsync(request);
            
            if (!response.IsSuccessStatusCode)
            {
                var error = await response.Content.ReadAsStringAsync();
                _logger.LogError("Failed to refresh Google token: {StatusCode} {Error}", 
                    response.StatusCode, error);
                return Result<OAuthTokenInfo>.Failure("刷新令牌失败");
            }
            
            var content = await response.Content.ReadAsStringAsync();
            var tokenInfo = ParseTokenResponse(content);
            
            if (tokenInfo == null)
            {
                return Result<OAuthTokenInfo>.Failure("解析令牌响应失败");
            }
            
            // Google might not return a new refresh token
            if (string.IsNullOrEmpty(tokenInfo.RefreshToken))
            {
                tokenInfo.RefreshToken = refreshToken;
            }
            
            return Result<OAuthTokenInfo>.Success(tokenInfo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to refresh Google token");
            return Result<OAuthTokenInfo>.Failure("刷新令牌失败");
        }
    }
    
    private class GoogleTokenResponse
    {
        public string? AccessToken { get; set; }
        public string? RefreshToken { get; set; }
        public int? ExpiresIn { get; set; }
        public string? TokenType { get; set; }
        public string? IdToken { get; set; }
    }
    
    private class GoogleUserResponse
    {
        public string? Id { get; set; }
        public string? Email { get; set; }
        public bool? VerifiedEmail { get; set; }
        public string? Name { get; set; }
        public string? GivenName { get; set; }
        public string? FamilyName { get; set; }
        public string? Picture { get; set; }
        public string? Locale { get; set; }
    }
}