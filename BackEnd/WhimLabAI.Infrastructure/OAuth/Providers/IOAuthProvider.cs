using WhimLabAI.Abstractions.Application;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Infrastructure.OAuth.Providers;

public interface IOAuthProvider
{
    string GetAuthorizationUrl(string state, string redirectUri);
    Task<Result<OAuthTokenInfo>> ExchangeCodeForTokenAsync(string code, string redirectUri);
    Task<Result<OAuthUserInfo>> GetUserInfoAsync(string accessToken);
    Task<Result<OAuthTokenInfo>> RefreshTokenAsync(string refreshToken);
}