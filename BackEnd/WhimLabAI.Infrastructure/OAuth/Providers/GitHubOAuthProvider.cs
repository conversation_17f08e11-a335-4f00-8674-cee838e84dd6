using System.Net.Http.Headers;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Infrastructure.OAuth.Configuration;

namespace WhimLabAI.Infrastructure.OAuth.Providers;

public class GitHubOAuthProvider : BaseOAuthProvider
{
    public GitHubOAuthProvider(OAuthProviderOptions options, ILogger logger) 
        : base(options, logger)
    {
    }
    
    public override string GetAuthorizationUrl(string state, string redirectUri)
    {
        var parameters = new Dictionary<string, string>
        {
            ["client_id"] = _options.ClientId ?? throw new InvalidOperationException("GitHub ClientId is not configured"),
            ["redirect_uri"] = redirectUri,
            ["scope"] = _options.Scope ?? "user:email",
            ["state"] = state
        };
        
        var queryString = BuildQueryString(parameters);
        return $"{_options.AuthorizationEndpoint}?{queryString}";
    }
    
    protected override HttpRequestMessage BuildTokenRequest(string code, string redirectUri)
    {
        var parameters = new Dictionary<string, string>
        {
            ["client_id"] = _options.ClientId ?? throw new InvalidOperationException("GitHub ClientId is not configured"),
            ["client_secret"] = _options.ClientSecret ?? throw new InvalidOperationException("GitHub ClientSecret is not configured"),
            ["code"] = code,
            ["redirect_uri"] = redirectUri
        };
        
        var request = new HttpRequestMessage(HttpMethod.Post, _options.TokenEndpoint);
        request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
        request.Content = new FormUrlEncodedContent(parameters);
        
        return request;
    }
    
    protected override OAuthTokenInfo? ParseTokenResponse(string response)
    {
        var tokenData = TryParseJson<GitHubTokenResponse>(response);
        if (tokenData == null)
        {
            return null;
        }
        
        return new OAuthTokenInfo
        {
            AccessToken = tokenData.AccessToken ?? string.Empty,
            TokenType = tokenData.TokenType ?? "Bearer",
            ExpiresIn = 0, // GitHub tokens don't expire
            RefreshToken = string.Empty // GitHub doesn't provide refresh tokens
        };
    }
    
    protected override HttpRequestMessage BuildUserInfoRequest(string accessToken)
    {
        var request = new HttpRequestMessage(HttpMethod.Get, _options.UserInfoEndpoint);
        request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
        request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
        
        return request;
    }
    
    protected override OAuthUserInfo? ParseUserInfoResponse(string response)
    {
        var userData = TryParseJson<GitHubUserResponse>(response);
        if (userData == null)
        {
            return null;
        }
        
        return new OAuthUserInfo
        {
            ProviderId = userData.Id?.ToString() ?? string.Empty,
            Email = userData.Email ?? string.Empty,
            Name = userData.Name ?? userData.Login ?? string.Empty,
            Avatar = userData.AvatarUrl ?? string.Empty,
            ExtraData = new Dictionary<string, object>
            {
                ["login"] = userData.Login ?? string.Empty,
                ["company"] = userData.Company ?? string.Empty,
                ["location"] = userData.Location ?? string.Empty,
                ["bio"] = userData.Bio ?? string.Empty
            }
        };
    }
    
    private class GitHubTokenResponse
    {
        public string? AccessToken { get; set; }
        public string? TokenType { get; set; }
        public string? Scope { get; set; }
    }
    
    private class GitHubUserResponse
    {
        public long? Id { get; set; }
        public string? Login { get; set; }
        public string? Name { get; set; }
        public string? Email { get; set; }
        public string? AvatarUrl { get; set; }
        public string? Company { get; set; }
        public string? Location { get; set; }
        public string? Bio { get; set; }
    }
}