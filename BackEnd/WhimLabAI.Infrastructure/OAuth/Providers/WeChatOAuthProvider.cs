using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Infrastructure.OAuth.Configuration;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Infrastructure.OAuth.Providers;

public class WeChatOAuthProvider : BaseOAuthProvider
{
    public WeChatOAuthProvider(OAuthProviderOptions options, ILogger logger) 
        : base(options, logger)
    {
    }
    
    public override string GetAuthorizationUrl(string state, string redirectUri)
    {
        // WeChat uses AppId instead of ClientId
        var appId = _options.AppId ?? _options.ClientId ?? 
            throw new InvalidOperationException("WeChat AppId is not configured");
        
        var parameters = new Dictionary<string, string>
        {
            ["appid"] = appId,
            ["redirect_uri"] = redirectUri,
            ["response_type"] = "code",
            ["scope"] = _options.Scope ?? "snsapi_userinfo",
            ["state"] = state
        };
        
        var queryString = BuildQueryString(parameters);
        // WeChat requires the fragment identifier at the end
        return $"{_options.AuthorizationEndpoint}?{queryString}#wechat_redirect";
    }
    
    protected override HttpRequestMessage BuildTokenRequest(string code, string redirectUri)
    {
        var appId = _options.AppId ?? _options.ClientId ?? 
            throw new InvalidOperationException("WeChat AppId is not configured");
        var appSecret = _options.AppSecret ?? _options.ClientSecret ?? 
            throw new InvalidOperationException("WeChat AppSecret is not configured");
        
        var parameters = new Dictionary<string, string>
        {
            ["appid"] = appId,
            ["secret"] = appSecret,
            ["code"] = code,
            ["grant_type"] = "authorization_code"
        };
        
        var queryString = BuildQueryString(parameters);
        var request = new HttpRequestMessage(HttpMethod.Get, $"{_options.TokenEndpoint}?{queryString}");
        
        return request;
    }
    
    protected override OAuthTokenInfo? ParseTokenResponse(string response)
    {
        var tokenData = TryParseJson<WeChatTokenResponse>(response);
        if (tokenData == null || !string.IsNullOrEmpty(tokenData.ErrCode))
        {
            _logger.LogError("WeChat token error: {ErrCode} - {ErrMsg}", 
                tokenData?.ErrCode, tokenData?.ErrMsg);
            return null;
        }
        
        // WeChat requires both access_token and openid, so we encode them together
        var accessToken = $"{tokenData.AccessToken}|{tokenData.OpenId}";
        
        return new OAuthTokenInfo
        {
            AccessToken = accessToken,
            RefreshToken = tokenData.RefreshToken ?? string.Empty,
            ExpiresIn = tokenData.ExpiresIn ?? 7200,
            TokenType = "Bearer"
        };
    }
    
    protected override HttpRequestMessage BuildUserInfoRequest(string accessToken)
    {
        // WeChat requires both access_token and openid for user info
        // We need to parse the openid from the token response
        // For simplicity, we'll assume it's passed in the access token string as "token|openid"
        var parts = accessToken.Split('|');
        var token = parts[0];
        var openid = parts.Length > 1 ? parts[1] : string.Empty;
        
        var parameters = new Dictionary<string, string>
        {
            ["access_token"] = token,
            ["openid"] = openid,
            ["lang"] = "zh_CN"
        };
        
        var queryString = BuildQueryString(parameters);
        var request = new HttpRequestMessage(HttpMethod.Get, $"{_options.UserInfoEndpoint}?{queryString}");
        
        return request;
    }
    
    protected override OAuthUserInfo? ParseUserInfoResponse(string response)
    {
        var userData = TryParseJson<WeChatUserResponse>(response);
        if (userData == null || !string.IsNullOrEmpty(userData.ErrCode))
        {
            _logger.LogError("WeChat user info error: {ErrCode} - {ErrMsg}", 
                userData?.ErrCode, userData?.ErrMsg);
            return null;
        }
        
        return new OAuthUserInfo
        {
            ProviderId = userData.OpenId ?? string.Empty,
            Email = string.Empty, // WeChat doesn't provide email
            Name = userData.Nickname ?? string.Empty,
            Avatar = userData.HeadImgUrl ?? string.Empty,
            ExtraData = new Dictionary<string, object>
            {
                ["sex"] = userData.Sex ?? 0,
                ["language"] = userData.Language ?? string.Empty,
                ["city"] = userData.City ?? string.Empty,
                ["province"] = userData.Province ?? string.Empty,
                ["country"] = userData.Country ?? string.Empty,
                ["unionid"] = userData.UnionId ?? string.Empty
            }
        };
    }
    
    public override async Task<Result<OAuthTokenInfo>> RefreshTokenAsync(string refreshToken)
    {
        try
        {
            var appId = _options.AppId ?? _options.ClientId ?? 
                throw new InvalidOperationException("WeChat AppId is not configured");
            
            var parameters = new Dictionary<string, string>
            {
                ["appid"] = appId,
                ["grant_type"] = "refresh_token",
                ["refresh_token"] = refreshToken
            };
            
            var queryString = BuildQueryString(parameters);
            var request = new HttpRequestMessage(HttpMethod.Get, 
                $"https://api.weixin.qq.com/sns/oauth2/refresh_token?{queryString}");
            
            var response = await _httpClient.SendAsync(request);
            
            if (!response.IsSuccessStatusCode)
            {
                var error = await response.Content.ReadAsStringAsync();
                _logger.LogError("Failed to refresh WeChat token: {StatusCode} {Error}", 
                    response.StatusCode, error);
                return Result<OAuthTokenInfo>.Failure("刷新令牌失败");
            }
            
            var content = await response.Content.ReadAsStringAsync();
            var tokenInfo = ParseTokenResponse(content);
            
            if (tokenInfo == null)
            {
                return Result<OAuthTokenInfo>.Failure("解析令牌响应失败");
            }
            
            return Result<OAuthTokenInfo>.Success(tokenInfo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to refresh WeChat token");
            return Result<OAuthTokenInfo>.Failure("刷新令牌失败");
        }
    }
    
    private class WeChatTokenResponse
    {
        public string? AccessToken { get; set; }
        public int? ExpiresIn { get; set; }
        public string? RefreshToken { get; set; }
        public string? OpenId { get; set; }
        public string? Scope { get; set; }
        public string? UnionId { get; set; }
        public string? ErrCode { get; set; }
        public string? ErrMsg { get; set; }
    }
    
    private class WeChatUserResponse
    {
        public string? OpenId { get; set; }
        public string? Nickname { get; set; }
        public int? Sex { get; set; }
        public string? Province { get; set; }
        public string? City { get; set; }
        public string? Country { get; set; }
        public string? HeadImgUrl { get; set; }
        public string? Privilege { get; set; }
        public string? UnionId { get; set; }
        public string? Language { get; set; }
        public string? ErrCode { get; set; }
        public string? ErrMsg { get; set; }
    }
}