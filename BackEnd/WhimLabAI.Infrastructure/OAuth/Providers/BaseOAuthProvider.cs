using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Web;
using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Infrastructure.OAuth.Configuration;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Infrastructure.OAuth.Providers;

public abstract class BaseOAuthProvider : IOAuthProvider
{
    protected readonly OAuthProviderOptions _options;
    protected readonly ILogger _logger;
    protected readonly HttpClient _httpClient;
    
    protected BaseOAuthProvider(OAuthProviderOptions options, ILogger logger)
    {
        _options = options;
        _logger = logger;
        _httpClient = new HttpClient();
        _httpClient.DefaultRequestHeaders.UserAgent.ParseAdd("WhimLabAI/1.0");
    }
    
    public abstract string GetAuthorizationUrl(string state, string redirectUri);
    
    public virtual async Task<Result<OAuthTokenInfo>> ExchangeCodeForTokenAsync(string code, string redirectUri)
    {
        try
        {
            var tokenRequest = BuildTokenRequest(code, redirectUri);
            var response = await _httpClient.SendAsync(tokenRequest);
            
            if (!response.IsSuccessStatusCode)
            {
                var error = await response.Content.ReadAsStringAsync();
                _logger.LogError("Failed to exchange code for token: {StatusCode} {Error}", 
                    response.StatusCode, error);
                return Result<OAuthTokenInfo>.Failure("获取访问令牌失败");
            }
            
            var content = await response.Content.ReadAsStringAsync();
            var tokenInfo = ParseTokenResponse(content);
            
            if (tokenInfo == null)
            {
                return Result<OAuthTokenInfo>.Failure("解析令牌响应失败");
            }
            
            return Result<OAuthTokenInfo>.Success(tokenInfo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to exchange OAuth code for token");
            return Result<OAuthTokenInfo>.Failure("获取访问令牌失败");
        }
    }
    
    public virtual async Task<Result<OAuthUserInfo>> GetUserInfoAsync(string accessToken)
    {
        try
        {
            var userInfoRequest = BuildUserInfoRequest(accessToken);
            var response = await _httpClient.SendAsync(userInfoRequest);
            
            if (!response.IsSuccessStatusCode)
            {
                var error = await response.Content.ReadAsStringAsync();
                _logger.LogError("Failed to get user info: {StatusCode} {Error}", 
                    response.StatusCode, error);
                return Result<OAuthUserInfo>.Failure("获取用户信息失败");
            }
            
            var content = await response.Content.ReadAsStringAsync();
            var userInfo = ParseUserInfoResponse(content);
            
            if (userInfo == null)
            {
                return Result<OAuthUserInfo>.Failure("解析用户信息失败");
            }
            
            return Result<OAuthUserInfo>.Success(userInfo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get OAuth user info");
            return Result<OAuthUserInfo>.Failure("获取用户信息失败");
        }
    }
    
    public virtual async Task<Result<OAuthTokenInfo>> RefreshTokenAsync(string refreshToken)
    {
        // Not all providers support refresh tokens
        return Result<OAuthTokenInfo>.Failure("该登录方式不支持刷新令牌");
    }
    
    protected abstract HttpRequestMessage BuildTokenRequest(string code, string redirectUri);
    protected abstract OAuthTokenInfo? ParseTokenResponse(string response);
    protected abstract HttpRequestMessage BuildUserInfoRequest(string accessToken);
    protected abstract OAuthUserInfo? ParseUserInfoResponse(string response);
    
    protected string BuildQueryString(Dictionary<string, string> parameters)
    {
        var query = HttpUtility.ParseQueryString(string.Empty);
        foreach (var param in parameters)
        {
            query[param.Key] = param.Value;
        }
        return query.ToString() ?? string.Empty;
    }
    
    protected T? TryParseJson<T>(string json) where T : class
    {
        try
        {
            return JsonSerializer.Deserialize<T>(json, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to parse JSON response");
            return null;
        }
    }
}