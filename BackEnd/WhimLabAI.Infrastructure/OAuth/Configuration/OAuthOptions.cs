namespace WhimLabAI.Infrastructure.OAuth.Configuration;

public class OAuthOptions
{
    public const string SectionName = "OAuth";
    
    public string BaseUrl { get; set; } = string.Empty;
    public int StateTokenExpirationMinutes { get; set; } = 10;
    public Dictionary<string, OAuthProviderOptions> Providers { get; set; } = new();
    
    public OAuthProviderOptions? GetProvider(string providerName)
    {
        return Providers.TryGetValue(providerName, out var provider) ? provider : null;
    }
    
    public bool IsProviderEnabled(string providerName)
    {
        var provider = GetProvider(providerName);
        return provider?.Enabled ?? false;
    }
}

public class OAuthProviderOptions
{
    public bool Enabled { get; set; }
    
    // Common OAuth properties
    public string? ClientId { get; set; }
    public string? ClientSecret { get; set; }
    public string? Scope { get; set; }
    public string? AuthorizationEndpoint { get; set; }
    public string? TokenEndpoint { get; set; }
    public string? UserInfoEndpoint { get; set; }
    
    // WeChat specific
    public string? AppId { get; set; }
    public string? AppSecret { get; set; }
    
    // Microsoft specific
    public string? TenantId { get; set; }
    
    // Additional configuration that might be provider-specific
    public Dictionary<string, string> ExtraConfig { get; set; } = new();
}