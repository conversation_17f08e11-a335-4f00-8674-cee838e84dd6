using System.Text.Json;
using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Domain.Common;
using WhimLabAI.Domain.Entities.EventSourcing;
using WhimLabAI.Domain.Events;
using WhimLabAI.Domain.Repositories;

namespace WhimLabAI.Infrastructure.EventSourcing;

public class EventStore : IEventStore
{
    private readonly IEventStoreRepository _eventStoreRepository;
    private readonly ILogger<EventStore> _logger;
    private readonly JsonSerializerOptions _jsonOptions;

    public EventStore(
        IEventStoreRepository eventStoreRepository,
        ILogger<EventStore> logger)
    {
        _eventStoreRepository = eventStoreRepository;
        _logger = logger;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false
        };
    }

    public async Task SaveEventsAsync<TAggregate>(
        Guid aggregateId,
        IEnumerable<IDomainEvent> events,
        int expectedVersion,
        CancellationToken cancellationToken = default)
        where TAggregate : AggregateRoot
    {
        var aggregateType = typeof(TAggregate).Name;
        var currentVersion = await _eventStoreRepository.GetLatestVersionAsync(aggregateId, cancellationToken);

        if (currentVersion != expectedVersion)
        {
            throw new InvalidOperationException($"Concurrency conflict. Expected version {expectedVersion} but current version is {currentVersion}");
        }

        var eventStores = new List<Domain.Entities.EventSourcing.EventStore>();
        var version = expectedVersion;

        foreach (var @event in events)
        {
            version++;
            var eventData = JsonSerializer.Serialize(@event, @event.GetType(), _jsonOptions);
            var eventStore = new Domain.Entities.EventSourcing.EventStore(
                aggregateId,
                aggregateType,
                @event.GetType().Name,
                eventData,
                version,
                @event.OccurredOn);

            eventStores.Add(eventStore);
        }

        await _eventStoreRepository.SaveEventsAsync(eventStores, cancellationToken);

        _logger.LogInformation("Saved {EventCount} events for aggregate {AggregateId} of type {AggregateType}",
            eventStores.Count, aggregateId, aggregateType);
    }

    public async Task<IReadOnlyList<IDomainEvent>> GetEventsAsync(
        Guid aggregateId,
        int? fromVersion = null,
        CancellationToken cancellationToken = default)
    {
        var eventStores = await _eventStoreRepository.GetEventsAsync(aggregateId, fromVersion, cancellationToken);
        var events = new List<IDomainEvent>();

        foreach (var eventStore in eventStores)
        {
            var eventType = Type.GetType($"WhimLabAI.Domain.DomainEvents.{eventStore.EventType}, WhimLabAI.Domain");
            if (eventType == null)
            {
                _logger.LogWarning("Could not find event type {EventType}", eventStore.EventType);
                continue;
            }

            var @event = JsonSerializer.Deserialize(eventStore.EventData, eventType, _jsonOptions) as IDomainEvent;
            if (@event != null)
            {
                events.Add(@event);
            }
        }

        return events;
    }

    public async Task<TAggregate?> GetAggregateAsync<TAggregate>(
        Guid aggregateId,
        CancellationToken cancellationToken = default)
        where TAggregate : AggregateRoot, new()
    {
        // Try to get from snapshot first
        var aggregate = await GetAggregateFromSnapshotAsync<TAggregate>(aggregateId, cancellationToken);
        var fromVersion = 0;

        if (aggregate == null)
        {
            aggregate = new TAggregate();
        }
        else
        {
            var snapshot = await _eventStoreRepository.GetLatestSnapshotAsync(aggregateId, cancellationToken);
            if (snapshot != null)
            {
                fromVersion = snapshot.Version;
            }
        }

        // Apply events after snapshot
        var events = await GetEventsAsync(aggregateId, fromVersion, cancellationToken);

        if (!events.Any() && fromVersion == 0)
        {
            return null; // Aggregate doesn't exist
        }

        // Apply events to rebuild state
        foreach (var @event in events)
        {
            ApplyEvent(aggregate, @event);
        }

        return aggregate;
    }

    public async Task SaveSnapshotAsync<TAggregate>(
        TAggregate aggregate,
        CancellationToken cancellationToken = default)
        where TAggregate : AggregateRoot
    {
        var aggregateType = typeof(TAggregate).Name;
        var snapshotData = JsonSerializer.Serialize(aggregate, _jsonOptions);
        var version = await _eventStoreRepository.GetLatestVersionAsync(aggregate.Id, cancellationToken);

        var snapshot = new Snapshot(
            aggregate.Id,
            aggregateType,
            snapshotData,
            version);

        await _eventStoreRepository.SaveSnapshotAsync(snapshot, cancellationToken);

        _logger.LogInformation("Saved snapshot for aggregate {AggregateId} of type {AggregateType} at version {Version}",
            aggregate.Id, aggregateType, version);
    }

    public async Task<TAggregate?> GetAggregateFromSnapshotAsync<TAggregate>(
        Guid aggregateId,
        CancellationToken cancellationToken = default)
        where TAggregate : AggregateRoot
    {
        var snapshot = await _eventStoreRepository.GetLatestSnapshotAsync(aggregateId, cancellationToken);
        if (snapshot == null)
        {
            return null;
        }

        var aggregate = JsonSerializer.Deserialize<TAggregate>(snapshot.SnapshotData, _jsonOptions);
        return aggregate;
    }

    private void ApplyEvent<TAggregate>(TAggregate aggregate, IDomainEvent @event) where TAggregate : AggregateRoot
    {
        // Use reflection to find and invoke the Apply method
        var applyMethod = aggregate.GetType()
            .GetMethods(System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
            .FirstOrDefault(m => m.Name == "Apply" && m.GetParameters().Length == 1 &&
                m.GetParameters()[0].ParameterType == @event.GetType());

        if (applyMethod != null)
        {
            applyMethod.Invoke(aggregate, new object[] { @event });
        }
        else
        {
            _logger.LogWarning("No Apply method found for event type {EventType} on aggregate {AggregateType}",
                @event.GetType().Name, aggregate.GetType().Name);
        }
    }
}
