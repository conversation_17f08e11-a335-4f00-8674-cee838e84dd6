namespace WhimLabAI.Infrastructure.Performance;

public class OptimizationAction
{
    public string ActionType { get; set; } = string.Empty;
    public string Target { get; set; } = string.Empty;
    public DateTime StartTime { get; set; } = DateTime.UtcNow;
    public DateTime? EndTime { get; set; }
    public bool Success { get; set; }
    public string? Result { get; set; }
    public string? Error { get; set; }
    public Dictionary<string, object> Metrics { get; set; } = new();
}