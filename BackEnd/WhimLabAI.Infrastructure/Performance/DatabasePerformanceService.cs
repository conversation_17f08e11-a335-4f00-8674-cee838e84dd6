using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Npgsql;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Infrastructure.Data;
using WhimLabAI.Shared.Results;
using WhimLabAI.Shared.Dtos.Performance;

namespace WhimLabAI.Infrastructure.Performance;

/// <summary>
/// 数据库性能服务实现（PostgreSQL）
/// </summary>
public class DatabasePerformanceService : IDatabasePerformanceService
{
    private readonly WhimLabAIDbContext _dbContext;
    private readonly ILogger<DatabasePerformanceService> _logger;
    private readonly string _connectionString;

    public DatabasePerformanceService(
        WhimLabAIDbContext dbContext,
        ILogger<DatabasePerformanceService> logger)
    {
        _dbContext = dbContext;
        _logger = logger;
        _connectionString = dbContext.Database.GetConnectionString() ?? 
            throw new InvalidOperationException("Database connection string not found");
    }

    public async Task<Result<IndexAnalysisResult>> AnalyzeIndexesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var result = new IndexAnalysisResult
            {
                AnalyzedAt = DateTime.UtcNow
            };

            // 获取现有索引
            result.ExistingIndexes = await GetExistingIndexesAsync(cancellationToken);

            // 分析索引使用情况
            result.UnusedIndexes = await FindUnusedIndexesAsync(cancellationToken);

            // 查找重复索引
            result.DuplicateIndexes = await FindDuplicateIndexesAsync(cancellationToken);

            // 查找碎片化索引
            result.FragmentedIndexes = await FindFragmentedIndexesAsync(cancellationToken);

            // 生成索引建议
            result.SuggestedIndexes = await GenerateIndexSuggestionsAsync(cancellationToken);

            // 计算健康评分
            result.HealthScore = CalculateIndexHealthScore(result);

            _logger.LogInformation("Index analysis completed. Found {UnusedCount} unused, {DuplicateCount} duplicate, {FragmentedCount} fragmented indexes",
                result.UnusedIndexes.Count, result.DuplicateIndexes.Count, result.FragmentedIndexes.Count);

            return Result<IndexAnalysisResult>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to analyze indexes");
            return Result<IndexAnalysisResult>.Failure($"Index analysis failed: {ex.Message}");
        }
    }

    public async Task<Result<SlowQueryAnalysisResult>> AnalyzeSlowQueriesAsync(
        TimeSpan threshold,
        int limit = 100,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var result = new SlowQueryAnalysisResult
            {
                AnalyzedAt = DateTime.UtcNow,
                AnalysisPeriod = TimeSpan.FromDays(7) // 分析过去7天
            };

            // 获取慢查询
            result.SlowQueries = await GetSlowQueriesAsync(threshold, limit, cancellationToken);
            result.TotalSlowQueries = result.SlowQueries.Count;

            // 分析查询模式
            result.CommonPatterns = AnalyzeQueryPatterns(result.SlowQueries);

            // 按表统计慢查询
            result.SlowQueryByTable = result.SlowQueries
                .SelectMany(q => q.Tables)
                .GroupBy(t => t)
                .ToDictionary(g => g.Key, g => g.Count());

            // 生成优化建议
            result.Suggestions = GenerateQueryOptimizationSuggestions(result.SlowQueries);

            _logger.LogInformation("Slow query analysis completed. Found {Count} slow queries", result.TotalSlowQueries);

            return Result<SlowQueryAnalysisResult>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to analyze slow queries");
            return Result<SlowQueryAnalysisResult>.Failure($"Slow query analysis failed: {ex.Message}");
        }
    }

    public async Task<Result<QueryPlanAnalysis>> AnalyzeQueryPlanAsync(
        string query,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var analysis = new QueryPlanAnalysis
            {
                Query = query
            };

            using var connection = new NpgsqlConnection(_connectionString);
            await connection.OpenAsync(cancellationToken);

            // 获取查询计划
            using var command = new NpgsqlCommand($"EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) {query}", connection);
            var planJson = await command.ExecuteScalarAsync(cancellationToken) as string;

            if (!string.IsNullOrEmpty(planJson))
            {
                analysis.ExecutionPlan = planJson;
                ParseQueryPlan(planJson, analysis);
            }

            // 分析问题
            analysis.Issues = AnalyzeQueryPlanIssues(analysis);

            // 生成优化建议
            analysis.Optimizations = GenerateQueryOptimizations(analysis);

            return Result<QueryPlanAnalysis>.Success(analysis);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to analyze query plan");
            return Result<QueryPlanAnalysis>.Failure($"Query plan analysis failed: {ex.Message}");
        }
    }

    public async Task<Result<DatabaseStatistics>> GetDatabaseStatisticsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var stats = new DatabaseStatistics
            {
                CollectedAt = DateTime.UtcNow
            };

            using var connection = new NpgsqlConnection(_connectionString);
            await connection.OpenAsync(cancellationToken);

            // 获取数据库名称和大小
            stats.DatabaseName = connection.Database;
            stats.DatabaseSize = await GetDatabaseSizeAsync(connection, cancellationToken);

            // 获取表和索引统计
            var (tableCount, indexCount, totalRows, dataSize, indexSize) = 
                await GetTableAndIndexStatsAsync(connection, cancellationToken);
            
            stats.TableCount = tableCount;
            stats.IndexCount = indexCount;
            stats.TotalRows = totalRows;
            stats.DataSize = dataSize;
            stats.IndexSize = indexSize;

            // 获取连接统计
            stats.ConnectionCount = await GetConnectionCountAsync(connection, cancellationToken);
            stats.ActiveQueries = await GetActiveQueryCountAsync(connection, cancellationToken);

            // 获取缓存统计
            stats.CacheHitRatio = await GetCacheHitRatioAsync(connection, cancellationToken);
            stats.BufferPoolSize = await GetBufferPoolSizeAsync(connection, cancellationToken);

            return Result<DatabaseStatistics>.Success(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get database statistics");
            return Result<DatabaseStatistics>.Failure($"Failed to get statistics: {ex.Message}");
        }
    }

    public async Task<Result<List<TableStatistics>>> GetTableStatisticsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var tables = new List<TableStatistics>();

            using var connection = new NpgsqlConnection(_connectionString);
            await connection.OpenAsync(cancellationToken);

            // 获取所有表的统计信息
            var sql = @"
                SELECT 
                    schemaname,
                    tablename,
                    n_live_tup as row_count,
                    pg_table_size(schemaname||'.'||tablename) as data_size,
                    pg_indexes_size(schemaname||'.'||tablename) as index_size,
                    pg_total_relation_size(schemaname||'.'||tablename) as total_size,
                    last_analyze,
                    last_autoanalyze
                FROM pg_stat_user_tables
                WHERE schemaname = 'public'
                ORDER BY total_size DESC";

            using var command = new NpgsqlCommand(sql, connection);
            using var reader = await command.ExecuteReaderAsync(cancellationToken);

            while (await reader.ReadAsync(cancellationToken))
            {
                var tableStat = new TableStatistics
                {
                    TableName = reader.GetString(1),
                    RowCount = reader.GetInt64(2),
                    DataSize = reader.GetInt64(3),
                    IndexSize = reader.GetInt64(4),
                    TotalSize = reader.GetInt64(5),
                    LastAnalyzed = reader.IsDBNull(6) ? DateTime.MinValue : reader.GetDateTime(6)
                };

                // 获取索引数量
                tableStat.IndexCount = await GetTableIndexCountAsync(connection, tableStat.TableName, cancellationToken);

                // 获取列统计
                tableStat.Columns = await GetColumnStatisticsAsync(connection, tableStat.TableName, cancellationToken);

                tables.Add(tableStat);
            }

            return Result<List<TableStatistics>>.Success(tables);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get table statistics");
            return Result<List<TableStatistics>>.Failure($"Failed to get table statistics: {ex.Message}");
        }
    }

    public async Task<Result<DatabaseOptimizationResult>> OptimizeDatabaseAsync(
        DatabaseOptimizationOptions options,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var result = new DatabaseOptimizationResult
            {
                StartTime = DateTime.UtcNow,
                Success = true
            };

            var actions = new List<OptimizationAction>();

            using var connection = new NpgsqlConnection(_connectionString);
            await connection.OpenAsync(cancellationToken);

            // 分析表
            if (options.AnalyzeTables)
            {
                var action = await AnalyzeTablesAsync(connection, cancellationToken);
                actions.Add(action);
            }

            // 清理死元组（VACUUM）
            if (options.VacuumDatabase)
            {
                var action = await VacuumDatabaseAsync(connection, cancellationToken);
                actions.Add(action);
            }

            // 重建索引
            if (options.RebuildIndexes)
            {
                var fragmentedIndexes = await FindFragmentedIndexesAsync(cancellationToken);
                foreach (var index in fragmentedIndexes.Where(i => i.FragmentationPercent > options.FragmentationThreshold))
                {
                    var action = await RebuildIndexAsync(connection, index, cancellationToken);
                    actions.Add(action);
                }
            }

            // 更新统计信息
            if (options.UpdateStatistics)
            {
                var action = await UpdateStatisticsAsync(connection, cancellationToken);
                actions.Add(action);
            }

            result.ActionsPerformed = actions.Select(a => new DatabaseOptimizationAction
            {
                ActionType = a.ActionType,
                Target = a.Target,
                Status = a.Success ? "Completed" : "Failed",
                Duration = a.EndTime.HasValue ? a.EndTime.Value - a.StartTime : TimeSpan.Zero,
                Result = a.Result ?? a.Error ?? string.Empty
            }).ToList();
            result.EndTime = DateTime.UtcNow;

            // 计算回收的空间
            result.SpaceReclaimed = await CalculateSpaceReclaimedAsync(connection, cancellationToken);

            _logger.LogInformation("Database optimization completed in {Duration}. Space reclaimed: {Space} bytes",
                result.Duration, result.SpaceReclaimed);

            return Result<DatabaseOptimizationResult>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to optimize database");
            return Result<DatabaseOptimizationResult>.Failure($"Database optimization failed: {ex.Message}");
        }
    }

    public async Task<Result<IndexCreationResult>> CreateSuggestedIndexesAsync(
        List<IndexSuggestion> suggestions,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var result = new IndexCreationResult
            {
                RequestedIndexes = suggestions.Count
            };

            var details = new List<IndexCreationDetail>();
            var startTime = DateTime.UtcNow;

            using var connection = new NpgsqlConnection(_connectionString);
            await connection.OpenAsync(cancellationToken);

            foreach (var suggestion in suggestions)
            {
                var detail = new IndexCreationDetail
                {
                    IndexName = GenerateIndexName(suggestion),
                    TableName = suggestion.TableName
                };

                var indexStartTime = DateTime.UtcNow;

                try
                {
                    using var command = new NpgsqlCommand(suggestion.CreateStatement, connection);
                    await command.ExecuteNonQueryAsync(cancellationToken);
                    
                    detail.Success = true;
                    result.CreatedIndexes++;
                }
                catch (Exception ex)
                {
                    detail.Success = false;
                    detail.ErrorMessage = ex.Message;
                    result.FailedIndexes++;
                    _logger.LogError(ex, "Failed to create index {IndexName}", detail.IndexName);
                }

                detail.CreationTime = DateTime.UtcNow - indexStartTime;
                details.Add(detail);
            }

            result.Details = details;
            result.TotalDuration = DateTime.UtcNow - startTime;

            _logger.LogInformation("Index creation completed. Created: {Created}, Failed: {Failed}",
                result.CreatedIndexes, result.FailedIndexes);

            return Result<IndexCreationResult>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create suggested indexes");
            return Result<IndexCreationResult>.Failure($"Index creation failed: {ex.Message}");
        }
    }

    public async Task<Result<DatabaseCleanupResult>> CleanupDatabaseAsync(
        DatabaseCleanupOptions options,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var result = new DatabaseCleanupResult
            {
                StartTime = DateTime.UtcNow
            };

            using var connection = new NpgsqlConnection(_connectionString);
            await connection.OpenAsync(cancellationToken);

            // 删除旧日志
            if (options.DeleteOldLogs)
            {
                result.LogsDeleted = await DeleteOldLogsAsync(connection, options.LogRetentionDays, cancellationToken);
            }

            // 删除孤立记录
            if (options.DeleteOrphanedRecords)
            {
                result.OrphanedRecordsDeleted = await DeleteOrphanedRecordsAsync(connection, cancellationToken);
            }

            // 压缩表
            if (options.CompactTables)
            {
                result.TablesCompacted = await CompactTablesAsync(connection, cancellationToken);
            }

            // 清理临时表
            if (options.ClearTempTables)
            {
                await ClearTempTablesAsync(connection, cancellationToken);
            }

            // 归档旧数据
            if (options.ArchiveOldData)
            {
                result.RecordsArchived = await ArchiveOldDataAsync(connection, options.ArchiveThresholdDays, cancellationToken);
            }

            result.EndTime = DateTime.UtcNow;
            result.SpaceReclaimed = await CalculateSpaceReclaimedAsync(connection, cancellationToken);

            _logger.LogInformation("Database cleanup completed. Space reclaimed: {Space} bytes", result.SpaceReclaimed);

            return Result<DatabaseCleanupResult>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to cleanup database");
            return Result<DatabaseCleanupResult>.Failure($"Database cleanup failed: {ex.Message}");
        }
    }

    public async Task<Result<DatabasePerformanceMetrics>> MonitorPerformanceAsync(
        TimeSpan duration,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var metrics = new DatabasePerformanceMetrics
            {
                StartTime = DateTime.UtcNow,
                Snapshots = new List<PerformanceSnapshot>(),
                Alerts = new List<PerformanceAlert>()
            };

            var endTime = DateTime.UtcNow.Add(duration);
            var snapshotInterval = TimeSpan.FromSeconds(10);

            while (DateTime.UtcNow < endTime && !cancellationToken.IsCancellationRequested)
            {
                var snapshot = await CapturePerformanceSnapshotAsync(cancellationToken);
                metrics.Snapshots.Add(snapshot);

                // 检查性能警报
                var alerts = CheckPerformanceAlerts(snapshot);
                metrics.Alerts.AddRange(alerts);

                await Task.Delay(snapshotInterval, cancellationToken);
            }

            metrics.EndTime = DateTime.UtcNow;

            // 生成性能摘要
            metrics.Summary = GeneratePerformanceSummary(metrics.Snapshots);

            return Result<DatabasePerformanceMetrics>.Success(metrics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to monitor database performance");
            return Result<DatabasePerformanceMetrics>.Failure($"Performance monitoring failed: {ex.Message}");
        }
    }

    public async Task<Result<DatabasePerformanceReport>> GeneratePerformanceReportAsync(
        CancellationToken cancellationToken = default)
    {
        try
        {
            var report = new DatabasePerformanceReport
            {
                GeneratedAt = DateTime.UtcNow,
                ReportPeriod = TimeSpan.FromDays(7)
            };

            // 获取当前统计信息
            var statsResult = await GetDatabaseStatisticsAsync(cancellationToken);
            if (statsResult.IsSuccess)
                report.CurrentStatistics = statsResult.Value!;

            // 运行索引分析
            var indexResult = await AnalyzeIndexesAsync(cancellationToken);
            if (indexResult.IsSuccess)
                report.IndexAnalysis = indexResult.Value!;

            // 运行慢查询分析
            var slowQueryResult = await AnalyzeSlowQueriesAsync(TimeSpan.FromSeconds(1), 50, cancellationToken);
            if (slowQueryResult.IsSuccess)
                report.SlowQueryAnalysis = slowQueryResult.Value!;

            // 收集性能指标
            var metricsResult = await MonitorPerformanceAsync(TimeSpan.FromMinutes(5), cancellationToken);
            if (metricsResult.IsSuccess)
                report.PerformanceMetrics = metricsResult.Value!;

            // 生成建议
            report.Recommendations = GeneratePerformanceRecommendations(report);

            // 计算性能评分
            report.Score = CalculatePerformanceScore(report);

            _logger.LogInformation("Database performance report generated: {ReportId}", report.ReportId);

            return Result<DatabasePerformanceReport>.Success(report);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate performance report");
            return Result<DatabasePerformanceReport>.Failure($"Report generation failed: {ex.Message}");
        }
    }

    #region Private Methods - Index Analysis

    private async Task<List<IndexInfo>> GetExistingIndexesAsync(CancellationToken cancellationToken)
    {
        var indexes = new List<IndexInfo>();

        using var connection = new NpgsqlConnection(_connectionString);
        await connection.OpenAsync(cancellationToken);

        var sql = @"
            SELECT 
                i.indexname,
                i.tablename,
                i.indexdef,
                pg_relation_size(indexrelid) as index_size,
                idx_stat.idx_scan as usage_count,
                idx_stat.idx_tup_read,
                idx_stat.idx_tup_fetch,
                indisunique,
                indisprimary
            FROM pg_indexes i
            JOIN pg_stat_user_indexes idx_stat 
                ON i.indexname = idx_stat.indexrelname 
                AND i.tablename = idx_stat.relname
            JOIN pg_index idx 
                ON idx_stat.indexrelid = idx.indexrelid
            WHERE i.schemaname = 'public'";

        using var command = new NpgsqlCommand(sql, connection);
        using var reader = await command.ExecuteReaderAsync(cancellationToken);

        while (await reader.ReadAsync(cancellationToken))
        {
            var indexInfo = new IndexInfo
            {
                IndexName = reader.GetString(0),
                TableName = reader.GetString(1),
                IndexSize = reader.GetInt64(3),
                UsageCount = reader.GetInt64(4),
                IsUnique = reader.GetBoolean(7),
                IsPrimary = reader.GetBoolean(8)
            };

            // 解析列信息
            var indexDef = reader.GetString(2);
            indexInfo.Columns = ParseIndexColumns(indexDef);

            indexes.Add(indexInfo);
        }

        return indexes;
    }

    private async Task<List<UnusedIndex>> FindUnusedIndexesAsync(CancellationToken cancellationToken)
    {
        var unusedIndexes = new List<UnusedIndex>();

        using var connection = new NpgsqlConnection(_connectionString);
        await connection.OpenAsync(cancellationToken);

        var sql = @"
            SELECT 
                s.indexrelname AS index_name,
                s.relname AS table_name,
                pg_relation_size(s.indexrelid) AS index_size,
                s.idx_scan as scan_count
            FROM pg_stat_user_indexes s
            JOIN pg_index i ON s.indexrelid = i.indexrelid
            WHERE s.idx_scan = 0
                AND s.schemaname = 'public'
                AND NOT i.indisprimary
                AND NOT i.indisunique";

        using var command = new NpgsqlCommand(sql, connection);
        using var reader = await command.ExecuteReaderAsync(cancellationToken);

        while (await reader.ReadAsync(cancellationToken))
        {
            unusedIndexes.Add(new UnusedIndex
            {
                IndexName = reader.GetString(0),
                TableName = reader.GetString(1),
                IndexSize = reader.GetInt64(2),
                DropStatement = $"DROP INDEX IF EXISTS {reader.GetString(0)};"
            });
        }

        return unusedIndexes;
    }

    private async Task<List<DuplicateIndex>> FindDuplicateIndexesAsync(CancellationToken cancellationToken)
    {
        var duplicates = new List<DuplicateIndex>();

        using var connection = new NpgsqlConnection(_connectionString);
        await connection.OpenAsync(cancellationToken);

        // 查找具有相同列的索引
        var sql = @"
            WITH index_columns AS (
                SELECT 
                    n.nspname as schema_name,
                    t.relname as table_name,
                    i.relname as index_name,
                    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as columns,
                    pg_relation_size(i.oid) as index_size
                FROM pg_index ix
                JOIN pg_class t ON t.oid = ix.indrelid
                JOIN pg_class i ON i.oid = ix.indexrelid
                JOIN pg_namespace n ON n.oid = t.relnamespace
                JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
                WHERE n.nspname = 'public'
                GROUP BY n.nspname, t.relname, i.relname, i.oid
            )
            SELECT 
                ic1.table_name,
                ic1.index_name as primary_index,
                array_agg(ic2.index_name) as duplicate_indexes,
                sum(ic2.index_size) as wasted_space
            FROM index_columns ic1
            JOIN index_columns ic2 
                ON ic1.table_name = ic2.table_name 
                AND ic1.columns = ic2.columns
                AND ic1.index_name < ic2.index_name
            GROUP BY ic1.table_name, ic1.index_name";

        using var command = new NpgsqlCommand(sql, connection);
        using var reader = await command.ExecuteReaderAsync(cancellationToken);

        while (await reader.ReadAsync(cancellationToken))
        {
            var duplicateIndexNames = reader.GetFieldValue<string[]>(2);
            duplicates.Add(new DuplicateIndex
            {
                TableName = reader.GetString(0),
                PrimaryIndex = reader.GetString(1),
                DuplicateIndexes = duplicateIndexNames.ToList(),
                TotalWastedSpace = reader.GetInt64(3),
                DropStatements = duplicateIndexNames.Select(idx => $"DROP INDEX IF EXISTS {idx};").ToList()
            });
        }

        return duplicates;
    }

    private async Task<List<FragmentedIndex>> FindFragmentedIndexesAsync(CancellationToken cancellationToken)
    {
        var fragmentedIndexes = new List<FragmentedIndex>();

        using var connection = new NpgsqlConnection(_connectionString);
        await connection.OpenAsync(cancellationToken);

        // PostgreSQL doesn't have direct fragmentation metrics like SQL Server
        // We use bloat estimation instead
        var sql = @"
            WITH btree_index_atts AS (
                SELECT 
                    nspname, 
                    indexclass.relname as index_name,
                    indexclass.reltuples,
                    indexclass.relpages,
                    tableclass.relname as table_name,
                    regexp_split_to_table(indkey::text, ' ')::smallint AS attnum,
                    indexrelid as index_oid
                FROM pg_index
                JOIN pg_class AS indexclass ON pg_index.indexrelid = indexclass.oid
                JOIN pg_class AS tableclass ON pg_index.indrelid = tableclass.oid
                JOIN pg_namespace ON pg_namespace.oid = indexclass.relnamespace
                WHERE indexclass.relkind = 'i'
                    AND nspname = 'public'
            ),
            index_item_sizes AS (
                SELECT
                    index_name,
                    table_name,
                    current_setting('block_size')::numeric AS bs,
                    CASE WHEN max(coalesce(pg_stats.null_frac, 0)) = 1 THEN 2 ELSE 6 END AS index_tuple_hdr,
                    sum((1 - coalesce(pg_stats.null_frac, 0)) * coalesce(pg_stats.avg_width, 1024)) AS nulldatawidth
                FROM btree_index_atts
                JOIN pg_attribute ON pg_attribute.attrelid = btree_index_atts.index_oid 
                    AND pg_attribute.attnum = btree_index_atts.attnum
                JOIN pg_stats ON pg_stats.schemaname = btree_index_atts.nspname
                    AND ((pg_stats.tablename = btree_index_atts.table_name 
                        AND pg_stats.attname = pg_attribute.attname))
                GROUP BY index_name, table_name
            ),
            bloat_summary AS (
                SELECT
                    index_name,
                    table_name,
                    bs * (relpages)::bigint AS real_size,
                    bs * (relpages - est_pages)::bigint AS bloat_size,
                    100 * (relpages - est_pages)::float / relpages AS bloat_pct
                FROM (
                    SELECT
                        index_name,
                        table_name,
                        bs,
                        relpages,
                        coalesce(ceil((reltuples * (6 + nulldatawidth))::numeric / (bs - 20)), 0) AS est_pages
                    FROM index_item_sizes
                    JOIN btree_index_atts USING (index_name, table_name)
                ) AS estimates
                WHERE relpages > 1
            )
            SELECT * FROM bloat_summary WHERE bloat_pct > 20";

        using var command = new NpgsqlCommand(sql, connection);
        using var reader = await command.ExecuteReaderAsync(cancellationToken);

        while (await reader.ReadAsync(cancellationToken))
        {
            fragmentedIndexes.Add(new FragmentedIndex
            {
                IndexName = reader.GetString(0),
                TableName = reader.GetString(1),
                FragmentationPercent = reader.GetDouble(4),
                RecommendedAction = reader.GetDouble(4) > 50 ? "REINDEX" : "VACUUM",
                RebuildStatement = $"REINDEX INDEX CONCURRENTLY {reader.GetString(0)};"
            });
        }

        return fragmentedIndexes;
    }

    private async Task<List<IndexSuggestion>> GenerateIndexSuggestionsAsync(CancellationToken cancellationToken)
    {
        var suggestions = new List<IndexSuggestion>();

        using var connection = new NpgsqlConnection(_connectionString);
        await connection.OpenAsync(cancellationToken);

        // 查找缺失的外键索引
        var fkSql = @"
            SELECT 
                tc.table_name,
                kcu.column_name,
                ccu.table_name AS foreign_table_name,
                ccu.column_name AS foreign_column_name
            FROM information_schema.table_constraints AS tc
            JOIN information_schema.key_column_usage AS kcu
                ON tc.constraint_name = kcu.constraint_name
                AND tc.table_schema = kcu.table_schema
            JOIN information_schema.constraint_column_usage AS ccu
                ON ccu.constraint_name = tc.constraint_name
                AND ccu.table_schema = tc.table_schema
            WHERE tc.constraint_type = 'FOREIGN KEY' 
                AND tc.table_schema = 'public'
                AND NOT EXISTS (
                    SELECT 1
                    FROM pg_indexes
                    WHERE schemaname = 'public'
                        AND tablename = tc.table_name
                        AND indexdef LIKE '%' || kcu.column_name || '%'
                )";

        using var fkCommand = new NpgsqlCommand(fkSql, connection);
        using var fkReader = await fkCommand.ExecuteReaderAsync(cancellationToken);

        while (await fkReader.ReadAsync(cancellationToken))
        {
            var tableName = fkReader.GetString(0);
            var columnName = fkReader.GetString(1);
            
            suggestions.Add(new IndexSuggestion
            {
                TableName = tableName,
                Columns = new List<string> { columnName },
                IndexType = "BTREE",
                Reason = "Missing index on foreign key column",
                ExpectedImprovement = 30,
                CreateStatement = $"CREATE INDEX idx_{tableName}_{columnName} ON {tableName} ({columnName});"
            });
        }

        return suggestions;
    }

    private IndexHealthScore CalculateIndexHealthScore(IndexAnalysisResult result)
    {
        var score = new IndexHealthScore
        {
            OverallScore = 100,
            CategoryScores = new Dictionary<string, int>(),
            Issues = new List<string>(),
            Recommendations = new List<string>()
        };

        // 计算各类别分数
        var unusedPenalty = Math.Min(result.UnusedIndexes.Count * 5, 30);
        var duplicatePenalty = Math.Min(result.DuplicateIndexes.Count * 10, 30);
        var fragmentedPenalty = Math.Min(result.FragmentedIndexes.Count * 3, 20);
        var missingSuggestionsPenalty = Math.Min(result.SuggestedIndexes.Count * 2, 20);

        score.CategoryScores["Unused Indexes"] = 100 - unusedPenalty * 100 / 30;
        score.CategoryScores["Duplicate Indexes"] = 100 - duplicatePenalty * 100 / 30;
        score.CategoryScores["Index Fragmentation"] = 100 - fragmentedPenalty * 100 / 20;
        score.CategoryScores["Missing Indexes"] = 100 - missingSuggestionsPenalty * 100 / 20;

        score.OverallScore = score.OverallScore - unusedPenalty - duplicatePenalty - fragmentedPenalty - missingSuggestionsPenalty;

        // 添加问题和建议
        if (result.UnusedIndexes.Any())
        {
            score.Issues.Add($"Found {result.UnusedIndexes.Count} unused indexes");
            score.Recommendations.Add("Consider dropping unused indexes to save storage space");
        }

        if (result.DuplicateIndexes.Any())
        {
            score.Issues.Add($"Found {result.DuplicateIndexes.Count} duplicate indexes");
            score.Recommendations.Add("Remove duplicate indexes to improve write performance");
        }

        if (result.FragmentedIndexes.Any())
        {
            score.Issues.Add($"Found {result.FragmentedIndexes.Count} fragmented indexes");
            score.Recommendations.Add("Rebuild fragmented indexes to improve query performance");
        }

        if (result.SuggestedIndexes.Any())
        {
            score.Issues.Add($"Found {result.SuggestedIndexes.Count} missing indexes");
            score.Recommendations.Add("Create suggested indexes to improve query performance");
        }

        // 评级
        score.Grade = score.OverallScore switch
        {
            >= 90 => "Excellent",
            >= 80 => "Good",
            >= 70 => "Fair",
            >= 60 => "Poor",
            _ => "Critical"
        };

        return score;
    }

    #endregion

    #region Private Methods - Query Analysis

    private async Task<List<SlowQuery>> GetSlowQueriesAsync(
        TimeSpan threshold,
        int limit,
        CancellationToken cancellationToken)
    {
        var slowQueries = new List<SlowQuery>();

        using var connection = new NpgsqlConnection(_connectionString);
        await connection.OpenAsync(cancellationToken);

        // 使用pg_stat_statements扩展（需要预先启用）
        var sql = @"
            SELECT 
                query,
                mean_exec_time,
                calls,
                total_exec_time,
                rows,
                100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
            FROM pg_stat_statements
            WHERE mean_exec_time > @threshold
            ORDER BY mean_exec_time DESC
            LIMIT @limit";

        try
        {
            using var command = new NpgsqlCommand(sql, connection);
            command.Parameters.AddWithValue("@threshold", threshold.TotalMilliseconds);
            command.Parameters.AddWithValue("@limit", limit);

            using var reader = await command.ExecuteReaderAsync(cancellationToken);
            while (await reader.ReadAsync(cancellationToken))
            {
                var query = reader.GetString(0);
                slowQueries.Add(new SlowQuery
                {
                    QueryId = query.GetHashCode().ToString(),
                    QueryText = query,
                    ExecutionTime = TimeSpan.FromMilliseconds(reader.GetDouble(1)),
                    ExecutionCount = reader.GetInt32(2),
                    RowsReturned = reader.GetInt64(4),
                    QueryType = DetermineQueryType(query),
                    Tables = ExtractTableNames(query)
                });
            }
        }
        catch (PostgresException ex) when (ex.SqlState == "42P01") // table does not exist
        {
            _logger.LogWarning("pg_stat_statements extension not enabled. Using alternative method.");
            // 使用备用方法
            return await GetSlowQueriesFromLogAsync(threshold, limit, cancellationToken);
        }

        return slowQueries;
    }

    private async Task<List<SlowQuery>> GetSlowQueriesFromLogAsync(
        TimeSpan threshold,
        int limit,
        CancellationToken cancellationToken)
    {
        // 备用方法：从pg_stat_activity获取当前慢查询
        var slowQueries = new List<SlowQuery>();

        using var connection = new NpgsqlConnection(_connectionString);
        await connection.OpenAsync(cancellationToken);

        var sql = @"
            SELECT 
                pid,
                now() - pg_stat_activity.query_start AS duration,
                query,
                state
            FROM pg_stat_activity
            WHERE (now() - pg_stat_activity.query_start) > interval '@threshold seconds'
                AND state = 'active'
            ORDER BY duration DESC
            LIMIT @limit";

        using var command = new NpgsqlCommand(sql, connection);
        command.Parameters.AddWithValue("@threshold", threshold.TotalSeconds);
        command.Parameters.AddWithValue("@limit", limit);

        using var reader = await command.ExecuteReaderAsync(cancellationToken);
        while (await reader.ReadAsync(cancellationToken))
        {
            var query = reader.GetString(2);
            slowQueries.Add(new SlowQuery
            {
                QueryId = reader.GetInt32(0).ToString(),
                QueryText = query,
                ExecutionTime = reader.GetTimeSpan(1),
                ExecutionCount = 1,
                QueryType = DetermineQueryType(query),
                Tables = ExtractTableNames(query)
            });
        }

        return slowQueries;
    }

    private List<QueryPattern> AnalyzeQueryPatterns(List<SlowQuery> queries)
    {
        var patterns = new List<QueryPattern>();

        // 按查询类型分组
        var typeGroups = queries.GroupBy(q => q.QueryType);
        foreach (var group in typeGroups)
        {
            patterns.Add(new QueryPattern
            {
                Pattern = $"{group.Key} queries",
                Frequency = group.Count(),
                AverageExecutionTime = TimeSpan.FromMilliseconds(group.Average(q => q.ExecutionTime.TotalMilliseconds)),
                Examples = group.Take(3).Select(q => q.QueryText).ToList(),
                OptimizationHint = GetOptimizationHint(group.Key)
            });
        }

        // 查找缺少WHERE子句的查询
        var noWhereQueries = queries.Where(q => 
            q.QueryType == "SELECT" && 
            !q.QueryText.Contains("WHERE", StringComparison.OrdinalIgnoreCase)).ToList();
        
        if (noWhereQueries.Any())
        {
            patterns.Add(new QueryPattern
            {
                Pattern = "SELECT without WHERE clause",
                Frequency = noWhereQueries.Count,
                AverageExecutionTime = TimeSpan.FromMilliseconds(noWhereQueries.Average(q => q.ExecutionTime.TotalMilliseconds)),
                Examples = noWhereQueries.Take(3).Select(q => q.QueryText).ToList(),
                OptimizationHint = "Add WHERE clause to limit result set"
            });
        }

        return patterns;
    }

    private List<QueryOptimizationSuggestion> GenerateQueryOptimizationSuggestions(List<SlowQuery> queries)
    {
        var suggestions = new List<QueryOptimizationSuggestion>();

        // 分析高频慢查询
        var frequentSlowQueries = queries
            .Where(q => q.ExecutionCount > 100)
            .OrderByDescending(q => q.ExecutionCount * q.ExecutionTime.TotalMilliseconds)
            .Take(10);

        foreach (var query in frequentSlowQueries)
        {
            suggestions.Add(new QueryOptimizationSuggestion
            {
                QueryPattern = TruncateQuery(query.QueryText, 100),
                Issue = "High frequency slow query",
                Suggestion = "Consider adding appropriate indexes or optimizing the query structure",
                ExpectedImprovement = 50,
                Priority = "High"
            });
        }

        // 查找大表扫描
        var tableScans = queries.Where(q => q.RowsExamined > 10000).ToList();
        if (tableScans.Any())
        {
            suggestions.Add(new QueryOptimizationSuggestion
            {
                QueryPattern = "Large table scans",
                Issue = $"Found {tableScans.Count} queries examining > 10000 rows",
                Suggestion = "Add indexes on filter columns or implement pagination",
                ExpectedImprovement = 70,
                Priority = "High"
            });
        }

        return suggestions;
    }

    #endregion

    #region Private Methods - Database Operations

    private async Task<long> GetDatabaseSizeAsync(NpgsqlConnection connection, CancellationToken cancellationToken)
    {
        var sql = "SELECT pg_database_size(current_database())";
        using var command = new NpgsqlCommand(sql, connection);
        return (long)await command.ExecuteScalarAsync(cancellationToken)!;
    }

    private async Task<(int tableCount, int indexCount, long totalRows, long dataSize, long indexSize)> 
        GetTableAndIndexStatsAsync(NpgsqlConnection connection, CancellationToken cancellationToken)
    {
        var sql = @"
            SELECT 
                COUNT(DISTINCT tablename) as table_count,
                COUNT(DISTINCT indexname) as index_count,
                SUM(n_live_tup) as total_rows,
                SUM(pg_table_size(schemaname||'.'||tablename)) as data_size,
                SUM(pg_indexes_size(schemaname||'.'||tablename)) as index_size
            FROM pg_stat_user_tables
            WHERE schemaname = 'public'";

        using var command = new NpgsqlCommand(sql, connection);
        using var reader = await command.ExecuteReaderAsync(cancellationToken);
        
        if (await reader.ReadAsync(cancellationToken))
        {
            return (
                reader.GetInt32(0),
                reader.GetInt32(1),
                reader.GetInt64(2),
                reader.GetInt64(3),
                reader.GetInt64(4)
            );
        }

        return (0, 0, 0, 0, 0);
    }

    private async Task<int> GetConnectionCountAsync(NpgsqlConnection connection, CancellationToken cancellationToken)
    {
        var sql = "SELECT count(*) FROM pg_stat_activity";
        using var command = new NpgsqlCommand(sql, connection);
        return Convert.ToInt32(await command.ExecuteScalarAsync(cancellationToken));
    }

    private async Task<int> GetActiveQueryCountAsync(NpgsqlConnection connection, CancellationToken cancellationToken)
    {
        var sql = "SELECT count(*) FROM pg_stat_activity WHERE state = 'active'";
        using var command = new NpgsqlCommand(sql, connection);
        return Convert.ToInt32(await command.ExecuteScalarAsync(cancellationToken));
    }

    private async Task<double> GetCacheHitRatioAsync(NpgsqlConnection connection, CancellationToken cancellationToken)
    {
        var sql = @"
            SELECT 
                sum(heap_blks_hit) / (sum(heap_blks_hit) + sum(heap_blks_read)) as ratio
            FROM pg_statio_user_tables";
        
        using var command = new NpgsqlCommand(sql, connection);
        var result = await command.ExecuteScalarAsync(cancellationToken);
        return result == DBNull.Value ? 0 : Convert.ToDouble(result);
    }

    private async Task<long> GetBufferPoolSizeAsync(NpgsqlConnection connection, CancellationToken cancellationToken)
    {
        var sql = "SELECT setting::bigint * 8192 FROM pg_settings WHERE name = 'shared_buffers'";
        using var command = new NpgsqlCommand(sql, connection);
        return (long)await command.ExecuteScalarAsync(cancellationToken)!;
    }

    private async Task<int> GetTableIndexCountAsync(NpgsqlConnection connection, string tableName, CancellationToken cancellationToken)
    {
        var sql = "SELECT COUNT(*) FROM pg_indexes WHERE tablename = @tableName AND schemaname = 'public'";
        using var command = new NpgsqlCommand(sql, connection);
        command.Parameters.AddWithValue("@tableName", tableName);
        return Convert.ToInt32(await command.ExecuteScalarAsync(cancellationToken));
    }

    private async Task<List<ColumnStatistics>> GetColumnStatisticsAsync(
        NpgsqlConnection connection, 
        string tableName, 
        CancellationToken cancellationToken)
    {
        var columns = new List<ColumnStatistics>();

        var sql = @"
            SELECT 
                a.attname as column_name,
                t.typname as data_type,
                s.n_distinct as distinct_values,
                s.null_frac as null_fraction,
                s.avg_width as avg_width
            FROM pg_attribute a
            JOIN pg_type t ON a.atttypid = t.oid
            LEFT JOIN pg_stats s ON s.tablename = @tableName 
                AND s.attname = a.attname 
                AND s.schemaname = 'public'
            WHERE a.attrelid = @tableName::regclass
                AND a.attnum > 0
                AND NOT a.attisdropped";

        using var command = new NpgsqlCommand(sql, connection);
        command.Parameters.AddWithValue("@tableName", tableName);

        using var reader = await command.ExecuteReaderAsync(cancellationToken);
        while (await reader.ReadAsync(cancellationToken))
        {
            columns.Add(new ColumnStatistics
            {
                ColumnName = reader.GetString(0),
                DataType = reader.GetString(1),
                DistinctValues = reader.IsDBNull(2) ? 0 : Convert.ToInt32(reader.GetFloat(2)),
                NullPercent = reader.IsDBNull(3) ? 0 : reader.GetFloat(3) * 100,
                AverageLength = reader.IsDBNull(4) ? 0 : reader.GetFloat(4)
            });
        }

        return columns;
    }

    #endregion

    #region Private Methods - Optimization

    private async Task<OptimizationAction> AnalyzeTablesAsync(NpgsqlConnection connection, CancellationToken cancellationToken)
    {
        var action = new OptimizationAction
        {
            ActionType = "ANALYZE",
            Target = "All tables",
            // Status will be set based on Success flag
        };

        var startTime = DateTime.UtcNow;

        try
        {
            using var command = new NpgsqlCommand("ANALYZE", connection);
            await command.ExecuteNonQueryAsync(cancellationToken);
            
            action.Success = true;
            action.Result = "Statistics updated successfully";
            action.EndTime = DateTime.UtcNow;
        }
        catch (Exception ex)
        {
            action.Success = false;
            action.Error = ex.Message;
            action.EndTime = DateTime.UtcNow;
        }

        // Duration is calculated from StartTime and EndTime
        return action;
    }

    private async Task<OptimizationAction> VacuumDatabaseAsync(NpgsqlConnection connection, CancellationToken cancellationToken)
    {
        var action = new OptimizationAction
        {
            ActionType = "VACUUM",
            Target = "Database",
            // Status will be set based on Success flag
        };

        var startTime = DateTime.UtcNow;

        try
        {
            // VACUUM cannot run inside a transaction block
            await connection.CloseAsync();
            
            var builder = new NpgsqlConnectionStringBuilder(connection.ConnectionString);
            builder.Pooling = false;
            
            using var vacuumConnection = new NpgsqlConnection(builder.ToString());
            await vacuumConnection.OpenAsync(cancellationToken);
            
            using var command = new NpgsqlCommand("VACUUM ANALYZE", vacuumConnection);
            await command.ExecuteNonQueryAsync(cancellationToken);
            
            await connection.OpenAsync(cancellationToken);
            
            action.Success = true;
            action.EndTime = DateTime.UtcNow;
            action.Result = "Vacuum completed successfully";
        }
        catch (Exception ex)
        {
            action.Success = false;
            action.Error = ex.Message;
            action.EndTime = DateTime.UtcNow;
        }

        // Duration is calculated from StartTime and EndTime
        return action;
    }

    private async Task<OptimizationAction> RebuildIndexAsync(
        NpgsqlConnection connection, 
        FragmentedIndex index, 
        CancellationToken cancellationToken)
    {
        var action = new OptimizationAction
        {
            ActionType = "REINDEX",
            Target = index.IndexName,
            // Status will be set based on Success flag
        };

        var startTime = DateTime.UtcNow;

        try
        {
            using var command = new NpgsqlCommand($"REINDEX INDEX CONCURRENTLY {index.IndexName}", connection);
            await command.ExecuteNonQueryAsync(cancellationToken);
            
            action.Success = true;
            action.EndTime = DateTime.UtcNow;
            action.Result = $"Index rebuilt successfully. Fragmentation was {index.FragmentationPercent:F2}%";
        }
        catch (Exception ex)
        {
            action.Success = false;
            action.Error = ex.Message;
            action.EndTime = DateTime.UtcNow;
        }

        // Duration is calculated from StartTime and EndTime
        return action;
    }

    private async Task<OptimizationAction> UpdateStatisticsAsync(NpgsqlConnection connection, CancellationToken cancellationToken)
    {
        var action = new OptimizationAction
        {
            ActionType = "UPDATE STATISTICS",
            Target = "All tables",
            // Status will be set based on Success flag
        };

        var startTime = DateTime.UtcNow;

        try
        {
            // 更新所有表的统计信息
            var sql = @"
                DO $$
                DECLARE
                    r RECORD;
                BEGIN
                    FOR r IN SELECT tablename FROM pg_tables WHERE schemaname = 'public'
                    LOOP
                        EXECUTE 'ANALYZE ' || quote_ident(r.tablename);
                    END LOOP;
                END $$;";

            using var command = new NpgsqlCommand(sql, connection);
            await command.ExecuteNonQueryAsync(cancellationToken);
            
            action.Success = true;
            action.EndTime = DateTime.UtcNow;
            action.Result = "Statistics updated for all tables";
        }
        catch (Exception ex)
        {
            action.Success = false;
            action.Error = ex.Message;
            action.EndTime = DateTime.UtcNow;
        }

        // Duration is calculated from StartTime and EndTime
        return action;
    }

    private async Task<long> CalculateSpaceReclaimedAsync(NpgsqlConnection connection, CancellationToken cancellationToken)
    {
        // 这是一个简化的实现，实际应该比较优化前后的空间使用
        var sql = @"
            SELECT 
                sum(pg_total_relation_size(schemaname||'.'||tablename)) -
                sum(pg_relation_size(schemaname||'.'||tablename))
            FROM pg_tables
            WHERE schemaname = 'public'";

        using var command = new NpgsqlCommand(sql, connection);
        var result = await command.ExecuteScalarAsync(cancellationToken);
        return result == DBNull.Value ? 0 : Convert.ToInt64(result);
    }

    #endregion

    #region Private Methods - Cleanup

    private async Task<int> DeleteOldLogsAsync(NpgsqlConnection connection, int retentionDays, CancellationToken cancellationToken)
    {
        var sql = @"
            DELETE FROM AuditLogs 
            WHERE CreatedAt < @cutoffDate";

        using var command = new NpgsqlCommand(sql, connection);
        command.Parameters.AddWithValue("@cutoffDate", DateTime.UtcNow.AddDays(-retentionDays));
        
        return await command.ExecuteNonQueryAsync(cancellationToken);
    }

    private async Task<int> DeleteOrphanedRecordsAsync(NpgsqlConnection connection, CancellationToken cancellationToken)
    {
        var totalDeleted = 0;

        // 删除孤立的会话消息
        var sql1 = @"
            DELETE FROM ConversationMessages 
            WHERE ConversationId NOT IN (SELECT Id FROM Conversations)";
        
        using (var command = new NpgsqlCommand(sql1, connection))
        {
            totalDeleted += await command.ExecuteNonQueryAsync(cancellationToken);
        }

        // 删除孤立的订阅记录
        var sql2 = @"
            DELETE FROM Subscriptions 
            WHERE UserId NOT IN (SELECT Id FROM CustomerUsers)";
        
        using (var command = new NpgsqlCommand(sql2, connection))
        {
            totalDeleted += await command.ExecuteNonQueryAsync(cancellationToken);
        }

        return totalDeleted;
    }

    private async Task<int> CompactTablesAsync(NpgsqlConnection connection, CancellationToken cancellationToken)
    {
        var tablesCompacted = 0;

        // 获取需要压缩的表
        var sql = @"
            SELECT tablename 
            FROM pg_tables 
            WHERE schemaname = 'public'
            ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
            LIMIT 10";

        var tables = new List<string>();
        using (var command = new NpgsqlCommand(sql, connection))
        using (var reader = await command.ExecuteReaderAsync(cancellationToken))
        {
            while (await reader.ReadAsync(cancellationToken))
            {
                tables.Add(reader.GetString(0));
            }
        }

        // 压缩每个表
        foreach (var table in tables)
        {
            try
            {
                using var command = new NpgsqlCommand($"VACUUM FULL {table}", connection);
                await command.ExecuteNonQueryAsync(cancellationToken);
                tablesCompacted++;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to compact table {Table}", table);
            }
        }

        return tablesCompacted;
    }

    private async Task ClearTempTablesAsync(NpgsqlConnection connection, CancellationToken cancellationToken)
    {
        var sql = @"
            SELECT tablename 
            FROM pg_tables 
            WHERE tablename LIKE 'tmp_%' OR tablename LIKE 'temp_%'";

        var tempTables = new List<string>();
        using (var command = new NpgsqlCommand(sql, connection))
        using (var reader = await command.ExecuteReaderAsync(cancellationToken))
        {
            while (await reader.ReadAsync(cancellationToken))
            {
                tempTables.Add(reader.GetString(0));
            }
        }

        foreach (var table in tempTables)
        {
            try
            {
                using var command = new NpgsqlCommand($"DROP TABLE IF EXISTS {table}", connection);
                await command.ExecuteNonQueryAsync(cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to drop temp table {Table}", table);
            }
        }
    }

    private async Task<long> ArchiveOldDataAsync(NpgsqlConnection connection, int thresholdDays, CancellationToken cancellationToken)
    {
        // 这是一个简化的实现，实际应该将数据移动到归档表
        var cutoffDate = DateTime.UtcNow.AddDays(-thresholdDays);
        long totalArchived = 0;

        // 归档旧的会话记录
        var sql = @"
            WITH archived AS (
                DELETE FROM Conversations 
                WHERE LastMessageAt < @cutoffDate
                RETURNING *
            )
            SELECT COUNT(*) FROM archived";

        using var command = new NpgsqlCommand(sql, connection);
        command.Parameters.AddWithValue("@cutoffDate", cutoffDate);
        
        var result = await command.ExecuteScalarAsync(cancellationToken);
        totalArchived += Convert.ToInt64(result ?? 0);

        return totalArchived;
    }

    #endregion

    #region Private Methods - Performance Monitoring

    private async Task<PerformanceSnapshot> CapturePerformanceSnapshotAsync(CancellationToken cancellationToken)
    {
        var snapshot = new PerformanceSnapshot
        {
            Timestamp = DateTime.UtcNow
        };

        using var connection = new NpgsqlConnection(_connectionString);
        await connection.OpenAsync(cancellationToken);

        // 获取查询统计
        var qpsSql = @"
            SELECT 
                (SELECT count(*) FROM pg_stat_activity WHERE state = 'active') as active_queries,
                (SELECT count(*) FROM pg_stat_activity) as total_connections";

        using (var command = new NpgsqlCommand(qpsSql, connection))
        using (var reader = await command.ExecuteReaderAsync(cancellationToken))
        {
            if (await reader.ReadAsync(cancellationToken))
            {
                snapshot.QueriesPerSecond = reader.GetInt32(0);
                snapshot.ConnectionCount = reader.GetInt32(1);
            }
        }

        // 获取缓存统计
        var cacheSql = @"
            SELECT 
                sum(blks_hit) as hits,
                sum(blks_read) as misses
            FROM pg_stat_database
            WHERE datname = current_database()";

        using (var command = new NpgsqlCommand(cacheSql, connection))
        using (var reader = await command.ExecuteReaderAsync(cancellationToken))
        {
            if (await reader.ReadAsync(cancellationToken))
            {
                snapshot.BufferPoolHits = reader.GetInt64(0);
                snapshot.BufferPoolMisses = reader.GetInt64(1);
            }
        }

        // 获取锁统计
        var lockSql = @"
            SELECT 
                count(*) FILTER (WHERE NOT granted) as lock_waits,
                count(*) FILTER (WHERE mode = 'AccessExclusiveLock') as potential_deadlocks
            FROM pg_locks";

        using (var command = new NpgsqlCommand(lockSql, connection))
        using (var reader = await command.ExecuteReaderAsync(cancellationToken))
        {
            if (await reader.ReadAsync(cancellationToken))
            {
                snapshot.LockWaits = reader.GetInt32(0);
                snapshot.Deadlocks = reader.GetInt32(1);
            }
        }

        return snapshot;
    }

    private List<PerformanceAlert> CheckPerformanceAlerts(PerformanceSnapshot snapshot)
    {
        var alerts = new List<PerformanceAlert>();

        // 检查连接数
        if (snapshot.ConnectionCount > 100)
        {
            alerts.Add(new PerformanceAlert
            {
                Timestamp = snapshot.Timestamp,
                AlertType = "High Connection Count",
                Severity = snapshot.ConnectionCount > 200 ? "Critical" : "Warning",
                Description = $"Connection count is {snapshot.ConnectionCount}",
                Value = snapshot.ConnectionCount,
                Threshold = 100,
                Source = "Database"
            });
        }

        // 检查锁等待
        if (snapshot.LockWaits > 10)
        {
            alerts.Add(new PerformanceAlert
            {
                Timestamp = snapshot.Timestamp,
                AlertType = "Lock Contention",
                Severity = "Warning",
                Description = $"{snapshot.LockWaits} queries waiting for locks",
                Value = snapshot.LockWaits,
                Threshold = 10,
                Source = "Database"
            });
        }

        // 检查缓存命中率
        var cacheHitRatio = snapshot.BufferPoolHits > 0 ? 
            (double)snapshot.BufferPoolHits / (snapshot.BufferPoolHits + snapshot.BufferPoolMisses) : 0;
        
        if (cacheHitRatio < 0.9)
        {
            alerts.Add(new PerformanceAlert
            {
                Timestamp = snapshot.Timestamp,
                AlertType = "Low Cache Hit Ratio",
                Severity = "Warning",
                Description = $"Cache hit ratio is {cacheHitRatio:P}",
                Value = cacheHitRatio,
                Threshold = 0.9,
                Source = "Database"
            });
        }

        return alerts;
    }

    private PerformanceSummary GeneratePerformanceSummary(List<PerformanceSnapshot> snapshots)
    {
        if (!snapshots.Any())
            return new PerformanceSummary();

        var summary = new PerformanceSummary
        {
            AverageQPS = snapshots.Average(s => s.QueriesPerSecond),
            PeakQPS = snapshots.Max(s => s.QueriesPerSecond),
            TotalDeadlocks = snapshots.Sum(s => s.Deadlocks),
            TotalLockWaits = snapshots.Sum(s => s.LockWaits)
        };

        // 计算缓存命中率
        var totalHits = snapshots.Sum(s => s.BufferPoolHits);
        var totalMisses = snapshots.Sum(s => s.BufferPoolMisses);
        summary.CacheHitRatio = totalHits > 0 ? (double)totalHits / (totalHits + totalMisses) : 0;

        return summary;
    }

    #endregion

    #region Private Methods - Report Generation

    private List<PerformanceRecommendation> GeneratePerformanceRecommendations(DatabasePerformanceReport report)
    {
        var recommendations = new List<PerformanceRecommendation>();

        // 基于索引分析的建议
        if (report.IndexAnalysis.UnusedIndexes.Count > 5)
        {
            recommendations.Add(new PerformanceRecommendation
            {
                Category = "Index Management",
                Issue = $"Found {report.IndexAnalysis.UnusedIndexes.Count} unused indexes",
                Recommendation = "Drop unused indexes to improve write performance and save storage",
                Priority = "Medium",
                ExpectedImprovement = 20,
                Implementation = "Execute the provided DROP INDEX statements during maintenance window"
            });
        }

        if (report.IndexAnalysis.SuggestedIndexes.Any())
        {
            recommendations.Add(new PerformanceRecommendation
            {
                Category = "Index Creation",
                Issue = $"Missing {report.IndexAnalysis.SuggestedIndexes.Count} potentially beneficial indexes",
                Recommendation = "Create suggested indexes to improve query performance",
                Priority = "High",
                ExpectedImprovement = 40,
                Implementation = "Review and execute the CREATE INDEX statements"
            });
        }

        // 基于慢查询分析的建议
        if (report.SlowQueryAnalysis.TotalSlowQueries > 10)
        {
            recommendations.Add(new PerformanceRecommendation
            {
                Category = "Query Optimization",
                Issue = $"Found {report.SlowQueryAnalysis.TotalSlowQueries} slow queries",
                Recommendation = "Optimize slow queries by adding indexes or rewriting queries",
                Priority = "High",
                ExpectedImprovement = 50,
                Implementation = "Analyze query plans and optimize based on suggestions"
            });
        }

        // 基于性能指标的建议
        if (report.PerformanceMetrics.Summary.CacheHitRatio < 0.9)
        {
            recommendations.Add(new PerformanceRecommendation
            {
                Category = "Memory Configuration",
                Issue = $"Low cache hit ratio: {report.PerformanceMetrics.Summary.CacheHitRatio:P}",
                Recommendation = "Increase shared_buffers or optimize queries to reduce data access",
                Priority = "Medium",
                ExpectedImprovement = 30,
                Implementation = "Adjust PostgreSQL memory settings and restart service"
            });
        }

        // 基于统计信息的建议
        var oldStats = report.CurrentStatistics.CollectedAt.AddDays(-7);
        recommendations.Add(new PerformanceRecommendation
        {
            Category = "Maintenance",
            Issue = "Regular maintenance required",
            Recommendation = "Schedule regular VACUUM and ANALYZE operations",
            Priority = "Low",
            ExpectedImprovement = 10,
            Implementation = "Set up automated maintenance jobs"
        });

        return recommendations;
    }

    private PerformanceScore CalculatePerformanceScore(DatabasePerformanceReport report)
    {
        var score = new PerformanceScore
        {
            OverallScore = 100,
            CategoryScores = new Dictionary<string, int>(),
            Strengths = new List<string>(),
            Weaknesses = new List<string>()
        };

        // 索引健康评分
        score.CategoryScores["Index Health"] = report.IndexAnalysis.HealthScore.OverallScore;
        
        // 查询性能评分
        var queryScore = 100;
        if (report.SlowQueryAnalysis.TotalSlowQueries > 50) queryScore -= 30;
        else if (report.SlowQueryAnalysis.TotalSlowQueries > 20) queryScore -= 20;
        else if (report.SlowQueryAnalysis.TotalSlowQueries > 10) queryScore -= 10;
        score.CategoryScores["Query Performance"] = queryScore;

        // 缓存效率评分
        var cacheScore = (int)(report.PerformanceMetrics.Summary.CacheHitRatio * 100);
        score.CategoryScores["Cache Efficiency"] = cacheScore;

        // 资源利用评分
        var resourceScore = 100;
        if (report.PerformanceMetrics.Summary.TotalLockWaits > 100) resourceScore -= 20;
        if (report.PerformanceMetrics.Summary.TotalDeadlocks > 0) resourceScore -= 30;
        score.CategoryScores["Resource Utilization"] = resourceScore;

        // 计算总分
        score.OverallScore = (int)score.CategoryScores.Values.Average();

        // 评级
        score.Grade = score.OverallScore switch
        {
            >= 90 => "Excellent",
            >= 80 => "Good",
            >= 70 => "Fair",
            >= 60 => "Poor",
            _ => "Critical"
        };

        // 识别优势和劣势
        foreach (var category in score.CategoryScores)
        {
            if (category.Value >= 80)
                score.Strengths.Add($"{category.Key}: {category.Value}%");
            else if (category.Value < 60)
                score.Weaknesses.Add($"{category.Key}: {category.Value}%");
        }

        return score;
    }

    #endregion

    #region Private Methods - Utilities

    private List<string> ParseIndexColumns(string indexDef)
    {
        // 简单的列解析，实际可能需要更复杂的解析
        var match = System.Text.RegularExpressions.Regex.Match(indexDef, @"\((.*?)\)");
        if (match.Success)
        {
            return match.Groups[1].Value
                .Split(',')
                .Select(c => c.Trim())
                .ToList();
        }
        return new List<string>();
    }

    private string DetermineQueryType(string query)
    {
        var normalizedQuery = query.Trim().ToUpperInvariant();
        
        if (normalizedQuery.StartsWith("SELECT")) return "SELECT";
        if (normalizedQuery.StartsWith("INSERT")) return "INSERT";
        if (normalizedQuery.StartsWith("UPDATE")) return "UPDATE";
        if (normalizedQuery.StartsWith("DELETE")) return "DELETE";
        if (normalizedQuery.StartsWith("CREATE")) return "CREATE";
        if (normalizedQuery.StartsWith("ALTER")) return "ALTER";
        if (normalizedQuery.StartsWith("DROP")) return "DROP";
        
        return "OTHER";
    }

    private List<string> ExtractTableNames(string query)
    {
        var tables = new List<string>();
        
        // 简单的表名提取，实际需要SQL解析器
        var fromMatch = System.Text.RegularExpressions.Regex.Match(
            query, 
            @"FROM\s+(\w+)", 
            System.Text.RegularExpressions.RegexOptions.IgnoreCase);
        
        if (fromMatch.Success)
        {
            tables.Add(fromMatch.Groups[1].Value);
        }
        
        var joinMatches = System.Text.RegularExpressions.Regex.Matches(
            query, 
            @"JOIN\s+(\w+)", 
            System.Text.RegularExpressions.RegexOptions.IgnoreCase);
        
        foreach (System.Text.RegularExpressions.Match match in joinMatches)
        {
            tables.Add(match.Groups[1].Value);
        }
        
        return tables.Distinct().ToList();
    }

    private string GetOptimizationHint(string queryType)
    {
        return queryType switch
        {
            "SELECT" => "Consider adding indexes on WHERE and JOIN columns",
            "INSERT" => "Use batch inserts for better performance",
            "UPDATE" => "Ensure WHERE clause uses indexed columns",
            "DELETE" => "Consider archiving instead of deleting large amounts of data",
            _ => "Review query structure and execution plan"
        };
    }

    private string TruncateQuery(string query, int maxLength)
    {
        if (query.Length <= maxLength)
            return query;
        
        return query.Substring(0, maxLength - 3) + "...";
    }

    private void ParseQueryPlan(string planJson, QueryPlanAnalysis analysis)
    {
        // 简化的解析实现，实际需要完整的JSON解析
        try
        {
            analysis.PlanNodes = new List<QueryPlanNode>();
            
            // 查找使用的索引
            if (planJson.Contains("Index Scan"))
            {
                analysis.UsedIndexes.Add("Index scan detected");
            }
            
            if (planJson.Contains("Seq Scan"))
            {
                analysis.MissingIndexes.Add("Sequential scan detected - consider adding index");
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to parse query plan");
        }
    }

    private List<QueryPlanIssue> AnalyzeQueryPlanIssues(QueryPlanAnalysis analysis)
    {
        var issues = new List<QueryPlanIssue>();
        
        if (analysis.MissingIndexes.Any())
        {
            issues.Add(new QueryPlanIssue
            {
                IssueType = "Missing Index",
                Description = "Query performing sequential scan",
                Impact = "Slow query performance on large tables",
                Resolution = "Create appropriate indexes"
            });
        }
        
        return issues;
    }

    private List<string> GenerateQueryOptimizations(QueryPlanAnalysis analysis)
    {
        var optimizations = new List<string>();
        
        if (analysis.MissingIndexes.Any())
        {
            optimizations.Add("Add indexes to avoid sequential scans");
        }
        
        if (analysis.EstimatedCost > 10000)
        {
            optimizations.Add("Consider query restructuring to reduce cost");
        }
        
        return optimizations;
    }

    private string GenerateIndexName(IndexSuggestion suggestion)
    {
        var columns = string.Join("_", suggestion.Columns);
        return $"idx_{suggestion.TableName}_{columns}".ToLowerInvariant();
    }

    #endregion
}