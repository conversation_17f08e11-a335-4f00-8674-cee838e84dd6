using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Infrastructure.Caching;
using WhimLabAI.Infrastructure.Data;
using WhimLabAI.Infrastructure.Data.Repositories;
using WhimLabAI.Infrastructure.PaymentGateways;
using WhimLabAI.Infrastructure.Security;
using WhimLabAI.Infrastructure.Auditing;
using WhimLabAI.Infrastructure.Compliance;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Infrastructure.ExternalServices;
using WhimLabAI.Infrastructure.Messaging;
using WhimLabAI.Infrastructure.Extensions;
using Microsoft.IO;

namespace WhimLabAI.Infrastructure;

public static class DependencyInjection
{
    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        // 配置数据库
        var connectionString = configuration.GetConnectionString("DefaultConnection");
        
        // Configure Npgsql data source with dynamic JSON support
        var dataSourceBuilder = new Npgsql.NpgsqlDataSourceBuilder(connectionString);
        dataSourceBuilder.EnableDynamicJson();
        var dataSource = dataSourceBuilder.Build();
        
        // 注册 DbContext
        services.AddDbContext<WhimLabAIDbContext>((serviceProvider, options) =>
        {
            options.UseNpgsql(dataSource, 
                npgsqlOptions =>
                {
                    npgsqlOptions.MigrationsAssembly(typeof(WhimLabAIDbContext).Assembly.FullName);
                    npgsqlOptions.EnableRetryOnFailure(3);
                });
            
            // 开发环境启用敏感数据日志
            if (configuration.GetValue<bool>("Development"))
            {
                options.EnableSensitiveDataLogging();
            }
        });

        // 注册自定义的 DbContextFactory 以避免生命周期冲突
        services.AddSingleton<IDbContextFactory<WhimLabAIDbContext>>(serviceProvider =>
        {
            var optionsBuilder = new DbContextOptionsBuilder<WhimLabAIDbContext>();
            optionsBuilder.UseNpgsql(dataSource,
                npgsqlOptions =>
                {
                    npgsqlOptions.MigrationsAssembly(typeof(WhimLabAIDbContext).Assembly.FullName);
                    npgsqlOptions.EnableRetryOnFailure(3);
                });

            // 开发环境启用敏感数据日志
            if (configuration.GetValue<bool>("Development"))
            {
                optionsBuilder.EnableSensitiveDataLogging();
            }

            return new Data.WhimLabAIDbContextFactory(serviceProvider, optionsBuilder.Options);
        });

        // 配置Redis
        var redisConnectionString = configuration.GetConnectionString("Redis");
        if (!string.IsNullOrEmpty(redisConnectionString))
        {
            services.AddSingleton<IConnectionMultiplexer>(sp =>
            {
                var configurationOptions = ConfigurationOptions.Parse(redisConnectionString);
                configurationOptions.AbortOnConnectFail = false;
                return ConnectionMultiplexer.Connect(configurationOptions);
            });
        }
        
        // 配置分布式缓存
        services.AddDistributedMemoryCache();

        // 注册仓储
        services.AddScoped<IUnitOfWork, UnitOfWork>();
        services.AddScoped(typeof(IRepository<>), typeof(Repository<>));
        services.AddScoped<ICustomerUserRepository, CustomerUserRepository>();
        services.AddScoped<IAdminUserRepository, AdminUserRepository>();
        services.AddScoped<IRecoveryCodeRepository, RecoveryCodeRepository>();
        services.AddScoped<IRoleRepository, RoleRepository>();
        services.AddScoped<IPermissionRepository, PermissionRepository>();
        services.AddScoped<IAgentRepository, AgentRepository>();
        services.AddScoped<IConversationRepository, ConversationRepository>();
        services.AddScoped<ISubscriptionRepository, SubscriptionRepository>();
        services.AddScoped<ISubscriptionPlanRepository, SubscriptionPlanRepository>();
        services.AddScoped<IUsageRecordRepository, UsageRecordRepository>();
        services.AddScoped<IOrderRepository, OrderRepository>();
        services.AddScoped<IPaymentRepository, PaymentRepository>();
        services.AddScoped<IInvoiceRepository, InvoiceRepository>();
        services.AddScoped<IRefundRepository, RefundRepository>();
        services.AddScoped<IRefundRecordRepository, RefundRecordRepository>();
        services.AddScoped<ITokenUsageRepository, TokenUsageRepository>();
        services.AddScoped<IDifyConversationMappingRepository, DifyConversationMappingRepository>();
        services.AddScoped<IKnowledgeBaseRepository, KnowledgeBaseRepository>();
        services.AddScoped<IDocumentRepository, DocumentRepository>();
        services.AddScoped<IDocumentChunkRepository, DocumentChunkRepository>();
        services.AddScoped<ICouponRepository, CouponRepository>();
        services.AddScoped<IQRCodeSessionRepository, QRCodeSessionRepository>();
        services.AddScoped<IAuditLogRepository, AuditLogRepository>();
        services.AddScoped<INotificationRepository, NotificationRepository>();
        services.AddScoped<IAnalyticsRepository, AnalyticsRepository>();
        services.AddScoped<ISystemEventRepository, SystemEventRepository>();
        services.AddScoped<ICustomerSessionRepository, CustomerSessionRepository>();
        services.AddScoped<IAdminSessionRepository, AdminSessionRepository>();
        services.AddScoped<IFileRepository, FileRepository>();
        services.AddScoped<IVerificationCodeRepository, VerificationCodeRepository>();
        services.AddScoped<IAgentLikeRepository, AgentLikeRepository>();
        services.AddScoped<IAgentCategoryRepository, AgentCategoryRepository>();
        services.AddScoped<IAgentRatingRepository, AgentRatingRepository>();
        services.AddScoped<IRatingHelpfulnessRepository, RatingHelpfulnessRepository>();
        services.AddScoped<ITokenPackageRepository, TokenPackageRepository>();

        // 注册基础设施服务
        services.AddScoped<ICacheService, RedisCacheService>();
        services.AddScoped<IJwtTokenService, JwtTokenService>();
        services.AddScoped<ITotpService, TotpService>();
        services.AddScoped<IQRCodeGeneratorService, Services.QRCodeGeneratorService>();
        
        // 注册缓存相关服务
        services.Configure<CacheOptions>(configuration.GetSection("Cache"));
        services.AddScoped<ICacheInvalidationService, CacheInvalidationService>();
        services.AddHostedService<CacheWarmingService>();
        
        // 注册加密服务
        services.Configure<EncryptionOptions>(configuration.GetSection("Security:Encryption"));
        services.AddSingleton<IDataEncryptionService, DataEncryptionService>();
        services.AddSingleton<IReversibleEncryptionService, ReversibleEncryptionService>();
        
        // 注册审计服务
        services.Configure<AuditOptions>(configuration.GetSection("Security:Audit"));
        services.AddSingleton<IAuditLogger, AuditLogService>();
        services.AddSingleton<RecyclableMemoryStreamManager>();
        services.AddHttpContextAccessor();
        
        // 注册合规服务
        services.AddScoped<IComplianceService, ComplianceService>();
        services.AddScoped<IDataExportService, DataExportService>();
        services.AddScoped<IDataAnonymizationService, DataAnonymizationService>();
        
        // 注册安全验证服务
        services.AddScoped<ISecurityValidationService, SecurityValidationService>();
        
        // 注册PDF服务
        services.AddScoped<IPdfService, Pdf.PdfService>();
        
        // 注册性能基准测试服务
        services.AddScoped<IPerformanceBenchmarkService, Performance.PerformanceBenchmarkService>();
        services.AddScoped<ILoadTestingService, Performance.LoadTestingService>();
        services.AddScoped<IDatabasePerformanceService, Performance.DatabasePerformanceService>();
        services.AddScoped<ICacheOptimizationService, Performance.CacheOptimizationService>();
        
        // 注册后台服务
        services.AddHostedService<BackgroundServices.SubscriptionMaintenanceService>();
        services.AddHostedService<BackgroundServices.TokenResetService>();
        services.AddHostedService<BackgroundServices.UsageCleanupService>();
        services.AddHostedService<AuditLogBackgroundService>();
        services.AddHostedService<BackgroundServices.PerformanceMonitoringService>();
        services.AddHostedService<BackgroundServices.SessionCleanupService>();
        services.AddHostedService<BackgroundJobs.QRCodeSessionCleanupJob>();

        // 注册支付网关
        services.AddScoped<IPaymentGateway, AlipayGateway>();
        services.AddScoped<IPaymentGateway, WeChatPayGateway>();
        services.AddScoped<WhimLabAI.Abstractions.Infrastructure.IPaymentGatewayManager, PaymentGatewayManager>();
        services.AddScoped<AlipayGateway>();
        services.AddScoped<WeChatPayGateway>();
        
        // 注册消息队列服务
        services.AddSingleton<IMessageQueueService, MessageQueueService>();
        
        // 注册支付相关服务
        services.AddScoped<IPaymentNotificationService, PaymentNotificationService>();
        services.AddSingleton<PaymentMetricsCollector>();
        services.AddSingleton<IPaymentMetricsCollector>(sp => sp.GetRequiredService<PaymentMetricsCollector>());
        services.AddScoped<PaymentMonitoringService>();
        services.AddScoped<IPaymentMonitoringService>(sp => sp.GetRequiredService<PaymentMonitoringService>());
        services.AddScoped<PaymentValidationService>();
        services.AddScoped<IPaymentValidationService>(sp => sp.GetRequiredService<PaymentValidationService>());
        services.AddScoped<PaymentTestHelper>();
        
        // 配置AI提供商设置
        services.Configure<AI.Configuration.DifyProviderSettings>(configuration.GetSection("AI:Dify"));
        
        // 注册AI提供商
        services.AddScoped<IAIProvider, AI.Providers.SemanticKernelProvider>();
        services.AddScoped<IDifyProvider, AI.Providers.DifyProvider>();
        services.AddScoped<IAIProvider, AI.Providers.MockAIProvider>();
        services.AddScoped<IAIProviderManager, AI.AIProviderManager>();
        services.AddScoped<AI.Providers.SemanticKernelProvider>();
        services.AddScoped<AI.Providers.DifyProvider>();
        services.AddScoped<AI.Providers.MockAIProvider>();
        
        // 注册HTTP客户端
        services.AddHttpClient("Dify", client =>
        {
            client.Timeout = TimeSpan.FromSeconds(300); // 5分钟超时，适合长对话
            client.DefaultRequestHeaders.Add("Accept", "application/json");
            client.DefaultRequestHeaders.Add("User-Agent", "WhimLabAI/1.0");
        });

        // 注册存储服务
        var storageProvider = configuration.GetValue<string>("Storage:Provider");
        switch (storageProvider?.ToLower())
        {
            case "minio":
                services.Configure<Storage.MinIOOptions>(configuration.GetSection("Storage:MinIO"));
                services.AddScoped<IStorageService, Storage.MinIOStorageService>();
                break;
            case "local":
            default:
                services.AddScoped<IStorageService, Storage.LocalStorageService>();
                break;
        }
        
        // 注册消息服务
        services.Configure<EmailOptions>(configuration.GetSection("Email"));
        services.Configure<SmsOptions>(configuration.GetSection("Sms"));
        services.AddScoped<IMessageService, MessageService>();
        
        // 注册邮件服务 - 必须在消息队列消费者之前注册
        services.AddScoped<IEmailService, Email.EmailService>();
        
        // 注册IP地理位置服务
        services.AddScoped<IIpGeolocationService, IpGeolocationService>();
        
        // 注册验证服务配置
        services.Configure<VerificationCodeOptions>(configuration.GetSection("Verification:Code"));
        services.Configure<CaptchaOptions>(configuration.GetSection("Verification:Captcha"));
        
        // 注册OAuth配置
        services.Configure<OAuth.Configuration.OAuthOptions>(configuration.GetSection("OAuth"));
        services.AddScoped<IOAuthService, OAuth.OAuthService>();
        
        // 注册向量数据库服务
        services.AddSingleton<VectorDatabase.InMemoryVectorDatabase>();
        services.AddScoped<VectorDatabase.PgVectorDatabase>(provider =>
        {
            var dbContext = provider.GetRequiredService<WhimLabAIDbContext>();
            var logger = provider.GetRequiredService<ILogger<VectorDatabase.PgVectorDatabase>>();
            var connectionString = configuration.GetConnectionString("DefaultConnection") ?? "";
            return new VectorDatabase.PgVectorDatabase(dbContext, logger, connectionString);
        });
        services.AddScoped<IVectorDatabaseFactory, VectorDatabase.VectorDatabaseFactory>();
        
        // 注册文档处理服务
        services.AddScoped<IDocumentProcessor, DocumentProcessing.DocumentProcessor>();
        
        // 注册嵌入服务
        services.AddScoped<IEmbeddingService, AI.EmbeddingService>();

        // 注册监控服务
        services.AddSingleton<IMetricsService, Monitoring.PrometheusMetricsService>();
        services.AddHostedService<Monitoring.BusinessMetricsCollector>();
        
        // 注册事件驱动架构 - 在所有依赖服务注册之后
        services.AddEventDrivenArchitecture(configuration);
        services.AddDomainEventHandlers();
        
        return services;
    }
}