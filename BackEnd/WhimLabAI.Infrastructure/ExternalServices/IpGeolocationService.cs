using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using WhimLabAI.Abstractions.Infrastructure;

namespace WhimLabAI.Infrastructure.ExternalServices;

/// <summary>
/// IP地理位置解析服务实现
/// </summary>
public class IpGeolocationService : IIpGeolocationService
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IDistributedCache _cache;
    private readonly IConfiguration _configuration;
    private readonly ILogger<IpGeolocationService> _logger;
    private const string CacheKeyPrefix = "ipgeo:";
    private const int CacheExpirationMinutes = 1440; // 24小时

    public IpGeolocationService(
        IHttpClientFactory httpClientFactory,
        IDistributedCache cache,
        IConfiguration configuration,
        ILogger<IpGeolocationService> logger)
    {
        _httpClientFactory = httpClientFactory;
        _cache = cache;
        _configuration = configuration;
        _logger = logger;
    }

    public async Task<IpGeolocationInfo?> GetLocationAsync(string ipAddress, CancellationToken cancellationToken = default)
    {
        try
        {
            // 检查缓存
            var cacheKey = $"{CacheKeyPrefix}{ipAddress}";
            var cachedData = await _cache.GetStringAsync(cacheKey, cancellationToken);
            if (!string.IsNullOrEmpty(cachedData))
            {
                return JsonSerializer.Deserialize<IpGeolocationInfo>(cachedData);
            }

            // 对于本地IP地址，返回默认位置
            if (IsLocalIpAddress(ipAddress))
            {
                var localInfo = new IpGeolocationInfo
                {
                    IpAddress = ipAddress,
                    City = "Local",
                    Country = "Local Network",
                    CountryCode = "LO",
                    Region = "Local",
                    Latitude = 0,
                    Longitude = 0,
                    TimeZone = TimeZoneInfo.Local.Id,
                    Isp = "Local Network",
                    IsVpn = false,
                    IsProxy = false
                };
                
                await CacheLocationAsync(cacheKey, localInfo, cancellationToken);
                return localInfo;
            }

            // 在生产环境中，这里应该调用实际的IP地理位置API
            // 例如: ip-api.com, ipgeolocation.io, ipinfo.io 等
            // 现在使用模拟数据进行开发
            var mockInfo = GenerateMockGeolocationInfo(ipAddress);
            
            await CacheLocationAsync(cacheKey, mockInfo, cancellationToken);
            return mockInfo;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解析IP地址 {IpAddress} 的地理位置时发生错误", ipAddress);
            return null;
        }
    }

    public double CalculateDistance(double lat1, double lon1, double lat2, double lon2)
    {
        const double R = 6371; // 地球半径（公里）
        var dLat = ToRadians(lat2 - lat1);
        var dLon = ToRadians(lon2 - lon1);
        
        var a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) +
                Math.Cos(ToRadians(lat1)) * Math.Cos(ToRadians(lat2)) *
                Math.Sin(dLon / 2) * Math.Sin(dLon / 2);
        
        var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
        return R * c;
    }

    public bool IsAnomalousLocation(IpGeolocationInfo currentLocation, IEnumerable<IpGeolocationInfo> previousLocations, double thresholdKm = 500)
    {
        if (currentLocation?.Latitude == null || currentLocation.Longitude == null)
            return false;

        var recentLocations = previousLocations
            .Where(l => l.Latitude.HasValue && l.Longitude.HasValue)
            .Take(5) // 只考虑最近5个位置
            .ToList();

        if (!recentLocations.Any())
            return false;

        // 检查当前位置是否远离所有历史位置
        foreach (var location in recentLocations)
        {
            var distance = CalculateDistance(
                currentLocation.Latitude.Value,
                currentLocation.Longitude.Value,
                location.Latitude!.Value,
                location.Longitude!.Value);

            if (distance <= thresholdKm)
                return false; // 如果接近任何一个历史位置，则不是异常
        }

        return true; // 远离所有历史位置，标记为异常
    }

    private bool IsLocalIpAddress(string ipAddress)
    {
        return ipAddress == "::1" || 
               ipAddress == "127.0.0.1" ||
               ipAddress.StartsWith("192.168.") ||
               ipAddress.StartsWith("10.") ||
               ipAddress.StartsWith("172.16.") ||
               ipAddress.StartsWith("172.17.") ||
               ipAddress.StartsWith("172.18.") ||
               ipAddress.StartsWith("172.19.") ||
               ipAddress.StartsWith("172.2") && ipAddress[6] >= '0' && ipAddress[6] <= '9' ||
               ipAddress.StartsWith("172.3") && ipAddress[6] >= '0' && ipAddress[6] <= '1';
    }

    private double ToRadians(double degrees)
    {
        return degrees * Math.PI / 180;
    }

    private async Task CacheLocationAsync(string key, IpGeolocationInfo info, CancellationToken cancellationToken)
    {
        var json = JsonSerializer.Serialize(info);
        await _cache.SetStringAsync(key, json, new DistributedCacheEntryOptions
        {
            AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(CacheExpirationMinutes)
        }, cancellationToken);
    }

    private IpGeolocationInfo GenerateMockGeolocationInfo(string ipAddress)
    {
        // 模拟数据生成 - 在生产环境中应该调用实际的API
        var random = new Random(ipAddress.GetHashCode());
        var cities = new[]
        {
            ("北京", "中国", "CN", "Beijing", 39.9042, 116.4074, "Asia/Shanghai"),
            ("上海", "中国", "CN", "Shanghai", 31.2304, 121.4737, "Asia/Shanghai"),
            ("深圳", "中国", "CN", "Guangdong", 22.5431, 114.0579, "Asia/Shanghai"),
            ("广州", "中国", "CN", "Guangdong", 23.1291, 113.2644, "Asia/Shanghai"),
            ("杭州", "中国", "CN", "Zhejiang", 30.2741, 120.1551, "Asia/Shanghai"),
            ("成都", "中国", "CN", "Sichuan", 30.5728, 104.0668, "Asia/Shanghai"),
            ("New York", "United States", "US", "New York", 40.7128, -74.0060, "America/New_York"),
            ("London", "United Kingdom", "GB", "England", 51.5074, -0.1278, "Europe/London"),
            ("Tokyo", "Japan", "JP", "Tokyo", 35.6762, 139.6503, "Asia/Tokyo"),
            ("Singapore", "Singapore", "SG", "Singapore", 1.3521, 103.8198, "Asia/Singapore")
        };

        var city = cities[random.Next(cities.Length)];
        
        return new IpGeolocationInfo
        {
            IpAddress = ipAddress,
            City = city.Item1,
            Country = city.Item2,
            CountryCode = city.Item3,
            Region = city.Item4,
            Latitude = city.Item5,
            Longitude = city.Item6,
            TimeZone = city.Item7,
            Isp = "Mock ISP Provider",
            IsVpn = random.Next(100) < 5, // 5%概率是VPN
            IsProxy = random.Next(100) < 3 // 3%概率是代理
        };
    }
}