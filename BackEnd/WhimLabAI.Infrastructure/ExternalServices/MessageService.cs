using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.DependencyInjection;
using System.Net;
using System.Net.Mail;
using System.Text;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Domain.Repositories;

namespace WhimLabAI.Infrastructure.ExternalServices;

public class MessageService : IMessageService
{
    private readonly ILogger<MessageService> _logger;
    private readonly EmailOptions _emailOptions;
    private readonly SmsOptions _smsOptions;
    private readonly IServiceProvider _serviceProvider;

    public MessageService(
        ILogger<MessageService> logger,
        IOptions<EmailOptions> emailOptions,
        IOptions<SmsOptions> smsOptions,
        IServiceProvider serviceProvider)
    {
        _logger = logger;
        _emailOptions = emailOptions.Value;
        _smsOptions = smsOptions.Value;
        _serviceProvider = serviceProvider;
    }

    public async Task<SendResult> SendEmailAsync(EmailMessage message, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!_emailOptions.Enabled)
            {
                _logger.LogInformation("邮件服务未启用，模拟发送邮件: To={To}, Subject={Subject}", 
                    string.Join(", ", message.To), message.Subject);
                
                // 开发环境下的模拟发送
                await Task.Delay(100, cancellationToken);
                return new SendResult
                {
                    Success = true,
                    MessageId = Guid.NewGuid().ToString(),
                    Extra = new Dictionary<string, object> { ["simulated"] = true }
                };
            }

            using var smtpClient = new SmtpClient(_emailOptions.SmtpServer, _emailOptions.SmtpPort)
            {
                Credentials = new NetworkCredential(_emailOptions.Username, _emailOptions.Password),
                EnableSsl = _emailOptions.EnableSsl
            };

            var mailMessage = new MailMessage
            {
                From = new MailAddress(_emailOptions.FromAddress, _emailOptions.FromName),
                Subject = message.Subject,
                Body = message.Body,
                IsBodyHtml = message.IsHtml
            };

            // 添加收件人
            foreach (var to in message.To)
            {
                mailMessage.To.Add(to);
            }

            // 添加抄送
            if (message.Cc != null)
            {
                foreach (var cc in message.Cc)
                {
                    mailMessage.CC.Add(cc);
                }
            }

            // 添加密送
            if (message.Bcc != null)
            {
                foreach (var bcc in message.Bcc)
                {
                    mailMessage.Bcc.Add(bcc);
                }
            }

            // 添加附件
            if (message.Attachments != null)
            {
                foreach (var attachment in message.Attachments)
                {
                    var stream = new MemoryStream(attachment.Content);
                    var mailAttachment = new Attachment(stream, attachment.FileName, attachment.ContentType);
                    mailMessage.Attachments.Add(mailAttachment);
                }
            }

            // 添加自定义头部
            if (message.Headers != null)
            {
                foreach (var header in message.Headers)
                {
                    mailMessage.Headers.Add(header.Key, header.Value);
                }
            }

            await smtpClient.SendMailAsync(mailMessage);

            _logger.LogInformation("邮件发送成功: To={To}, Subject={Subject}", 
                string.Join(", ", message.To), message.Subject);

            return new SendResult
            {
                Success = true,
                MessageId = Guid.NewGuid().ToString()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "邮件发送失败: To={To}, Subject={Subject}", 
                string.Join(", ", message.To), message.Subject);

            return new SendResult
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }

    public async Task<SendResult> SendSmsAsync(SmsMessage message, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!_smsOptions.Enabled)
            {
                _logger.LogInformation("短信服务未启用，模拟发送短信: Phone={Phone}, Content={Content}", 
                    message.PhoneNumber, message.Content);
                
                // 开发环境下的模拟发送
                await Task.Delay(100, cancellationToken);
                return new SendResult
                {
                    Success = true,
                    MessageId = Guid.NewGuid().ToString(),
                    Extra = new Dictionary<string, object> { ["simulated"] = true }
                };
            }

            // 根据配置的短信服务提供商发送短信
            
            switch (_smsOptions.Provider.ToLower())
            {
                case "aliyun":
                    return await SendAliyunSmsAsync(message, cancellationToken);
                case "tencent":
                    return await SendTencentSmsAsync(message, cancellationToken);
                case "twilio":
                    return await SendTwilioSmsAsync(message, cancellationToken);
                default:
                    _logger.LogWarning("未知的短信服务提供商: {Provider}", _smsOptions.Provider);
                    return new SendResult
                    {
                        Success = false,
                        ErrorMessage = "未配置短信服务提供商"
                    };
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "短信发送失败: Phone={Phone}", message.PhoneNumber);
            return new SendResult
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }

    public async Task<SendResult> SendNotificationAsync(NotificationMessage message, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("发送系统通知: UserId={UserId}, Type={Type}, Title={Title}", 
                message.UserId, message.Type, message.Title);

            // Create notification entity
            var metadataJson = message.Data != null 
                ? System.Text.Json.JsonSerializer.Serialize(message.Data)
                : null;
                
            var notification = new Domain.Entities.Notification.Notification(
                message.UserId,
                message.Title,
                message.Content,
                message.Type,
                "info", // Default level
                metadataJson,
                null); // No expiration by default

            // Save to database using unit of work from DI container
            using var scope = _serviceProvider.CreateScope();
            var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();
            
            await unitOfWork.Repository<Domain.Entities.Notification.Notification>()
                .AddAsync(notification, cancellationToken);
            await unitOfWork.SaveChangesAsync(cancellationToken);

            // Send real-time notification by default
            if (message.SaveToDatabase)
            {
                try
                {
                    // Get hub type dynamically to avoid compile-time dependency
                    var hubContextType = scope.ServiceProvider
                        .GetServices(typeof(object))
                        .FirstOrDefault(s => s.GetType().Name.Contains("IHubContext") && 
                                             s.GetType().GetGenericArguments().Any(t => t.Name == "NotificationHub"));
                    
                    if (hubContextType != null)
                    {
                        // Use reflection to send notification
                        var clientsProperty = hubContextType.GetType().GetProperty("Clients");
                        if (clientsProperty != null)
                        {
                            var clients = clientsProperty.GetValue(hubContextType);
                            var userMethod = clients?.GetType().GetMethod("User");
                            if (userMethod != null)
                            {
                                var userClient = userMethod.Invoke(clients, new object[] { message.UserId.ToString() });
                                var sendAsyncMethod = userClient?.GetType().GetMethod("SendAsync");
                                if (sendAsyncMethod != null)
                                {
                                    await (Task)sendAsyncMethod.Invoke(userClient, new object[] 
                                    { 
                                        "ReceiveNotification", 
                                        new
                                        {
                                            Id = notification.Id,
                                            Title = notification.Title,
                                            Content = notification.Content,
                                            Type = notification.Type,
                                            Level = notification.Level,
                                            CreatedAt = notification.CreatedAt
                                        }, 
                                        cancellationToken 
                                    });
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to send real-time notification, but notification was saved");
                }
            }

            // Send push notification if user has enabled it
            var userRepository = unitOfWork.Repository<Domain.Entities.User.CustomerUser>();
            var user = await userRepository.GetByIdAsync(message.UserId, cancellationToken);
            
            if (user?.NotificationSetting?.SystemNotification == true)
            {
                // Here you would integrate with push notification services like FCM, APNS
                // For now, we'll log it
                _logger.LogInformation("Push notification would be sent to user {UserId}", message.UserId);
            }

            return new SendResult
            {
                Success = true,
                MessageId = notification.Id.ToString(),
                Extra = new Dictionary<string, object>
                {
                    ["notificationId"] = notification.Id,
                    ["realTimeSent"] = message.SaveToDatabase
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "系统通知发送失败: UserId={UserId}", message.UserId);
            return new SendResult
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }

    public async Task<List<MessageTemplate>> GetTemplatesAsync(string messageType, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("获取消息模板: Type={Type}", messageType);
            
            // For now, just return built-in templates
            // In the future, you can implement database storage for custom templates
            await Task.CompletedTask; // Satisfy async requirement
            
            return GetBuiltInTemplates(messageType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取消息模板失败: Type={Type}", messageType);
            return new List<MessageTemplate>();
        }
    }
    
    private List<MessageTemplate> GetBuiltInTemplates(string messageType)
    {
        var templates = new List<MessageTemplate>();
        
        switch (messageType.ToLower())
        {
            case "email":
                templates.AddRange(new[]
                {
                    new MessageTemplate
                    {
                        Id = "verification_email",
                        Name = "邮箱验证",
                        Type = "email",
                        Content = "【邮箱验证】您的验证码是：{code}，有效期为{expiration}分钟。请勿将此验证码告诉他人。",
                        Variables = new List<string> { "code", "expiration" },
                        IsActive = true
                    },
                    new MessageTemplate
                    {
                        Id = "password_reset",
                        Name = "密码重置",
                        Type = "email",
                        Content = "【密码重置】您正在重置密码，验证码是：{code}，有效期为{expiration}分钟。如果这不是您的操作，请忽略此邮件。",
                        Variables = new List<string> { "code", "expiration" },
                        IsActive = true
                    },
                    new MessageTemplate
                    {
                        Id = "welcome_email",
                        Name = "欢迎邮件",
                        Type = "email",
                        Content = "【欢迎加入WhimLabAI】亲爱的{username}，欢迎加入WhimLabAI！开始探索AI的无限可能吧。",
                        Variables = new List<string> { "username" },
                        IsActive = true
                    }
                });
                break;
                
            case "sms":
                templates.AddRange(new[]
                {
                    new MessageTemplate
                    {
                        Id = "sms_verification",
                        Name = "短信验证",
                        Type = "sms",
                        Content = "【WhimLabAI】您的验证码是：{code}，有效期为{expiration}分钟。",
                        Variables = new List<string> { "code", "expiration" },
                        IsActive = true
                    },
                    new MessageTemplate
                    {
                        Id = "sms_login",
                        Name = "登录验证",
                        Type = "sms",
                        Content = "【WhimLabAI】您正在登录，验证码是：{code}，有效期为{expiration}分钟。",
                        Variables = new List<string> { "code", "expiration" },
                        IsActive = true
                    }
                });
                break;
                
            case "notification":
                templates.AddRange(new[]
                {
                    new MessageTemplate
                    {
                        Id = "subscription_renewal",
                        Name = "订阅续费",
                        Type = "notification",
                        Content = "您的{plan}订阅即将到期，请及时续费以继续享受服务。",
                        Variables = new List<string> { "plan" },
                        IsActive = true
                    },
                    new MessageTemplate
                    {
                        Id = "token_usage",
                        Name = "Token使用提醒",
                        Type = "notification",
                        Content = "您本月已使用{used}个Token，剩余{remaining}个Token。",
                        Variables = new List<string> { "used", "remaining" },
                        IsActive = true
                    },
                    new MessageTemplate
                    {
                        Id = "agent_published",
                        Name = "Agent发布通知",
                        Type = "notification",
                        Content = "您创建的Agent \"{agentName}\" 已成功发布到市场。",
                        Variables = new List<string> { "agentName" },
                        IsActive = true
                    }
                });
                break;
        }
        
        return templates;
    }

    private async Task<SendResult> SendAliyunSmsAsync(SmsMessage message, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("使用阿里云发送短信: Phone={Phone}", message.PhoneNumber);
            
            // Validate Aliyun configuration
            if (string.IsNullOrEmpty(_smsOptions.Providers.Aliyun.AccessKeyId) ||
                string.IsNullOrEmpty(_smsOptions.Providers.Aliyun.AccessKeySecret))
            {
                _logger.LogError("阿里云短信配置不完整: AccessKeyId或AccessKeySecret为空");
                return new SendResult
                {
                    Success = false,
                    ErrorMessage = "阿里云短信配置不完整"
                };
            }
            
            // Aliyun SMS API parameters
            var timestamp = DateTime.UtcNow.ToString("yyyy-MM-dd'T'HH:mm:ss'Z'");
            var nonce = Guid.NewGuid().ToString("N");
            var signatureMethod = "HMAC-SHA1";
            var signatureVersion = "1.0";
            var action = "SendSms";
            var version = "2017-05-25";
            
            var parameters = new Dictionary<string, string>
            {
                ["Action"] = action,
                ["Version"] = version,
                ["Format"] = "JSON",
                ["SignatureMethod"] = signatureMethod,
                ["SignatureVersion"] = signatureVersion,
                ["SignatureNonce"] = nonce,
                ["Timestamp"] = timestamp,
                ["AccessKeyId"] = _smsOptions.Providers.Aliyun.AccessKeyId,
                ["PhoneNumbers"] = message.PhoneNumber,
                ["SignName"] = message.SignName ?? _smsOptions.Providers.Aliyun.SignName,
                ["TemplateCode"] = message.TemplateId ?? _smsOptions.Providers.Aliyun.TemplateCode
            };
            
            // Add template parameters
            if (message.TemplateParams != null && message.TemplateParams.Any())
            {
                parameters["TemplateParam"] = System.Text.Json.JsonSerializer.Serialize(message.TemplateParams);
            }
            
            // Generate signature
            var signature = GenerateAliyunSignature(parameters, _smsOptions.Providers.Aliyun.AccessKeySecret, "GET");
            parameters["Signature"] = signature;
            
            // Build request URL
            var queryString = string.Join("&", parameters.Select(kv => $"{kv.Key}={Uri.EscapeDataString(kv.Value)}"));
            var baseUrl = _smsOptions.Providers.Aliyun.Domain ?? "dysmsapi.aliyuncs.com";
            var requestUrl = $"https://{baseUrl}/?{queryString}";
            
            // Send request
            using var httpClient = new HttpClient();
            var response = await httpClient.GetAsync(requestUrl, cancellationToken);
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
            
            if (response.IsSuccessStatusCode)
            {
                var responseData = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(responseContent);
                if (responseData != null && responseData.ContainsKey("Code") && responseData["Code"].ToString() == "OK")
                {
                    return new SendResult
                    {
                        Success = true,
                        MessageId = responseData.ContainsKey("BizId") ? responseData["BizId"].ToString() : Guid.NewGuid().ToString(),
                        Extra = new Dictionary<string, object> 
                        { 
                            ["provider"] = "aliyun",
                            ["requestId"] = responseData.ContainsKey("RequestId") ? responseData["RequestId"].ToString() : ""
                        }
                    };
                }
                else
                {
                    var errorMessage = responseData?.ContainsKey("Message") == true ? responseData["Message"].ToString() : "Unknown error";
                    _logger.LogError("阿里云短信发送失败: {Error}", errorMessage);
                    return new SendResult
                    {
                        Success = false,
                        ErrorMessage = errorMessage
                    };
                }
            }
            else
            {
                _logger.LogError("阿里云短信API请求失败: {StatusCode} - {Content}", response.StatusCode, responseContent);
                return new SendResult
                {
                    Success = false,
                    ErrorMessage = $"API请求失败: {response.StatusCode}"
                };
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "阿里云短信发送异常");
            return new SendResult
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }
    
    private string GenerateAliyunSignature(Dictionary<string, string> parameters, string accessKeySecret, string httpMethod)
    {
        // Sort parameters by key
        var sortedParams = parameters.OrderBy(kv => kv.Key, StringComparer.Ordinal);
        
        // Build canonical query string
        var canonicalQueryString = string.Join("&", sortedParams.Select(kv => 
            $"{Uri.EscapeDataString(kv.Key)}={Uri.EscapeDataString(kv.Value)}"));
        
        // Build string to sign
        var stringToSign = $"{httpMethod}&{Uri.EscapeDataString("/")}&{Uri.EscapeDataString(canonicalQueryString)}";
        
        // Calculate HMAC-SHA1
        using var hmac = new System.Security.Cryptography.HMACSHA1(Encoding.UTF8.GetBytes(accessKeySecret + "&"));
        var signatureBytes = hmac.ComputeHash(Encoding.UTF8.GetBytes(stringToSign));
        return Convert.ToBase64String(signatureBytes);
    }

    private async Task<SendResult> SendTencentSmsAsync(SmsMessage message, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("使用腾讯云发送短信: Phone={Phone}", message.PhoneNumber);
            
            // Validate Tencent configuration
            if (string.IsNullOrEmpty(_smsOptions.Providers.Tencent.SecretId) ||
                string.IsNullOrEmpty(_smsOptions.Providers.Tencent.SecretKey) ||
                string.IsNullOrEmpty(_smsOptions.Providers.Tencent.SmsSdkAppId))
            {
                _logger.LogError("腾讯云短信配置不完整: SecretId、SecretKey或SmsSdkAppId为空");
                return new SendResult
                {
                    Success = false,
                    ErrorMessage = "腾讯云短信配置不完整"
                };
            }
            
            // Tencent SMS API configuration
            var endpoint = "sms.tencentcloudapi.com";
            var service = "sms";
            var version = "2021-01-11";
            var action = "SendSms";
            var region = _smsOptions.Providers.Tencent.Region;
            
            // Build request payload
            var requestPayload = new Dictionary<string, object>
            {
                ["SmsSdkAppId"] = _smsOptions.Providers.Tencent.SmsSdkAppId,
                ["SignName"] = message.SignName ?? _smsOptions.Providers.Tencent.SignName,
                ["TemplateId"] = message.TemplateId ?? _smsOptions.Providers.Tencent.TemplateId,
                ["PhoneNumberSet"] = new[] { FormatPhoneNumberForTencent(message.PhoneNumber) }
            };
            
            // Add template parameters
            if (message.TemplateParams != null && message.TemplateParams.Any())
            {
                requestPayload["TemplateParamSet"] = message.TemplateParams.Values.ToArray();
            }
            
            var jsonPayload = System.Text.Json.JsonSerializer.Serialize(requestPayload);
            
            // Generate authorization headers using TC3-HMAC-SHA256
            var timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            var date = DateTime.UtcNow.ToString("yyyy-MM-dd");
            var headers = GenerateTencentAuthHeaders(
                _smsOptions.Providers.Tencent.SecretId,
                _smsOptions.Providers.Tencent.SecretKey,
                service,
                endpoint,
                action,
                version,
                region,
                jsonPayload,
                timestamp);
            
            // Send request
            using var httpClient = new HttpClient();
            var request = new HttpRequestMessage(HttpMethod.Post, $"https://{endpoint}/");
            request.Content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");
            
            foreach (var header in headers)
            {
                request.Headers.TryAddWithoutValidation(header.Key, header.Value);
            }
            
            var response = await httpClient.SendAsync(request, cancellationToken);
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
            
            if (response.IsSuccessStatusCode)
            {
                var responseData = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(responseContent);
                if (responseData != null && responseData.ContainsKey("Response"))
                {
                    var responseObj = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(responseData["Response"].ToString());
                    
                    if (responseObj != null && responseObj.ContainsKey("SendStatusSet"))
                    {
                        var sendStatusArray = System.Text.Json.JsonSerializer.Deserialize<List<Dictionary<string, object>>>(responseObj["SendStatusSet"].ToString());
                        if (sendStatusArray?.Count > 0)
                        {
                            var status = sendStatusArray[0];
                            if (status.ContainsKey("Code") && status["Code"].ToString() == "Ok")
                            {
                                return new SendResult
                                {
                                    Success = true,
                                    MessageId = status.ContainsKey("MessageId") ? status["MessageId"].ToString() : Guid.NewGuid().ToString(),
                                    Extra = new Dictionary<string, object> 
                                    { 
                                        ["provider"] = "tencent",
                                        ["requestId"] = responseObj.ContainsKey("RequestId") ? responseObj["RequestId"].ToString() : ""
                                    }
                                };
                            }
                            else
                            {
                                var errorMessage = status.ContainsKey("Message") ? status["Message"].ToString() : "Unknown error";
                                _logger.LogError("腾讯云短信发送失败: {Error}", errorMessage);
                                return new SendResult
                                {
                                    Success = false,
                                    ErrorMessage = errorMessage
                                };
                            }
                        }
                    }
                    
                    // Check for error response
                    if (responseObj != null && responseObj.ContainsKey("Error"))
                    {
                        var errorObj = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(responseObj["Error"].ToString());
                        var errorMessage = errorObj?.ContainsKey("Message") == true ? errorObj["Message"].ToString() : "Unknown error";
                        _logger.LogError("腾讯云短信API错误: {Error}", errorMessage);
                        return new SendResult
                        {
                            Success = false,
                            ErrorMessage = errorMessage
                        };
                    }
                }
                
                _logger.LogError("腾讯云短信响应格式异常: {Response}", responseContent);
                return new SendResult
                {
                    Success = false,
                    ErrorMessage = "响应格式异常"
                };
            }
            else
            {
                _logger.LogError("腾讯云短信API请求失败: {StatusCode} - {Content}", response.StatusCode, responseContent);
                return new SendResult
                {
                    Success = false,
                    ErrorMessage = $"API请求失败: {response.StatusCode}"
                };
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "腾讯云短信发送异常");
            return new SendResult
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }
    
    private string FormatPhoneNumberForTencent(string phoneNumber)
    {
        // Tencent SMS requires country code, default to +86 for China
        if (!phoneNumber.StartsWith("+"))
        {
            return "+86" + phoneNumber.TrimStart('0');
        }
        return phoneNumber;
    }
    
    private Dictionary<string, string> GenerateTencentAuthHeaders(
        string secretId, 
        string secretKey, 
        string service,
        string endpoint,
        string action,
        string version,
        string region,
        string payload,
        long timestamp)
    {
        var algorithm = "TC3-HMAC-SHA256";
        var date = DateTimeOffset.FromUnixTimeSeconds(timestamp).UtcDateTime.ToString("yyyy-MM-dd");
        var canonicalUri = "/";
        var canonicalQueryString = "";
        var canonicalHeaders = $"content-type:application/json\nhost:{endpoint}\nx-tc-action:{action.ToLower()}\n";
        var signedHeaders = "content-type;host;x-tc-action";
        
        // Calculate payload hash
        var payloadHash = ComputeSHA256Hash(payload);
        
        // Build canonical request
        var canonicalRequest = $"POST\n{canonicalUri}\n{canonicalQueryString}\n{canonicalHeaders}\n{signedHeaders}\n{payloadHash}";
        
        // Build string to sign
        var credentialScope = $"{date}/{service}/tc3_request";
        var hashedCanonicalRequest = ComputeSHA256Hash(canonicalRequest);
        var stringToSign = $"{algorithm}\n{timestamp}\n{credentialScope}\n{hashedCanonicalRequest}";
        
        // Calculate signature
        var tc3SecretKey = Encoding.UTF8.GetBytes("TC3" + secretKey);
        var secretDate = ComputeHMACSHA256(tc3SecretKey, date);
        var secretService = ComputeHMACSHA256(secretDate, service);
        var secretSigning = ComputeHMACSHA256(secretService, "tc3_request");
        var signature = BitConverter.ToString(ComputeHMACSHA256(secretSigning, stringToSign)).Replace("-", "").ToLower();
        
        // Build authorization header
        var authorization = $"{algorithm} Credential={secretId}/{credentialScope}, SignedHeaders={signedHeaders}, Signature={signature}";
        
        return new Dictionary<string, string>
        {
            ["Authorization"] = authorization,
            ["Content-Type"] = "application/json",
            ["Host"] = endpoint,
            ["X-TC-Action"] = action,
            ["X-TC-Timestamp"] = timestamp.ToString(),
            ["X-TC-Version"] = version,
            ["X-TC-Region"] = region
        };
    }
    
    private string ComputeSHA256Hash(string text)
    {
        using var sha256 = System.Security.Cryptography.SHA256.Create();
        var bytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(text));
        return BitConverter.ToString(bytes).Replace("-", "").ToLower();
    }
    
    private byte[] ComputeHMACSHA256(byte[] key, string data)
    {
        using var hmac = new System.Security.Cryptography.HMACSHA256(key);
        return hmac.ComputeHash(Encoding.UTF8.GetBytes(data));
    }
    
    private async Task<SendResult> SendTwilioSmsAsync(SmsMessage message, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("使用Twilio发送短信: Phone={Phone}", message.PhoneNumber);
            
            // Validate Twilio configuration
            if (string.IsNullOrEmpty(_smsOptions.Providers.Twilio.AccountSid) ||
                string.IsNullOrEmpty(_smsOptions.Providers.Twilio.AuthToken))
            {
                _logger.LogError("Twilio短信配置不完整: AccountSid或AuthToken为空");
                return new SendResult
                {
                    Success = false,
                    ErrorMessage = "Twilio短信配置不完整"
                };
            }
            
            // Twilio API configuration
            var accountSid = _smsOptions.Providers.Twilio.AccountSid;
            var authToken = _smsOptions.Providers.Twilio.AuthToken;
            var fromPhone = _smsOptions.Providers.Twilio.FromPhoneNumber;
            
            // Validate from phone number when not using MessagingServiceSid
            if (string.IsNullOrEmpty(_smsOptions.Providers.Twilio.MessagingServiceSid) && 
                string.IsNullOrEmpty(fromPhone))
            {
                _logger.LogError("Twilio短信配置错误: 必须提供FromPhoneNumber或MessagingServiceSid");
                return new SendResult
                {
                    Success = false,
                    ErrorMessage = "Twilio短信配置错误: 缺少发送号码"
                };
            }
            
            // Build the Twilio API URL
            var baseUrl = _smsOptions.Providers.Twilio.ApiBaseUrl ?? "https://api.twilio.com/2010-04-01";
            var twilioUrl = $"{baseUrl}/Accounts/{accountSid}/Messages.json";
            
            // Prepare the message data
            var messageBody = message.Content;
            
            // If using template, replace variables
            if (!string.IsNullOrEmpty(message.TemplateId) && message.TemplateParams != null)
            {
                foreach (var param in message.TemplateParams)
                {
                    messageBody = messageBody.Replace($"{{{param.Key}}}", param.Value);
                }
            }
            
            // Format phone number for Twilio (must include country code)
            var toPhone = FormatPhoneNumberForTwilio(message.PhoneNumber);
            
            // Create form data
            var formData = new Dictionary<string, string>
            {
                ["From"] = fromPhone,
                ["To"] = toPhone,
                ["Body"] = messageBody
            };
            
            // Add optional parameters
            if (!string.IsNullOrEmpty(_smsOptions.Providers.Twilio.MessagingServiceSid))
            {
                formData["MessagingServiceSid"] = _smsOptions.Providers.Twilio.MessagingServiceSid;
                formData.Remove("From"); // When using MessagingServiceSid, From is not needed
            }
            
            if (_smsOptions.ExtraConfig.ContainsKey("StatusCallback"))
            {
                formData["StatusCallback"] = _smsOptions.ExtraConfig["StatusCallback"];
            }
            
            // Send request with Basic Authentication
            using var httpClient = new HttpClient();
            
            // Set Basic Authentication header
            var authValue = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{accountSid}:{authToken}"));
            httpClient.DefaultRequestHeaders.Authorization = 
                new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", authValue);
            
            // Create form content
            var content = new FormUrlEncodedContent(formData);
            
            // Send POST request
            var response = await httpClient.PostAsync(twilioUrl, content, cancellationToken);
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
            
            if (response.IsSuccessStatusCode)
            {
                try
                {
                    // Parse Twilio response
                    var responseData = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(responseContent);
                    
                    if (responseData != null)
                    {
                        var messageId = responseData.ContainsKey("sid") ? responseData["sid"].ToString() : Guid.NewGuid().ToString();
                        var status = responseData.ContainsKey("status") ? responseData["status"].ToString() : "unknown";
                        
                        _logger.LogInformation("Twilio短信发送成功: MessageId={MessageId}, Status={Status}", messageId, status);
                        
                        return new SendResult
                        {
                            Success = true,
                            MessageId = messageId,
                            Extra = new Dictionary<string, object> 
                            { 
                                ["provider"] = "twilio",
                                ["status"] = status,
                                ["accountSid"] = accountSid
                            }
                        };
                    }
                    
                    // Response data is null
                    _logger.LogWarning("Twilio响应数据为空，但HTTP状态码表示成功");
                    return new SendResult
                    {
                        Success = true,
                        MessageId = Guid.NewGuid().ToString(),
                        Extra = new Dictionary<string, object> { ["provider"] = "twilio" }
                    };
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "解析Twilio响应失败，但短信可能已发送成功");
                    return new SendResult
                    {
                        Success = true,
                        MessageId = Guid.NewGuid().ToString(),
                        Extra = new Dictionary<string, object> { ["provider"] = "twilio" }
                    };
                }
            }
            else
            {
                // Parse error response
                try
                {
                    var errorData = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(responseContent);
                    var errorMessage = errorData?.ContainsKey("message") == true 
                        ? errorData["message"].ToString() 
                        : $"HTTP {response.StatusCode}";
                    var errorCode = errorData?.ContainsKey("code") == true 
                        ? errorData["code"].ToString() 
                        : "";
                    
                    _logger.LogError("Twilio短信发送失败: Code={ErrorCode}, Message={ErrorMessage}", errorCode, errorMessage);
                    
                    return new SendResult
                    {
                        Success = false,
                        ErrorMessage = $"{errorCode}: {errorMessage}"
                    };
                }
                catch
                {
                    _logger.LogError("Twilio短信API请求失败: {StatusCode} - {Content}", response.StatusCode, responseContent);
                    return new SendResult
                    {
                        Success = false,
                        ErrorMessage = $"API请求失败: {response.StatusCode}"
                    };
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Twilio短信发送异常");
            return new SendResult
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }
    
    private string FormatPhoneNumberForTwilio(string phoneNumber)
    {
        // Twilio requires E.164 format: +[country code][number]
        if (!phoneNumber.StartsWith("+"))
        {
            // If no country code, assume US (+1) or China (+86) based on number format
            if (phoneNumber.Length == 10 && (phoneNumber.StartsWith("2") || phoneNumber.StartsWith("3") || 
                phoneNumber.StartsWith("4") || phoneNumber.StartsWith("5") || phoneNumber.StartsWith("6") || 
                phoneNumber.StartsWith("7") || phoneNumber.StartsWith("8") || phoneNumber.StartsWith("9")))
            {
                // US phone number format
                return "+1" + phoneNumber;
            }
            else if (phoneNumber.Length == 11 && phoneNumber.StartsWith("1"))
            {
                // China mobile number
                return "+86" + phoneNumber;
            }
            else
            {
                // Default to China
                return "+86" + phoneNumber.TrimStart('0');
            }
        }
        return phoneNumber;
    }
}

/// <summary>
/// 邮件配置选项
/// </summary>
public class EmailOptions
{
    public bool Enabled { get; set; } = false;
    public string SmtpServer { get; set; } = string.Empty;
    public int SmtpPort { get; set; } = 587;
    public string Username { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public string FromAddress { get; set; } = string.Empty;
    public string FromName { get; set; } = "WhimLabAI";
    public bool EnableSsl { get; set; } = true;
}

/// <summary>
/// 短信配置选项
/// </summary>
public class SmsOptions
{
    public bool Enabled { get; set; } = false;
    public string Provider { get; set; } = "twilio"; // Default to twilio
    public string SignName { get; set; } = string.Empty;
    public string TemplateCode { get; set; } = string.Empty;
    
    // For backward compatibility
    public string AccessKeyId { get; set; } = string.Empty;
    public string AccessKeySecret { get; set; } = string.Empty;
    
    public SmsProviders Providers { get; set; } = new();
    public SmsRateLimits RateLimits { get; set; } = new();
    public Dictionary<string, string> ExtraConfig { get; set; } = new();
}

public class SmsProviders
{
    public TwilioConfig Twilio { get; set; } = new();
    public AliyunSmsConfig Aliyun { get; set; } = new();
    public TencentSmsConfig Tencent { get; set; } = new();
}

public class TwilioConfig
{
    public string AccountSid { get; set; } = string.Empty;
    public string AuthToken { get; set; } = string.Empty;
    public string FromPhoneNumber { get; set; } = string.Empty;
    public string MessagingServiceSid { get; set; } = string.Empty;
    public string ApiBaseUrl { get; set; } = "https://api.twilio.com/2010-04-01";
    public int MaxRetries { get; set; } = 3;
    public int RetryDelaySeconds { get; set; } = 2;
}

public class AliyunSmsConfig
{
    public string AccessKeyId { get; set; } = string.Empty;
    public string AccessKeySecret { get; set; } = string.Empty;
    public string SignName { get; set; } = string.Empty;
    public string TemplateCode { get; set; } = string.Empty;
    public string RegionId { get; set; } = "cn-hangzhou";
    public string Product { get; set; } = "Dysmsapi";
    public string Domain { get; set; } = "dysmsapi.aliyuncs.com";
    public string Version { get; set; } = "2017-05-25";
    public string Action { get; set; } = "SendSms";
}

public class TencentSmsConfig
{
    public string SecretId { get; set; } = string.Empty;
    public string SecretKey { get; set; } = string.Empty;
    public string SmsSdkAppId { get; set; } = string.Empty;
    public string SignName { get; set; } = string.Empty;
    public string TemplateId { get; set; } = string.Empty;
    public string Region { get; set; } = "ap-guangzhou";
    public string Endpoint { get; set; } = "sms.tencentcloudapi.com";
}

public class SmsRateLimits
{
    public int MaxSmsPerPhonePerDay { get; set; } = 10;
    public int MaxSmsPerPhonePerHour { get; set; } = 5;
    public int MaxSmsPerIpPerDay { get; set; } = 100;
}