using System.Security.Cryptography;
using System.Text;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Infrastructure.PaymentGateways;

/// <summary>
/// 支付安全中间件
/// 提供签名验证、防重放攻击、IP白名单等安全功能
/// </summary>
public class PaymentSecurityMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<PaymentSecurityMiddleware> _logger;
    private readonly IMemoryCache _cache;
    private readonly IConfiguration _configuration;
    private readonly HashSet<string> _whitelistedIps;
    private readonly bool _enableIpWhitelist;
    private readonly int _replayWindowMinutes;

    public PaymentSecurityMiddleware(
        RequestDelegate next,
        ILogger<PaymentSecurityMiddleware> logger,
        IMemoryCache cache,
        IConfiguration configuration)
    {
        _next = next;
        _logger = logger;
        _cache = cache;
        _configuration = configuration;
        
        // Load IP whitelist
        var ipWhitelistConfig = configuration["Payment:Security:IpWhitelist"];
        string[] ipWhitelist;
        
        // Support both array format and comma-separated string from environment variables
        if (!string.IsNullOrEmpty(ipWhitelistConfig) && ipWhitelistConfig.Contains(','))
        {
            ipWhitelist = ipWhitelistConfig.Split(',', StringSplitOptions.RemoveEmptyEntries)
                                          .Select(ip => ip.Trim())
                                          .ToArray();
        }
        else
        {
            ipWhitelist = configuration.GetSection("Payment:Security:IpWhitelist").Get<string[]>() ?? Array.Empty<string>();
        }
        
        _whitelistedIps = new HashSet<string>(ipWhitelist);
        _enableIpWhitelist = configuration.GetValue<bool>("Payment:Security:EnableIpWhitelist");
        _replayWindowMinutes = configuration.GetValue<int>("Payment:Security:ReplayWindowMinutes", 5);
    }

    public async Task InvokeAsync(HttpContext context)
    {
        // Only apply to payment callback endpoints
        if (!IsPaymentCallbackEndpoint(context.Request.Path))
        {
            await _next(context);
            return;
        }

        try
        {
            // 1. IP Whitelist check
            if (_enableIpWhitelist && !IsIpWhitelisted(context))
            {
                _logger.LogWarning("Payment callback from non-whitelisted IP: {IP}", 
                    context.Connection.RemoteIpAddress);
                context.Response.StatusCode = 403;
                await context.Response.WriteAsync("Forbidden");
                return;
            }

            // 2. Request body buffering for signature verification
            context.Request.EnableBuffering();
            
            // 3. Extract payment method from path
            var paymentMethod = ExtractPaymentMethod(context.Request.Path);
            
            // 4. Verify signature based on payment method
            if (!await VerifySignatureAsync(context, paymentMethod))
            {
                _logger.LogWarning("Invalid payment callback signature for {Method}", paymentMethod);
                context.Response.StatusCode = 401;
                await context.Response.WriteAsync("Unauthorized");
                return;
            }

            // 5. Check for replay attack
            if (await IsReplayAttackAsync(context, paymentMethod))
            {
                _logger.LogWarning("Potential replay attack detected for {Method}", paymentMethod);
                context.Response.StatusCode = 400;
                await context.Response.WriteAsync("Invalid Request");
                return;
            }

            // Reset stream position for next middleware
            context.Request.Body.Position = 0;
            
            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in payment security middleware");
            context.Response.StatusCode = 500;
            await context.Response.WriteAsync("Internal Server Error");
        }
    }

    private bool IsPaymentCallbackEndpoint(PathString path)
    {
        var pathValue = path.Value?.ToLower() ?? "";
        return pathValue.Contains("/payment/callback/") || 
               pathValue.Contains("/payment/refund-callback/");
    }

    private bool IsIpWhitelisted(HttpContext context)
    {
        var remoteIp = context.Connection.RemoteIpAddress?.ToString();
        if (string.IsNullOrEmpty(remoteIp))
            return false;
            
        // Allow localhost in development
        if (remoteIp == "::1" || remoteIp == "127.0.0.1")
            return true;
            
        return _whitelistedIps.Contains(remoteIp);
    }

    private PaymentMethod? ExtractPaymentMethod(PathString path)
    {
        var pathValue = path.Value?.ToLower() ?? "";
        
        if (pathValue.Contains("alipay"))
            return PaymentMethod.Alipay;
        if (pathValue.Contains("wechatpay"))
            return PaymentMethod.WeChatPay;
            
        return null;
    }

    private async Task<bool> VerifySignatureAsync(HttpContext context, PaymentMethod? paymentMethod)
    {
        if (!paymentMethod.HasValue)
            return false;

        try
        {
            switch (paymentMethod.Value)
            {
                case PaymentMethod.Alipay:
                    return await VerifyAlipaySignatureAsync(context);
                    
                case PaymentMethod.WeChatPay:
                    return await VerifyWeChatPaySignatureAsync(context);
                    
                default:
                    return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error verifying signature for {Method}", paymentMethod);
            return false;
        }
    }

    private async Task<bool> VerifyAlipaySignatureAsync(HttpContext context)
    {
        // For Alipay, signature is in form data
        if (context.Request.HasFormContentType)
        {
            var form = await context.Request.ReadFormAsync();
            var parameters = form.ToDictionary(kvp => kvp.Key, kvp => kvp.Value.ToString());
            
            // Extract sign for validation
            if (parameters.TryGetValue("sign", out var sign))
            {
                // Basic validation - in production, use proper RSA verification
                return !string.IsNullOrEmpty(sign);
            }
        }
        
        return false;
    }

    private async Task<bool> VerifyWeChatPaySignatureAsync(HttpContext context)
    {
        // For WeChat Pay, read XML body
        using var reader = new StreamReader(context.Request.Body, leaveOpen: true);
        var xmlContent = await reader.ReadToEndAsync();
        context.Request.Body.Position = 0; // Reset position
        
        // Basic validation - check if XML contains required elements
        return xmlContent.Contains("<sign>") && xmlContent.Contains("</sign>");
    }

    private async Task<bool> IsReplayAttackAsync(HttpContext context, PaymentMethod? paymentMethod)
    {
        if (!paymentMethod.HasValue)
            return true;

        try
        {
            // Generate request fingerprint
            var fingerprint = await GenerateRequestFingerprintAsync(context, paymentMethod.Value);
            if (string.IsNullOrEmpty(fingerprint))
                return false;

            // Check if we've seen this request before
            var cacheKey = $"payment_request_{fingerprint}";
            if (_cache.TryGetValue(cacheKey, out _))
            {
                _logger.LogWarning("Duplicate payment request detected: {Fingerprint}", fingerprint);
                return true;
            }

            // Store fingerprint to prevent replay
            _cache.Set(cacheKey, true, TimeSpan.FromMinutes(_replayWindowMinutes));
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking for replay attack");
            return false;
        }
    }

    private async Task<string> GenerateRequestFingerprintAsync(HttpContext context, PaymentMethod paymentMethod)
    {
        var components = new List<string> { paymentMethod.ToString() };

        if (context.Request.HasFormContentType)
        {
            var form = await context.Request.ReadFormAsync();
            
            // Add key fields based on payment method
            if (paymentMethod == PaymentMethod.Alipay)
            {
                components.Add(form["out_trade_no"].ToString());
                components.Add(form["trade_no"].ToString());
                components.Add(form["total_amount"].ToString());
            }
        }
        else
        {
            // For XML content (WeChat Pay)
            using var reader = new StreamReader(context.Request.Body, leaveOpen: true);
            var content = await reader.ReadToEndAsync();
            context.Request.Body.Position = 0;
            
            // Simple hash of content
            using var sha256 = SHA256.Create();
            var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(content));
            components.Add(Convert.ToBase64String(hash));
        }

        return string.Join("_", components);
    }
}

/// <summary>
/// Extension methods for PaymentSecurityMiddleware
/// </summary>
public static class PaymentSecurityMiddlewareExtensions
{
    public static IApplicationBuilder UsePaymentSecurity(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<PaymentSecurityMiddleware>();
    }
}