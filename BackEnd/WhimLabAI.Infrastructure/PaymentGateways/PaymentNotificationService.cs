using System.Text.Json;
using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Domain.Entities.Payment;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Infrastructure.PaymentGateways;

/// <summary>
/// 支付通知服务
/// 处理异步支付结果通知和重试机制
/// </summary>
public class PaymentNotificationService : IPaymentNotificationService
{
    private readonly IMessageQueueService _messageQueue;
    private readonly ILogger<PaymentNotificationService> _logger;
    private readonly INotificationService _notificationService;
    private readonly ICacheService _cacheService;

    public PaymentNotificationService(
        IMessageQueueService messageQueue,
        ILogger<PaymentNotificationService> logger,
        INotificationService notificationService,
        ICacheService cacheService)
    {
        _messageQueue = messageQueue;
        _logger = logger;
        _notificationService = notificationService;
        _cacheService = cacheService;
    }

    public async Task SendPaymentNotificationAsync(
        Guid userId,
        string orderNo,
        decimal amount,
        TransactionStatus status,
        string? failureReason = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var notification = new PaymentNotification
            {
                UserId = userId,
                OrderNo = orderNo,
                Amount = amount,
                Status = status,
                FailureReason = failureReason,
                Timestamp = DateTime.UtcNow
            };

            // Send to message queue for processing
            await _messageQueue.PublishAsync(
                "payment.notifications",
                notification,
                cancellationToken);

            _logger.LogInformation(
                "Payment notification queued for user {UserId}, order {OrderNo}, status {Status}",
                userId, orderNo, status);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, 
                "Failed to queue payment notification for order {OrderNo}", 
                orderNo);
            
            // Fallback to direct notification
            await SendDirectNotificationAsync(userId, orderNo, amount, status, failureReason);
        }
    }

    public async Task ProcessPaymentCallbackAsync(
        string paymentNo,
        PaymentCallbackResult callback,
        int retryCount = 0,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Check if already processed
            var processedKey = $"payment:callback:processed:{paymentNo}";
            if (await _cacheService.ExistsAsync(processedKey))
            {
                _logger.LogInformation(
                    "Payment callback already processed for {PaymentNo}",
                    paymentNo);
                return;
            }

            // Process callback
            var callbackData = new PaymentCallbackData
            {
                PaymentNo = paymentNo,
                Callback = callback,
                RetryCount = retryCount,
                ProcessedAt = DateTime.UtcNow
            };

            // Send to processing queue
            await _messageQueue.PublishAsync(
                "payment.callbacks",
                callbackData,
                cancellationToken);

            // Mark as processed
            await _cacheService.SetAsync(
                processedKey,
                true,
                TimeSpan.FromHours(24),
                cancellationToken);

            _logger.LogInformation(
                "Payment callback queued for processing: {PaymentNo}, Status: {Status}",
                paymentNo, callback.Status);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "Failed to process payment callback for {PaymentNo}, retry count: {RetryCount}",
                paymentNo, retryCount);

            // Retry logic
            if (retryCount < 3)
            {
                await Task.Delay(TimeSpan.FromSeconds(Math.Pow(2, retryCount)), cancellationToken);
                await ProcessPaymentCallbackAsync(paymentNo, callback, retryCount + 1, cancellationToken);
            }
        }
    }

    public async Task SendRefundNotificationAsync(
        Guid userId,
        string refundNo,
        decimal amount,
        RefundStatus status,
        string? failureReason = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var notification = new RefundNotification
            {
                UserId = userId,
                RefundNo = refundNo,
                Amount = amount,
                Status = status,
                FailureReason = failureReason,
                Timestamp = DateTime.UtcNow
            };

            // Send to message queue
            await _messageQueue.PublishAsync(
                "refund.notifications",
                notification,
                cancellationToken);

            _logger.LogInformation(
                "Refund notification queued for user {UserId}, refund {RefundNo}, status {Status}",
                userId, refundNo, status);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "Failed to queue refund notification for refund {RefundNo}",
                refundNo);
        }
    }

    public async Task<bool> CheckPaymentHealthAsync(
        PaymentMethod paymentMethod,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var healthKey = $"payment:health:{paymentMethod}";
            var lastCheckKey = $"payment:health:lastcheck:{paymentMethod}";

            // Check if we have recent health data
            var healthStatus = await _cacheService.GetAsync<PaymentHealthStatus>(healthKey);
            if (healthStatus != null)
            {
                return healthStatus.IsHealthy;
            }

            // Perform health check
            var isHealthy = await PerformHealthCheckAsync(paymentMethod, cancellationToken);
            
            // Cache result
            await _cacheService.SetAsync(
                healthKey,
                new PaymentHealthStatus 
                { 
                    IsHealthy = isHealthy, 
                    CheckedAt = DateTime.UtcNow,
                    PaymentMethod = paymentMethod
                },
                TimeSpan.FromMinutes(5),
                cancellationToken);

            return isHealthy;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "Failed to check payment health for {PaymentMethod}",
                paymentMethod);
            return false;
        }
    }

    private async Task<bool> PerformHealthCheckAsync(
        PaymentMethod paymentMethod,
        CancellationToken cancellationToken)
    {
        // This would typically make a test API call to the payment gateway
        // For now, return true
        await Task.Delay(100, cancellationToken);
        return true;
    }

    private async Task SendDirectNotificationAsync(
        Guid userId,
        string orderNo,
        decimal amount,
        TransactionStatus status,
        string? failureReason)
    {
        try
        {
            var title = status == TransactionStatus.Success ? "支付成功" : "支付失败";
            var content = status == TransactionStatus.Success
                ? $"您的订单 {orderNo} 支付成功，金额：￥{amount}"
                : $"您的订单 {orderNo} 支付失败，原因：{failureReason ?? "未知错误"}";

            await _notificationService.SendNotificationAsync(
                new Shared.Dtos.SendNotificationDto
                {
                    UserId = userId,
                    Title = title,
                    Content = content,
                    Type = "payment",
                    Level = status == TransactionStatus.Success ? "success" : "error"
                });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "Failed to send direct notification for order {OrderNo}",
                orderNo);
        }
    }
}

// Data models for notifications
public class PaymentNotification
{
    public Guid UserId { get; set; }
    public string OrderNo { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public TransactionStatus Status { get; set; }
    public string? FailureReason { get; set; }
    public DateTime Timestamp { get; set; }
}

public class RefundNotification
{
    public Guid UserId { get; set; }
    public string RefundNo { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public RefundStatus Status { get; set; }
    public string? FailureReason { get; set; }
    public DateTime Timestamp { get; set; }
}

public class PaymentCallbackData
{
    public string PaymentNo { get; set; } = string.Empty;
    public PaymentCallbackResult Callback { get; set; } = null!;
    public int RetryCount { get; set; }
    public DateTime ProcessedAt { get; set; }
}

public class PaymentHealthStatus
{
    public bool IsHealthy { get; set; }
    public DateTime CheckedAt { get; set; }
    public PaymentMethod PaymentMethod { get; set; }
}