using System.Diagnostics;
using System.Diagnostics.Metrics;
using Microsoft.Extensions.Logging;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Abstractions.Infrastructure;

namespace WhimLabAI.Infrastructure.PaymentGateways;

/// <summary>
/// 支付指标收集器
/// 用于监控支付性能和异常
/// </summary>
public class PaymentMetricsCollector : IPaymentMetricsCollector, IDisposable
{
    private readonly ILogger<PaymentMetricsCollector> _logger;
    private readonly Meter _meter;
    private readonly Counter<long> _paymentCounter;
    private readonly Counter<long> _paymentErrorCounter;
    private readonly Counter<decimal> _paymentAmountCounter;
    private readonly Histogram<double> _paymentDuration;
    private readonly Counter<long> _refundCounter;
    private readonly Counter<long> _refundErrorCounter;
    private readonly Dictionary<string, ActivitySource> _activitySources;

    public PaymentMetricsCollector(ILogger<PaymentMetricsCollector> logger)
    {
        _logger = logger;
        _meter = new Meter("WhimLabAI.Payment", "1.0.0");
        
        // Initialize counters
        _paymentCounter = _meter.CreateCounter<long>(
            "payment_requests_total",
            description: "Total number of payment requests");
            
        _paymentErrorCounter = _meter.CreateCounter<long>(
            "payment_errors_total",
            description: "Total number of payment errors");
            
        _paymentAmountCounter = _meter.CreateCounter<decimal>(
            "payment_amount_total",
            unit: "CNY",
            description: "Total payment amount processed");
            
        _paymentDuration = _meter.CreateHistogram<double>(
            "payment_duration_seconds",
            unit: "s",
            description: "Payment processing duration");
            
        _refundCounter = _meter.CreateCounter<long>(
            "refund_requests_total",
            description: "Total number of refund requests");
            
        _refundErrorCounter = _meter.CreateCounter<long>(
            "refund_errors_total",
            description: "Total number of refund errors");
            
        // Initialize activity sources for distributed tracing
        _activitySources = new Dictionary<string, ActivitySource>
        {
            ["Payment"] = new ActivitySource("WhimLabAI.Payment"),
            ["Refund"] = new ActivitySource("WhimLabAI.Refund")
        };
    }

    public void RecordPayment(
        PaymentMethod method,
        decimal amount,
        TransactionStatus status,
        TimeSpan duration)
    {
        var tags = new TagList
        {
            { "method", method.ToString() },
            { "status", status.ToString() }
        };

        _paymentCounter.Add(1, tags);
        
        if (status == TransactionStatus.Success)
        {
            _paymentAmountCounter.Add(amount, tags);
        }
        else if (status == TransactionStatus.Failed)
        {
            _paymentErrorCounter.Add(1, tags);
        }
        
        _paymentDuration.Record(duration.TotalSeconds, tags);
        
        _logger.LogInformation(
            "Payment metric recorded: Method={Method}, Amount={Amount}, Status={Status}, Duration={Duration}ms",
            method, amount, status, duration.TotalMilliseconds);
    }

    public void RecordRefund(
        PaymentMethod method,
        decimal amount,
        RefundStatus status,
        TimeSpan duration)
    {
        var tags = new TagList
        {
            { "method", method.ToString() },
            { "status", status.ToString() }
        };

        _refundCounter.Add(1, tags);
        
        if (status == RefundStatus.Failed)
        {
            _refundErrorCounter.Add(1, tags);
        }
        
        _logger.LogInformation(
            "Refund metric recorded: Method={Method}, Amount={Amount}, Status={Status}, Duration={Duration}ms",
            method, amount, status, duration.TotalMilliseconds);
    }

    public Activity? StartPaymentActivity(
        string operationName,
        PaymentMethod method,
        string paymentNo)
    {
        var activity = _activitySources["Payment"].StartActivity(
            operationName,
            ActivityKind.Internal);
            
        activity?.SetTag("payment.method", method.ToString());
        activity?.SetTag("payment.no", paymentNo);
        
        return activity;
    }

    public Activity? StartRefundActivity(
        string operationName,
        PaymentMethod method,
        string refundNo)
    {
        var activity = _activitySources["Refund"].StartActivity(
            operationName,
            ActivityKind.Internal);
            
        activity?.SetTag("payment.method", method.ToString());
        activity?.SetTag("refund.no", refundNo);
        
        return activity;
    }

    public void RecordException(Exception ex, string operation, PaymentMethod? method = null)
    {
        var tags = new TagList
        {
            { "operation", operation },
            { "exception_type", ex.GetType().Name }
        };
        
        if (method.HasValue)
        {
            tags.Add("method", method.Value.ToString());
        }
        
        _paymentErrorCounter.Add(1, tags);
        
        _logger.LogError(ex,
            "Payment operation error: Operation={Operation}, Method={Method}",
            operation, method?.ToString() ?? "Unknown");
    }

    public void Dispose()
    {
        _meter?.Dispose();
        foreach (var source in _activitySources.Values)
        {
            source?.Dispose();
        }
    }
}

/// <summary>
/// 支付监控服务
/// </summary>
public class PaymentMonitoringService : IPaymentMonitoringService
{
    private readonly IPaymentMetricsCollector _metricsCollector;
    private readonly ILogger<PaymentMonitoringService> _logger;

    public PaymentMonitoringService(
        IPaymentMetricsCollector metricsCollector,
        ILogger<PaymentMonitoringService> logger)
    {
        _metricsCollector = metricsCollector;
        _logger = logger;
    }

    public async Task<T> MonitorPaymentOperationAsync<T>(
        Func<Task<T>> operation,
        string operationName,
        PaymentMethod method,
        string referenceNo)
    {
        using var activity = _metricsCollector.StartPaymentActivity(
            operationName, method, referenceNo);
            
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            var result = await operation();
            stopwatch.Stop();
            
            activity?.SetStatus(ActivityStatusCode.Ok);
            
            _logger.LogInformation(
                "Payment operation completed: {Operation}, Method={Method}, RefNo={RefNo}, Duration={Duration}ms",
                operationName, method, referenceNo, stopwatch.ElapsedMilliseconds);
                
            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            
            activity?.SetStatus(ActivityStatusCode.Error, ex.Message);
            
            _metricsCollector.RecordException(ex, operationName, method);
            
            throw;
        }
    }

    public async Task AlertOnPaymentFailureAsync(
        string paymentNo,
        PaymentMethod method,
        string failureReason,
        Exception? exception = null)
    {
        try
        {
            var alertMessage = $"Payment failure detected: " +
                $"PaymentNo={paymentNo}, Method={method}, Reason={failureReason}";
                
            if (exception != null)
            {
                alertMessage += $", Exception={exception.Message}";
            }
            
            _logger.LogError(exception, alertMessage);
            
            // In production, this would send alerts to monitoring systems
            // like PagerDuty, Slack, etc.
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send payment failure alert");
        }
    }
}