using Microsoft.Extensions.Logging;
using WhimLabAI.Domain.Entities.Payment;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Results;
using WhimLabAI.Abstractions.Infrastructure;

namespace WhimLabAI.Infrastructure.PaymentGateways;

/// <summary>
/// 支付验证服务
/// 确保支付金额、订单状态等信息的正确性
/// </summary>
public class PaymentValidationService : IPaymentValidationService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<PaymentValidationService> _logger;
    private readonly IPaymentMetricsCollector _metricsCollector;

    public PaymentValidationService(
        IUnitOfWork unitOfWork,
        ILogger<PaymentValidationService> logger,
        IPaymentMetricsCollector metricsCollector)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
        _metricsCollector = metricsCollector;
    }

    /// <summary>
    /// 验证支付回调数据
    /// </summary>
    public async Task<Result<PaymentValidationResult>> ValidatePaymentCallbackAsync(
        string paymentNo,
        decimal callbackAmount,
        string? transactionId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Get payment record
            var paymentObj = await _unitOfWork.Payments.GetByPaymentNoAsync(paymentNo, cancellationToken);
            if (paymentObj is not PaymentTransaction payment)
            {
                _logger.LogWarning("Payment not found for validation: {PaymentNo}", paymentNo);
                return Result<PaymentValidationResult>.Failure(
                    "PAYMENT_NOT_FOUND", 
                    $"支付记录不存在: {paymentNo}");
            }

            // Validate amount
            if (Math.Abs(payment.Amount.Amount - callbackAmount) > 0.01m)
            {
                _logger.LogError(
                    "Payment amount mismatch: PaymentNo={PaymentNo}, Expected={Expected}, Actual={Actual}",
                    paymentNo, payment.Amount.Amount, callbackAmount);
                    
                _metricsCollector.RecordException(
                    new InvalidOperationException("Payment amount mismatch"),
                    "ValidateCallback",
                    payment.PaymentMethod);
                    
                return Result<PaymentValidationResult>.Failure(
                    "AMOUNT_MISMATCH", 
                    $"支付金额不匹配: 预期{payment.Amount.Amount}, 实际{callbackAmount}");
            }

            // Validate payment status
            if (payment.Status != TransactionStatus.Pending)
            {
                _logger.LogWarning(
                    "Payment already processed: PaymentNo={PaymentNo}, Status={Status}",
                    paymentNo, payment.Status);
                    
                return Result<PaymentValidationResult>.Success(
                    new PaymentValidationResult
                    {
                        IsValid = true,
                        PaymentId = payment.Id,
                        OrderId = payment.OrderId,
                        Amount = payment.Amount.Amount,
                        IsAlreadyProcessed = true,
                        CurrentStatus = payment.Status
                    });
            }

            // Get order for additional validation
            var orderObj = await _unitOfWork.Orders.GetByIdAsync(payment.OrderId, cancellationToken);
            if (orderObj is not Order order)
            {
                return Result<PaymentValidationResult>.Failure(
                    "ORDER_NOT_FOUND", 
                    "关联订单不存在");
            }

            // Validate order status
            if (order.Status != OrderStatus.Pending)
            {
                _logger.LogWarning(
                    "Order not in pending status: OrderNo={OrderNo}, Status={Status}",
                    order.OrderNo, order.Status);
                    
                return Result<PaymentValidationResult>.Failure(
                    "INVALID_ORDER_STATUS", 
                    $"订单状态不允许支付: {order.Status}");
            }

            // Check expiration
            if (DateTime.UtcNow > order.ExpireAt)
            {
                return Result<PaymentValidationResult>.Failure(
                    "ORDER_EXPIRED", 
                    "订单已过期");
            }

            var result = new PaymentValidationResult
            {
                IsValid = true,
                PaymentId = payment.Id,
                OrderId = order.Id,
                Amount = payment.Amount.Amount,
                IsAlreadyProcessed = false,
                CurrentStatus = payment.Status,
                OrderNo = order.OrderNo,
                CustomerUserId = order.CustomerUserId
            };

            _logger.LogInformation(
                "Payment callback validated successfully: PaymentNo={PaymentNo}, Amount={Amount}",
                paymentNo, callbackAmount);
                
            return Result<PaymentValidationResult>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating payment callback for {PaymentNo}", paymentNo);
            _metricsCollector.RecordException(ex, "ValidateCallback");
            return Result<PaymentValidationResult>.Failure("VALIDATION_ERROR", "验证支付回调失败");
        }
    }

    /// <summary>
    /// 验证退款请求
    /// </summary>
    public async Task<Result<RefundValidationResult>> ValidateRefundRequestAsync(
        Guid orderId,
        Guid paymentId,
        decimal refundAmount,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Get payment
            var paymentObj = await _unitOfWork.Payments.GetByIdAsync(paymentId, cancellationToken);
            if (paymentObj is not PaymentTransaction payment)
            {
                return Result<RefundValidationResult>.Failure(
                    "PAYMENT_NOT_FOUND", 
                    "支付记录不存在");
            }

            // Validate payment belongs to order
            if (payment.OrderId != orderId)
            {
                return Result<RefundValidationResult>.Failure(
                    "PAYMENT_ORDER_MISMATCH", 
                    "支付记录与订单不匹配");
            }

            // Check payment status
            if (payment.Status != TransactionStatus.Success)
            {
                return Result<RefundValidationResult>.Failure(
                    "PAYMENT_NOT_SUCCESS", 
                    "只有成功的支付才能退款");
            }

            // Get existing refunds
            var existingRefunds = await _unitOfWork.Refunds.GetOrderRefundsAsync(orderId, cancellationToken);
            var totalRefunded = existingRefunds
                .Where(r => r.Status == RefundStatus.Completed)
                .Sum(r => r.RefundAmount.Amount);

            // Check if refund amount exceeds payment amount
            var remainingAmount = payment.Amount.Amount - totalRefunded;
            if (refundAmount > remainingAmount)
            {
                return Result<RefundValidationResult>.Failure(
                    "REFUND_EXCEEDS_PAYMENT", 
                    $"退款金额超过可退金额: 已支付{payment.Amount.Amount}, 已退款{totalRefunded}, 请求退款{refundAmount}");
            }

            // Get order
            var orderObj = await _unitOfWork.Orders.GetByIdAsync(orderId, cancellationToken);
            if (orderObj is not Order order)
            {
                return Result<RefundValidationResult>.Failure(
                    "ORDER_NOT_FOUND", 
                    "订单不存在");
            }

            // Check order refund policy
            var canRefund = await CheckRefundPolicyAsync(order, cancellationToken);
            if (!canRefund)
            {
                return Result<RefundValidationResult>.Failure(
                    "REFUND_NOT_ALLOWED", 
                    "订单不满足退款条件");
            }

            var result = new RefundValidationResult
            {
                IsValid = true,
                PaymentId = payment.Id,
                PaymentNo = payment.PaymentNo,
                TransactionId = payment.TransactionId!,
                TotalPaidAmount = payment.Amount.Amount,
                TotalRefundedAmount = totalRefunded,
                RemainingAmount = remainingAmount,
                PaymentMethod = payment.PaymentMethod
            };

            _logger.LogInformation(
                "Refund request validated: OrderId={OrderId}, RefundAmount={RefundAmount}, Remaining={Remaining}",
                orderId, refundAmount, remainingAmount);
                
            return Result<RefundValidationResult>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating refund request for order {OrderId}", orderId);
            _metricsCollector.RecordException(ex, "ValidateRefund");
            return Result<RefundValidationResult>.Failure("VALIDATION_ERROR", "验证退款请求失败");
        }
    }

    private async Task<bool> CheckRefundPolicyAsync(Order order, CancellationToken cancellationToken)
    {
        // Check refund time window (e.g., within 7 days for subscription)
        if (order.Type == OrderType.Subscription)
        {
            var refundWindow = TimeSpan.FromDays(7);
            if (DateTime.UtcNow - order.CreatedAt > refundWindow)
            {
                _logger.LogInformation(
                    "Order {OrderNo} outside refund window: Created {CreatedAt}, Window {Window}",
                    order.OrderNo, order.CreatedAt, refundWindow);
                return false;
            }
        }

        // Check order status
        if (order.Status == OrderStatus.Refunded || order.Status == OrderStatus.PartiallyRefunded)
        {
            // Already has refunds, need more complex validation
            _logger.LogInformation(
                "Order {OrderNo} already has refunds: Status={Status}",
                order.OrderNo, order.Status);
        }

        // Additional business rules can be added here
        await Task.CompletedTask;
        return true;
    }
}