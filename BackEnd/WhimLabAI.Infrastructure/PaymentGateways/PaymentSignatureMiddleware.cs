using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using WhimLabAI.Shared.Enums;

namespace WhimLabAI.Infrastructure.PaymentGateways;

/// <summary>
/// 支付签名验证中间件
/// 提供支付宝和微信支付的签名验证、防重放攻击等安全功能
/// </summary>
public class PaymentSignatureMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<PaymentSignatureMiddleware> _logger;
    private readonly IMemoryCache _cache;
    private readonly IConfiguration _configuration;
    private readonly Dictionary<PaymentMethod, ISignatureVerifier> _verifiers;
    private readonly int _nonceExpirationMinutes;
    private readonly int _timestampToleranceSeconds;
    private readonly bool _enableSignatureValidation;

    public PaymentSignatureMiddleware(
        RequestDelegate next,
        ILogger<PaymentSignatureMiddleware> logger,
        IMemoryCache cache,
        IConfiguration configuration)
    {
        _next = next;
        _logger = logger;
        _cache = cache;
        _configuration = configuration;
        
        // Check if signature validation is enabled
        _enableSignatureValidation = configuration.GetValue<bool>("Payment:Security:EnableSignatureValidation", true);
        
        // Initialize signature verifiers only if validation is enabled
        _verifiers = new Dictionary<PaymentMethod, ISignatureVerifier>();
        if (_enableSignatureValidation)
        {
            try
            {
                _verifiers[PaymentMethod.Alipay] = new AlipaySignatureVerifier(configuration, logger);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to initialize Alipay signature verifier. Alipay signature validation will be disabled.");
            }
            
            try
            {
                _verifiers[PaymentMethod.WeChatPay] = new WeChatPaySignatureVerifier(configuration, logger);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to initialize WeChat Pay signature verifier. WeChat Pay signature validation will be disabled.");
            }
        }
        else
        {
            _logger.LogWarning("Payment signature validation is disabled in configuration. This should only be used in development!");
        }
        
        // Security configuration
        _nonceExpirationMinutes = configuration.GetValue<int>("Payment:Security:NonceExpirationMinutes", 5);
        _timestampToleranceSeconds = configuration.GetValue<int>("Payment:Security:TimestampToleranceSeconds", 300); // 5 minutes
    }

    public async Task InvokeAsync(HttpContext context)
    {
        // Only apply to payment callback endpoints
        if (!IsPaymentCallbackEndpoint(context.Request.Path))
        {
            await _next(context);
            return;
        }

        // If signature validation is disabled, just pass through with a warning
        if (!_enableSignatureValidation)
        {
            _logger.LogWarning("Payment signature validation is disabled. Processing payment callback without verification. Path: {Path}", context.Request.Path);
            
            // Enable request body buffering for downstream middleware
            context.Request.EnableBuffering();
            
            // Extract payment method for downstream use
            var paymentMethod = ExtractPaymentMethod(context.Request.Path);
            if (paymentMethod.HasValue)
            {
                context.Items["PaymentMethod"] = paymentMethod.Value;
            }
            
            await _next(context);
            return;
        }

        try
        {
            // Enable request body buffering for reading multiple times
            context.Request.EnableBuffering();
            
            // Extract payment method from path
            var paymentMethod = ExtractPaymentMethod(context.Request.Path);
            if (!paymentMethod.HasValue)
            {
                _logger.LogWarning("Unable to determine payment method from path: {Path}", context.Request.Path);
                await RejectRequest(context, "Invalid payment method");
                return;
            }

            // Get the appropriate verifier
            if (!_verifiers.TryGetValue(paymentMethod.Value, out var verifier))
            {
                _logger.LogWarning("No signature verifier for payment method: {Method}", paymentMethod);
                await RejectRequest(context, "Unsupported payment method");
                return;
            }

            // Extract parameters based on content type
            var parameters = await ExtractParametersAsync(context);
            if (parameters == null || parameters.Count == 0)
            {
                _logger.LogWarning("No parameters extracted from request");
                await RejectRequest(context, "Invalid request data");
                return;
            }

            // 1. Verify timestamp (prevent old request replay)
            if (!await VerifyTimestampAsync(parameters, paymentMethod.Value))
            {
                _logger.LogWarning("Timestamp verification failed for {Method}", paymentMethod);
                await RejectRequest(context, "Request expired");
                return;
            }

            // 2. Verify nonce (prevent duplicate requests)
            if (!await VerifyNonceAsync(parameters, paymentMethod.Value))
            {
                _logger.LogWarning("Nonce verification failed for {Method}", paymentMethod);
                await RejectRequest(context, "Duplicate request");
                return;
            }

            // 3. Verify signature
            if (!await verifier.VerifySignatureAsync(parameters))
            {
                _logger.LogWarning("Signature verification failed for {Method}", paymentMethod);
                await RejectRequest(context, "Invalid signature");
                return;
            }

            // Store parameters in HttpContext for downstream use
            context.Items["PaymentCallbackParameters"] = parameters;
            context.Items["PaymentMethod"] = paymentMethod.Value;

            // Reset stream position for next middleware
            if (context.Request.Body.CanSeek)
            {
                context.Request.Body.Position = 0;
            }

            _logger.LogInformation("Payment callback security verification passed for {Method}", paymentMethod);
            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in payment signature middleware");
            await RejectRequest(context, "Internal error");
        }
    }

    private bool IsPaymentCallbackEndpoint(PathString path)
    {
        var pathValue = path.Value?.ToLower() ?? "";
        return pathValue.Contains("/api/payment/callback/") || 
               pathValue.Contains("/api/payment/refund-callback/");
    }

    private PaymentMethod? ExtractPaymentMethod(PathString path)
    {
        var pathValue = path.Value?.ToLower() ?? "";
        
        if (pathValue.Contains("/alipay"))
            return PaymentMethod.Alipay;
        if (pathValue.Contains("/wechatpay") || pathValue.Contains("/wechat"))
            return PaymentMethod.WeChatPay;
            
        return null;
    }

    private async Task<Dictionary<string, string>?> ExtractParametersAsync(HttpContext context)
    {
        var contentType = context.Request.ContentType?.ToLower() ?? "";
        
        // For form data (Alipay typically uses this)
        if (contentType.Contains("application/x-www-form-urlencoded") || context.Request.HasFormContentType)
        {
            var form = await context.Request.ReadFormAsync();
            return form.ToDictionary(kvp => kvp.Key, kvp => kvp.Value.ToString());
        }
        
        // For XML data (WeChat Pay uses this)
        if (contentType.Contains("text/xml") || contentType.Contains("application/xml"))
        {
            using var reader = new StreamReader(context.Request.Body, leaveOpen: true);
            var xmlContent = await reader.ReadToEndAsync();
            context.Request.Body.Position = 0;
            
            // Store raw XML for processing
            return new Dictionary<string, string> { ["xml_content"] = xmlContent };
        }
        
        // For JSON data (modern APIs)
        if (contentType.Contains("application/json"))
        {
            using var reader = new StreamReader(context.Request.Body, leaveOpen: true);
            var jsonContent = await reader.ReadToEndAsync();
            context.Request.Body.Position = 0;
            
            try
            {
                var jsonDoc = JsonDocument.Parse(jsonContent);
                var parameters = new Dictionary<string, string>();
                
                foreach (var property in jsonDoc.RootElement.EnumerateObject())
                {
                    parameters[property.Name] = property.Value.ToString();
                }
                
                return parameters;
            }
            catch
            {
                return null;
            }
        }
        
        return null;
    }

    private async Task<bool> VerifyTimestampAsync(Dictionary<string, string> parameters, PaymentMethod paymentMethod)
    {
        string? timestampStr = null;
        
        // Extract timestamp based on payment method
        switch (paymentMethod)
        {
            case PaymentMethod.Alipay:
                parameters.TryGetValue("timestamp", out timestampStr);
                break;
            case PaymentMethod.WeChatPay:
                // WeChat uses time_end in callback, but we can check request time
                // For better security, WeChat should include a timestamp in header
                return true; // WeChat doesn't provide timestamp in standard callback
        }
        
        if (string.IsNullOrEmpty(timestampStr))
        {
            return true; // If no timestamp provided, skip this check
        }
        
        try
        {
            var timestamp = DateTime.Parse(timestampStr);
            var now = DateTime.UtcNow;
            var timeDiff = Math.Abs((now - timestamp).TotalSeconds);
            
            if (timeDiff > _timestampToleranceSeconds)
            {
                _logger.LogWarning("Timestamp too old or too far in future: {Timestamp}, Difference: {Diff}s", 
                    timestampStr, timeDiff);
                return false;
            }
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to parse timestamp: {Timestamp}", timestampStr);
            return false;
        }
    }

    private async Task<bool> VerifyNonceAsync(Dictionary<string, string> parameters, PaymentMethod paymentMethod)
    {
        string? nonce = null;
        string? paymentNo = null;
        
        // Extract nonce and payment number based on payment method
        switch (paymentMethod)
        {
            case PaymentMethod.Alipay:
                parameters.TryGetValue("out_trade_no", out paymentNo);
                parameters.TryGetValue("trade_no", out nonce); // Use Alipay transaction ID as nonce
                break;
            case PaymentMethod.WeChatPay:
                if (parameters.TryGetValue("xml_content", out var xmlContent))
                {
                    // Parse XML to get nonce_str and out_trade_no
                    var xmlParams = ParseXmlToDictionary(xmlContent);
                    xmlParams.TryGetValue("nonce_str", out nonce);
                    xmlParams.TryGetValue("out_trade_no", out paymentNo);
                }
                break;
        }
        
        if (string.IsNullOrEmpty(nonce) || string.IsNullOrEmpty(paymentNo))
        {
            return true; // Skip nonce check if not available
        }
        
        // Create a unique key for this request
        var cacheKey = $"payment_nonce:{paymentMethod}:{paymentNo}:{nonce}";
        
        // Check if we've seen this nonce before
        if (_cache.TryGetValue(cacheKey, out _))
        {
            _logger.LogWarning("Duplicate nonce detected: Method={Method}, PaymentNo={PaymentNo}, Nonce={Nonce}", 
                paymentMethod, paymentNo, nonce);
            return false;
        }
        
        // Store the nonce to prevent replay
        _cache.Set(cacheKey, true, TimeSpan.FromMinutes(_nonceExpirationMinutes));
        
        await Task.CompletedTask;
        return true;
    }

    private Dictionary<string, string> ParseXmlToDictionary(string xml)
    {
        var result = new Dictionary<string, string>();
        
        try
        {
            var doc = System.Xml.Linq.XDocument.Parse(xml);
            var root = doc.Root;
            
            if (root != null)
            {
                foreach (var element in root.Elements())
                {
                    result[element.Name.LocalName] = element.Value;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to parse XML");
        }
        
        return result;
    }

    private async Task RejectRequest(HttpContext context, string reason)
    {
        context.Response.StatusCode = 400;
        context.Response.ContentType = "text/plain";
        await context.Response.WriteAsync(reason);
    }
}

/// <summary>
/// Interface for payment method specific signature verification
/// </summary>
public interface ISignatureVerifier
{
    Task<bool> VerifySignatureAsync(Dictionary<string, string> parameters);
}

/// <summary>
/// Alipay signature verifier
/// </summary>
public class AlipaySignatureVerifier : ISignatureVerifier
{
    private readonly IConfiguration _configuration;
    private readonly ILogger _logger;
    private readonly string? _publicKey;

    public AlipaySignatureVerifier(IConfiguration configuration, ILogger logger)
    {
        _configuration = configuration;
        _logger = logger;
        _publicKey = configuration["Payment:Alipay:AlipayPublicKey"];
        
        if (string.IsNullOrEmpty(_publicKey))
        {
            throw new InvalidOperationException("Alipay public key not configured. Please set Payment:Alipay:AlipayPublicKey in configuration.");
        }
    }

    public async Task<bool> VerifySignatureAsync(Dictionary<string, string> parameters)
    {
        try
        {
            if (!parameters.TryGetValue("sign", out var sign))
            {
                _logger.LogWarning("No sign parameter in Alipay callback");
                return false;
            }

            var signType = parameters.GetValueOrDefault("sign_type", "RSA2");
            if (signType != "RSA2")
            {
                _logger.LogWarning("Unsupported Alipay sign type: {SignType}", signType);
                return false;
            }

            // Remove sign fields for verification
            var verifyParams = new Dictionary<string, string>(parameters);
            verifyParams.Remove("sign");
            verifyParams.Remove("sign_type");

            // Sort and build sign string
            var sortedParams = verifyParams.OrderBy(p => p.Key).ToList();
            var signContent = string.Join("&", sortedParams.Select(p => $"{p.Key}={p.Value}"));

            // Verify with RSA2
            using var rsa = RSA.Create();
            rsa.ImportSubjectPublicKeyInfo(Convert.FromBase64String(_publicKey), out _);
            
            var verified = rsa.VerifyData(
                Encoding.UTF8.GetBytes(signContent),
                Convert.FromBase64String(sign),
                HashAlgorithmName.SHA256,
                RSASignaturePadding.Pkcs1);

            if (!verified)
            {
                _logger.LogWarning("Alipay signature verification failed");
            }

            await Task.CompletedTask;
            return verified;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error verifying Alipay signature");
            return false;
        }
    }
}

/// <summary>
/// WeChat Pay signature verifier
/// </summary>
public class WeChatPaySignatureVerifier : ISignatureVerifier
{
    private readonly IConfiguration _configuration;
    private readonly ILogger _logger;
    private readonly string? _apiKey;

    public WeChatPaySignatureVerifier(IConfiguration configuration, ILogger logger)
    {
        _configuration = configuration;
        _logger = logger;
        _apiKey = configuration["Payment:WeChatPay:ApiKey"];
        
        if (string.IsNullOrEmpty(_apiKey))
        {
            throw new InvalidOperationException("WeChat Pay API key not configured. Please set Payment:WeChatPay:ApiKey in configuration.");
        }
    }

    public async Task<bool> VerifySignatureAsync(Dictionary<string, string> parameters)
    {
        try
        {
            if (!parameters.TryGetValue("xml_content", out var xmlContent))
            {
                _logger.LogWarning("No XML content in WeChat Pay callback");
                return false;
            }

            // Parse XML
            var xmlParams = ParseXml(xmlContent);
            
            if (!xmlParams.TryGetValue("sign", out var sign))
            {
                _logger.LogWarning("No sign in WeChat Pay callback");
                return false;
            }

            // Remove sign for verification
            var verifyParams = new SortedDictionary<string, string>(xmlParams);
            verifyParams.Remove("sign");

            // Build sign string
            var signStr = string.Join("&", verifyParams.Select(p => $"{p.Key}={p.Value}")) + $"&key={_apiKey}";
            
            // Calculate MD5
            using var md5 = MD5.Create();
            var hash = md5.ComputeHash(Encoding.UTF8.GetBytes(signStr));
            var calculatedSign = BitConverter.ToString(hash).Replace("-", "").ToUpper();

            var verified = calculatedSign == sign.ToUpper();
            
            if (!verified)
            {
                _logger.LogWarning("WeChat Pay signature verification failed");
            }

            await Task.CompletedTask;
            return verified;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error verifying WeChat Pay signature");
            return false;
        }
    }

    private Dictionary<string, string> ParseXml(string xml)
    {
        var result = new Dictionary<string, string>();
        
        try
        {
            var doc = System.Xml.Linq.XDocument.Parse(xml);
            var root = doc.Root;
            
            if (root != null)
            {
                foreach (var element in root.Elements())
                {
                    if (!element.HasElements) // Skip CDATA
                    {
                        result[element.Name.LocalName] = element.Value;
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to parse WeChat XML");
        }
        
        return result;
    }
}

/// <summary>
/// Extension methods for PaymentSignatureMiddleware
/// </summary>
public static class PaymentSignatureMiddlewareExtensions
{
    public static IApplicationBuilder UsePaymentSignatureVerification(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<PaymentSignatureMiddleware>();
    }
}