namespace WhimLabAI.Infrastructure.PaymentGateways;

/// <summary>
/// WeChat Pay configuration options
/// </summary>
public class WeChatPayOptions
{
    /// <summary>
    /// WeChat App ID
    /// </summary>
    public string AppId { get; set; } = string.Empty;
    
    /// <summary>
    /// Merchant ID
    /// </summary>
    public string MchId { get; set; } = string.Empty;
    
    /// <summary>
    /// API Key for signing
    /// </summary>
    public string ApiKey { get; set; } = string.Empty;
    
    /// <summary>
    /// API v3 Key for new API
    /// </summary>
    public string ApiV3Key { get; set; } = string.Empty;
    
    /// <summary>
    /// Certificate serial number
    /// </summary>
    public string CertificateSerialNo { get; set; } = string.Empty;
    
    /// <summary>
    /// Certificate private key
    /// </summary>
    public string CertificatePrivateKey { get; set; } = string.Empty;
    
    /// <summary>
    /// Notify URL for async callbacks
    /// </summary>
    public string NotifyUrl { get; set; } = string.Empty;
}