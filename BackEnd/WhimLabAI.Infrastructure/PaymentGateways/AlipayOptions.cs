namespace WhimLabAI.Infrastructure.PaymentGateways;

/// <summary>
/// Alipay configuration options
/// </summary>
public class AlipayOptions
{
    /// <summary>
    /// Alipay App ID
    /// </summary>
    public string AppId { get; set; } = string.Empty;
    
    /// <summary>
    /// Alipay public key for signature verification
    /// </summary>
    public string AlipayPublicKey { get; set; } = string.Empty;
    
    /// <summary>
    /// Merchant private key for signing requests
    /// </summary>
    public string PrivateKey { get; set; } = string.Empty;
    
    /// <summary>
    /// Gateway URL
    /// </summary>
    public string GatewayUrl { get; set; } = "https://openapi.alipay.com/gateway.do";
    
    /// <summary>
    /// Notify URL for async callbacks
    /// </summary>
    public string NotifyUrl { get; set; } = string.Empty;
    
    /// <summary>
    /// Return URL for sync callbacks
    /// </summary>
    public string ReturnUrl { get; set; } = string.Empty;
}