using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Abstractions.Application;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Infrastructure.PaymentGateways;

/// <summary>
/// 支付测试辅助类
/// 用于在开发环境中测试支付流程
/// </summary>
public class PaymentTestHelper
{
    private readonly IServiceProvider _serviceProvider;
    private readonly IConfiguration _configuration;
    private readonly ILogger<PaymentTestHelper> _logger;

    public PaymentTestHelper(
        IServiceProvider serviceProvider,
        IConfiguration configuration,
        ILogger<PaymentTestHelper> logger)
    {
        _serviceProvider = serviceProvider;
        _configuration = configuration;
        _logger = logger;
    }

    /// <summary>
    /// 模拟支付回调
    /// </summary>
    public async Task<Result> SimulatePaymentCallbackAsync(
        PaymentMethod method,
        string paymentNo,
        bool success = true,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var isDevelopment = _configuration["ASPNETCORE_ENVIRONMENT"] == "Development";
            if (!isDevelopment)
            {
                return Result.Failure("NOT_DEVELOPMENT", "只能在开发环境使用");
            }

            var parameters = method switch
            {
                PaymentMethod.Alipay => GenerateAlipayCallbackParams(paymentNo, success),
                PaymentMethod.WeChatPay => GenerateWeChatPayCallbackParams(paymentNo, success),
                _ => throw new NotSupportedException($"不支持的支付方式: {method}")
            };

            using var scope = _serviceProvider.CreateScope();
            var paymentService = scope.ServiceProvider.GetRequiredService<IPaymentService>();
            
            var result = await paymentService.ProcessCallbackAsync(method, parameters, cancellationToken);
            
            _logger.LogInformation(
                "Simulated payment callback: Method={Method}, PaymentNo={PaymentNo}, Success={Success}, Result={Result}",
                method, paymentNo, success, result.IsSuccess);
                
            return result.IsSuccess ? Result.Success() : Result.Failure(result.ErrorCode!, result.Error!);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error simulating payment callback");
            return Result.Failure("SIMULATION_ERROR", ex.Message);
        }
    }

    /// <summary>
    /// 模拟退款回调
    /// </summary>
    public async Task<Result> SimulateRefundCallbackAsync(
        PaymentMethod method,
        string refundNo,
        bool success = true,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var isDevelopment = _configuration["ASPNETCORE_ENVIRONMENT"] == "Development";
            if (!isDevelopment)
            {
                return Result.Failure("NOT_DEVELOPMENT", "只能在开发环境使用");
            }

            // In real implementation, this would call refund status check
            using var scope = _serviceProvider.CreateScope();
            var paymentService = scope.ServiceProvider.GetRequiredService<IPaymentService>();
            
            await paymentService.CheckRefundStatusAsync(refundNo, cancellationToken);
            
            _logger.LogInformation(
                "Simulated refund callback: Method={Method}, RefundNo={RefundNo}, Success={Success}",
                method, refundNo, success);
                
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error simulating refund callback");
            return Result.Failure("SIMULATION_ERROR", ex.Message);
        }
    }

    /// <summary>
    /// 测试支付网关连接
    /// </summary>
    public async Task<PaymentHealthCheckResult> TestPaymentGatewayAsync(
        PaymentMethod method,
        CancellationToken cancellationToken = default)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var gatewayManager = scope.ServiceProvider.GetRequiredService<IPaymentGatewayManager>();
            
            var isValid = await gatewayManager.ValidateGatewayConfigurationAsync(method, cancellationToken);
            
            return new PaymentHealthCheckResult
            {
                Method = method,
                IsHealthy = isValid,
                CheckedAt = DateTime.UtcNow,
                Message = isValid ? "配置有效" : "配置无效"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error testing payment gateway {Method}", method);
            return new PaymentHealthCheckResult
            {
                Method = method,
                IsHealthy = false,
                CheckedAt = DateTime.UtcNow,
                Message = ex.Message
            };
        }
    }

    private Dictionary<string, string> GenerateAlipayCallbackParams(string paymentNo, bool success)
    {
        var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        var tradeNo = $"TEST{DateTime.Now:yyyyMMddHHmmss}{new Random().Next(1000, 9999)}";
        
        return new Dictionary<string, string>
        {
            ["app_id"] = _configuration["Payment:Alipay:AppId"] ?? "202**********000",
            ["trade_no"] = tradeNo,
            ["out_trade_no"] = paymentNo,
            ["trade_status"] = success ? "TRADE_SUCCESS" : "TRADE_CLOSED",
            ["total_amount"] = "100.00",
            ["buyer_id"] = "************",
            ["buyer_logon_id"] = "<EMAIL>",
            ["gmt_create"] = timestamp,
            ["gmt_payment"] = success ? timestamp : "",
            ["notify_time"] = timestamp,
            ["notify_type"] = "trade_status_sync",
            ["notify_id"] = Guid.NewGuid().ToString("N"),
            ["sign"] = "test_signature",
            ["sign_type"] = "RSA2"
        };
    }

    private Dictionary<string, string> GenerateWeChatPayCallbackParams(string paymentNo, bool success)
    {
        var timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
        var transactionId = $"TEST{timestamp}{new Random().Next(1000, 9999)}";
        
        var xml = $@"<xml>
            <appid><![CDATA[{_configuration["Payment:WeChatPay:AppId"] ?? "wx****************"}]]></appid>
            <mch_id><![CDATA[{_configuration["Payment:WeChatPay:MchId"] ?? "**********"}]]></mch_id>
            <nonce_str><![CDATA[{Guid.NewGuid():N}]]></nonce_str>
            <sign><![CDATA[test_signature]]></sign>
            <result_code><![CDATA[{(success ? "SUCCESS" : "FAIL")}]]></result_code>
            <return_code><![CDATA[SUCCESS]]></return_code>
            <openid><![CDATA[otest_user_openid]]></openid>
            <trade_type><![CDATA[NATIVE]]></trade_type>
            <bank_type><![CDATA[CMB_CREDIT]]></bank_type>
            <total_fee>10000</total_fee>
            <fee_type><![CDATA[CNY]]></fee_type>
            <transaction_id><![CDATA[{transactionId}]]></transaction_id>
            <out_trade_no><![CDATA[{paymentNo}]]></out_trade_no>
            <time_end><![CDATA[{timestamp}]]></time_end>
            <trade_state><![CDATA[{(success ? "SUCCESS" : "CLOSED")}]]></trade_state>
        </xml>";
        
        return new Dictionary<string, string>
        {
            ["xml_content"] = xml
        };
    }
}

/// <summary>
/// 支付健康检查结果
/// </summary>
public class PaymentHealthCheckResult
{
    public PaymentMethod Method { get; set; }
    public bool IsHealthy { get; set; }
    public DateTime CheckedAt { get; set; }
    public string Message { get; set; } = string.Empty;
}