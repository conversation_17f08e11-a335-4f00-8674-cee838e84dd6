using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Exceptions;

namespace WhimLabAI.Infrastructure.PaymentGateways;

/// <summary>
/// 支付网关管理器
/// </summary>
public class PaymentGatewayManager : IPaymentGatewayManager
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<PaymentGatewayManager> _logger;
    private readonly Dictionary<PaymentMethod, Type> _gatewayTypes;

    public PaymentGatewayManager(
        IServiceProvider serviceProvider,
        ILogger<PaymentGatewayManager> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        
        // Register gateway types
        _gatewayTypes = new Dictionary<PaymentMethod, Type>
        {
            { PaymentMethod.Alipay, typeof(AlipayGateway) },
            { PaymentMethod.WeChatPay, typeof(WeChatPayGateway) }
        };
    }

    public IPaymentGateway GetGateway(PaymentMethod paymentMethod)
    {
        if (!_gatewayTypes.TryGetValue(paymentMethod, out var gatewayType))
        {
            throw new BusinessException($"不支持的支付方式: {paymentMethod}");
        }

        var gateway = _serviceProvider.GetService(gatewayType) as IPaymentGateway;
        if (gateway == null)
        {
            throw new InvalidOperationException($"支付网关 {paymentMethod} 未正确配置");
        }

        _logger.LogDebug("Retrieved payment gateway for {PaymentMethod}", paymentMethod);
        return gateway;
    }

    public IEnumerable<PaymentMethod> GetSupportedMethods()
    {
        return _gatewayTypes.Keys;
    }
    
    public Task<PaymentMethod[]> GetSupportedMethodsAsync(CancellationToken cancellationToken = default)
    {
        return Task.FromResult(_gatewayTypes.Keys.ToArray());
    }

    public bool IsMethodSupported(PaymentMethod paymentMethod)
    {
        return _gatewayTypes.ContainsKey(paymentMethod);
    }

    public async Task<bool> ValidateGatewayConfigurationAsync(PaymentMethod paymentMethod, CancellationToken cancellationToken = default)
    {
        try
        {
            var gateway = GetGateway(paymentMethod);
            
            // Try to create a test payment request to validate configuration
            var testRequest = new PaymentCreateRequest
            {
                OrderNo = "TEST_ORDER",
                PaymentNo = $"TEST_PAYMENT_{Guid.NewGuid():N}",
                Amount = 0.01m,
                Subject = "Configuration Test",
                ReturnUrl = "https://test.com/return",
                NotifyUrl = "https://test.com/notify",
                ExpireTime = DateTime.UtcNow.AddMinutes(30)
            };

            // We don't actually create the payment, just check if gateway can be initialized
            await Task.Delay(1, cancellationToken);
            
            _logger.LogInformation("Payment gateway {PaymentMethod} configuration validated", paymentMethod);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Payment gateway {PaymentMethod} configuration validation failed", paymentMethod);
            return false;
        }
    }
}