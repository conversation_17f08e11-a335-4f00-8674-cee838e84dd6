using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Prometheus;
using WhimLabAI.Abstractions.Infrastructure;

namespace WhimLabAI.Infrastructure.Monitoring;

/// <summary>
/// Prometheus指标服务实现
/// </summary>
public class PrometheusMetricsService : IMetricsService
{
    private readonly ILogger<PrometheusMetricsService> _logger;
    
    // 计数器
    private readonly Dictionary<string, Counter> _counters = new();
    
    // 仪表
    private readonly Dictionary<string, Gauge> _gauges = new();
    
    // 直方图
    private readonly Dictionary<string, Histogram> _histograms = new();
    
    // 摘要
    private readonly Dictionary<string, Summary> _summaries = new();
    
    // 业务指标
    private readonly Gauge _activeUsersGauge;
    private readonly Counter _userRegistrationsCounter;
    private readonly Gauge _agentsGauge;
    private readonly Counter _agentInvocationsCounter;
    private readonly Gauge _conversationsGauge;
    private readonly Counter _messagesCounter;
    private readonly Counter _tokensCounter;
    private readonly Counter _paymentsCounter;
    private readonly Counter _revenueCounter;
    private readonly Gauge _subscriptionsGauge;
    
    // 技术指标
    private readonly Counter _httpRequestsCounter;
    private readonly Histogram _httpRequestDuration;
    private readonly Counter _aiRequestsCounter;
    private readonly Histogram _aiRequestDuration;
    private readonly Gauge _dbConnectionsGauge;
    private readonly Histogram _dbQueryDuration;
    private readonly Counter _cacheHitsCounter;
    private readonly Counter _cacheMissesCounter;
    private readonly Histogram _cacheOperationDuration;
    private readonly Gauge _queueMessagesGauge;
    private readonly Histogram _queueProcessingDuration;
    
    public PrometheusMetricsService(ILogger<PrometheusMetricsService> logger)
    {
        _logger = logger;
        
        // 初始化业务指标
        _activeUsersGauge = Metrics.CreateGauge(
            "whimlab_users_active_total",
            "Number of active users",
            new GaugeConfiguration
            {
                LabelNames = new[] { "period" }
            });
            
        _userRegistrationsCounter = Metrics.CreateCounter(
            "whimlab_users_new_total",
            "Total number of new user registrations",
            new CounterConfiguration
            {
                LabelNames = new[] { "period" }
            });
            
        _agentsGauge = Metrics.CreateGauge(
            "whimlab_agents_total",
            "Total number of agents",
            new GaugeConfiguration
            {
                LabelNames = new[] { "status" }
            });
            
        _agentInvocationsCounter = Metrics.CreateCounter(
            "whimlab_agents_invocations_total",
            "Total number of agent invocations",
            new CounterConfiguration
            {
                LabelNames = new[] { "agent_id", "model" }
            });
            
        _conversationsGauge = Metrics.CreateGauge(
            "whimlab_conversations_total",
            "Total number of conversations",
            new GaugeConfiguration
            {
                LabelNames = new[] { "status" }
            });
            
        _messagesCounter = Metrics.CreateCounter(
            "whimlab_messages_total",
            "Total number of messages",
            new CounterConfiguration
            {
                LabelNames = new[] { "role" }
            });
            
        _tokensCounter = Metrics.CreateCounter(
            "whimlab_messages_tokens_total",
            "Total number of tokens consumed",
            new CounterConfiguration
            {
                LabelNames = new[] { "model", "tier" }
            });
            
        _paymentsCounter = Metrics.CreateCounter(
            "whimlab_payments_total",
            "Total number of payments",
            new CounterConfiguration
            {
                LabelNames = new[] { "status", "method" }
            });
            
        _revenueCounter = Metrics.CreateCounter(
            "whimlab_revenue_total",
            "Total revenue",
            new CounterConfiguration
            {
                LabelNames = new[] { "currency", "tier" }
            });
            
        _subscriptionsGauge = Metrics.CreateGauge(
            "whimlab_subscriptions_active_total",
            "Total number of active subscriptions",
            new GaugeConfiguration
            {
                LabelNames = new[] { "tier" }
            });
        
        // 初始化技术指标
        _httpRequestsCounter = Metrics.CreateCounter(
            "whimlab_http_requests_total",
            "Total number of HTTP requests",
            new CounterConfiguration
            {
                LabelNames = new[] { "method", "endpoint", "status" }
            });
            
        _httpRequestDuration = Metrics.CreateHistogram(
            "whimlab_http_request_duration_seconds",
            "HTTP request duration in seconds",
            new HistogramConfiguration
            {
                LabelNames = new[] { "method", "endpoint" },
                Buckets = Histogram.ExponentialBuckets(0.001, 2, 10) // 1ms to ~1s
            });
            
        _aiRequestsCounter = Metrics.CreateCounter(
            "whimlab_ai_requests_total",
            "Total number of AI requests",
            new CounterConfiguration
            {
                LabelNames = new[] { "provider", "model", "status" }
            });
            
        _aiRequestDuration = Metrics.CreateHistogram(
            "whimlab_ai_request_duration_seconds",
            "AI request duration in seconds",
            new HistogramConfiguration
            {
                LabelNames = new[] { "provider", "model" },
                Buckets = Histogram.ExponentialBuckets(0.1, 2, 10) // 100ms to ~100s
            });
            
        _dbConnectionsGauge = Metrics.CreateGauge(
            "whimlab_db_connections_active",
            "Number of active database connections");
            
        _dbQueryDuration = Metrics.CreateHistogram(
            "whimlab_db_query_duration_seconds",
            "Database query duration in seconds",
            new HistogramConfiguration
            {
                LabelNames = new[] { "query_type" },
                Buckets = Histogram.ExponentialBuckets(0.001, 2, 10) // 1ms to ~1s
            });
            
        _cacheHitsCounter = Metrics.CreateCounter(
            "whimlab_cache_hits_total",
            "Total number of cache hits");
            
        _cacheMissesCounter = Metrics.CreateCounter(
            "whimlab_cache_misses_total",
            "Total number of cache misses");
            
        _cacheOperationDuration = Metrics.CreateHistogram(
            "whimlab_cache_operations_duration_seconds",
            "Cache operation duration in seconds",
            new HistogramConfiguration
            {
                LabelNames = new[] { "operation" },
                Buckets = Histogram.ExponentialBuckets(0.0001, 2, 10) // 0.1ms to ~100ms
            });
            
        _queueMessagesGauge = Metrics.CreateGauge(
            "whimlab_queue_messages_total",
            "Total number of messages in queue",
            new GaugeConfiguration
            {
                LabelNames = new[] { "queue", "status" }
            });
            
        _queueProcessingDuration = Metrics.CreateHistogram(
            "whimlab_queue_processing_duration_seconds",
            "Queue message processing duration in seconds",
            new HistogramConfiguration
            {
                LabelNames = new[] { "queue" },
                Buckets = Histogram.ExponentialBuckets(0.01, 2, 10) // 10ms to ~10s
            });
    }
    
    public void IncrementCounter(string name, double value = 1, Dictionary<string, string>? labels = null)
    {
        var counter = GetOrCreateCounter(name, labels?.Keys.ToArray());
        
        if (labels != null && labels.Any())
        {
            counter.WithLabels(labels.Values.ToArray()).Inc(value);
        }
        else
        {
            counter.Inc(value);
        }
    }
    
    public void SetGauge(string name, double value, Dictionary<string, string>? labels = null)
    {
        var gauge = GetOrCreateGauge(name, labels?.Keys.ToArray());
        
        if (labels != null && labels.Any())
        {
            gauge.WithLabels(labels.Values.ToArray()).Set(value);
        }
        else
        {
            gauge.Set(value);
        }
    }
    
    public void RecordHistogram(string name, double value, Dictionary<string, string>? labels = null)
    {
        var histogram = GetOrCreateHistogram(name, labels?.Keys.ToArray());
        
        if (labels != null && labels.Any())
        {
            histogram.WithLabels(labels.Values.ToArray()).Observe(value);
        }
        else
        {
            histogram.Observe(value);
        }
    }
    
    public void RecordSummary(string name, double value, Dictionary<string, string>? labels = null)
    {
        var summary = GetOrCreateSummary(name, labels?.Keys.ToArray());
        
        if (labels != null && labels.Any())
        {
            summary.WithLabels(labels.Values.ToArray()).Observe(value);
        }
        else
        {
            summary.Observe(value);
        }
    }
    
    public IDisposable MeasureDuration(string name, Dictionary<string, string>? labels = null)
    {
        return new MetricTimer(this, name, labels);
    }
    
    public async Task RecordBusinessMetricsAsync(BusinessMetrics metrics, CancellationToken cancellationToken = default)
    {
        await Task.Run(() =>
        {
            // 用户指标
            _activeUsersGauge.WithLabels("daily").Set(metrics.ActiveUsersDaily);
            _activeUsersGauge.WithLabels("monthly").Set(metrics.ActiveUsersMonthly);
            _userRegistrationsCounter.WithLabels("day").Inc(metrics.NewRegistrations);
            
            // Agent指标
            _agentsGauge.WithLabels("active").Set(metrics.ActiveAgents);
            _agentsGauge.WithLabels("total").Set(metrics.TotalAgents);
            
            foreach (var usage in metrics.AgentUsageByType)
            {
                // 这里需要Agent ID，暂时使用type作为示例
                _agentInvocationsCounter.WithLabels(usage.Key, "default").Inc(usage.Value);
            }
            
            // 对话指标
            _conversationsGauge.WithLabels("active").Set(metrics.ActiveConversations);
            _conversationsGauge.WithLabels("total").Set(metrics.TotalConversations);
            
            // Token指标
            foreach (var tokenUsage in metrics.TokensByModel)
            {
                _tokensCounter.WithLabels(tokenUsage.Key, "all").Inc(tokenUsage.Value);
            }
            
            foreach (var tokenUsage in metrics.TokensByUserTier)
            {
                _tokensCounter.WithLabels("all", tokenUsage.Key).Inc(tokenUsage.Value);
            }
            
            // 收入指标
            foreach (var revenue in metrics.RevenueByTier)
            {
                _revenueCounter.WithLabels("CNY", revenue.Key).Inc((double)revenue.Value);
            }
        }, cancellationToken);
    }
    
    public async Task RecordTechnicalMetricsAsync(TechnicalMetrics metrics, CancellationToken cancellationToken = default)
    {
        await Task.Run(() =>
        {
            // 数据库指标
            _dbConnectionsGauge.Set(metrics.ActiveConnections);
            
            // 缓存指标
            var totalCacheRequests = _cacheHitsCounter.Value + _cacheMissesCounter.Value;
            if (totalCacheRequests > 0)
            {
                var expectedHitRate = _cacheHitsCounter.Value / totalCacheRequests;
                // 如果实际命中率与预期不符，调整计数器
                if (Math.Abs(expectedHitRate - metrics.CacheHitRate) > 0.01)
                {
                    var expectedHits = totalCacheRequests * metrics.CacheHitRate;
                    var expectedMisses = totalCacheRequests * (1 - metrics.CacheHitRate);
                    // 这里只是示例，实际应该通过增量更新
                }
            }
            
            // 队列指标
            foreach (var queue in metrics.QueueDepth)
            {
                _queueMessagesGauge.WithLabels(queue.Key, "pending").Set(queue.Value);
            }
        }, cancellationToken);
    }
    
    public async Task<MetricsSnapshot> GetMetricsSnapshotAsync(CancellationToken cancellationToken = default)
    {
        var snapshot = new MetricsSnapshot
        {
            Timestamp = DateTime.UtcNow,
            BusinessMetrics = new BusinessMetrics
            {
                ActiveUsersDaily = (int)_activeUsersGauge.WithLabels("daily").Value,
                ActiveUsersMonthly = (int)_activeUsersGauge.WithLabels("monthly").Value,
                ActiveAgents = (int)_agentsGauge.WithLabels("active").Value,
                TotalAgents = (int)_agentsGauge.WithLabels("total").Value,
                ActiveConversations = (int)_conversationsGauge.WithLabels("active").Value,
                TotalConversations = (int)_conversationsGauge.WithLabels("total").Value
            },
            TechnicalMetrics = new TechnicalMetrics
            {
                ActiveConnections = (int)_dbConnectionsGauge.Value
            }
        };
        
        return await Task.FromResult(snapshot);
    }
    
    private Counter GetOrCreateCounter(string name, string[]? labelNames)
    {
        var key = GetMetricKey(name, labelNames);
        if (!_counters.TryGetValue(key, out var counter))
        {
            counter = Metrics.CreateCounter(name, name, new CounterConfiguration
            {
                LabelNames = labelNames ?? Array.Empty<string>()
            });
            _counters[key] = counter;
        }
        return counter;
    }
    
    private Gauge GetOrCreateGauge(string name, string[]? labelNames)
    {
        var key = GetMetricKey(name, labelNames);
        if (!_gauges.TryGetValue(key, out var gauge))
        {
            gauge = Metrics.CreateGauge(name, name, new GaugeConfiguration
            {
                LabelNames = labelNames ?? Array.Empty<string>()
            });
            _gauges[key] = gauge;
        }
        return gauge;
    }
    
    private Histogram GetOrCreateHistogram(string name, string[]? labelNames)
    {
        var key = GetMetricKey(name, labelNames);
        if (!_histograms.TryGetValue(key, out var histogram))
        {
            histogram = Metrics.CreateHistogram(name, name, new HistogramConfiguration
            {
                LabelNames = labelNames ?? Array.Empty<string>()
            });
            _histograms[key] = histogram;
        }
        return histogram;
    }
    
    private Summary GetOrCreateSummary(string name, string[]? labelNames)
    {
        var key = GetMetricKey(name, labelNames);
        if (!_summaries.TryGetValue(key, out var summary))
        {
            summary = Metrics.CreateSummary(name, name, new SummaryConfiguration
            {
                LabelNames = labelNames ?? Array.Empty<string>()
            });
            _summaries[key] = summary;
        }
        return summary;
    }
    
    private static string GetMetricKey(string name, string[]? labelNames)
    {
        if (labelNames == null || labelNames.Length == 0)
            return name;
        return $"{name}:{string.Join(",", labelNames)}";
    }
    
    private class MetricTimer : IMetricTimer
    {
        private readonly PrometheusMetricsService _service;
        private readonly string _metricName;
        private readonly Dictionary<string, string>? _labels;
        private readonly Stopwatch _stopwatch;
        private bool _disposed;
        
        public MetricTimer(PrometheusMetricsService service, string metricName, Dictionary<string, string>? labels)
        {
            _service = service;
            _metricName = metricName;
            _labels = labels;
            _stopwatch = Stopwatch.StartNew();
        }
        
        public void Stop()
        {
            if (!_disposed)
            {
                _stopwatch.Stop();
                _service.RecordHistogram(_metricName, _stopwatch.Elapsed.TotalSeconds, _labels);
                _disposed = true;
            }
        }
        
        public void Dispose()
        {
            Stop();
        }
    }
}