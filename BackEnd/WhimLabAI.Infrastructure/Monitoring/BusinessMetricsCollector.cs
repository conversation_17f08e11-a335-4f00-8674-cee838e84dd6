using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Infrastructure.Data;

namespace WhimLabAI.Infrastructure.Monitoring;

/// <summary>
/// 业务指标收集器后台服务
/// </summary>
public class BusinessMetricsCollector : BackgroundService
{
    private readonly IServiceScopeFactory _scopeFactory;
    private readonly ILogger<BusinessMetricsCollector> _logger;
    private readonly IMetricsService _metricsService;
    private readonly TimeSpan _collectionInterval = TimeSpan.FromMinutes(1);
    
    public BusinessMetricsCollector(
        IServiceScopeFactory scopeFactory,
        ILogger<BusinessMetricsCollector> logger,
        IMetricsService metricsService)
    {
        _scopeFactory = scopeFactory;
        _logger = logger;
        _metricsService = metricsService;
    }
    
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Business metrics collector started");
        
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await CollectMetricsAsync(stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error collecting business metrics");
            }
            
            await Task.Delay(_collectionInterval, stoppingToken);
        }
    }
    
    private async Task CollectMetricsAsync(CancellationToken cancellationToken)
    {
        using var scope = _scopeFactory.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<WhimLabAIDbContext>();
        
        var now = DateTime.UtcNow;
        var today = now.Date;
        var thirtyDaysAgo = today.AddDays(-30);
        
        var metrics = new BusinessMetrics
        {
            Timestamp = now
        };
        
        // 收集用户指标
        await CollectUserMetricsAsync(dbContext, metrics, today, thirtyDaysAgo, cancellationToken);
        
        // 收集Agent指标
        await CollectAgentMetricsAsync(dbContext, metrics, now, cancellationToken);
        
        // 收集对话指标
        await CollectConversationMetricsAsync(dbContext, metrics, now, cancellationToken);
        
        // 收集Token指标
        await CollectTokenMetricsAsync(dbContext, metrics, today, cancellationToken);
        
        // 收集收入指标
        await CollectRevenueMetricsAsync(dbContext, metrics, today, thirtyDaysAgo, cancellationToken);
        
        // 记录指标
        await _metricsService.RecordBusinessMetricsAsync(metrics, cancellationToken);
        
        _logger.LogInformation("Business metrics collected successfully");
    }
    
    private async Task CollectUserMetricsAsync(
        WhimLabAIDbContext dbContext,
        BusinessMetrics metrics,
        DateTime today,
        DateTime thirtyDaysAgo,
        CancellationToken cancellationToken)
    {
        // 日活跃用户数
        metrics.ActiveUsersDaily = await dbContext.Set<Domain.Entities.Conversation.Conversation>()
            .Where(c => c.UpdatedAt >= today)
            .Select(c => c.CustomerUserId)
            .Distinct()
            .CountAsync(cancellationToken);
        
        // 月活跃用户数
        metrics.ActiveUsersMonthly = await dbContext.Set<Domain.Entities.Conversation.Conversation>()
            .Where(c => c.UpdatedAt >= thirtyDaysAgo)
            .Select(c => c.CustomerUserId)
            .Distinct()
            .CountAsync(cancellationToken);
        
        // 新注册用户数（今日）
        metrics.NewRegistrations = await dbContext.Set<Domain.Entities.User.CustomerUser>()
            .Where(u => u.CreatedAt >= today)
            .CountAsync(cancellationToken);
        
        // 用户留存率（简化计算：昨天活跃用户中今天仍活跃的比例）
        var yesterday = today.AddDays(-1);
        var yesterdayActiveUsers = await dbContext.Set<Domain.Entities.Conversation.Conversation>()
            .Where(c => c.UpdatedAt >= yesterday && c.UpdatedAt < today)
            .Select(c => c.CustomerUserId)
            .Distinct()
            .ToListAsync(cancellationToken);
        
        if (yesterdayActiveUsers.Any())
        {
            var retainedUsers = await dbContext.Set<Domain.Entities.Conversation.Conversation>()
                .Where(c => c.UpdatedAt >= today && yesterdayActiveUsers.Contains(c.CustomerUserId))
                .Select(c => c.CustomerUserId)
                .Distinct()
                .CountAsync(cancellationToken);
            
            metrics.UserRetentionRate = (double)retainedUsers / yesterdayActiveUsers.Count;
        }
    }
    
    private async Task CollectAgentMetricsAsync(
        WhimLabAIDbContext dbContext,
        BusinessMetrics metrics,
        DateTime now,
        CancellationToken cancellationToken)
    {
        // 总Agent数
        metrics.TotalAgents = await dbContext.Set<Domain.Entities.Agent.Agent>()
            .CountAsync(cancellationToken);
        
        // 活跃Agent数（已发布）
        metrics.ActiveAgents = await dbContext.Set<Domain.Entities.Agent.Agent>()
            .Where(a => a.Status == Shared.Enums.AgentStatus.Published)
            .CountAsync(cancellationToken);
        
        // Agent调用次数（最近1小时）
        var oneHourAgo = now.AddHours(-1);
        var agentInvocations = await dbContext.Set<Domain.Entities.Conversation.Conversation>()
            .Where(c => c.CreatedAt >= oneHourAgo && c.AgentId != Guid.Empty)
            .GroupBy(c => c.AgentId)
            .Select(g => new { AgentId = g.Key, Count = g.Count() })
            .ToListAsync(cancellationToken);
        
        metrics.AgentInvocations = agentInvocations.Sum(a => a.Count);
        
        // 按类型统计Agent使用（这里简化为按Agent ID统计）
        foreach (var invocation in agentInvocations)
        {
            metrics.AgentUsageByType[invocation.AgentId.ToString()] = invocation.Count;
        }
    }
    
    private async Task CollectConversationMetricsAsync(
        WhimLabAIDbContext dbContext,
        BusinessMetrics metrics,
        DateTime now,
        CancellationToken cancellationToken)
    {
        // 总对话数
        metrics.TotalConversations = await dbContext.Set<Domain.Entities.Conversation.Conversation>()
            .CountAsync(cancellationToken);
        
        // 活跃对话数（最近24小时有消息）
        var oneDayAgo = now.AddDays(-1);
        metrics.ActiveConversations = await dbContext.Set<Domain.Entities.Conversation.Conversation>()
            .Where(c => c.UpdatedAt >= oneDayAgo)
            .CountAsync(cancellationToken);
        
        // 平均每个对话的消息数
        var conversationStats = await dbContext.Set<Domain.Entities.Conversation.Conversation>()
            .Select(c => new { c.Id, c.MessageCount })
            .ToListAsync(cancellationToken);
        
        if (conversationStats.Any())
        {
            metrics.AverageMessagesPerConversation = conversationStats.Average(c => c.MessageCount);
        }
        
        // 平均对话时长（分钟）
        var completedConversations = await dbContext.Set<Domain.Entities.Conversation.Conversation>()
            .Where(c => c.UpdatedAt < now.AddHours(-1)) // 假设1小时未更新的对话已结束
            .Select(c => new { c.CreatedAt, c.UpdatedAt })
            .ToListAsync(cancellationToken);
        
        if (completedConversations.Any())
        {
            metrics.AverageConversationDuration = completedConversations
                .Average(c => (c.UpdatedAt - c.CreatedAt).TotalMinutes);
        }
    }
    
    private async Task CollectTokenMetricsAsync(
        WhimLabAIDbContext dbContext,
        BusinessMetrics metrics,
        DateTime today,
        CancellationToken cancellationToken)
    {
        // 总Token消耗（今日）
        var tokenUsageToday = await dbContext.Set<Domain.Entities.Subscription.TokenUsage>()
            .Where(t => t.CreatedAt >= today)
            .GroupBy(t => new { t.Model, t.SubscriptionId })
            .Select(g => new 
            { 
                Model = g.Key.Model,
                SubscriptionId = g.Key.SubscriptionId,
                TotalTokens = g.Sum(t => t.Tokens)
            })
            .ToListAsync(cancellationToken);
        
        metrics.TotalTokensConsumed = tokenUsageToday.Sum(t => t.TotalTokens);
        
        // 按模型统计Token
        var tokensByModel = tokenUsageToday
            .GroupBy(t => t.Model)
            .ToDictionary(g => g.Key, g => (long)g.Sum(t => t.TotalTokens));
        
        foreach (var kvp in tokensByModel)
        {
            metrics.TokensByModel[kvp.Key] = kvp.Value;
        }
        
        // 按用户层级统计Token（需要关联用户订阅信息）
        var subscriptionIds = tokenUsageToday.Select(t => t.SubscriptionId).Distinct().ToList();
        var userSubscriptions = await dbContext.Set<Domain.Entities.Subscription.Subscription>()
            .Include(s => s.Plan)
            .Where(s => subscriptionIds.Contains(s.Id) && s.Status == Shared.Enums.SubscriptionStatus.Active)
            .Select(s => new { s.Id, s.CustomerUserId, Tier = s.Plan != null ? s.Plan.Tier : Shared.Enums.SubscriptionTier.Free })
            .ToListAsync(cancellationToken);
        
        var subscriptionDict = userSubscriptions.ToDictionary(s => s.Id, s => s.Tier.ToString());
        
        var tokensByTier = tokenUsageToday
            .Where(t => subscriptionDict.ContainsKey(t.SubscriptionId))
            .GroupBy(t => subscriptionDict[t.SubscriptionId])
            .ToDictionary(g => g.Key, g => (long)g.Sum(t => t.TotalTokens));
        
        foreach (var kvp in tokensByTier)
        {
            metrics.TokensByUserTier[kvp.Key] = kvp.Value;
        }
    }
    
    private async Task CollectRevenueMetricsAsync(
        WhimLabAIDbContext dbContext,
        BusinessMetrics metrics,
        DateTime today,
        DateTime thirtyDaysAgo,
        CancellationToken cancellationToken)
    {
        // 总收入（最近30天）
        var revenueData = await dbContext.Set<Domain.Entities.Payment.Order>()
            .Where(o => o.Status == Shared.Enums.OrderStatus.Paid && o.UpdatedAt >= thirtyDaysAgo)
            .GroupBy(o => o.Type)
            .Select(g => new { OrderType = g.Key, TotalAmount = g.Sum(o => o.FinalAmount.Amount) })
            .ToListAsync(cancellationToken);
        
        metrics.TotalRevenue = revenueData.Sum(r => r.TotalAmount);
        
        // 按层级统计收入
        foreach (var revenue in revenueData)
        {
            metrics.RevenueByTier[revenue.OrderType.ToString()] = revenue.TotalAmount;
        }
        
        // 支付成功率（最近30天）
        var totalPayments = await dbContext.Set<Domain.Entities.Payment.Order>()
            .Where(o => o.CreatedAt >= thirtyDaysAgo)
            .CountAsync(cancellationToken);
        
        var successfulPayments = await dbContext.Set<Domain.Entities.Payment.Order>()
            .Where(o => o.CreatedAt >= thirtyDaysAgo && o.Status == Shared.Enums.OrderStatus.Paid)
            .CountAsync(cancellationToken);
        
        if (totalPayments > 0)
        {
            metrics.PaymentSuccessRate = (double)successfulPayments / totalPayments;
        }
        
        // 平均每用户收入（ARPU）
        var payingUsers = await dbContext.Set<Domain.Entities.Payment.Order>()
            .Where(o => o.Status == Shared.Enums.OrderStatus.Paid && o.UpdatedAt >= thirtyDaysAgo)
            .Select(o => o.CustomerUserId)
            .Distinct()
            .CountAsync(cancellationToken);
        
        if (payingUsers > 0)
        {
            metrics.AverageRevenuePerUser = metrics.TotalRevenue / payingUsers;
        }
    }
}