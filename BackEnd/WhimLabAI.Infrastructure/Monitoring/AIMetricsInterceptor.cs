using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Infrastructure.Monitoring;

/// <summary>
/// AI服务指标拦截器
/// </summary>
public class AIMetricsInterceptor : IAIProvider
{
    private readonly IAIProvider _innerProvider;
    private readonly IMetricsService _metricsService;
    private readonly ILogger<AIMetricsInterceptor> _logger;
    
    public AIMetricsInterceptor(
        IAIProvider innerProvider,
        IMetricsService metricsService,
        ILogger<AIMetricsInterceptor> logger)
    {
        _innerProvider = innerProvider;
        _metricsService = metricsService;
        _logger = logger;
    }
    
    public AIProviderType ProviderType => _innerProvider.ProviderType;
    public string ProviderName => _innerProvider.ProviderName;
    public IReadOnlyList<string> SupportedModels => _innerProvider.SupportedModels;
    
    public async Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            var result = await _innerProvider.IsAvailableAsync(cancellationToken);
            
            _metricsService.SetGauge(
                "whimlab_ai_provider_availability",
                result ? 1 : 0,
                new Dictionary<string, string>
                {
                    ["provider"] = ProviderType.ToString().ToLower()
                });
            
            return result;
        }
        finally
        {
            stopwatch.Stop();
            _logger.LogDebug("AI provider availability check took {Duration}ms", stopwatch.ElapsedMilliseconds);
        }
    }
    
    public async Task<Result<AIResponse>> SendMessageAsync(
        AIRequest request,
        CancellationToken cancellationToken = default)
    {
        var labels = new Dictionary<string, string>
        {
            ["provider"] = ProviderType.ToString().ToLower(),
            ["model"] = request.Model
        };
        
        using var timer = _metricsService.MeasureDuration("whimlab_ai_request_duration_seconds", labels);
        
        try
        {
            var result = await _innerProvider.SendMessageAsync(request, cancellationToken);
            
            // 记录请求计数
            _metricsService.IncrementCounter(
                "whimlab_ai_requests_total",
                labels: new Dictionary<string, string>(labels)
                {
                    ["status"] = result.IsSuccess ? "success" : "failure"
                });
            
            if (result.IsSuccess)
            {
                // 记录Token使用
                _metricsService.IncrementCounter(
                    "whimlab_ai_tokens_total",
                    result.Value!.PromptTokens,
                    new Dictionary<string, string>(labels)
                    {
                        ["type"] = "prompt"
                    });
                
                _metricsService.IncrementCounter(
                    "whimlab_ai_tokens_total",
                    result.Value!.CompletionTokens,
                    new Dictionary<string, string>(labels)
                    {
                        ["type"] = "completion"
                    });
            }
            
            return result;
        }
        catch (Exception ex)
        {
            _metricsService.IncrementCounter(
                "whimlab_ai_requests_total",
                labels: new Dictionary<string, string>(labels)
                {
                    ["status"] = "error"
                });
            
            _logger.LogError(ex, "Error in AI request to {Provider} using {Model}", ProviderType, request.Model);
            throw;
        }
    }
    
    public async IAsyncEnumerable<Result<AIStreamChunk>> StreamMessageAsync(
        AIRequest request,
        [EnumeratorCancellation] CancellationToken cancellationToken = default)
    {
        var labels = new Dictionary<string, string>
        {
            ["provider"] = ProviderType.ToString().ToLower(),
            ["model"] = request.Model
        };
        
        var stopwatch = Stopwatch.StartNew();
        var firstTokenTime = 0L;
        var tokenCount = 0;
        var hasError = false;
        
        try
        {
            await foreach (var chunk in _innerProvider.StreamMessageAsync(request, cancellationToken))
            {
                if (firstTokenTime == 0 && chunk.IsSuccess && !string.IsNullOrEmpty(chunk.Value?.Content))
                {
                    firstTokenTime = stopwatch.ElapsedMilliseconds;
                    
                    // 记录首个Token时间
                    _metricsService.RecordHistogram(
                        "whimlab_ai_stream_first_token_seconds",
                        firstTokenTime / 1000.0,
                        labels);
                }
                
                if (chunk.IsSuccess)
                {
                    tokenCount++;
                }
                else
                {
                    hasError = true;
                }
                
                yield return chunk;
            }
        }
        finally
        {
            stopwatch.Stop();
            
            // 记录流式请求统计
            _metricsService.IncrementCounter(
                "whimlab_ai_requests_total",
                labels: new Dictionary<string, string>(labels)
                {
                    ["status"] = hasError ? "error" : "success"
                });
            
            // 记录总时长
            _metricsService.RecordHistogram(
                "whimlab_ai_stream_duration_seconds",
                stopwatch.Elapsed.TotalSeconds,
                labels);
            
            // 记录Token生成速度
            if (tokenCount > 0 && stopwatch.ElapsedMilliseconds > 0)
            {
                var tokensPerSecond = tokenCount / (stopwatch.ElapsedMilliseconds / 1000.0);
                _metricsService.RecordHistogram(
                    "whimlab_ai_tokens_per_second",
                    tokensPerSecond,
                    labels);
            }
        }
    }
    
    public async Task<Result<int>> GetTokenCountAsync(
        string text,
        string? model = null,
        CancellationToken cancellationToken = default)
    {
        return await _innerProvider.GetTokenCountAsync(text, model, cancellationToken);
    }
    
    public async Task<Result<AIModelInfo>> GetModelInfoAsync(
        string model,
        CancellationToken cancellationToken = default)
    {
        return await _innerProvider.GetModelInfoAsync(model, cancellationToken);
    }
    
    public async Task<Result<AIEmbeddingResponse>> GetEmbeddingAsync(
        AIEmbeddingRequest request,
        CancellationToken cancellationToken = default)
    {
        var labels = new Dictionary<string, string>
        {
            ["provider"] = ProviderType.ToString().ToLower(),
            ["model"] = request.Model
        };
        
        using var timer = _metricsService.MeasureDuration("whimlab_ai_embedding_duration_seconds", labels);
        
        try
        {
            var result = await _innerProvider.GetEmbeddingAsync(request, cancellationToken);
            
            // 记录嵌入请求计数
            _metricsService.IncrementCounter(
                "whimlab_ai_embeddings_total",
                labels: new Dictionary<string, string>(labels)
                {
                    ["status"] = result.IsSuccess ? "success" : "failure"
                });
            
            if (result.IsSuccess)
            {
                // 记录Token使用
                _metricsService.IncrementCounter(
                    "whimlab_ai_embedding_tokens_total",
                    result.Value!.TokensUsed,
                    labels);
            }
            
            return result;
        }
        catch (Exception ex)
        {
            _metricsService.IncrementCounter(
                "whimlab_ai_embeddings_total",
                labels: new Dictionary<string, string>(labels)
                {
                    ["status"] = "error"
                });
            
            _logger.LogError(ex, "Error in embedding request to {Provider} using {Model}", ProviderType, request.Model);
            throw;
        }
    }
}