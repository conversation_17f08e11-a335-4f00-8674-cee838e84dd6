﻿using System.Collections.Generic;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WhimLabAI.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddIpWhitelistToAdminUser : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "PasswordExpiredAt",
                table: "admin_users",
                newName: "password_expired_at");

            migrationBuilder.AddColumn<bool>(
                name: "enable_ip_whitelist",
                table: "admin_users",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<List<string>>(
                name: "ip_whitelist",
                table: "admin_users",
                type: "jsonb",
                nullable: false,
                defaultValueSql: "'[]'::jsonb");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "enable_ip_whitelist",
                table: "admin_users");

            migrationBuilder.DropColumn(
                name: "ip_whitelist",
                table: "admin_users");

            migrationBuilder.RenameColumn(
                name: "password_expired_at",
                table: "admin_users",
                newName: "PasswordExpiredAt");
        }
    }
}
