﻿// <auto-generated />
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using WhimLabAI.Infrastructure.Data;

#nullable disable

namespace WhimLabAI.Infrastructure.Migrations
{
    [DbContext(typeof(WhimLabAIDbContext))]
    [Migration("20250713140030_AddApiKeySaltField")]
    partial class AddApiKeySaltField
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.HasPostgresExtension(modelBuilder, "hstore");
            NpgsqlModelBuilderExtensions.HasPostgresExtension(modelBuilder, "vector");
            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Agent.Agent", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid?>("AgentCategoryId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("ArchivedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("archived_at");

                    b.Property<double>("AverageRating")
                        .HasPrecision(3, 1)
                        .HasColumnType("double precision")
                        .HasColumnName("average_rating");

                    b.Property<Guid?>("CategoryId")
                        .HasColumnType("uuid")
                        .HasColumnName("category_id");

                    b.Property<string>("Cover")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("cover");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid>("CreatorId")
                        .HasColumnType("uuid")
                        .HasColumnName("creator_id");

                    b.Property<Guid?>("CurrentVersionId")
                        .HasColumnType("uuid")
                        .HasColumnName("current_version_id");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("description");

                    b.Property<string>("DetailedIntro")
                        .HasColumnType("text")
                        .HasColumnName("detailed_intro");

                    b.Property<string>("Icon")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("icon");

                    b.Property<int>("LikeCount")
                        .HasColumnType("integer");

                    b.Property<Dictionary<string, object>>("Metadata")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("metadata");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<DateTime?>("PublishedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("published_at");

                    b.Property<int>("RatingCount")
                        .HasColumnType("integer")
                        .HasColumnName("rating_count");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("status");

                    b.Property<string>("UniqueKey")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("unique_key");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<int>("UsageCount")
                        .HasColumnType("integer")
                        .HasColumnName("usage_count");

                    b.Property<int>("ViewCount")
                        .HasColumnType("integer")
                        .HasColumnName("view_count");

                    b.HasKey("Id");

                    b.HasIndex("AgentCategoryId");

                    b.HasIndex("CategoryId");

                    b.HasIndex("CurrentVersionId");

                    b.HasIndex("UniqueKey")
                        .IsUnique();

                    b.ToTable("agents", (string)null);
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Agent.AgentApiKey", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("AgentId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DifyAppId")
                        .HasColumnType("text");

                    b.Property<string>("EncryptedKey")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("ExpiresAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("KeyType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ProviderName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("AgentId");

                    b.ToTable("AgentApiKey");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Agent.AgentCategory", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<int>("AgentCount")
                        .HasColumnType("integer")
                        .HasColumnName("agent_count");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("description");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("display_name");

                    b.Property<string>("Icon")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("icon");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean")
                        .HasColumnName("is_active");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("name");

                    b.Property<Guid?>("ParentId")
                        .HasColumnType("uuid");

                    b.Property<int>("SortOrder")
                        .HasColumnType("integer")
                        .HasColumnName("sort_order");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.HasIndex("ParentId");

                    b.ToTable("agent_categories", (string)null);

                    b.HasData(
                        new
                        {
                            Id = new Guid("10000000-0000-0000-0000-000000000001"),
                            AgentCount = 0,
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "编程、开发、调试相关的AI助手",
                            DisplayName = "编程开发",
                            Icon = "code",
                            IsActive = true,
                            Name = "programming-development",
                            SortOrder = 1,
                            UpdatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("10000000-0000-0000-0000-000000000002"),
                            AgentCount = 0,
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "文章写作、内容创作相关的AI助手",
                            DisplayName = "写作创作",
                            Icon = "edit",
                            IsActive = true,
                            Name = "creative-writing",
                            SortOrder = 2,
                            UpdatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("10000000-0000-0000-0000-000000000003"),
                            AgentCount = 0,
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "多语言翻译、语言学习相关的AI助手",
                            DisplayName = "翻译语言",
                            Icon = "translate",
                            IsActive = true,
                            Name = "translation-language",
                            SortOrder = 3,
                            UpdatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("10000000-0000-0000-0000-000000000004"),
                            AgentCount = 0,
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "数据处理、分析、可视化相关的AI助手",
                            DisplayName = "数据分析",
                            Icon = "analytics",
                            IsActive = true,
                            Name = "data-analysis",
                            SortOrder = 4,
                            UpdatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("10000000-0000-0000-0000-000000000005"),
                            AgentCount = 0,
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "教育、培训、学习辅导相关的AI助手",
                            DisplayName = "教育学习",
                            Icon = "school",
                            IsActive = true,
                            Name = "education-learning",
                            SortOrder = 5,
                            UpdatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("10000000-0000-0000-0000-000000000006"),
                            AgentCount = 0,
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "日常生活、个人助理相关的AI助手",
                            DisplayName = "生活助理",
                            Icon = "assistant",
                            IsActive = true,
                            Name = "life-assistant",
                            SortOrder = 6,
                            UpdatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("10000000-0000-0000-0000-000000000007"),
                            AgentCount = 0,
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "商业分析、金融咨询相关的AI助手",
                            DisplayName = "商业金融",
                            Icon = "business",
                            IsActive = true,
                            Name = "business-finance",
                            SortOrder = 7,
                            UpdatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        },
                        new
                        {
                            Id = new Guid("10000000-0000-0000-0000-000000000008"),
                            AgentCount = 0,
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "其他类型的AI助手",
                            DisplayName = "其他",
                            Icon = "more",
                            IsActive = true,
                            Name = "others",
                            SortOrder = 99,
                            UpdatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc)
                        });
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Agent.AgentRating", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid?>("AgentId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Feedback")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("feedback");

                    b.Property<int>("HelpfulCount")
                        .HasColumnType("integer")
                        .HasColumnName("helpful_count");

                    b.Property<bool>("IsVerifiedPurchase")
                        .HasColumnType("boolean")
                        .HasColumnName("is_verified_purchase");

                    b.Property<DateTime>("RatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("rated_at");

                    b.Property<int>("Score")
                        .HasColumnType("integer")
                        .HasColumnName("score");

                    b.Property<int>("UnhelpfulCount")
                        .HasColumnType("integer")
                        .HasColumnName("unhelpful_count");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.HasKey("Id");

                    b.HasIndex("AgentId");

                    b.HasIndex("Score");

                    b.HasIndex("UserId");

                    b.ToTable("agent_ratings", (string)null);
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Agent.AgentVersion", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("AgentId")
                        .HasColumnType("uuid")
                        .HasColumnName("agent_id");

                    b.Property<string>("ChangeLog")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("change_log");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("KnowledgeBases")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("knowledge_bases");

                    b.Property<string>("ModelConfig")
                        .HasColumnType("jsonb")
                        .HasColumnName("model_config");

                    b.Property<string>("Plugins")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("plugins");

                    b.Property<DateTime?>("PublishedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("published_at");

                    b.Property<string>("PublishedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("published_by");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("status");

                    b.Property<string>("SystemPrompt")
                        .HasColumnType("text")
                        .HasColumnName("system_prompt");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<string>("UserPrompt")
                        .HasColumnType("text")
                        .HasColumnName("user_prompt");

                    b.Property<int>("VersionNumber")
                        .HasColumnType("integer")
                        .HasColumnName("version_number");

                    b.HasKey("Id");

                    b.HasIndex("Status");

                    b.HasIndex("AgentId", "VersionNumber")
                        .IsUnique();

                    b.ToTable("agent_versions", (string)null);
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.ApiKey.ApiKey", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AdminUserId")
                        .HasColumnType("uuid");

                    b.PrimitiveCollection<List<string>>("AllowedDomains")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CustomerUserId")
                        .HasColumnType("uuid");

                    b.Property<int?>("DailyQuota")
                        .HasColumnType("integer");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<DateTime?>("ExpiresAt")
                        .HasColumnType("timestamp with time zone");

                    b.PrimitiveCollection<List<string>>("IpWhitelist")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("KeyHash")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("KeyPrefix")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("KeySalt")
                        .HasColumnType("text");

                    b.Property<string>("KeySuffix")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("KeyType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastUsedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastUsedIp")
                        .HasColumnType("text");

                    b.Property<string>("Metadata")
                        .HasColumnType("jsonb");

                    b.Property<int?>("MonthlyQuota")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("RateLimit")
                        .HasColumnType("integer");

                    b.PrimitiveCollection<List<string>>("Scopes")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<long>("TotalUsageCount")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<string>("UserType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("AdminUserId")
                        .HasFilter("\"AdminUserId\" IS NOT NULL");

                    b.HasIndex("CustomerUserId")
                        .HasFilter("\"CustomerUserId\" IS NOT NULL");

                    b.HasIndex("KeyPrefix", "KeySuffix", "IsActive");

                    b.ToTable("ApiKeys");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.ApiKey.ApiKeyUsage", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("ApiKeyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ErrorMessage")
                        .HasColumnType("text");

                    b.Property<string>("IpAddress")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsSuccess")
                        .HasColumnType("boolean");

                    b.Property<string>("Origin")
                        .HasColumnType("text");

                    b.Property<string>("RequestMethod")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("RequestPath")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long?>("RequestSize")
                        .HasColumnType("bigint");

                    b.Property<long?>("ResponseSize")
                        .HasColumnType("bigint");

                    b.Property<int>("ResponseStatusCode")
                        .HasColumnType("integer");

                    b.Property<long>("ResponseTime")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("UsedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UserAgent")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ApiKeyId");

                    b.HasIndex("UsedAt");

                    b.ToTable("ApiKeyUsages");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Audit.AuditLog", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Action")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ActionName")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("AdditionalData")
                        .HasColumnType("jsonb");

                    b.Property<string>("ChangedProperties")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("ClientInfo")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("ControllerName")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("CorrelationId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("EntityId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("EntityType")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("ExceptionStack")
                        .HasColumnType("text");

                    b.Property<long?>("ExecutionDuration")
                        .HasColumnType("bigint");

                    b.Property<string>("GeoLocation")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("HttpMethod")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<string>("IpAddress")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<bool>("IsSensitive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsSuccess")
                        .HasColumnType("boolean");

                    b.Property<string>("JwtId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Module")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("NewValues")
                        .HasColumnType("jsonb");

                    b.Property<string>("OldValues")
                        .HasColumnType("jsonb");

                    b.Property<string>("RequestId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("RequestParameters")
                        .HasColumnType("jsonb");

                    b.Property<string>("RequestUrl")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<int?>("ResponseStatusCode")
                        .HasColumnType("integer");

                    b.Property<string>("RiskLevel")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasDefaultValue("Low");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<string>("UserAgent")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uuid");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("UserType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("IsSuccess");

                    b.HasIndex("RiskLevel");

                    b.HasIndex("UserId");

                    b.HasIndex("CreatedAt", "UserId")
                        .HasDatabaseName("IX_AuditLogs_CreatedAt_UserId");

                    b.HasIndex("EntityType", "EntityId");

                    b.HasIndex("Module", "Action");

                    b.ToTable("AuditLogs", (string)null);
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Auth.Permission", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("category");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("code");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("description");

                    b.Property<int>("DisplayOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("display_order");

                    b.Property<bool>("IsEnabled")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("is_enabled");

                    b.Property<bool>("IsSystem")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_system");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<Guid?>("ParentId")
                        .HasColumnType("uuid")
                        .HasColumnName("parent_id");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique()
                        .HasDatabaseName("ix_permissions_code");

                    b.ToTable("permissions", (string)null);
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Auth.Role", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("code");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("description");

                    b.Property<int>("DisplayOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("display_order");

                    b.Property<bool>("IsEnabled")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("is_enabled");

                    b.Property<bool>("IsSystem")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_system");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("updated_by");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique()
                        .HasDatabaseName("ix_roles_code");

                    b.ToTable("roles", (string)null);
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Auth.RolePermission", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid>("PermissionId")
                        .HasColumnType("uuid")
                        .HasColumnName("permission_id");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uuid")
                        .HasColumnName("role_id");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("PermissionId");

                    b.HasIndex("RoleId", "PermissionId")
                        .IsUnique()
                        .HasDatabaseName("ix_role_permissions_role_permission");

                    b.ToTable("role_permissions", (string)null);
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Compliance.DataExportRequest", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("DownloadCount")
                        .HasColumnType("integer");

                    b.Property<string>("ErrorMessage")
                        .HasColumnType("text");

                    b.Property<DateTime?>("ExpiresAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ExportFormat")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("FileHash")
                        .HasColumnType("text");

                    b.Property<long?>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<string>("FileUrl")
                        .HasColumnType("text");

                    b.PrimitiveCollection<List<string>>("IncludedDataTypes")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<string>("IpAddress")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsDownloaded")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastDownloadedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("ProcessingStartedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Progress")
                        .HasColumnType("integer");

                    b.Property<string>("RequestType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("RequestedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<string>("UserAgent")
                        .HasColumnType("text");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("Status");

                    b.HasIndex("UserId");

                    b.ToTable("DataExportRequests");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Compliance.DataRetentionPolicy", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("AllowUserOverride")
                        .HasColumnType("boolean");

                    b.Property<bool>("AutoDelete")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DataType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("DeletionMethod")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastExecutedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("NextExecutionAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("PolicyName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Priority")
                        .HasColumnType("integer");

                    b.Property<int>("RetentionDays")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("DataType");

                    b.ToTable("DataRetentionPolicies");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Compliance.PrivacySettings", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("AllowAnalytics")
                        .HasColumnType("boolean");

                    b.Property<bool>("AllowConversationHistory")
                        .HasColumnType("boolean");

                    b.Property<bool>("AllowLocationAccess")
                        .HasColumnType("boolean");

                    b.Property<bool>("AllowMarketing")
                        .HasColumnType("boolean");

                    b.Property<bool>("AllowPersonalization")
                        .HasColumnType("boolean");

                    b.Property<bool>("AllowPublicProfile")
                        .HasColumnType("boolean");

                    b.Property<bool>("AllowSearchEngineIndexing")
                        .HasColumnType("boolean");

                    b.Property<bool>("AllowThirdPartySharing")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("DataRetentionDays")
                        .HasColumnType("integer");

                    b.Property<DateTime>("LastUpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("LoginNotificationEnabled")
                        .HasColumnType("boolean");

                    b.Property<string>("PrivacyMode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("UserId")
                        .IsUnique();

                    b.ToTable("PrivacySettings");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Conversation.Conversation", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("AgentId")
                        .HasColumnType("uuid")
                        .HasColumnName("agent_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid>("CustomerUserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.Property<int>("InputTokens")
                        .HasColumnType("integer")
                        .HasColumnName("input_tokens");

                    b.Property<bool>("IsArchived")
                        .HasColumnType("boolean")
                        .HasColumnName("is_archived");

                    b.Property<DateTime>("LastMessageAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("last_message_at");

                    b.Property<int>("MessageCount")
                        .HasColumnType("integer")
                        .HasColumnName("message_count");

                    b.Property<Dictionary<string, object>>("Metadata")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("metadata");

                    b.Property<string>("Model")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("model");

                    b.Property<int>("OutputTokens")
                        .HasColumnType("integer")
                        .HasColumnName("output_tokens");

                    b.Property<DateTime>("StartedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("started_at");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("title");

                    b.Property<int>("TotalTokens")
                        .HasColumnType("integer")
                        .HasColumnName("total_tokens");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("AgentId");

                    b.HasIndex("CustomerUserId");

                    b.HasIndex("LastMessageAt");

                    b.HasIndex("CustomerUserId", "IsArchived");

                    b.ToTable("conversations", (string)null);
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Conversation.ConversationMessage", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("content");

                    b.Property<Guid>("ConversationId")
                        .HasColumnType("uuid")
                        .HasColumnName("conversation_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Role")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("role");

                    b.Property<int>("SequenceNumber")
                        .HasColumnType("integer")
                        .HasColumnName("sequence_number");

                    b.Property<int>("TokenCount")
                        .HasColumnType("integer")
                        .HasColumnName("token_count");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ConversationId");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("SequenceNumber");

                    b.ToTable("conversation_messages", (string)null);
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Conversation.MessageAttachment", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("ContentType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("content_type");

                    b.Property<Guid?>("ConversationMessageId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("file_name");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint")
                        .HasColumnName("file_size");

                    b.Property<string>("FileUrl")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("file_url");

                    b.Property<Dictionary<string, object>>("Metadata")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("metadata");

                    b.Property<string>("ThumbnailUrl")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("thumbnail_url");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ConversationMessageId");

                    b.ToTable("message_attachments", (string)null);
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.KnowledgeBase.Document", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("ChunkCount")
                        .HasColumnType("integer");

                    b.Property<string>("ContentType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("DocumentType")
                        .HasColumnType("integer");

                    b.Property<string>("FileHash")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<Guid>("KnowledgeBaseId")
                        .HasColumnType("uuid");

                    b.Property<string>("Metadata")
                        .HasColumnType("jsonb");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("OriginalFileUrl")
                        .HasColumnType("text");

                    b.Property<DateTime?>("ProcessingCompletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ProcessingError")
                        .HasColumnType("text");

                    b.Property<DateTime?>("ProcessingStartedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<int>("TotalCharacters")
                        .HasColumnType("integer");

                    b.Property<int>("TotalTokens")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("Status");

                    b.HasIndex("FileHash", "KnowledgeBaseId");

                    b.HasIndex("KnowledgeBaseId", "IsDeleted");

                    b.ToTable("Documents");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.KnowledgeBase.DocumentChunk", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("CharacterCount")
                        .HasColumnType("integer");

                    b.Property<int>("ChunkIndex")
                        .HasColumnType("integer");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ContentHash")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("DocumentId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("EmbeddedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsEmbedded")
                        .HasColumnType("boolean");

                    b.Property<Guid>("KnowledgeBaseId")
                        .HasColumnType("uuid");

                    b.Property<string>("Metadata")
                        .HasColumnType("jsonb");

                    b.Property<int>("TokenCount")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<string>("VectorId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("DocumentId");

                    b.HasIndex("KnowledgeBaseId");

                    b.HasIndex("VectorId");

                    b.HasIndex("KnowledgeBaseId", "IsEmbedded");

                    b.ToTable("DocumentChunks");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.KnowledgeBase.KnowledgeBase", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ChunkingConfig")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<int>("DocumentCount")
                        .HasColumnType("integer");

                    b.Property<string>("EmbeddingModel")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsPublic")
                        .HasColumnType("boolean");

                    b.Property<string>("Metadata")
                        .HasColumnType("jsonb");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("OwnerId")
                        .HasColumnType("uuid");

                    b.Property<int>("OwnerType")
                        .HasColumnType("integer");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<long>("StorageSize")
                        .HasColumnType("bigint");

                    b.Property<int>("TotalVectors")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<string>("VectorDbConfig")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<int>("VectorDbType")
                        .HasColumnType("integer");

                    b.Property<int>("VectorDimension")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("Status");

                    b.HasIndex("OwnerId", "OwnerType", "IsDeleted");

                    b.ToTable("KnowledgeBases");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Monitoring.AlertHistoryEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AlertName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("AnnotationsJson")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<int?>("DurationSeconds")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("EndsAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Fingerprint")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("GeneratorUrl")
                        .HasColumnType("text");

                    b.Property<bool>("IsFalsePositive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsNotified")
                        .HasColumnType("boolean");

                    b.Property<string>("LabelsJson")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Notes")
                        .HasColumnType("text");

                    b.Property<string>("NotificationChannels")
                        .HasColumnType("text");

                    b.Property<DateTime?>("NotifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Receiver")
                        .HasColumnType("text");

                    b.Property<string>("Resolution")
                        .HasColumnType("text");

                    b.Property<string>("ResolvedBy")
                        .HasColumnType("text");

                    b.Property<string>("Service")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Severity")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("StartsAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Summary")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("AlertHistories");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Notification.Notification", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("content");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<DateTime?>("ExpiresAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("expires_at");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<bool>("IsRead")
                        .HasColumnType("boolean")
                        .HasColumnName("is_read");

                    b.Property<string>("Level")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("level");

                    b.Property<string>("MetadataJson")
                        .HasColumnType("jsonb")
                        .HasColumnName("metadata_json");

                    b.Property<DateTime?>("ReadAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("read_at");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("title");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("type");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("updated_by");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("idx_notifications_created_at");

                    b.HasIndex("ExpiresAt")
                        .HasDatabaseName("idx_notifications_expires_at");

                    b.HasIndex("Type")
                        .HasDatabaseName("idx_notifications_type");

                    b.HasIndex("UserId")
                        .HasDatabaseName("idx_notifications_user_id");

                    b.HasIndex("UserId", "IsRead", "IsDeleted")
                        .HasDatabaseName("idx_notifications_user_read_deleted");

                    b.ToTable("notifications", (string)null);
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Payment.Coupon", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ApplicableProducts")
                        .HasColumnType("jsonb");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<decimal?>("DiscountPercentage")
                        .HasColumnType("decimal(5,2)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<string>("Metadata")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Rules")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<string>("Scope")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("TotalQuota")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(-1);

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<int?>("UsagePerUser")
                        .HasColumnType("integer");

                    b.Property<int>("UsedCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<DateTime>("ValidFrom")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("ValidTo")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.ToTable("Coupons", (string)null);
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Payment.CouponUsage", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CouponId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("UsedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("CouponId");

                    b.HasIndex("OrderId");

                    b.HasIndex("UserId");

                    b.HasIndex("UserId", "UsedAt");

                    b.ToTable("CouponUsages", (string)null);
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Payment.Order", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CouponCode")
                        .HasColumnType("text");

                    b.Property<Guid?>("CouponId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("CustomerUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("ExpireAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<Dictionary<string, object>>("Metadata")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<string>("OrderNo")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("PaidAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("PaymentMethod")
                        .HasColumnType("integer");

                    b.Property<Guid?>("ProductId")
                        .HasColumnType("uuid");

                    b.Property<string>("ProductName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("RefundedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Remark")
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Orders");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Payment.PaymentTransaction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("ExpireAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("FailReason")
                        .HasColumnType("text");

                    b.Property<DateTime?>("FailedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uuid");

                    b.Property<string>("PayerAccount")
                        .HasColumnType("text");

                    b.Property<string>("PayerName")
                        .HasColumnType("text");

                    b.Property<int>("PaymentMethod")
                        .HasColumnType("integer");

                    b.Property<string>("PaymentNo")
                        .HasColumnType("text");

                    b.Property<Dictionary<string, string>>("RawData")
                        .IsRequired()
                        .HasColumnType("hstore");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("TransactionId")
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("OrderId");

                    b.ToTable("PaymentTransactions");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Payment.RefundRecord", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("FailureReason")
                        .HasColumnType("text");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("ProcessedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ProcessedBy")
                        .HasColumnType("text");

                    b.Property<string>("Reason")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("RefundNo")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("RefundTransactionId")
                        .HasColumnType("text");

                    b.Property<string>("Remark")
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("OrderId");

                    b.ToTable("RefundRecords");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Subscription.QuotaAlert", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("AlertedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsNotified")
                        .HasColumnType("boolean");

                    b.Property<string>("NotificationMethod")
                        .HasColumnType("text");

                    b.Property<DateTime?>("NotifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("SubscriptionId")
                        .HasColumnType("uuid");

                    b.Property<int>("TotalTokens")
                        .HasColumnType("integer");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<double>("UsagePercentage")
                        .HasColumnType("double precision");

                    b.Property<int>("UsedTokens")
                        .HasColumnType("integer");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("SubscriptionId");

                    b.ToTable("QuotaAlerts");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Subscription.Subscription", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<bool>("AutoRenew")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("CancellationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CancellationReason")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<Guid>("CustomerUserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("ExpiredAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastRenewalDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastResetDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("NextBillingDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("NextPlanId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("NextResetDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("OrderId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("PauseDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("PaymentMethod")
                        .HasColumnType("text");

                    b.Property<Guid>("PlanId")
                        .HasColumnType("uuid");

                    b.Property<int>("RemainingTokens")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("ResumeDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CustomerUserId");

                    b.HasIndex("EndDate");

                    b.HasIndex("PlanId");

                    b.HasIndex("Status");

                    b.HasIndex("CustomerUserId", "Status");

                    b.ToTable("Subscriptions", (string)null);
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Subscription.SubscriptionPlan", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<bool>("AllowCustomAgents")
                        .HasColumnType("boolean");

                    b.Property<bool>("AllowKnowledgeBase")
                        .HasColumnType("boolean");

                    b.Property<bool>("AllowPlugins")
                        .HasColumnType("boolean");

                    b.Property<string>("BillingCycle")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Features")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsPopular")
                        .HasColumnType("boolean");

                    b.Property<IReadOnlyDictionary<string, object>>("Limits")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<int>("MaxAgents")
                        .HasColumnType("integer");

                    b.Property<int>("MaxConversationsPerDay")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("SortOrder")
                        .HasColumnType("integer");

                    b.Property<string>("Tier")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("TokenQuota")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("ValidFrom")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("ValidTo")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("IsActive");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.HasIndex("Tier");

                    b.ToTable("SubscriptionPlans", (string)null);
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Subscription.TokenUsage", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AgentId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ConversationId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("MessageId")
                        .HasColumnType("text");

                    b.Property<Dictionary<string, object>>("Metadata")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<string>("Model")
                        .HasColumnType("text");

                    b.Property<Guid>("SubscriptionId")
                        .HasColumnType("uuid");

                    b.Property<int>("Tokens")
                        .HasColumnType("integer");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("UsedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("SubscriptionId");

                    b.ToTable("TokenUsages");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Subscription.UsageRecord", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<Guid>("AgentId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("ConversationId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("CostAmount")
                        .HasPrecision(18, 6)
                        .HasColumnType("numeric(18,6)");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<Dictionary<string, object>>("Metadata")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<string>("ModelName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ModelProvider")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("RequestId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("SessionId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<Guid>("SubscriptionId")
                        .HasColumnType("uuid");

                    b.Property<int>("TokensUsed")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("UsageTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("SubscriptionId");

                    b.HasIndex("UserId");

                    b.HasIndex("SubscriptionId", "CreatedAt");

                    b.HasIndex("UserId", "CreatedAt");

                    b.ToTable("UsageRecords", (string)null);
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.System.AdminUserSession", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("AdminUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("City")
                        .HasColumnType("text");

                    b.Property<string>("Country")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DeviceId")
                        .HasColumnType("text");

                    b.Property<string>("DeviceName")
                        .HasColumnType("text");

                    b.Property<DateTime>("ExpiresAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("IpAddress")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsAnomalousLocation")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("LastActivityAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<double?>("Latitude")
                        .HasColumnType("double precision");

                    b.Property<double?>("Longitude")
                        .HasColumnType("double precision");

                    b.Property<bool>("MfaVerified")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("MfaVerifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("RefreshToken")
                        .HasColumnType("text");

                    b.Property<DateTime?>("RefreshTokenExpiresAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Region")
                        .HasColumnType("text");

                    b.Property<bool>("RequiresMfa")
                        .HasColumnType("boolean");

                    b.Property<string>("SessionToken")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<string>("UserAgent")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("AdminUserSessions");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.System.CustomerUserSession", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("CustomerUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeviceId")
                        .HasColumnType("text");

                    b.Property<string>("DeviceName")
                        .HasColumnType("text");

                    b.Property<DateTime>("ExpiresAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("IpAddress")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("LastActivityAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("RefreshToken")
                        .HasColumnType("text");

                    b.Property<DateTime?>("RefreshTokenExpiresAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("SessionToken")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<string>("UserAgent")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("CustomerUserSessions");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.User.AdminUser", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Avatar")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("avatar");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("email");

                    b.Property<bool>("EnableIpWhitelist")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("enable_ip_whitelist");

                    b.Property<int>("FailedLoginAttempts")
                        .HasColumnType("integer")
                        .HasColumnName("login_failed_count");

                    b.PrimitiveCollection<List<string>>("IpWhitelist")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("ip_whitelist");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsSuperAdmin")
                        .HasColumnType("boolean")
                        .HasColumnName("is_super");

                    b.Property<DateTime?>("LastLoginAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("last_login_at");

                    b.Property<string>("LastLoginIp")
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)")
                        .HasColumnName("last_login_ip");

                    b.Property<DateTime?>("LockedUntil")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("locked_until");

                    b.Property<string>("Nickname")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("nickname");

                    b.Property<DateTime?>("PasswordExpiredAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("password_expired_at");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("password_hash");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("status");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("two_factor_enabled");

                    b.Property<string>("TwoFactorSecret")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("two_factor_secret");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("username");

                    b.HasKey("Id");

                    b.HasIndex("Email")
                        .HasDatabaseName("ix_admin_users_email");

                    b.HasIndex("Username")
                        .IsUnique()
                        .HasDatabaseName("ix_admin_users_username");

                    b.ToTable("admin_users", (string)null);
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.User.AdminUserRole", b =>
                {
                    b.Property<Guid>("AdminUserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uuid")
                        .HasColumnName("role_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.HasKey("AdminUserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.HasIndex("AdminUserId", "RoleId")
                        .IsUnique()
                        .HasDatabaseName("ix_admin_user_roles_admin_user_role");

                    b.ToTable("admin_user_roles", (string)null);
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.User.CustomerProfile", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Address")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("address");

                    b.Property<string>("City")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("city");

                    b.Property<string>("Company")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("company");

                    b.Property<string>("Country")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("country");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("CustomFields")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("custom_fields");

                    b.Property<string>("Department")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("department");

                    b.Property<string>("GitHub")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("github");

                    b.Property<string>("JobTitle")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("job_title");

                    b.Property<string>("LinkedIn")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("linkedin");

                    b.Property<string>("PostalCode")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("postal_code");

                    b.Property<string>("Province")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("province");

                    b.Property<string>("Twitter")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("twitter");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.Property<string>("Website")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("website");

                    b.Property<string>("WorkEmail")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("work_email");

                    b.Property<string>("WorkPhone")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("work_phone");

                    b.HasKey("Id");

                    b.HasIndex("UserId")
                        .IsUnique()
                        .HasDatabaseName("ix_customer_profiles_user_id");

                    b.ToTable("customer_profiles", (string)null);
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.User.CustomerUser", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Avatar")
                        .HasColumnType("text");

                    b.Property<string>("Bio")
                        .HasColumnType("text");

                    b.Property<DateTime?>("Birthday")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<int>("Gender")
                        .HasColumnType("integer");

                    b.Property<string>("Industry")
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean")
                        .HasColumnName("is_active");

                    b.Property<bool>("IsBanned")
                        .HasColumnType("boolean")
                        .HasColumnName("is_banned");

                    b.Property<bool>("IsEmailVerified")
                        .HasColumnType("boolean")
                        .HasColumnName("is_email_verified");

                    b.Property<bool>("IsPhoneVerified")
                        .HasColumnType("boolean")
                        .HasColumnName("is_phone_verified");

                    b.Property<DateTime?>("LastLoginAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("last_login_at");

                    b.Property<string>("LastLoginIp")
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)")
                        .HasColumnName("last_login_ip");

                    b.Property<string>("Nickname")
                        .HasColumnType("text");

                    b.Property<string>("Position")
                        .HasColumnType("text");

                    b.Property<string>("RefreshToken")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("refresh_token");

                    b.Property<string>("Region")
                        .HasColumnType("text");

                    b.Property<string>("RegisterIp")
                        .HasColumnType("text");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("two_factor_enabled");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("username");

                    b.Property<DateTime?>("_lockedUntil")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("locked_until");

                    b.Property<int>("_loginFailedCount")
                        .HasColumnType("integer")
                        .HasColumnName("login_failed_count");

                    b.Property<DateTime?>("_refreshTokenExpiry")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("refresh_token_expiry");

                    b.Property<string>("_twoFactorSecret")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("two_factor_secret");

                    b.HasKey("Id");

                    b.HasIndex("Username")
                        .IsUnique()
                        .HasDatabaseName("ix_customer_users_username");

                    b.ToTable("customer_users", (string)null);
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.User.DeviceAuthorization", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("AuthorizedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CustomerUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DeviceId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("DeviceInfo")
                        .HasColumnType("text");

                    b.Property<string>("DeviceName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("DeviceType")
                        .HasColumnType("text");

                    b.Property<string>("IpAddress")
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("LastAccessAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("OperatingSystem")
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CustomerUserId");

                    b.ToTable("DeviceAuthorizations");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.User.LoginHistory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Browser")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Device")
                        .HasColumnType("text");

                    b.Property<string>("FailureReason")
                        .HasColumnType("text");

                    b.Property<bool>("IsSuccess")
                        .HasColumnType("boolean");

                    b.Property<string>("Location")
                        .HasColumnType("text");

                    b.Property<DateTime>("LoginAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LoginIp")
                        .HasColumnType("text");

                    b.Property<int>("LoginMethod")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LogoutAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("SessionToken")
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<string>("UserAgent")
                        .HasColumnType("text");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("LoginHistories");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.User.OAuthBinding", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AccessToken")
                        .HasColumnType("text");

                    b.Property<string>("Avatar")
                        .HasColumnType("text");

                    b.Property<DateTime>("BindAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CustomerUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("DisplayName")
                        .HasColumnType("text");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<Dictionary<string, string>>("ExtraData")
                        .IsRequired()
                        .HasColumnType("hstore");

                    b.Property<DateTime>("LastUsedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Provider")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ProviderUserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("RefreshToken")
                        .HasColumnType("text");

                    b.Property<DateTime?>("TokenExpiresAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CustomerUserId");

                    b.ToTable("OAuthBindings");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.User.QRCodeSession", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("AuthenticatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ClientIp")
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DeviceId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("DeviceName")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("DeviceType")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("ExpiresAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("QRCodeData")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime?>("ScannedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ScannedByDeviceId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ScannedByIp")
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)");

                    b.Property<string>("SessionId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<string>("UserAgent")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("ExpiresAt");

                    b.HasIndex("SessionId")
                        .IsUnique();

                    b.HasIndex("Status");

                    b.HasIndex("UserId");

                    b.HasIndex("Status", "ExpiresAt");

                    b.ToTable("QRCodeSessions", (string)null);
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.User.UserConsent", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AdminUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("ConsentText")
                        .HasColumnType("text");

                    b.Property<string>("ConsentType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("ConsentedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CustomerUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("IpAddress")
                        .HasColumnType("text");

                    b.Property<bool>("IsRevoked")
                        .HasColumnType("boolean");

                    b.Property<Dictionary<string, object>>("Metadata")
                        .HasColumnType("jsonb");

                    b.Property<DateTime?>("RevokedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<string>("UserAgent")
                        .HasColumnType("text");

                    b.Property<string>("UserType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Version")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("AdminUserId", "ConsentType")
                        .HasFilter("\"AdminUserId\" IS NOT NULL");

                    b.HasIndex("CustomerUserId", "ConsentType")
                        .HasFilter("\"CustomerUserId\" IS NOT NULL");

                    b.ToTable("UserConsents");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Agent.Agent", b =>
                {
                    b.HasOne("WhimLabAI.Domain.Entities.Agent.AgentCategory", null)
                        .WithMany("Agents")
                        .HasForeignKey("AgentCategoryId");

                    b.HasOne("WhimLabAI.Domain.Entities.Agent.AgentCategory", "Category")
                        .WithMany()
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("WhimLabAI.Domain.Entities.Agent.AgentVersion", "CurrentVersion")
                        .WithMany()
                        .HasForeignKey("CurrentVersionId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.OwnsMany("WhimLabAI.Domain.Entities.Agent.AgentTag", "Tags", b1 =>
                        {
                            b1.Property<Guid>("AgentId")
                                .HasColumnType("uuid");

                            b1.Property<int>("__synthesizedOrdinal")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer");

                            b1.Property<string>("Name")
                                .IsRequired()
                                .HasMaxLength(50)
                                .HasColumnType("character varying(50)");

                            b1.HasKey("AgentId", "__synthesizedOrdinal");

                            b1.ToTable("agents");

                            b1.ToJson("tags");

                            b1.WithOwner()
                                .HasForeignKey("AgentId");
                        });

                    b.Navigation("Category");

                    b.Navigation("CurrentVersion");

                    b.Navigation("Tags");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Agent.AgentApiKey", b =>
                {
                    b.HasOne("WhimLabAI.Domain.Entities.Agent.Agent", null)
                        .WithMany("ApiKeys")
                        .HasForeignKey("AgentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Agent.AgentCategory", b =>
                {
                    b.HasOne("WhimLabAI.Domain.Entities.Agent.AgentCategory", "Parent")
                        .WithMany("Children")
                        .HasForeignKey("ParentId");

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Agent.AgentRating", b =>
                {
                    b.HasOne("WhimLabAI.Domain.Entities.Agent.Agent", null)
                        .WithMany("Ratings")
                        .HasForeignKey("AgentId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Agent.AgentVersion", b =>
                {
                    b.HasOne("WhimLabAI.Domain.Entities.Agent.Agent", "Agent")
                        .WithMany("Versions")
                        .HasForeignKey("AgentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Agent");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.ApiKey.ApiKeyUsage", b =>
                {
                    b.HasOne("WhimLabAI.Domain.Entities.ApiKey.ApiKey", "ApiKey")
                        .WithMany()
                        .HasForeignKey("ApiKeyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ApiKey");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Auth.RolePermission", b =>
                {
                    b.HasOne("WhimLabAI.Domain.Entities.Auth.Permission", "Permission")
                        .WithMany()
                        .HasForeignKey("PermissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WhimLabAI.Domain.Entities.Auth.Role", "Role")
                        .WithMany("Permissions")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Permission");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Conversation.ConversationMessage", b =>
                {
                    b.HasOne("WhimLabAI.Domain.Entities.Conversation.Conversation", "Conversation")
                        .WithMany("Messages")
                        .HasForeignKey("ConversationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("WhimLabAI.Domain.Entities.Conversation.MessageRating", "Rating", b1 =>
                        {
                            b1.Property<Guid>("ConversationMessageId")
                                .HasColumnType("uuid");

                            b1.Property<string>("Feedback")
                                .HasMaxLength(500)
                                .HasColumnType("character varying(500)")
                                .HasColumnName("rating_feedback");

                            b1.Property<DateTime>("RatedAt")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("rated_at");

                            b1.Property<int>("Score")
                                .HasColumnType("integer")
                                .HasColumnName("rating_score");

                            b1.Property<DateTime?>("UpdatedAt")
                                .HasColumnType("timestamp with time zone");

                            b1.HasKey("ConversationMessageId");

                            b1.ToTable("conversation_messages");

                            b1.WithOwner()
                                .HasForeignKey("ConversationMessageId");
                        });

                    b.Navigation("Conversation");

                    b.Navigation("Rating");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Conversation.MessageAttachment", b =>
                {
                    b.HasOne("WhimLabAI.Domain.Entities.Conversation.ConversationMessage", null)
                        .WithMany("Attachments")
                        .HasForeignKey("ConversationMessageId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.KnowledgeBase.Document", b =>
                {
                    b.HasOne("WhimLabAI.Domain.Entities.KnowledgeBase.KnowledgeBase", "KnowledgeBase")
                        .WithMany("Documents")
                        .HasForeignKey("KnowledgeBaseId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("KnowledgeBase");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.KnowledgeBase.DocumentChunk", b =>
                {
                    b.HasOne("WhimLabAI.Domain.Entities.KnowledgeBase.Document", "Document")
                        .WithMany("Chunks")
                        .HasForeignKey("DocumentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Document");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Payment.Coupon", b =>
                {
                    b.OwnsOne("WhimLabAI.Domain.ValueObjects.Money", "DiscountAmount", b1 =>
                        {
                            b1.Property<Guid>("CouponId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,2)")
                                .HasColumnName("DiscountAmount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("DiscountCurrency");

                            b1.HasKey("CouponId");

                            b1.ToTable("Coupons");

                            b1.WithOwner()
                                .HasForeignKey("CouponId");
                        });

                    b.OwnsOne("WhimLabAI.Domain.ValueObjects.Money", "MaximumDiscount", b1 =>
                        {
                            b1.Property<Guid>("CouponId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,2)")
                                .HasColumnName("MaximumDiscount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("MaximumDiscountCurrency");

                            b1.HasKey("CouponId");

                            b1.ToTable("Coupons");

                            b1.WithOwner()
                                .HasForeignKey("CouponId");
                        });

                    b.OwnsOne("WhimLabAI.Domain.ValueObjects.Money", "MinimumAmount", b1 =>
                        {
                            b1.Property<Guid>("CouponId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,2)")
                                .HasColumnName("MinimumAmount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("MinimumCurrency");

                            b1.HasKey("CouponId");

                            b1.ToTable("Coupons");

                            b1.WithOwner()
                                .HasForeignKey("CouponId");
                        });

                    b.Navigation("DiscountAmount");

                    b.Navigation("MaximumDiscount");

                    b.Navigation("MinimumAmount");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Payment.CouponUsage", b =>
                {
                    b.HasOne("WhimLabAI.Domain.Entities.Payment.Coupon", null)
                        .WithMany("Usages")
                        .HasForeignKey("CouponId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.OwnsOne("WhimLabAI.Domain.ValueObjects.Money", "DiscountAmount", b1 =>
                        {
                            b1.Property<Guid>("CouponUsageId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,2)")
                                .HasColumnName("DiscountAmount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("DiscountCurrency");

                            b1.HasKey("CouponUsageId");

                            b1.ToTable("CouponUsages");

                            b1.WithOwner()
                                .HasForeignKey("CouponUsageId");
                        });

                    b.Navigation("DiscountAmount")
                        .IsRequired();
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Payment.Order", b =>
                {
                    b.OwnsOne("WhimLabAI.Domain.ValueObjects.Money", "Amount", b1 =>
                        {
                            b1.Property<Guid>("OrderId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 2)
                                .HasColumnType("numeric(18,2)")
                                .HasColumnName("Amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("AmountCurrency");

                            b1.HasKey("OrderId");

                            b1.ToTable("Orders");

                            b1.WithOwner()
                                .HasForeignKey("OrderId");
                        });

                    b.OwnsOne("WhimLabAI.Domain.ValueObjects.Money", "DiscountAmount", b1 =>
                        {
                            b1.Property<Guid>("OrderId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 2)
                                .HasColumnType("numeric(18,2)")
                                .HasColumnName("DiscountAmount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("DiscountCurrency");

                            b1.HasKey("OrderId");

                            b1.ToTable("Orders");

                            b1.WithOwner()
                                .HasForeignKey("OrderId");
                        });

                    b.OwnsOne("WhimLabAI.Domain.ValueObjects.Money", "FinalAmount", b1 =>
                        {
                            b1.Property<Guid>("OrderId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 2)
                                .HasColumnType("numeric(18,2)")
                                .HasColumnName("FinalAmount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("FinalCurrency");

                            b1.HasKey("OrderId");

                            b1.ToTable("Orders");

                            b1.WithOwner()
                                .HasForeignKey("OrderId");
                        });

                    b.OwnsOne("WhimLabAI.Domain.ValueObjects.Money", "PayableAmount", b1 =>
                        {
                            b1.Property<Guid>("OrderId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 2)
                                .HasColumnType("numeric(18,2)")
                                .HasColumnName("PayableAmount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("PayableCurrency");

                            b1.HasKey("OrderId");

                            b1.ToTable("Orders");

                            b1.WithOwner()
                                .HasForeignKey("OrderId");
                        });

                    b.Navigation("Amount")
                        .IsRequired();

                    b.Navigation("DiscountAmount");

                    b.Navigation("FinalAmount")
                        .IsRequired();

                    b.Navigation("PayableAmount")
                        .IsRequired();
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Payment.PaymentTransaction", b =>
                {
                    b.HasOne("WhimLabAI.Domain.Entities.Payment.Order", "Order")
                        .WithMany("Transactions")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("WhimLabAI.Domain.ValueObjects.Money", "Amount", b1 =>
                        {
                            b1.Property<Guid>("PaymentTransactionId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 2)
                                .HasColumnType("numeric(18,2)")
                                .HasColumnName("amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("currency");

                            b1.HasKey("PaymentTransactionId");

                            b1.ToTable("PaymentTransactions");

                            b1.WithOwner()
                                .HasForeignKey("PaymentTransactionId");
                        });

                    b.Navigation("Amount")
                        .IsRequired();

                    b.Navigation("Order");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Payment.RefundRecord", b =>
                {
                    b.HasOne("WhimLabAI.Domain.Entities.Payment.Order", "Order")
                        .WithMany("Refunds")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("WhimLabAI.Domain.ValueObjects.Money", "RefundAmount", b1 =>
                        {
                            b1.Property<Guid>("RefundRecordId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 2)
                                .HasColumnType("numeric(18,2)")
                                .HasColumnName("refund_amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("refund_currency");

                            b1.HasKey("RefundRecordId");

                            b1.ToTable("RefundRecords");

                            b1.WithOwner()
                                .HasForeignKey("RefundRecordId");
                        });

                    b.Navigation("Order");

                    b.Navigation("RefundAmount")
                        .IsRequired();
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Subscription.QuotaAlert", b =>
                {
                    b.HasOne("WhimLabAI.Domain.Entities.Subscription.Subscription", "Subscription")
                        .WithMany()
                        .HasForeignKey("SubscriptionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Subscription");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Subscription.Subscription", b =>
                {
                    b.HasOne("WhimLabAI.Domain.Entities.Subscription.SubscriptionPlan", "Plan")
                        .WithMany()
                        .HasForeignKey("PlanId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.OwnsOne("WhimLabAI.Domain.ValueObjects.Money", "PaidAmount", b1 =>
                        {
                            b1.Property<Guid>("SubscriptionId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 2)
                                .HasColumnType("numeric(18,2)")
                                .HasColumnName("PaidAmount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("PaidCurrency");

                            b1.HasKey("SubscriptionId");

                            b1.ToTable("Subscriptions");

                            b1.WithOwner()
                                .HasForeignKey("SubscriptionId");
                        });

                    b.OwnsOne("WhimLabAI.Domain.ValueObjects.TokenQuota", "TokenQuota", b1 =>
                        {
                            b1.Property<Guid>("SubscriptionId")
                                .HasColumnType("uuid");

                            b1.Property<int>("Total")
                                .HasColumnType("integer")
                                .HasColumnName("TokenQuotaTotal");

                            b1.Property<int>("Used")
                                .HasColumnType("integer")
                                .HasColumnName("TokenQuotaUsed");

                            b1.HasKey("SubscriptionId");

                            b1.ToTable("Subscriptions");

                            b1.WithOwner()
                                .HasForeignKey("SubscriptionId");
                        });

                    b.Navigation("PaidAmount")
                        .IsRequired();

                    b.Navigation("Plan");

                    b.Navigation("TokenQuota")
                        .IsRequired();
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Subscription.SubscriptionPlan", b =>
                {
                    b.OwnsOne("WhimLabAI.Domain.ValueObjects.Money", "Price", b1 =>
                        {
                            b1.Property<Guid>("SubscriptionPlanId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 2)
                                .HasColumnType("numeric(18,2)")
                                .HasColumnName("price");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("currency");

                            b1.HasKey("SubscriptionPlanId");

                            b1.ToTable("SubscriptionPlans");

                            b1.WithOwner()
                                .HasForeignKey("SubscriptionPlanId");
                        });

                    b.Navigation("Price")
                        .IsRequired();
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Subscription.TokenUsage", b =>
                {
                    b.HasOne("WhimLabAI.Domain.Entities.Subscription.Subscription", "Subscription")
                        .WithMany()
                        .HasForeignKey("SubscriptionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Subscription");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Subscription.UsageRecord", b =>
                {
                    b.HasOne("WhimLabAI.Domain.Entities.Subscription.Subscription", "Subscription")
                        .WithMany("UsageRecords")
                        .HasForeignKey("SubscriptionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WhimLabAI.Domain.Entities.User.CustomerUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Subscription");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.User.AdminUser", b =>
                {
                    b.OwnsOne("WhimLabAI.Domain.ValueObjects.PhoneNumber", "Phone", b1 =>
                        {
                            b1.Property<Guid>("AdminUserId")
                                .HasColumnType("uuid");

                            b1.Property<string>("CountryCode")
                                .IsRequired()
                                .HasMaxLength(5)
                                .HasColumnType("character varying(5)")
                                .HasColumnName("phone_country_code");

                            b1.Property<string>("Value")
                                .IsRequired()
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)")
                                .HasColumnName("phone_number");

                            b1.HasKey("AdminUserId");

                            b1.ToTable("admin_users");

                            b1.WithOwner()
                                .HasForeignKey("AdminUserId");
                        });

                    b.Navigation("Phone");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.User.AdminUserRole", b =>
                {
                    b.HasOne("WhimLabAI.Domain.Entities.User.AdminUser", "AdminUser")
                        .WithMany("UserRoles")
                        .HasForeignKey("AdminUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WhimLabAI.Domain.Entities.Auth.Role", "Role")
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AdminUser");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.User.CustomerProfile", b =>
                {
                    b.HasOne("WhimLabAI.Domain.Entities.User.CustomerUser", "User")
                        .WithOne("Profile")
                        .HasForeignKey("WhimLabAI.Domain.Entities.User.CustomerProfile", "UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.User.CustomerUser", b =>
                {
                    b.OwnsOne("WhimLabAI.Domain.ValueObjects.PhoneNumber", "Phone", b1 =>
                        {
                            b1.Property<Guid>("CustomerUserId")
                                .HasColumnType("uuid");

                            b1.Property<string>("CountryCode")
                                .IsRequired()
                                .ValueGeneratedOnAdd()
                                .HasMaxLength(5)
                                .HasColumnType("character varying(5)")
                                .HasDefaultValue("+86")
                                .HasColumnName("country_code");

                            b1.Property<string>("Value")
                                .IsRequired()
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)")
                                .HasColumnName("phone_number");

                            b1.HasKey("CustomerUserId");

                            b1.HasIndex("Value")
                                .HasDatabaseName("ix_customer_users_phone_number");

                            b1.ToTable("customer_users");

                            b1.WithOwner()
                                .HasForeignKey("CustomerUserId");
                        });

                    b.OwnsOne("WhimLabAI.Domain.Entities.User.NotificationSetting", "NotificationSetting", b1 =>
                        {
                            b1.Property<Guid>("CustomerUserId")
                                .HasColumnType("uuid");

                            b1.Property<bool>("EmailNotification")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("boolean")
                                .HasDefaultValue(true)
                                .HasColumnName("notification_email");

                            b1.Property<bool>("NewFeatureNotification")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("boolean")
                                .HasDefaultValue(true)
                                .HasColumnName("notification_features");

                            b1.Property<bool>("NewsletterSubscription")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("boolean")
                                .HasDefaultValue(false)
                                .HasColumnName("notification_newsletter");

                            b1.Property<bool>("PromotionNotification")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("boolean")
                                .HasDefaultValue(false)
                                .HasColumnName("notification_promotion");

                            b1.Property<bool>("QuotaAlert")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("boolean")
                                .HasDefaultValue(true)
                                .HasColumnName("notification_quota");

                            b1.Property<bool>("SecurityAlert")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("boolean")
                                .HasDefaultValue(true)
                                .HasColumnName("notification_security");

                            b1.Property<bool>("SmsNotification")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("boolean")
                                .HasDefaultValue(false)
                                .HasColumnName("notification_sms");

                            b1.Property<bool>("SystemNotification")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("boolean")
                                .HasDefaultValue(true)
                                .HasColumnName("notification_system");

                            b1.HasKey("CustomerUserId");

                            b1.ToTable("NotificationSettings");

                            b1.WithOwner()
                                .HasForeignKey("CustomerUserId");
                        });

                    b.OwnsOne("WhimLabAI.Domain.ValueObjects.Email", "Email", b1 =>
                        {
                            b1.Property<Guid>("CustomerUserId")
                                .HasColumnType("uuid");

                            b1.Property<string>("Value")
                                .IsRequired()
                                .HasMaxLength(255)
                                .HasColumnType("character varying(255)")
                                .HasColumnName("email");

                            b1.HasKey("CustomerUserId");

                            b1.HasIndex("Value")
                                .HasDatabaseName("ix_customer_users_email");

                            b1.ToTable("customer_users");

                            b1.WithOwner()
                                .HasForeignKey("CustomerUserId");
                        });

                    b.OwnsOne("WhimLabAI.Domain.ValueObjects.Password", "PasswordHash", b1 =>
                        {
                            b1.Property<Guid>("CustomerUserId")
                                .HasColumnType("uuid");

                            b1.Property<string>("HashedValue")
                                .IsRequired()
                                .HasMaxLength(255)
                                .HasColumnType("character varying(255)")
                                .HasColumnName("password_hash");

                            b1.HasKey("CustomerUserId");

                            b1.ToTable("customer_users");

                            b1.WithOwner()
                                .HasForeignKey("CustomerUserId");
                        });

                    b.Navigation("Email");

                    b.Navigation("NotificationSetting")
                        .IsRequired();

                    b.Navigation("PasswordHash")
                        .IsRequired();

                    b.Navigation("Phone");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.User.DeviceAuthorization", b =>
                {
                    b.HasOne("WhimLabAI.Domain.Entities.User.CustomerUser", null)
                        .WithMany("DeviceAuthorizations")
                        .HasForeignKey("CustomerUserId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.User.LoginHistory", b =>
                {
                    b.HasOne("WhimLabAI.Domain.Entities.User.CustomerUser", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.User.OAuthBinding", b =>
                {
                    b.HasOne("WhimLabAI.Domain.Entities.User.CustomerUser", null)
                        .WithMany("OAuthBindings")
                        .HasForeignKey("CustomerUserId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.User.QRCodeSession", b =>
                {
                    b.HasOne("WhimLabAI.Domain.Entities.User.CustomerUser", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("User");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Agent.Agent", b =>
                {
                    b.Navigation("ApiKeys");

                    b.Navigation("Ratings");

                    b.Navigation("Versions");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Agent.AgentCategory", b =>
                {
                    b.Navigation("Agents");

                    b.Navigation("Children");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Auth.Role", b =>
                {
                    b.Navigation("Permissions");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Conversation.Conversation", b =>
                {
                    b.Navigation("Messages");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Conversation.ConversationMessage", b =>
                {
                    b.Navigation("Attachments");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.KnowledgeBase.Document", b =>
                {
                    b.Navigation("Chunks");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.KnowledgeBase.KnowledgeBase", b =>
                {
                    b.Navigation("Documents");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Payment.Coupon", b =>
                {
                    b.Navigation("Usages");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Payment.Order", b =>
                {
                    b.Navigation("Refunds");

                    b.Navigation("Transactions");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.Subscription.Subscription", b =>
                {
                    b.Navigation("UsageRecords");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.User.AdminUser", b =>
                {
                    b.Navigation("UserRoles");
                });

            modelBuilder.Entity("WhimLabAI.Domain.Entities.User.CustomerUser", b =>
                {
                    b.Navigation("DeviceAuthorizations");

                    b.Navigation("OAuthBindings");

                    b.Navigation("Profile");
                });
#pragma warning restore 612, 618
        }
    }
}
