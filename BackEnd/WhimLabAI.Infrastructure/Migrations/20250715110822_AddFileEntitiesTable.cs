﻿using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WhimLabAI.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddFileEntitiesTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_AuditLogs_CreatedAt",
                table: "AuditLogs");

            migrationBuilder.DropIndex(
                name: "IX_AuditLogs_CreatedAt_UserId",
                table: "AuditLogs");

            migrationBuilder.DropIndex(
                name: "IX_AuditLogs_EntityType_EntityId",
                table: "AuditLogs");

            migrationBuilder.DropIndex(
                name: "IX_AuditLogs_IsSuccess",
                table: "AuditLogs");

            migrationBuilder.DropIndex(
                name: "IX_AuditLogs_Module_Action",
                table: "AuditLogs");

            migrationBuilder.DropIndex(
                name: "IX_AuditLogs_RiskLevel",
                table: "AuditLogs");

            migrationBuilder.DropIndex(
                name: "IX_AuditLogs_UserId",
                table: "AuditLogs");

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletedAt",
                table: "conversations",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "conversations",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<DateTime>(
                name: "last_publish_attempt_at",
                table: "agent_versions",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "publish_attempt_count",
                table: "agent_versions",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "review_status",
                table: "agent_versions",
                type: "character varying(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "Pending");

            migrationBuilder.AddColumn<DateTime>(
                name: "submitted_for_review_at",
                table: "agent_versions",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "AgentVersionReviews",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    AgentVersionId = table.Column<Guid>(type: "uuid", nullable: false),
                    ReviewerId = table.Column<Guid>(type: "uuid", nullable: false),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Comments = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    ReviewedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ReviewDetails = table.Column<Dictionary<string, object>>(type: "jsonb", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AgentVersionReviews", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AgentVersionReviews_agent_versions_AgentVersionId",
                        column: x => x.AgentVersionId,
                        principalTable: "agent_versions",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AgentVersionUsageStats",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    AgentVersionId = table.Column<Guid>(type: "uuid", nullable: false),
                    Date = table.Column<DateTime>(type: "date", nullable: false),
                    ConversationCount = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    MessageCount = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalTokensConsumed = table.Column<long>(type: "bigint", nullable: false, defaultValue: 0L),
                    UniqueUserCount = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    AverageResponseTime = table.Column<double>(type: "double precision", nullable: false, defaultValue: 0.0),
                    ErrorCount = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    SuccessRate = table.Column<double>(type: "double precision", nullable: false, defaultValue: 100.0),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AgentVersionUsageStats", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AgentVersionUsageStats_agent_versions_AgentVersionId",
                        column: x => x.AgentVersionId,
                        principalTable: "agent_versions",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "EventStores",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    AggregateId = table.Column<Guid>(type: "uuid", nullable: false),
                    AggregateType = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    EventType = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    EventData = table.Column<string>(type: "jsonb", nullable: false),
                    Metadata = table.Column<string>(type: "jsonb", nullable: true),
                    Version = table.Column<int>(type: "integer", nullable: false),
                    OccurredOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UserId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    CorrelationId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EventStores", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "FileEntities",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    FileName = table.Column<string>(type: "text", nullable: false),
                    FileKey = table.Column<string>(type: "text", nullable: false),
                    ContentType = table.Column<string>(type: "text", nullable: false),
                    FileSize = table.Column<long>(type: "bigint", nullable: false),
                    FileHash = table.Column<string>(type: "text", nullable: true),
                    StorageProvider = table.Column<string>(type: "text", nullable: false),
                    BucketName = table.Column<string>(type: "text", nullable: false),
                    FilePath = table.Column<string>(type: "text", nullable: false),
                    UserId = table.Column<Guid>(type: "uuid", nullable: true),
                    UserType = table.Column<string>(type: "text", nullable: true),
                    Purpose = table.Column<string>(type: "text", nullable: true),
                    IsTemporary = table.Column<bool>(type: "boolean", nullable: false),
                    ExpiresAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Metadata = table.Column<Dictionary<string, string>>(type: "hstore", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FileEntities", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Snapshots",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    AggregateId = table.Column<Guid>(type: "uuid", nullable: false),
                    AggregateType = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    SnapshotData = table.Column<string>(type: "jsonb", nullable: false),
                    Version = table.Column<int>(type: "integer", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Snapshots", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AuditLogs_CorrelationId",
                table: "AuditLogs",
                column: "CorrelationId",
                filter: "\"CorrelationId\" IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_AuditLogs_CreatedAt_UserId_Action_Module",
                table: "AuditLogs",
                columns: new[] { "CreatedAt", "UserId", "Action", "Module" },
                descending: new[] { true, false, false, false });

            migrationBuilder.CreateIndex(
                name: "IX_AuditLogs_EntityType_EntityId_CreatedAt",
                table: "AuditLogs",
                columns: new[] { "EntityType", "EntityId", "CreatedAt" },
                descending: new[] { false, false, true });

            migrationBuilder.CreateIndex(
                name: "IX_AuditLogs_IpAddress_UserId_CreatedAt",
                table: "AuditLogs",
                columns: new[] { "IpAddress", "UserId", "CreatedAt" },
                descending: new[] { false, false, true });

            migrationBuilder.CreateIndex(
                name: "IX_AuditLogs_IsSensitive_CreatedAt",
                table: "AuditLogs",
                columns: new[] { "IsSensitive", "CreatedAt" },
                descending: new[] { false, true },
                filter: "\"IsSensitive\" = true");

            migrationBuilder.CreateIndex(
                name: "IX_AuditLogs_IsSuccess_CreatedAt",
                table: "AuditLogs",
                columns: new[] { "IsSuccess", "CreatedAt" },
                descending: new[] { false, true });

            migrationBuilder.CreateIndex(
                name: "IX_AuditLogs_Module_Action_CreatedAt",
                table: "AuditLogs",
                columns: new[] { "Module", "Action", "CreatedAt" },
                descending: new[] { false, false, true });

            migrationBuilder.CreateIndex(
                name: "IX_AuditLogs_RequestId",
                table: "AuditLogs",
                column: "RequestId",
                filter: "\"RequestId\" IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_AuditLogs_RiskLevel_CreatedAt",
                table: "AuditLogs",
                columns: new[] { "RiskLevel", "CreatedAt" },
                descending: new[] { false, true });

            migrationBuilder.CreateIndex(
                name: "IX_AuditLogs_UserId_CreatedAt",
                table: "AuditLogs",
                columns: new[] { "UserId", "CreatedAt" },
                descending: new[] { false, true });

            migrationBuilder.CreateIndex(
                name: "IX_agent_versions_review_status",
                table: "agent_versions",
                column: "review_status");

            migrationBuilder.CreateIndex(
                name: "IX_agent_versions_submitted_for_review_at",
                table: "agent_versions",
                column: "submitted_for_review_at");

            migrationBuilder.CreateIndex(
                name: "IX_AgentVersionReviews_AgentVersionId",
                table: "AgentVersionReviews",
                column: "AgentVersionId");

            migrationBuilder.CreateIndex(
                name: "IX_AgentVersionReviews_ReviewedAt",
                table: "AgentVersionReviews",
                column: "ReviewedAt");

            migrationBuilder.CreateIndex(
                name: "IX_AgentVersionReviews_ReviewerId",
                table: "AgentVersionReviews",
                column: "ReviewerId");

            migrationBuilder.CreateIndex(
                name: "IX_AgentVersionUsageStats_AgentVersionId_Date",
                table: "AgentVersionUsageStats",
                columns: new[] { "AgentVersionId", "Date" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_AgentVersionUsageStats_Date",
                table: "AgentVersionUsageStats",
                column: "Date");

            migrationBuilder.CreateIndex(
                name: "IX_EventStores_AggregateId",
                table: "EventStores",
                column: "AggregateId");

            migrationBuilder.CreateIndex(
                name: "IX_EventStores_AggregateId_Version",
                table: "EventStores",
                columns: new[] { "AggregateId", "Version" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_EventStores_CorrelationId",
                table: "EventStores",
                column: "CorrelationId");

            migrationBuilder.CreateIndex(
                name: "IX_EventStores_EventType",
                table: "EventStores",
                column: "EventType");

            migrationBuilder.CreateIndex(
                name: "IX_EventStores_OccurredOn",
                table: "EventStores",
                column: "OccurredOn");

            migrationBuilder.CreateIndex(
                name: "IX_Snapshots_AggregateId",
                table: "Snapshots",
                column: "AggregateId");

            migrationBuilder.CreateIndex(
                name: "IX_Snapshots_AggregateId_Version",
                table: "Snapshots",
                columns: new[] { "AggregateId", "Version" });

            migrationBuilder.CreateIndex(
                name: "IX_Snapshots_CreatedAt",
                table: "Snapshots",
                column: "CreatedAt");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AgentVersionReviews");

            migrationBuilder.DropTable(
                name: "AgentVersionUsageStats");

            migrationBuilder.DropTable(
                name: "EventStores");

            migrationBuilder.DropTable(
                name: "FileEntities");

            migrationBuilder.DropTable(
                name: "Snapshots");

            migrationBuilder.DropIndex(
                name: "IX_AuditLogs_CorrelationId",
                table: "AuditLogs");

            migrationBuilder.DropIndex(
                name: "IX_AuditLogs_CreatedAt_UserId_Action_Module",
                table: "AuditLogs");

            migrationBuilder.DropIndex(
                name: "IX_AuditLogs_EntityType_EntityId_CreatedAt",
                table: "AuditLogs");

            migrationBuilder.DropIndex(
                name: "IX_AuditLogs_IpAddress_UserId_CreatedAt",
                table: "AuditLogs");

            migrationBuilder.DropIndex(
                name: "IX_AuditLogs_IsSensitive_CreatedAt",
                table: "AuditLogs");

            migrationBuilder.DropIndex(
                name: "IX_AuditLogs_IsSuccess_CreatedAt",
                table: "AuditLogs");

            migrationBuilder.DropIndex(
                name: "IX_AuditLogs_Module_Action_CreatedAt",
                table: "AuditLogs");

            migrationBuilder.DropIndex(
                name: "IX_AuditLogs_RequestId",
                table: "AuditLogs");

            migrationBuilder.DropIndex(
                name: "IX_AuditLogs_RiskLevel_CreatedAt",
                table: "AuditLogs");

            migrationBuilder.DropIndex(
                name: "IX_AuditLogs_UserId_CreatedAt",
                table: "AuditLogs");

            migrationBuilder.DropIndex(
                name: "IX_agent_versions_review_status",
                table: "agent_versions");

            migrationBuilder.DropIndex(
                name: "IX_agent_versions_submitted_for_review_at",
                table: "agent_versions");

            migrationBuilder.DropColumn(
                name: "DeletedAt",
                table: "conversations");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "conversations");

            migrationBuilder.DropColumn(
                name: "last_publish_attempt_at",
                table: "agent_versions");

            migrationBuilder.DropColumn(
                name: "publish_attempt_count",
                table: "agent_versions");

            migrationBuilder.DropColumn(
                name: "review_status",
                table: "agent_versions");

            migrationBuilder.DropColumn(
                name: "submitted_for_review_at",
                table: "agent_versions");

            migrationBuilder.CreateIndex(
                name: "IX_AuditLogs_CreatedAt",
                table: "AuditLogs",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_AuditLogs_CreatedAt_UserId",
                table: "AuditLogs",
                columns: new[] { "CreatedAt", "UserId" });

            migrationBuilder.CreateIndex(
                name: "IX_AuditLogs_EntityType_EntityId",
                table: "AuditLogs",
                columns: new[] { "EntityType", "EntityId" });

            migrationBuilder.CreateIndex(
                name: "IX_AuditLogs_IsSuccess",
                table: "AuditLogs",
                column: "IsSuccess");

            migrationBuilder.CreateIndex(
                name: "IX_AuditLogs_Module_Action",
                table: "AuditLogs",
                columns: new[] { "Module", "Action" });

            migrationBuilder.CreateIndex(
                name: "IX_AuditLogs_RiskLevel",
                table: "AuditLogs",
                column: "RiskLevel");

            migrationBuilder.CreateIndex(
                name: "IX_AuditLogs_UserId",
                table: "AuditLogs",
                column: "UserId");
        }
    }
}
