﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WhimLabAI.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddLocationToAdminUserSession : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "City",
                table: "AdminUserSessions",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Country",
                table: "AdminUserSessions",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsAnomalousLocation",
                table: "AdminUserSessions",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<double>(
                name: "Latitude",
                table: "AdminUserSessions",
                type: "double precision",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "Longitude",
                table: "AdminUserSessions",
                type: "double precision",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Region",
                table: "AdminUserSessions",
                type: "text",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "City",
                table: "AdminUserSessions");

            migrationBuilder.DropColumn(
                name: "Country",
                table: "AdminUserSessions");

            migrationBuilder.DropColumn(
                name: "IsAnomalousLocation",
                table: "AdminUserSessions");

            migrationBuilder.DropColumn(
                name: "Latitude",
                table: "AdminUserSessions");

            migrationBuilder.DropColumn(
                name: "Longitude",
                table: "AdminUserSessions");

            migrationBuilder.DropColumn(
                name: "Region",
                table: "AdminUserSessions");
        }
    }
}
