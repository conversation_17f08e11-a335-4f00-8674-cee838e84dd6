using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using WhimLabAI.Domain.Common;
using WhimLabAI.Infrastructure.Data;

namespace WhimLabAI.Infrastructure.Middleware;

/// <summary>
/// 领域事件分发中间件
/// </summary>
public class DomainEventDispatcherMiddleware
{
    private readonly RequestDelegate _next;

    public DomainEventDispatcherMiddleware(RequestDelegate next)
    {
        _next = next;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        await _next(context);

        // After the request is processed, dispatch domain events
        var dbContext = context.RequestServices.GetService<WhimLabAIDbContext>();
        var mediator = context.RequestServices.GetService<IMediator>();

        if (dbContext != null && mediator != null)
        {
            await DispatchDomainEvents(dbContext, mediator);
        }
    }

    private static async Task DispatchDomainEvents(WhimLabAIDbContext dbContext, IMediator mediator)
    {
        var domainEntities = dbContext.ChangeTracker
            .Entries<AggregateRoot>()
            .Where(x => x.Entity.DomainEvents.Any())
            .ToList();

        var domainEvents = domainEntities
            .SelectMany(x => x.Entity.DomainEvents)
            .ToList();

        domainEntities.ForEach(entity => entity.Entity.ClearDomainEvents());

        foreach (var domainEvent in domainEvents)
        {
            await mediator.Publish(domainEvent);
        }
    }
}