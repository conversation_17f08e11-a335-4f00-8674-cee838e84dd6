﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\WhimLabAI.Domain\WhimLabAI.Domain.csproj" />
      <ProjectReference Include="..\WhimLabAI.Abstractions\WhimLabAI.Abstractions.csproj" />
      <ProjectReference Include="..\WhimLabAI.Shared\WhimLabAI.Shared.csproj" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.7">
        <PrivateAssets>all</PrivateAssets>
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      </PackageReference>
      <PackageReference Include="Microsoft.Extensions.Logging.Configuration" Version="9.0.7" />
      <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="9.0.7" />
      <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="9.0.7" />
      <PackageReference Include="Microsoft.Extensions.Logging.EventLog" Version="9.0.7" />
      <PackageReference Include="Microsoft.Extensions.Logging.EventSource" Version="9.0.7" />
      <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
      <PackageReference Include="Pgvector.EntityFrameworkCore" Version="0.2.2" />
      <PackageReference Include="prometheus-net" Version="8.2.1" />
      <PackageReference Include="StackExchange.Redis" Version="$(StackExchangeRedisVersion)" />
      <PackageReference Include="Minio" Version="$(MinioVersion)" />
      <PackageReference Include="Microsoft.SemanticKernel" Version="$(SemanticKernelVersion)" />
      <PackageReference Include="Alipay.AopSdk.Core" Version="$(AlipaySDKVersion)" />
      <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.7" />
      <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.7" />
      <PackageReference Include="HtmlAgilityPack" Version="1.11.67" />
      <PackageReference Include="Microsoft.IO.RecyclableMemoryStream" Version="3.0.1" />
      <PackageReference Include="HtmlSanitizer" Version="8.0.843" />
      <PackageReference Include="BCrypt.Net-Next" Version="4.*" />
      <PackageReference Include="CommandLineParser" Version="2.*" />
      <PackageReference Include="System.CommandLine" Version="2.0.0-beta4.22272.1" />
      <PackageReference Include="MassTransit.RabbitMQ" Version="$(MassTransitVersion)" />
      <PackageReference Include="MediatR" Version="$(MediatRVersion)" />
      <PackageReference Include="Otp.NET" Version="1.4.0" />
      <PackageReference Include="QRCoder" Version="1.6.0" />
    </ItemGroup>
    
    <ItemGroup>
      <FrameworkReference Include="Microsoft.AspNetCore.App" />
    </ItemGroup>

</Project>
