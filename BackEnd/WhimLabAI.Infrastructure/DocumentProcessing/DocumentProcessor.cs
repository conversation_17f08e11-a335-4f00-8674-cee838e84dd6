using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Shared.Enums;
using WhimLabAI.Shared.Results;

namespace WhimLabAI.Infrastructure.DocumentProcessing;

/// <summary>
/// 文档处理器实现
/// </summary>
public class DocumentProcessor : IDocumentProcessor
{
    private readonly IAIProviderManager _aiProviderManager;
    private readonly ILogger<DocumentProcessor> _logger;

    // Regex patterns for sentence splitting
    private static readonly Regex SentenceRegex = new(@"[.!?]+\s+", RegexOptions.Compiled);
    private static readonly Regex WordBoundaryRegex = new(@"\b", RegexOptions.Compiled);

    public DocumentProcessor(
        IAIProviderManager aiProviderManager,
        ILogger<DocumentProcessor> logger)
    {
        _aiProviderManager = aiProviderManager;
        _logger = logger;
    }

    public async Task<Result<DocumentContent>> ExtractContentAsync(
        Stream fileStream,
        string fileName,
        DocumentType documentType,
        CancellationToken cancellationToken = default)
    {
        try
        {
            DocumentContent content;

            switch (documentType)
            {
                case DocumentType.PlainText:
                case DocumentType.Markdown:
                    content = await ExtractTextContentAsync(fileStream, cancellationToken);
                    break;
                
                case DocumentType.JSON:
                    content = await ExtractJsonContentAsync(fileStream, cancellationToken);
                    break;
                
                case DocumentType.CSV:
                    content = await ExtractCsvContentAsync(fileStream, cancellationToken);
                    break;
                
                case DocumentType.HTML:
                    content = await ExtractHtmlContentAsync(fileStream, cancellationToken);
                    break;
                
                case DocumentType.PDF:
                case DocumentType.Word:
                case DocumentType.Excel:
                case DocumentType.PowerPoint:
                    // For complex document types, we would need specialized libraries
                    // For now, return an error
                    return Result<DocumentContent>.Failure("UNSUPPORTED_TYPE", 
                        $"文档类型 {documentType} 的提取功能尚未实现");
                
                default:
                    return Result<DocumentContent>.Failure("UNKNOWN_TYPE", "未知的文档类型");
            }

            content.Metadata["FileName"] = fileName;
            content.Metadata["DocumentType"] = documentType.ToString();
            content.Metadata["ExtractedAt"] = DateTime.UtcNow;

            return Result<DocumentContent>.Success(content);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting content from {FileName}", fileName);
            return Result<DocumentContent>.Failure("EXTRACTION_ERROR", "文档内容提取失败");
        }
    }

    public async Task<Result<IEnumerable<DocumentChunkData>>> ChunkDocumentAsync(
        DocumentContent content,
        ChunkingStrategy strategy,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var chunks = new List<DocumentChunkData>();
            
            // If content has sections, chunk each section separately
            if (content.Sections.Any())
            {
                int globalIndex = 0;
                foreach (var section in content.Sections)
                {
                    var sectionChunks = await ChunkTextAsync(
                        section.Content,
                        strategy,
                        globalIndex,
                        new Dictionary<string, object>
                        {
                            ["SectionTitle"] = section.Title,
                            ["PageNumber"] = section.PageNumber
                        },
                        cancellationToken);
                    
                    chunks.AddRange(sectionChunks);
                    globalIndex += sectionChunks.Count;
                }
            }
            else
            {
                // Chunk the entire text
                var textChunks = await ChunkTextAsync(content.Text, strategy, 0, null, cancellationToken);
                chunks.AddRange(textChunks);
            }

            return Result<IEnumerable<DocumentChunkData>>.Success(chunks);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error chunking document");
            return Result<IEnumerable<DocumentChunkData>>.Failure("CHUNKING_ERROR", "文档分块失败");
        }
    }

    public string ComputeHash(string content)
    {
        using var sha256 = SHA256.Create();
        var bytes = Encoding.UTF8.GetBytes(content);
        var hash = sha256.ComputeHash(bytes);
        return Convert.ToBase64String(hash);
    }

    public async Task<int> CountTokensAsync(string text, string model, CancellationToken cancellationToken = default)
    {
        try
        {
            var provider = _aiProviderManager.GetProviderByModel(model);
            var result = await provider.GetTokenCountAsync(text, model, cancellationToken);
            
            return result.IsSuccess ? result.Value : EstimateTokenCount(text);
        }
        catch
        {
            // Fallback to estimation
            return EstimateTokenCount(text);
        }
    }

    public bool IsDocumentTypeSupported(DocumentType documentType)
    {
        return documentType switch
        {
            DocumentType.PlainText => true,
            DocumentType.Markdown => true,
            DocumentType.JSON => true,
            DocumentType.CSV => true,
            DocumentType.HTML => true,
            DocumentType.PDF => false, // Not implemented yet
            DocumentType.Word => false,
            DocumentType.Excel => false,
            DocumentType.PowerPoint => false,
            DocumentType.XML => true,
            _ => false
        };
    }

    private async Task<DocumentContent> ExtractTextContentAsync(Stream stream, CancellationToken cancellationToken)
    {
        using var reader = new StreamReader(stream, Encoding.UTF8, leaveOpen: true);
        var text = await reader.ReadToEndAsync(cancellationToken);
        
        return new DocumentContent
        {
            Text = text,
            Metadata = new Dictionary<string, object>
            {
                ["CharacterCount"] = text.Length,
                ["LineCount"] = text.Split('\n').Length
            }
        };
    }

    private async Task<DocumentContent> ExtractJsonContentAsync(Stream stream, CancellationToken cancellationToken)
    {
        using var reader = new StreamReader(stream, Encoding.UTF8, leaveOpen: true);
        var json = await reader.ReadToEndAsync(cancellationToken);
        
        // For JSON, we convert it to a readable text format
        // In a real implementation, you might want to parse and structure it differently
        return new DocumentContent
        {
            Text = json,
            Metadata = new Dictionary<string, object>
            {
                ["Format"] = "JSON",
                ["CharacterCount"] = json.Length
            }
        };
    }

    private async Task<DocumentContent> ExtractCsvContentAsync(Stream stream, CancellationToken cancellationToken)
    {
        using var reader = new StreamReader(stream, Encoding.UTF8, leaveOpen: true);
        var csv = await reader.ReadToEndAsync(cancellationToken);
        var lines = csv.Split('\n', StringSplitOptions.RemoveEmptyEntries);
        
        return new DocumentContent
        {
            Text = csv,
            Metadata = new Dictionary<string, object>
            {
                ["Format"] = "CSV",
                ["RowCount"] = lines.Length,
                ["CharacterCount"] = csv.Length
            }
        };
    }

    private async Task<DocumentContent> ExtractHtmlContentAsync(Stream stream, CancellationToken cancellationToken)
    {
        using var reader = new StreamReader(stream, Encoding.UTF8, leaveOpen: true);
        var html = await reader.ReadToEndAsync(cancellationToken);
        
        // Simple HTML tag removal (in production, use a proper HTML parser)
        var text = Regex.Replace(html, @"<[^>]+>", " ");
        text = Regex.Replace(text, @"\s+", " ").Trim();
        
        return new DocumentContent
        {
            Text = text,
            Metadata = new Dictionary<string, object>
            {
                ["Format"] = "HTML",
                ["OriginalLength"] = html.Length,
                ["TextLength"] = text.Length
            }
        };
    }

    private async Task<List<DocumentChunkData>> ChunkTextAsync(
        string text,
        ChunkingStrategy strategy,
        int startIndex,
        Dictionary<string, object>? additionalMetadata,
        CancellationToken cancellationToken)
    {
        var chunks = new List<DocumentChunkData>();
        
        if (string.IsNullOrWhiteSpace(text))
        {
            return chunks;
        }

        // Determine splitting approach
        if (!string.IsNullOrEmpty(strategy.Separator))
        {
            // Split by custom separator
            var parts = text.Split(new[] { strategy.Separator }, StringSplitOptions.RemoveEmptyEntries);
            foreach (var part in parts)
            {
                await AddChunkIfValid(chunks, part.Trim(), startIndex + chunks.Count, strategy, additionalMetadata, cancellationToken);
            }
        }
        else if (strategy.PreserveSentenceBoundaries)
        {
            // Split by sentences
            await ChunkBySentencesAsync(text, chunks, startIndex, strategy, additionalMetadata, cancellationToken);
        }
        else
        {
            // Simple character-based chunking
            await ChunkByCharactersAsync(text, chunks, startIndex, strategy, additionalMetadata, cancellationToken);
        }

        return chunks;
    }

    private async Task ChunkBySentencesAsync(
        string text,
        List<DocumentChunkData> chunks,
        int startIndex,
        ChunkingStrategy strategy,
        Dictionary<string, object>? additionalMetadata,
        CancellationToken cancellationToken)
    {
        var sentences = SentenceRegex.Split(text);
        var currentChunk = new StringBuilder();
        var currentTokenCount = 0;

        foreach (var sentence in sentences)
        {
            if (string.IsNullOrWhiteSpace(sentence))
                continue;

            var sentenceTokens = await CountTokensAsync(sentence, "text-embedding-3-small", cancellationToken);
            
            if (currentTokenCount + sentenceTokens > strategy.MaxChunkSize && currentChunk.Length > 0)
            {
                // Save current chunk
                await AddChunkIfValid(chunks, currentChunk.ToString(), startIndex + chunks.Count, 
                    strategy, additionalMetadata, cancellationToken);
                
                // Start new chunk with overlap
                if (strategy.ChunkOverlap > 0)
                {
                    var overlap = GetOverlapText(currentChunk.ToString(), strategy.ChunkOverlap);
                    currentChunk = new StringBuilder(overlap);
                    currentTokenCount = await CountTokensAsync(overlap, "text-embedding-3-small", cancellationToken);
                }
                else
                {
                    currentChunk.Clear();
                    currentTokenCount = 0;
                }
            }

            currentChunk.Append(sentence).Append(' ');
            currentTokenCount += sentenceTokens;
        }

        // Add final chunk
        if (currentChunk.Length > 0)
        {
            await AddChunkIfValid(chunks, currentChunk.ToString(), startIndex + chunks.Count, 
                strategy, additionalMetadata, cancellationToken);
        }
    }

    private async Task ChunkByCharactersAsync(
        string text,
        List<DocumentChunkData> chunks,
        int startIndex,
        ChunkingStrategy strategy,
        Dictionary<string, object>? additionalMetadata,
        CancellationToken cancellationToken)
    {
        var position = 0;
        while (position < text.Length)
        {
            var chunkLength = Math.Min(strategy.MaxChunkSize, text.Length - position);
            
            // Adjust to word boundary if requested
            if (strategy.PreserveWordBoundaries && position + chunkLength < text.Length)
            {
                var lastSpace = text.LastIndexOf(' ', position + chunkLength, chunkLength);
                if (lastSpace > position)
                {
                    chunkLength = lastSpace - position;
                }
            }

            var chunkText = text.Substring(position, chunkLength);
            await AddChunkIfValid(chunks, chunkText, startIndex + chunks.Count, 
                strategy, additionalMetadata, cancellationToken);

            // Move position considering overlap
            position += chunkLength - strategy.ChunkOverlap;
            if (position < 0) position = chunkLength;
        }
    }

    private async Task AddChunkIfValid(
        List<DocumentChunkData> chunks,
        string text,
        int index,
        ChunkingStrategy strategy,
        Dictionary<string, object>? additionalMetadata,
        CancellationToken cancellationToken)
    {
        text = text.Trim();
        if (string.IsNullOrWhiteSpace(text))
            return;

        var tokenCount = await CountTokensAsync(text, "text-embedding-3-small", cancellationToken);
        
        var chunk = new DocumentChunkData
        {
            Index = index,
            Content = text,
            Hash = ComputeHash(text),
            CharacterCount = text.Length,
            TokenCount = tokenCount,
            Metadata = new Dictionary<string, object>()
        };

        // Add additional metadata
        if (additionalMetadata != null)
        {
            foreach (var (key, value) in additionalMetadata)
            {
                chunk.Metadata[key] = value;
            }
        }

        // Add chunk-specific metadata
        chunk.Metadata["ChunkIndex"] = index;
        chunk.Metadata["StartPosition"] = chunks.Sum(c => c.CharacterCount);
        
        chunks.Add(chunk);
    }

    private string GetOverlapText(string text, int overlapSize)
    {
        if (text.Length <= overlapSize)
            return text;

        // Try to get overlap from the end, preferring complete sentences
        var startPos = Math.Max(0, text.Length - overlapSize);
        var overlapText = text.Substring(startPos);
        
        // Find first sentence boundary
        var sentenceStart = overlapText.IndexOf(". ", StringComparison.Ordinal);
        if (sentenceStart > 0 && sentenceStart < overlapText.Length - 2)
        {
            overlapText = overlapText.Substring(sentenceStart + 2);
        }

        return overlapText.Trim();
    }

    private int EstimateTokenCount(string text)
    {
        // Rough estimation: ~4 characters per token
        return text.Length / 4;
    }
}