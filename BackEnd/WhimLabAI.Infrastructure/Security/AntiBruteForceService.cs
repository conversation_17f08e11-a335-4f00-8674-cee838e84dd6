using System;
using System.Collections.Concurrent;
using System.Threading.Tasks;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text.Json;

namespace WhimLabAI.Infrastructure.Security;

/// <summary>
/// 防暴力破解服务
/// </summary>
public class AntiBruteForceService : IAntiBruteForceService
{
    private readonly IDistributedCache _cache;
    private readonly ILogger<AntiBruteForceService> _logger;
    private readonly AntiBruteForceOptions _options;
    private readonly ConcurrentDictionary<string, DateTime> _localCache;

    public AntiBruteForceService(
        IDistributedCache cache,
        ILogger<AntiBruteForceService> logger,
        IOptions<AntiBruteForceOptions> options)
    {
        _cache = cache;
        _logger = logger;
        _options = options.Value;
        _localCache = new ConcurrentDictionary<string, DateTime>();
    }

    /// <summary>
    /// 检查是否被锁定
    /// </summary>
    public async Task<bool> IsLockedAsync(string key, string action = "default")
    {
        var cacheKey = GetCacheKey(key, action);
        var lockKey = GetLockKey(key, action);

        // 检查是否被永久锁定
        var lockInfo = await _cache.GetStringAsync(lockKey);
        if (!string.IsNullOrEmpty(lockInfo))
        {
            var lockData = JsonSerializer.Deserialize<LockInfo>(lockInfo);
            if (lockData != null && lockData.LockedUntil > DateTime.UtcNow)
            {
                _logger.LogWarning("Access denied for {Key} on action {Action}. Locked until {LockedUntil}",
                    key, action, lockData.LockedUntil);
                return true;
            }
        }

        return false;
    }

    /// <summary>
    /// 记录失败尝试
    /// </summary>
    public async Task<AttackCheckResult> RecordFailedAttemptAsync(string key, string action = "default")
    {
        var cacheKey = GetCacheKey(key, action);
        var lockKey = GetLockKey(key, action);

        // 获取当前失败次数
        var attemptsData = await _cache.GetStringAsync(cacheKey);
        var attempts = 0;
        var firstAttemptTime = DateTime.UtcNow;

        if (!string.IsNullOrEmpty(attemptsData))
        {
            var attemptInfo = JsonSerializer.Deserialize<AttemptInfo>(attemptsData);
            if (attemptInfo != null)
            {
                attempts = attemptInfo.Count;
                firstAttemptTime = attemptInfo.FirstAttemptTime;
            }
        }

        attempts++;

        // 检查是否超过阈值
        var options = GetActionOptions(action);
        var result = new AttackCheckResult
        {
            CurrentAttempts = attempts,
            MaxAttempts = options.MaxAttempts,
            IsLocked = false
        };

        if (attempts >= options.MaxAttempts)
        {
            // 计算锁定时间
            var lockDuration = CalculateLockDuration(attempts, options);
            var lockedUntil = DateTime.UtcNow.Add(lockDuration);

            // 设置锁定
            var lockInfo = new LockInfo
            {
                LockedAt = DateTime.UtcNow,
                LockedUntil = lockedUntil,
                Attempts = attempts,
                Reason = $"Too many failed attempts for action: {action}"
            };

            await _cache.SetStringAsync(lockKey, JsonSerializer.Serialize(lockInfo),
                new DistributedCacheEntryOptions
                {
                    AbsoluteExpiration = lockedUntil
                });

            result.IsLocked = true;
            result.LockedUntil = lockedUntil;
            result.LockDuration = lockDuration;

            _logger.LogWarning("Locked {Key} for action {Action} until {LockedUntil} after {Attempts} attempts",
                key, action, lockedUntil, attempts);

            // 触发安全事件
            await TriggerSecurityEventAsync(key, action, attempts);
        }
        else
        {
            // 更新失败次数
            var attemptInfo = new AttemptInfo
            {
                Count = attempts,
                FirstAttemptTime = firstAttemptTime,
                LastAttemptTime = DateTime.UtcNow
            };

            await _cache.SetStringAsync(cacheKey, JsonSerializer.Serialize(attemptInfo),
                new DistributedCacheEntryOptions
                {
                    SlidingExpiration = options.WindowDuration
                });
        }

        return result;
    }

    /// <summary>
    /// 记录成功尝试（清除失败记录）
    /// </summary>
    public async Task RecordSuccessfulAttemptAsync(string key, string action = "default")
    {
        var cacheKey = GetCacheKey(key, action);
        var lockKey = GetLockKey(key, action);

        await _cache.RemoveAsync(cacheKey);
        await _cache.RemoveAsync(lockKey);

        _logger.LogInformation("Cleared failed attempts for {Key} on action {Action}", key, action);
    }

    /// <summary>
    /// 检查IP是否在黑名单中
    /// </summary>
    public async Task<bool> IsBlacklistedAsync(string ipAddress)
    {
        var blacklistKey = $"blacklist:ip:{ipAddress}";
        var result = await _cache.GetStringAsync(blacklistKey);
        return !string.IsNullOrEmpty(result);
    }

    /// <summary>
    /// 添加IP到黑名单
    /// </summary>
    public async Task AddToBlacklistAsync(string ipAddress, TimeSpan duration, string reason)
    {
        var blacklistKey = $"blacklist:ip:{ipAddress}";
        var blacklistInfo = new BlacklistInfo
        {
            AddedAt = DateTime.UtcNow,
            ExpiresAt = DateTime.UtcNow.Add(duration),
            Reason = reason
        };

        await _cache.SetStringAsync(blacklistKey, JsonSerializer.Serialize(blacklistInfo),
            new DistributedCacheEntryOptions
            {
                AbsoluteExpiration = blacklistInfo.ExpiresAt
            });

        _logger.LogWarning("Added IP {IpAddress} to blacklist. Reason: {Reason}", ipAddress, reason);
    }

    /// <summary>
    /// 检查是否存在分布式攻击
    /// </summary>
    public async Task<bool> IsDistributedAttackAsync(string pattern)
    {
        var key = $"attack:pattern:{pattern}";
        var data = await _cache.GetStringAsync(key);
        
        if (string.IsNullOrEmpty(data))
        {
            var attackInfo = new DistributedAttackInfo
            {
                Count = 1,
                FirstSeen = DateTime.UtcNow,
                LastSeen = DateTime.UtcNow
            };
            
            await _cache.SetStringAsync(key, JsonSerializer.Serialize(attackInfo),
                new DistributedCacheEntryOptions
                {
                    SlidingExpiration = TimeSpan.FromMinutes(10)
                });
            
            return false;
        }

        var info = JsonSerializer.Deserialize<DistributedAttackInfo>(data);
        if (info != null)
        {
            info.Count++;
            info.LastSeen = DateTime.UtcNow;
            
            await _cache.SetStringAsync(key, JsonSerializer.Serialize(info),
                new DistributedCacheEntryOptions
                {
                    SlidingExpiration = TimeSpan.FromMinutes(10)
                });

            // 如果在短时间内有大量请求，认为是分布式攻击
            if (info.Count > _options.DistributedAttackThreshold)
            {
                _logger.LogError("Distributed attack detected for pattern: {Pattern}. Count: {Count}",
                    pattern, info.Count);
                return true;
            }
        }

        return false;
    }

    private string GetCacheKey(string key, string action)
    {
        return $"bruteforce:{action}:{key}";
    }

    private string GetLockKey(string key, string action)
    {
        return $"bruteforce:lock:{action}:{key}";
    }

    private ActionOptions GetActionOptions(string action)
    {
        if (_options.ActionSettings.TryGetValue(action, out var options))
        {
            return options;
        }

        return _options.DefaultSettings;
    }

    private TimeSpan CalculateLockDuration(int attempts, ActionOptions options)
    {
        // 指数退避算法
        var baseLockMinutes = options.BaseLockMinutes;
        var multiplier = Math.Pow(options.LockMultiplier, attempts - options.MaxAttempts);
        var lockMinutes = Math.Min(baseLockMinutes * multiplier, options.MaxLockMinutes);
        
        return TimeSpan.FromMinutes(lockMinutes);
    }

    private async Task TriggerSecurityEventAsync(string key, string action, int attempts)
    {
        // 这里可以集成安全事件通知系统
        // 例如：发送邮件、短信、Slack通知等
        var securityEvent = new SecurityEvent
        {
            Type = "BruteForceAttack",
            Key = key,
            Action = action,
            Attempts = attempts,
            Timestamp = DateTime.UtcNow,
            Severity = attempts > _options.CriticalThreshold ? "Critical" : "High"
        };

        _logger.LogError("Security Event: {Event}", JsonSerializer.Serialize(securityEvent));
        
        // TODO: 发送到安全事件队列
        await Task.CompletedTask;
    }

    private class AttemptInfo
    {
        public int Count { get; set; }
        public DateTime FirstAttemptTime { get; set; }
        public DateTime LastAttemptTime { get; set; }
    }

    private class LockInfo
    {
        public DateTime LockedAt { get; set; }
        public DateTime LockedUntil { get; set; }
        public int Attempts { get; set; }
        public string Reason { get; set; } = string.Empty;
    }

    private class BlacklistInfo
    {
        public DateTime AddedAt { get; set; }
        public DateTime ExpiresAt { get; set; }
        public string Reason { get; set; } = string.Empty;
    }

    private class DistributedAttackInfo
    {
        public int Count { get; set; }
        public DateTime FirstSeen { get; set; }
        public DateTime LastSeen { get; set; }
    }

    private class SecurityEvent
    {
        public string Type { get; set; } = string.Empty;
        public string Key { get; set; } = string.Empty;
        public string Action { get; set; } = string.Empty;
        public int Attempts { get; set; }
        public DateTime Timestamp { get; set; }
        public string Severity { get; set; } = string.Empty;
    }
}

/// <summary>
/// 防暴力破解配置选项
/// </summary>
public class AntiBruteForceOptions
{
    public ActionOptions DefaultSettings { get; set; } = new ActionOptions();
    public Dictionary<string, ActionOptions> ActionSettings { get; set; } = new();
    public int DistributedAttackThreshold { get; set; } = 100;
    public int CriticalThreshold { get; set; } = 10;

    public AntiBruteForceOptions()
    {
        // 默认配置
        DefaultSettings = new ActionOptions
        {
            MaxAttempts = 5,
            WindowDuration = TimeSpan.FromMinutes(15),
            BaseLockMinutes = 5,
            LockMultiplier = 2,
            MaxLockMinutes = 1440 // 24小时
        };

        // 特定操作的配置
        ActionSettings = new Dictionary<string, ActionOptions>
        {
            ["login"] = new ActionOptions
            {
                MaxAttempts = 5,
                WindowDuration = TimeSpan.FromMinutes(15),
                BaseLockMinutes = 5,
                LockMultiplier = 2,
                MaxLockMinutes = 60
            },
            ["password_reset"] = new ActionOptions
            {
                MaxAttempts = 3,
                WindowDuration = TimeSpan.FromMinutes(30),
                BaseLockMinutes = 30,
                LockMultiplier = 2,
                MaxLockMinutes = 1440
            },
            ["api_key"] = new ActionOptions
            {
                MaxAttempts = 10,
                WindowDuration = TimeSpan.FromMinutes(5),
                BaseLockMinutes = 1,
                LockMultiplier = 1.5,
                MaxLockMinutes = 60
            }
        };
    }
}

/// <summary>
/// 操作配置
/// </summary>
public class ActionOptions
{
    public int MaxAttempts { get; set; }
    public TimeSpan WindowDuration { get; set; }
    public double BaseLockMinutes { get; set; }
    public double LockMultiplier { get; set; }
    public double MaxLockMinutes { get; set; }
}

/// <summary>
/// 攻击检查结果
/// </summary>
public class AttackCheckResult
{
    public bool IsLocked { get; set; }
    public int CurrentAttempts { get; set; }
    public int MaxAttempts { get; set; }
    public DateTime? LockedUntil { get; set; }
    public TimeSpan? LockDuration { get; set; }
}

/// <summary>
/// 防暴力破解服务接口
/// </summary>
public interface IAntiBruteForceService
{
    Task<bool> IsLockedAsync(string key, string action = "default");
    Task<AttackCheckResult> RecordFailedAttemptAsync(string key, string action = "default");
    Task RecordSuccessfulAttemptAsync(string key, string action = "default");
    Task<bool> IsBlacklistedAsync(string ipAddress);
    Task AddToBlacklistAsync(string ipAddress, TimeSpan duration, string reason);
    Task<bool> IsDistributedAttackAsync(string pattern);
}