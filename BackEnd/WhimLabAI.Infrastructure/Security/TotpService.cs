using System.Security.Cryptography;
using System.Text;
using Microsoft.Extensions.Logging;
using OtpNet;
using WhimLabAI.Abstractions.Infrastructure;

namespace WhimLabAI.Infrastructure.Security;

/// <summary>
/// Time-based One-Time Password (TOTP) service implementation using OtpNet
/// </summary>
public class TotpService : ITotpService
{
    private readonly ILogger<TotpService> _logger;
    private const int SecretKeyLength = 20; // 160 bits
    private const int CodeDigits = 6;
    private const int TimeStep = 30; // seconds

    public TotpService(ILogger<TotpService> logger)
    {
        _logger = logger;
    }

    public string GenerateSecret()
    {
        // Generate a random 160-bit secret key
        var key = new byte[SecretKeyLength];
        using (var rng = RandomNumberGenerator.Create())
        {
            rng.GetBytes(key);
        }
        
        // Convert to Base32
        var base32Secret = Base32Encoding.ToString(key);
        _logger.LogDebug("Generated new TOTP secret");
        
        return base32Secret;
    }

    public string GenerateTotpUri(string issuer, string accountName, string secret)
    {
        // Format: otpauth://totp/ISSUER:ACCOUNT?secret=SECRET&issuer=ISSUER
        var uri = $"otpauth://totp/{Uri.EscapeDataString(issuer)}:{Uri.EscapeDataString(accountName)}" +
                  $"?secret={secret}" +
                  $"&issuer={Uri.EscapeDataString(issuer)}" +
                  $"&digits={CodeDigits}" +
                  $"&period={TimeStep}";
        
        _logger.LogDebug("Generated TOTP URI for account: {Account}", accountName);
        
        return uri;
    }

    public bool ValidateCode(string secret, string code, int window = 1)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(code) || code.Length != CodeDigits)
            {
                _logger.LogDebug("Invalid code format");
                return false;
            }

            // Parse the code
            if (!int.TryParse(code, out _))
            {
                _logger.LogDebug("Code is not numeric");
                return false;
            }

            // Convert Base32 secret to byte array
            var key = Base32Encoding.ToBytes(secret);
            
            // Create TOTP instance
            var totp = new Totp(key, TimeStep, OtpHashMode.Sha1, CodeDigits);
            
            // Verify the code with time window tolerance
            var verificationWindow = new VerificationWindow(window, window);
            var result = totp.VerifyTotp(code, out _, verificationWindow);
            
            _logger.LogDebug("TOTP validation result: {Result}", result);
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating TOTP code");
            return false;
        }
    }

    public string GetCurrentCode(string secret)
    {
        try
        {
            // Convert Base32 secret to byte array
            var key = Base32Encoding.ToBytes(secret);
            
            // Create TOTP instance
            var totp = new Totp(key, TimeStep, OtpHashMode.Sha1, CodeDigits);
            
            // Get current code
            var code = totp.ComputeTotp();
            
            _logger.LogDebug("Generated current TOTP code");
            
            return code;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating TOTP code");
            throw;
        }
    }

    public string FormatSecretForManualEntry(string secret)
    {
        if (string.IsNullOrWhiteSpace(secret))
            return string.Empty;
        
        // Format secret in groups of 4 characters for easier manual entry
        var formatted = new StringBuilder();
        for (int i = 0; i < secret.Length; i++)
        {
            if (i > 0 && i % 4 == 0)
                formatted.Append(' ');
            formatted.Append(secret[i]);
        }
        
        return formatted.ToString();
    }
}