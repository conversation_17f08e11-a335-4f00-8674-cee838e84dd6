using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace WhimLabAI.Infrastructure.Security;

/// <summary>
/// 数据加密服务
/// </summary>
public class DataEncryptionService : IDataEncryptionService
{
    private readonly ILogger<DataEncryptionService> _logger;
    private readonly EncryptionOptions _options;
    private readonly byte[] _key;
    private readonly byte[] _iv;

    public DataEncryptionService(
        ILogger<DataEncryptionService> logger,
        IOptions<EncryptionOptions> options)
    {
        _logger = logger;
        _options = options.Value;
        
        // 初始化密钥和IV
        _key = DeriveKey(_options.MasterKey, _options.Salt, 32);
        _iv = DeriveKey(_options.MasterKey, _options.Salt + "_IV", 16);
    }

    /// <summary>
    /// 加密字符串
    /// </summary>
    public string EncryptString(string plainText)
    {
        if (string.IsNullOrEmpty(plainText))
            return string.Empty;

        try
        {
            using var aes = CreateAes();
            using var encryptor = aes.CreateEncryptor();
            
            var plainBytes = Encoding.UTF8.GetBytes(plainText);
            var cipherBytes = encryptor.TransformFinalBlock(plainBytes, 0, plainBytes.Length);
            
            return Convert.ToBase64String(cipherBytes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error encrypting string");
            throw new EncryptionException("Failed to encrypt data", ex);
        }
    }

    /// <summary>
    /// 解密字符串
    /// </summary>
    public string DecryptString(string cipherText)
    {
        if (string.IsNullOrEmpty(cipherText))
            return string.Empty;

        try
        {
            using var aes = CreateAes();
            using var decryptor = aes.CreateDecryptor();
            
            var cipherBytes = Convert.FromBase64String(cipherText);
            var plainBytes = decryptor.TransformFinalBlock(cipherBytes, 0, cipherBytes.Length);
            
            return Encoding.UTF8.GetString(plainBytes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error decrypting string");
            throw new EncryptionException("Failed to decrypt data", ex);
        }
    }

    /// <summary>
    /// 加密字节数组
    /// </summary>
    public byte[] EncryptBytes(byte[] plainBytes)
    {
        if (plainBytes == null || plainBytes.Length == 0)
            return Array.Empty<byte>();

        try
        {
            using var aes = CreateAes();
            using var encryptor = aes.CreateEncryptor();
            
            return encryptor.TransformFinalBlock(plainBytes, 0, plainBytes.Length);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error encrypting bytes");
            throw new EncryptionException("Failed to encrypt data", ex);
        }
    }

    /// <summary>
    /// 解密字节数组
    /// </summary>
    public byte[] DecryptBytes(byte[] cipherBytes)
    {
        if (cipherBytes == null || cipherBytes.Length == 0)
            return Array.Empty<byte>();

        try
        {
            using var aes = CreateAes();
            using var decryptor = aes.CreateDecryptor();
            
            return decryptor.TransformFinalBlock(cipherBytes, 0, cipherBytes.Length);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error decrypting bytes");
            throw new EncryptionException("Failed to decrypt data", ex);
        }
    }

    /// <summary>
    /// 加密文件
    /// </summary>
    public async Task EncryptFileAsync(string inputPath, string outputPath)
    {
        if (!File.Exists(inputPath))
            throw new FileNotFoundException("Input file not found", inputPath);

        try
        {
            using var aes = CreateAes();
            using var encryptor = aes.CreateEncryptor();
            
            using var inputStream = File.OpenRead(inputPath);
            using var outputStream = File.Create(outputPath);
            using var cryptoStream = new CryptoStream(outputStream, encryptor, CryptoStreamMode.Write);
            
            await inputStream.CopyToAsync(cryptoStream);
            await cryptoStream.FlushAsync();
            
            _logger.LogInformation("File encrypted successfully: {InputPath} -> {OutputPath}", inputPath, outputPath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error encrypting file: {InputPath}", inputPath);
            throw new EncryptionException("Failed to encrypt file", ex);
        }
    }

    /// <summary>
    /// 解密文件
    /// </summary>
    public async Task DecryptFileAsync(string inputPath, string outputPath)
    {
        if (!File.Exists(inputPath))
            throw new FileNotFoundException("Input file not found", inputPath);

        try
        {
            using var aes = CreateAes();
            using var decryptor = aes.CreateDecryptor();
            
            using var inputStream = File.OpenRead(inputPath);
            using var outputStream = File.Create(outputPath);
            using var cryptoStream = new CryptoStream(inputStream, decryptor, CryptoStreamMode.Read);
            
            await cryptoStream.CopyToAsync(outputStream);
            await outputStream.FlushAsync();
            
            _logger.LogInformation("File decrypted successfully: {InputPath} -> {OutputPath}", inputPath, outputPath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error decrypting file: {InputPath}", inputPath);
            throw new EncryptionException("Failed to decrypt file", ex);
        }
    }

    /// <summary>
    /// 生成哈希
    /// </summary>
    public string ComputeHash(string input)
    {
        if (string.IsNullOrEmpty(input))
            return string.Empty;

        using var sha256 = SHA256.Create();
        var bytes = Encoding.UTF8.GetBytes(input + _options.Salt);
        var hash = sha256.ComputeHash(bytes);
        return Convert.ToBase64String(hash);
    }

    /// <summary>
    /// 验证哈希
    /// </summary>
    public bool VerifyHash(string input, string hash)
    {
        if (string.IsNullOrEmpty(input) || string.IsNullOrEmpty(hash))
            return false;

        var computedHash = ComputeHash(input);
        return computedHash == hash;
    }

    /// <summary>
    /// 生成安全随机密钥
    /// </summary>
    public string GenerateSecureKey(int length = 32)
    {
        using var rng = RandomNumberGenerator.Create();
        var bytes = new byte[length];
        rng.GetBytes(bytes);
        return Convert.ToBase64String(bytes);
    }

    /// <summary>
    /// 加密敏感字段（用于数据库存储）
    /// </summary>
    public string EncryptField(string fieldValue, string fieldName)
    {
        if (string.IsNullOrEmpty(fieldValue))
            return string.Empty;

        // 为每个字段使用不同的IV
        var fieldIv = DeriveKey(_options.MasterKey, $"{_options.Salt}_{fieldName}", 16);
        
        using var aes = Aes.Create();
        aes.Key = _key;
        aes.IV = fieldIv;
        aes.Mode = CipherMode.CBC;
        aes.Padding = PaddingMode.PKCS7;

        using var encryptor = aes.CreateEncryptor();
        var plainBytes = Encoding.UTF8.GetBytes(fieldValue);
        var cipherBytes = encryptor.TransformFinalBlock(plainBytes, 0, plainBytes.Length);
        
        return Convert.ToBase64String(cipherBytes);
    }

    /// <summary>
    /// 解密敏感字段
    /// </summary>
    public string DecryptField(string encryptedValue, string fieldName)
    {
        if (string.IsNullOrEmpty(encryptedValue))
            return string.Empty;

        // 使用相同的字段特定IV
        var fieldIv = DeriveKey(_options.MasterKey, $"{_options.Salt}_{fieldName}", 16);
        
        using var aes = Aes.Create();
        aes.Key = _key;
        aes.IV = fieldIv;
        aes.Mode = CipherMode.CBC;
        aes.Padding = PaddingMode.PKCS7;

        using var decryptor = aes.CreateDecryptor();
        var cipherBytes = Convert.FromBase64String(encryptedValue);
        var plainBytes = decryptor.TransformFinalBlock(cipherBytes, 0, cipherBytes.Length);
        
        return Encoding.UTF8.GetString(plainBytes);
    }

    private Aes CreateAes()
    {
        var aes = Aes.Create();
        aes.Key = _key;
        aes.IV = _iv;
        aes.Mode = _options.Mode;
        aes.Padding = _options.Padding;
        return aes;
    }

    private byte[] DeriveKey(string password, string salt, int keySize)
    {
        using var pbkdf2 = new Rfc2898DeriveBytes(
            password,
            Encoding.UTF8.GetBytes(salt),
            _options.Iterations,
            HashAlgorithmName.SHA256);
        
        return pbkdf2.GetBytes(keySize);
    }
}

/// <summary>
/// 可逆加密服务（用于需要解密的场景）
/// </summary>
public class ReversibleEncryptionService : IReversibleEncryptionService
{
    private readonly IDataEncryptionService _encryptionService;
    private readonly ILogger<ReversibleEncryptionService> _logger;

    public ReversibleEncryptionService(
        IDataEncryptionService encryptionService,
        ILogger<ReversibleEncryptionService> logger)
    {
        _encryptionService = encryptionService;
        _logger = logger;
    }

    /// <summary>
    /// 加密个人身份信息
    /// </summary>
    public EncryptedPii EncryptPii(PersonalInfo pii)
    {
        return new EncryptedPii
        {
            EncryptedName = _encryptionService.EncryptField(pii.Name, "Name"),
            EncryptedEmail = _encryptionService.EncryptField(pii.Email, "Email"),
            EncryptedPhone = _encryptionService.EncryptField(pii.Phone, "Phone"),
            EncryptedIdNumber = _encryptionService.EncryptField(pii.IdNumber, "IdNumber"),
            EncryptedAddress = _encryptionService.EncryptField(pii.Address, "Address"),
            EncryptedBankAccount = _encryptionService.EncryptField(pii.BankAccount, "BankAccount"),
            EncryptedAt = DateTime.UtcNow
        };
    }

    /// <summary>
    /// 解密个人身份信息
    /// </summary>
    public PersonalInfo DecryptPii(EncryptedPii encrypted)
    {
        return new PersonalInfo
        {
            Name = _encryptionService.DecryptField(encrypted.EncryptedName, "Name"),
            Email = _encryptionService.DecryptField(encrypted.EncryptedEmail, "Email"),
            Phone = _encryptionService.DecryptField(encrypted.EncryptedPhone, "Phone"),
            IdNumber = _encryptionService.DecryptField(encrypted.EncryptedIdNumber, "IdNumber"),
            Address = _encryptionService.DecryptField(encrypted.EncryptedAddress, "Address"),
            BankAccount = _encryptionService.DecryptField(encrypted.EncryptedBankAccount, "BankAccount")
        };
    }

    /// <summary>
    /// 部分解密（只解密必要字段）
    /// </summary>
    public PartialPersonalInfo PartialDecryptPii(EncryptedPii encrypted, params string[] fieldsToDecrypt)
    {
        var result = new PartialPersonalInfo();

        foreach (var field in fieldsToDecrypt)
        {
            switch (field.ToLower())
            {
                case "name":
                    result.Name = _encryptionService.DecryptField(encrypted.EncryptedName, "Name");
                    break;
                case "email":
                    result.Email = _encryptionService.DecryptField(encrypted.EncryptedEmail, "Email");
                    break;
                case "phone":
                    result.Phone = MaskPhone(_encryptionService.DecryptField(encrypted.EncryptedPhone, "Phone"));
                    break;
                case "idnumber":
                    result.IdNumber = MaskIdNumber(_encryptionService.DecryptField(encrypted.EncryptedIdNumber, "IdNumber"));
                    break;
            }
        }

        return result;
    }

    private string MaskPhone(string phone)
    {
        if (string.IsNullOrEmpty(phone) || phone.Length < 7)
            return phone;

        return phone.Substring(0, 3) + "****" + phone.Substring(phone.Length - 4);
    }

    private string MaskIdNumber(string idNumber)
    {
        if (string.IsNullOrEmpty(idNumber) || idNumber.Length < 10)
            return idNumber;

        return idNumber.Substring(0, 4) + "**********" + idNumber.Substring(idNumber.Length - 4);
    }
}

/// <summary>
/// 加密配置选项
/// </summary>
public class EncryptionOptions
{
    public string MasterKey { get; set; } = string.Empty;
    public string Salt { get; set; } = string.Empty;
    public CipherMode Mode { get; set; } = CipherMode.CBC;
    public PaddingMode Padding { get; set; } = PaddingMode.PKCS7;
    public int Iterations { get; set; } = 10000;
}

/// <summary>
/// 加密异常
/// </summary>
public class EncryptionException : Exception
{
    public EncryptionException(string message) : base(message) { }
    public EncryptionException(string message, Exception innerException) : base(message, innerException) { }
}

/// <summary>
/// 个人信息模型
/// </summary>
public class PersonalInfo
{
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Phone { get; set; } = string.Empty;
    public string IdNumber { get; set; } = string.Empty;
    public string Address { get; set; } = string.Empty;
    public string BankAccount { get; set; } = string.Empty;
}

/// <summary>
/// 加密的个人信息
/// </summary>
public class EncryptedPii
{
    public string EncryptedName { get; set; } = string.Empty;
    public string EncryptedEmail { get; set; } = string.Empty;
    public string EncryptedPhone { get; set; } = string.Empty;
    public string EncryptedIdNumber { get; set; } = string.Empty;
    public string EncryptedAddress { get; set; } = string.Empty;
    public string EncryptedBankAccount { get; set; } = string.Empty;
    public DateTime EncryptedAt { get; set; }
}

/// <summary>
/// 部分个人信息
/// </summary>
public class PartialPersonalInfo
{
    public string? Name { get; set; }
    public string? Email { get; set; }
    public string? Phone { get; set; }
    public string? IdNumber { get; set; }
}

/// <summary>
/// 数据加密服务接口
/// </summary>
public interface IDataEncryptionService
{
    string EncryptString(string plainText);
    string DecryptString(string cipherText);
    byte[] EncryptBytes(byte[] plainBytes);
    byte[] DecryptBytes(byte[] cipherBytes);
    Task EncryptFileAsync(string inputPath, string outputPath);
    Task DecryptFileAsync(string inputPath, string outputPath);
    string ComputeHash(string input);
    bool VerifyHash(string input, string hash);
    string GenerateSecureKey(int length = 32);
    string EncryptField(string fieldValue, string fieldName);
    string DecryptField(string encryptedValue, string fieldName);
}

/// <summary>
/// 可逆加密服务接口
/// </summary>
public interface IReversibleEncryptionService
{
    EncryptedPii EncryptPii(PersonalInfo pii);
    PersonalInfo DecryptPii(EncryptedPii encrypted);
    PartialPersonalInfo PartialDecryptPii(EncryptedPii encrypted, params string[] fieldsToDecrypt);
}