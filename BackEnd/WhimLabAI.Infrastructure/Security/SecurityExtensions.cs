using Microsoft.AspNetCore.Antiforgery;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Cors.Infrastructure;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.RateLimiting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.Threading.RateLimiting;

namespace WhimLabAI.Infrastructure.Security;

/// <summary>
/// 安全配置扩展方法
/// </summary>
public static class SecurityExtensions
{
    /// <summary>
    /// 添加安全服务
    /// </summary>
    public static IServiceCollection AddSecurityServices(this IServiceCollection services, IConfiguration configuration)
    {
        // 配置防伪令牌
        services.AddAntiforgery(options =>
        {
            options.HeaderName = "X-CSRF-TOKEN";
            options.Cookie.Name = "__Host-CSRF-TOKEN";
            options.Cookie.HttpOnly = true;
            options.Cookie.SecurePolicy = CookieSecurePolicy.Always;
            options.Cookie.SameSite = SameSiteMode.Strict;
        });

        // 配置CORS
        services.AddCors(options =>
        {
            options.AddPolicy("DefaultPolicy", builder =>
            {
                var allowedOrigins = configuration.GetSection("Cors:AllowedOrigins").Get<string[]>() ?? 
                    new[] { "https://localhost:7216", "https://localhost:7040" };
                
                builder
                    .WithOrigins(allowedOrigins)
                    .AllowAnyMethod()
                    .AllowAnyHeader()
                    .AllowCredentials()
                    .WithExposedHeaders("Content-Disposition", "X-Total-Count", "X-Page-Number", "X-Page-Size");
            });

            options.AddPolicy("ApiPolicy", builder =>
            {
                builder
                    .AllowAnyOrigin()
                    .AllowAnyMethod()
                    .AllowAnyHeader()
                    .WithExposedHeaders("Content-Disposition");
            });
        });

        // 配置HSTS
        services.AddHsts(options =>
        {
            options.Preload = true;
            options.IncludeSubDomains = true;
            options.MaxAge = TimeSpan.FromDays(365);
            options.ExcludedHosts.Add("localhost");
            options.ExcludedHosts.Add("127.0.0.1");
            options.ExcludedHosts.Add("[::1]");
        });

        // 配置HTTPS重定向
        services.AddHttpsRedirection(options =>
        {
            options.RedirectStatusCode = StatusCodes.Status307TemporaryRedirect;
            options.HttpsPort = configuration.GetValue<int?>("HttpsPort");
        });

        // 配置Cookie策略
        services.Configure<CookiePolicyOptions>(options =>
        {
            options.CheckConsentNeeded = context => true;
            options.MinimumSameSitePolicy = SameSiteMode.Strict;
            // HttpOnly is controlled per-cookie, not globally
            options.Secure = CookieSecurePolicy.Always;
        });

        // 配置数据保护
        services.AddDataProtection();
        // Additional data protection configuration can be added here

        return services;
    }

    /// <summary>
    /// 使用安全中间件
    /// </summary>
    public static IApplicationBuilder UseSecurityMiddleware(this IApplicationBuilder app, IConfiguration configuration)
    {
        // 强制HTTPS
        if (!IsLocalDevelopment(configuration))
        {
            app.UseHsts();
            app.UseHttpsRedirection();
        }

        // 使用安全头中间件
        app.UseMiddleware<SecurityHeadersMiddleware>();

        // 使用CORS
        app.UseCors("DefaultPolicy");

        // 使用Cookie策略
        app.UseCookiePolicy();

        return app;
    }

    /// <summary>
    /// 配置API版本控制
    /// </summary>
    public static IServiceCollection AddApiVersioningConfiguration(this IServiceCollection services)
    {
        // API versioning requires Microsoft.AspNetCore.Mvc.Versioning package
        // services.AddApiVersioning(options =>
        // {
        //     options.ReportApiVersions = true;
        //     options.AssumeDefaultVersionWhenUnspecified = true;
        //     options.DefaultApiVersion = new Microsoft.AspNetCore.Mvc.ApiVersion(1, 0);
        // });
        //
        // services.AddVersionedApiExplorer(options =>
        // {
        //     options.GroupNameFormat = "'v'VVV";
        //     options.SubstituteApiVersionInUrl = true;
        // });

        return services;
    }

    /// <summary>
    /// 配置输入验证
    /// </summary>
    public static IServiceCollection AddInputValidation(this IServiceCollection services)
    {
        services.Configure<Microsoft.AspNetCore.Mvc.ApiBehaviorOptions>(options =>
        {
            options.InvalidModelStateResponseFactory = context =>
            {
                var errors = context.ModelState
                    .Where(e => e.Value?.Errors.Count > 0)
                    .SelectMany(e => e.Value!.Errors.Select(error => new
                    {
                        Field = e.Key,
                        Message = error.ErrorMessage
                    }))
                    .ToList();

                return new Microsoft.AspNetCore.Mvc.BadRequestObjectResult(new
                {
                    error = "Validation failed",
                    details = errors
                });
            };
        });

        return services;
    }

    /// <summary>
    /// 添加速率限制配置
    /// </summary>
    public static IServiceCollection AddRateLimitingConfiguration(this IServiceCollection services, IConfiguration configuration)
    {
        // 检查是否禁用速率限制
        var disableRateLimiting = configuration.GetValue<bool>("Development:DisableRateLimiting");
        if (disableRateLimiting)
        {
            // 添加一个空的速率限制器，不做任何限制
            services.AddRateLimiter(options =>
            {
                options.RejectionStatusCode = StatusCodes.Status429TooManyRequests;
                options.OnRejected = async (context, cancellationToken) =>
                {
                    // 在QuickDev模式下，永远不会触发这个回调
                    await Task.CompletedTask;
                };
            });
            return services;
        }

        services.AddRateLimiter(options =>
        {
            options.RejectionStatusCode = StatusCodes.Status429TooManyRequests;
            
            // 全局速率限制策略
            options.GlobalLimiter = PartitionedRateLimiter.Create<HttpContext, string>(httpContext =>
            {
                var userIdentifier = httpContext.User?.Identity?.Name ?? httpContext.Connection.RemoteIpAddress?.ToString() ?? "anonymous";
                
                return RateLimitPartition.GetFixedWindowLimiter(
                    partitionKey: userIdentifier,
                    factory: partition => new FixedWindowRateLimiterOptions
                    {
                        AutoReplenishment = true,
                        PermitLimit = configuration.GetValue<int>("ApiSettings:RateLimitRequestCount", 100),
                        Window = TimeSpan.FromSeconds(configuration.GetValue<int>("ApiSettings:RateLimitWindowSeconds", 60))
                    });
            });
            
            // API通用限制
            options.AddPolicy("api", httpContext =>
                RateLimitPartition.GetFixedWindowLimiter(
                    partitionKey: httpContext.User?.Identity?.Name ?? httpContext.Connection.RemoteIpAddress?.ToString() ?? "anonymous",
                    factory: partition => new FixedWindowRateLimiterOptions
                    {
                        AutoReplenishment = true,
                        PermitLimit = 100,
                        Window = TimeSpan.FromMinutes(1)
                    }));
            
            // 登录限制（更严格）
            options.AddPolicy("auth", httpContext =>
                RateLimitPartition.GetFixedWindowLimiter(
                    partitionKey: httpContext.Connection.RemoteIpAddress?.ToString() ?? "anonymous",
                    factory: partition => new FixedWindowRateLimiterOptions
                    {
                        AutoReplenishment = true,
                        PermitLimit = 5,
                        Window = TimeSpan.FromMinutes(15)
                    }));
            
            // 注册限制
            options.AddPolicy("register", httpContext =>
                RateLimitPartition.GetFixedWindowLimiter(
                    partitionKey: httpContext.Connection.RemoteIpAddress?.ToString() ?? "anonymous",
                    factory: partition => new FixedWindowRateLimiterOptions
                    {
                        AutoReplenishment = true,
                        PermitLimit = 3,
                        Window = TimeSpan.FromHours(1)
                    }));
            
            // 验证码限制
            options.AddPolicy("captcha", httpContext =>
                RateLimitPartition.GetFixedWindowLimiter(
                    partitionKey: httpContext.Connection.RemoteIpAddress?.ToString() ?? "anonymous",
                    factory: partition => new FixedWindowRateLimiterOptions
                    {
                        AutoReplenishment = true,
                        PermitLimit = 10,
                        Window = TimeSpan.FromMinutes(5)
                    }));
            
            // AI对话限制（基于用户）
            options.AddPolicy("ai-conversation", httpContext =>
                RateLimitPartition.GetFixedWindowLimiter(
                    partitionKey: httpContext.User?.Identity?.Name ?? "anonymous",
                    factory: partition => new FixedWindowRateLimiterOptions
                    {
                        AutoReplenishment = true,
                        PermitLimit = 20,
                        Window = TimeSpan.FromMinutes(1)
                    }));
            
            // 文件上传限制
            options.AddPolicy("file-upload", httpContext =>
                RateLimitPartition.GetFixedWindowLimiter(
                    partitionKey: httpContext.User?.Identity?.Name ?? httpContext.Connection.RemoteIpAddress?.ToString() ?? "anonymous",
                    factory: partition => new FixedWindowRateLimiterOptions
                    {
                        AutoReplenishment = true,
                        PermitLimit = 10,
                        Window = TimeSpan.FromMinutes(10)
                    }));
            
            // 处理被限制的请求
            options.OnRejected = async (context, cancellationToken) =>
            {
                context.HttpContext.Response.StatusCode = StatusCodes.Status429TooManyRequests;
                context.HttpContext.Response.ContentType = "application/json";
                
                var response = new
                {
                    error = "TooManyRequests",
                    message = "请求过于频繁，请稍后再试",
                    retryAfter = context.Lease.TryGetMetadata(MetadataName.RetryAfter, out var retryAfterValue) 
                        ? ((TimeSpan)retryAfterValue).TotalSeconds 
                        : 60
                };
                
                await context.HttpContext.Response.WriteAsync(
                    System.Text.Json.JsonSerializer.Serialize(response), 
                    cancellationToken);
            };
        });

        return services;
    }

    private static bool IsLocalDevelopment(IConfiguration configuration)
    {
        var environment = configuration.GetValue<string>("ASPNETCORE_ENVIRONMENT");
        return environment == "Development" || environment == "Local";
    }
}

/// <summary>
/// 数据保护扩展
/// </summary>
public static class DataProtectionExtensions
{
    public static Microsoft.AspNetCore.DataProtection.IDataProtectionBuilder PersistKeysToDbContext<TContext>(
        this Microsoft.AspNetCore.DataProtection.IDataProtectionBuilder builder)
        where TContext : Microsoft.EntityFrameworkCore.DbContext
    {
        builder.Services.AddDbContext<TContext>();
        
        // 实际实现应该创建一个专门的表来存储数据保护密钥
        // 这里简化处理，实际项目中应该使用专门的密钥存储
        
        return builder;
    }
}