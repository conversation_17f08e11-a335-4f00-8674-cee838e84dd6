using System;
using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;
using Microsoft.Extensions.DependencyInjection;
using WhimLabAI.Abstractions.Application;

namespace WhimLabAI.Infrastructure.Security;

/// <summary>
/// SQL注入安全验证特性
/// </summary>
[AttributeUsage(AttributeTargets.Property | AttributeTargets.Field | AttributeTargets.Parameter)]
public class SqlSafeAttribute : ValidationAttribute
{
    private static readonly Regex SqlInjectionPattern = new(
        @"(\b(union|select|insert|update|delete|drop|create|alter|exec|execute|script|javascript|vbscript)\b)|(-{2})|(/\*.*\*/)",
        RegexOptions.IgnoreCase | RegexOptions.Compiled);

    public override bool IsValid(object? value)
    {
        if (value == null)
        {
            return true;
        }

        var stringValue = value.ToString() ?? string.Empty;
        
        if (SqlInjectionPattern.IsMatch(stringValue))
        {
            ErrorMessage = "Input contains potentially dangerous SQL patterns";
            return false;
        }

        return true;
    }
}

/// <summary>
/// XSS安全验证特性
/// </summary>
[AttributeUsage(AttributeTargets.Property | AttributeTargets.Field | AttributeTargets.Parameter)]
public class XssSafeAttribute : ValidationAttribute
{
    private static readonly Regex XssPattern = new(
        @"(<script.*?>.*?</script>)|(<.*?javascript:.*?>)|(<.*?onerror=.*?>)|(<.*?onload=.*?>)",
        RegexOptions.IgnoreCase | RegexOptions.Compiled);

    public override bool IsValid(object? value)
    {
        if (value == null)
        {
            return true;
        }

        var stringValue = value.ToString() ?? string.Empty;
        
        if (XssPattern.IsMatch(stringValue))
        {
            ErrorMessage = "Input contains potentially dangerous script patterns";
            return false;
        }

        return true;
    }
}

/// <summary>
/// 安全输入验证特性
/// </summary>
[AttributeUsage(AttributeTargets.Property | AttributeTargets.Field | AttributeTargets.Parameter)]
public class SecureInputAttribute : ValidationAttribute
{
    private readonly InputValidationType _validationType;

    public SecureInputAttribute(InputValidationType validationType)
    {
        _validationType = validationType;
    }

    public override bool IsValid(object? value)
    {
        if (value == null)
        {
            return true;
        }

        var stringValue = value.ToString() ?? string.Empty;
        
        // 执行基本验证
        return _validationType switch
        {
            InputValidationType.Email => new EmailAddressAttribute().IsValid(stringValue),
            InputValidationType.Url => new UrlAttribute().IsValid(stringValue),
            InputValidationType.PhoneNumber => new PhoneAttribute().IsValid(stringValue),
            _ => true
        };
    }
}

/// <summary>
/// 文件路径安全验证特性
/// </summary>
[AttributeUsage(AttributeTargets.Property | AttributeTargets.Field | AttributeTargets.Parameter)]
public class SafeFilePathAttribute : ValidationAttribute
{
    private static readonly Regex PathTraversalPattern = new(
        @"(\.\.[\\/])|([\/\\]\.\.[\/\\])",
        RegexOptions.Compiled);

    private static readonly Regex InvalidPathChars = new(
        @"[<>:""|?*]",
        RegexOptions.Compiled);

    public override bool IsValid(object? value)
    {
        if (value == null)
        {
            return true;
        }

        var path = value.ToString() ?? string.Empty;
        
        // 检查路径遍历攻击
        if (PathTraversalPattern.IsMatch(path))
        {
            ErrorMessage = "Path traversal patterns detected";
            return false;
        }

        // 检查无效字符
        if (InvalidPathChars.IsMatch(path))
        {
            ErrorMessage = "Path contains invalid characters";
            return false;
        }

        return true;
    }
}

/// <summary>
/// 密码强度验证特性
/// </summary>
[AttributeUsage(AttributeTargets.Property | AttributeTargets.Field | AttributeTargets.Parameter)]
public class StrongPasswordAttribute : ValidationAttribute
{
    public int MinimumLength { get; set; } = 8;
    public bool RequireUppercase { get; set; } = true;
    public bool RequireLowercase { get; set; } = true;
    public bool RequireDigit { get; set; } = true;
    public bool RequireSpecialCharacter { get; set; } = true;

    public override bool IsValid(object? value)
    {
        if (value == null)
        {
            ErrorMessage = "Password is required";
            return false;
        }

        var password = value.ToString() ?? string.Empty;
        
        if (password.Length < MinimumLength)
        {
            ErrorMessage = $"Password must be at least {MinimumLength} characters long";
            return false;
        }

        if (RequireUppercase && !Regex.IsMatch(password, @"[A-Z]"))
        {
            ErrorMessage = "Password must contain at least one uppercase letter";
            return false;
        }

        if (RequireLowercase && !Regex.IsMatch(password, @"[a-z]"))
        {
            ErrorMessage = "Password must contain at least one lowercase letter";
            return false;
        }

        if (RequireDigit && !Regex.IsMatch(password, @"\d"))
        {
            ErrorMessage = "Password must contain at least one digit";
            return false;
        }

        if (RequireSpecialCharacter && !Regex.IsMatch(password, @"[!@#$%^&*()_+\-=\[\]{};':""\\|,.<>\/?]"))
        {
            ErrorMessage = "Password must contain at least one special character";
            return false;
        }

        return true;
    }
}

/// <summary>
/// 安全HTML验证特性
/// </summary>
[AttributeUsage(AttributeTargets.Property | AttributeTargets.Field | AttributeTargets.Parameter)]
public class SafeHtmlAttribute : ValidationAttribute
{
    public bool AllowHtml { get; set; } = true;

    public override bool IsValid(object? value)
    {
        if (value == null)
        {
            return true;
        }

        var html = value.ToString() ?? string.Empty;
        
        if (!AllowHtml && html.Contains("<") && html.Contains(">"))
        {
            ErrorMessage = "HTML content is not allowed";
            return false;
        }

        return true;
    }
}

/// <summary>
/// 数字范围安全验证特性
/// </summary>
[AttributeUsage(AttributeTargets.Property | AttributeTargets.Field | AttributeTargets.Parameter)]
public class SecureRangeAttribute : RangeAttribute
{
    public SecureRangeAttribute(int minimum, int maximum) : base(minimum, maximum)
    {
    }

    public SecureRangeAttribute(double minimum, double maximum) : base(minimum, maximum)
    {
    }

    public override bool IsValid(object? value)
    {
        // 防止数字溢出攻击
        if (value == null)
        {
            return true;
        }

        try
        {
            return base.IsValid(value);
        }
        catch (OverflowException)
        {
            return false;
        }
    }
}

/// <summary>
/// GUID验证特性
/// </summary>
[AttributeUsage(AttributeTargets.Property | AttributeTargets.Field | AttributeTargets.Parameter)]
public class ValidGuidAttribute : ValidationAttribute
{
    public bool AllowEmpty { get; set; } = false;

    public override bool IsValid(object? value)
    {
        if (value == null)
        {
            return true;
        }

        if (value is Guid guid)
        {
            if (!AllowEmpty && guid == Guid.Empty)
            {
                ErrorMessage = "Empty GUID is not allowed";
                return false;
            }
            return true;
        }

        if (value is string stringValue)
        {
            if (Guid.TryParse(stringValue, out var parsedGuid))
            {
                if (!AllowEmpty && parsedGuid == Guid.Empty)
                {
                    ErrorMessage = "Empty GUID is not allowed";
                    return false;
                }
                return true;
            }
        }

        ErrorMessage = "Invalid GUID format";
        return false;
    }
}