using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;

namespace WhimLabAI.Infrastructure.Security;

/// <summary>
/// SQL注入防护服务
/// </summary>
public class SqlInjectionProtection : ISqlInjectionProtection
{
    private readonly ILogger<SqlInjectionProtection> _logger;
    private readonly HashSet<string> _sqlKeywords;
    private readonly List<Regex> _dangerousPatterns;

    public SqlInjectionProtection(ILogger<SqlInjectionProtection> logger)
    {
        _logger = logger;
        
        // SQL关键字列表
        _sqlKeywords = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            "SELECT", "INSERT", "UPDATE", "DELETE", "DROP", "CREATE", "ALTER",
            "EXEC", "EXECUTE", "UNION", "FROM", "WHERE", "JOIN", "ORDER BY",
            "GROUP BY", "HAVING", "INTO", "VALUES", "SET", "DECLARE", "CAST",
            "CONVERT", "TRUNCATE", "REPLACE", "MERGE", "CALL", "EXPLAIN",
            "GRANT", "REVOKE", "DENY", "BACKUP", "RESTORE", "ATTACH", "DETACH"
        };

        // 危险模式列表
        _dangerousPatterns = new List<Regex>
        {
            new Regex(@"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|EXECUTE)\b.*\b(FROM|INTO|WHERE|SET)\b)", RegexOptions.IgnoreCase | RegexOptions.Compiled),
            new Regex(@"(--)|(/\*)|(\*/)|(\bOR\b\s*\d+\s*=\s*\d+)|(\bAND\b\s*\d+\s*=\s*\d+)", RegexOptions.IgnoreCase | RegexOptions.Compiled),
            new Regex(@"(;)\s*(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|EXECUTE)", RegexOptions.IgnoreCase | RegexOptions.Compiled),
            new Regex(@"(UNION\s+(ALL\s+)?SELECT)", RegexOptions.IgnoreCase | RegexOptions.Compiled),
            new Regex(@"(xp_|sp_|@@|INFORMATION_SCHEMA|sys\.|sysobjects|syscolumns)", RegexOptions.IgnoreCase | RegexOptions.Compiled),
            new Regex(@"(<script|</script|javascript:|onerror=|onload=|eval\(|expression\()", RegexOptions.IgnoreCase | RegexOptions.Compiled),
            new Regex(@"(WAITFOR\s+DELAY|BENCHMARK\s*\(|SLEEP\s*\()", RegexOptions.IgnoreCase | RegexOptions.Compiled),
            new Regex(@"(\bCHAR\s*\(\d+\)|\bCONCAT\s*\(|\bCONVERT\s*\()", RegexOptions.IgnoreCase | RegexOptions.Compiled)
        };
    }

    /// <summary>
    /// 验证输入是否安全
    /// </summary>
    public ValidationResult ValidateInput(string input, ValidationContext context)
    {
        if (string.IsNullOrWhiteSpace(input))
            return ValidationResult.Success();

        var result = new ValidationResult();

        // 检查长度
        if (context.MaxLength > 0 && input.Length > context.MaxLength)
        {
            result.AddError($"Input exceeds maximum length of {context.MaxLength}");
        }

        // 检查SQL关键字（如果启用）
        if (context.CheckSqlKeywords)
        {
            var detectedKeywords = DetectSqlKeywords(input);
            if (detectedKeywords.Any())
            {
                result.AddWarning($"SQL keywords detected: {string.Join(", ", detectedKeywords)}");
                result.Risk = RiskLevel.Medium;
            }
        }

        // 检查危险模式
        var dangerousPattern = DetectDangerousPatterns(input);
        if (dangerousPattern != null)
        {
            result.AddError($"Dangerous pattern detected: {dangerousPattern}");
            result.Risk = RiskLevel.High;
            result.IsValid = false;
            
            _logger.LogWarning("SQL injection attempt detected. Pattern: {Pattern}, Input: {Input}",
                dangerousPattern, input.Substring(0, Math.Min(input.Length, 100)));
        }

        // 检查特殊字符
        if (context.CheckSpecialCharacters)
        {
            var specialChars = DetectSpecialCharacters(input);
            if (specialChars.Any())
            {
                result.AddWarning($"Special characters detected: {string.Join(", ", specialChars)}");
                if (result.Risk < RiskLevel.Low)
                    result.Risk = RiskLevel.Low;
            }
        }

        // 检查编码攻击
        if (DetectEncodingAttack(input))
        {
            result.AddError("Potential encoding attack detected");
            result.Risk = RiskLevel.High;
            result.IsValid = false;
        }

        return result;
    }

    /// <summary>
    /// 清理输入以防止SQL注入
    /// </summary>
    public string SanitizeInput(string input, SanitizationOptions options)
    {
        if (string.IsNullOrWhiteSpace(input))
            return string.Empty;

        var sanitized = input;

        // 移除注释
        if (options.RemoveComments)
        {
            sanitized = RemoveSqlComments(sanitized);
        }

        // 转义特殊字符
        if (options.EscapeSpecialCharacters)
        {
            sanitized = EscapeSpecialCharacters(sanitized);
        }

        // 移除或替换SQL关键字
        if (options.RemoveSqlKeywords)
        {
            sanitized = RemoveSqlKeywords(sanitized);
        }

        // 限制长度
        if (options.MaxLength > 0 && sanitized.Length > options.MaxLength)
        {
            sanitized = sanitized.Substring(0, options.MaxLength);
        }

        // 移除多余的空格
        if (options.NormalizeWhitespace)
        {
            sanitized = NormalizeWhitespace(sanitized);
        }

        return sanitized.Trim();
    }

    /// <summary>
    /// 生成参数化查询的安全参数名
    /// </summary>
    public string GenerateParameterName(string baseName)
    {
        // 移除所有非字母数字字符
        var safeName = Regex.Replace(baseName, @"[^a-zA-Z0-9]", "");
        
        // 确保以字母开头
        if (safeName.Length > 0 && char.IsDigit(safeName[0]))
        {
            safeName = "p" + safeName;
        }
        
        // 如果为空，使用默认名称
        if (string.IsNullOrEmpty(safeName))
        {
            safeName = "param";
        }
        
        // 添加唯一后缀
        return $"{safeName}_{Guid.NewGuid():N}".Substring(0, 30);
    }

    /// <summary>
    /// 验证参数化查询
    /// </summary>
    public bool IsParameterizedQuery(string query)
    {
        if (string.IsNullOrWhiteSpace(query))
            return false;

        // 检查是否包含参数占位符
        var hasParameters = Regex.IsMatch(query, @"@\w+|:\w+|\?");
        
        // 检查是否有字符串拼接
        var hasStringConcatenation = query.Contains("+") || query.Contains("||") || query.Contains("CONCAT");
        
        // 检查是否有动态SQL构建
        var hasDynamicSql = Regex.IsMatch(query, @"EXEC\s*\(|EXECUTE\s*\(|sp_executesql", RegexOptions.IgnoreCase);
        
        return hasParameters && !hasStringConcatenation && !hasDynamicSql;
    }

    private HashSet<string> DetectSqlKeywords(string input)
    {
        var detected = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
        var words = Regex.Split(input, @"\W+");
        
        foreach (var word in words)
        {
            if (_sqlKeywords.Contains(word))
            {
                detected.Add(word);
            }
        }
        
        return detected;
    }

    private string? DetectDangerousPatterns(string input)
    {
        foreach (var pattern in _dangerousPatterns)
        {
            if (pattern.IsMatch(input))
            {
                return pattern.ToString();
            }
        }
        
        return null;
    }

    private HashSet<char> DetectSpecialCharacters(string input)
    {
        var specialChars = new HashSet<char>();
        var dangerous = new[] { '\'', '"', ';', '-', '/', '*', '=', '<', '>', '(', ')', '{', '}', '[', ']', '\\', '|', '&', '%', '@', '!' };
        
        foreach (var ch in input)
        {
            if (dangerous.Contains(ch))
            {
                specialChars.Add(ch);
            }
        }
        
        return specialChars;
    }

    private bool DetectEncodingAttack(string input)
    {
        // 检查URL编码攻击
        if (input.Contains("%27") || input.Contains("%22") || input.Contains("%3B"))
            return true;
        
        // 检查Unicode编码攻击
        if (Regex.IsMatch(input, @"\\u[0-9a-fA-F]{4}"))
            return true;
        
        // 检查HTML实体编码攻击
        if (Regex.IsMatch(input, @"&#x?[0-9a-fA-F]+;"))
            return true;
        
        // 检查Base64编码的SQL
        try
        {
            var decoded = System.Text.Encoding.UTF8.GetString(Convert.FromBase64String(input));
            if (_sqlKeywords.Any(keyword => decoded.Contains(keyword, StringComparison.OrdinalIgnoreCase)))
                return true;
        }
        catch { }
        
        return false;
    }

    private string RemoveSqlComments(string input)
    {
        // 移除单行注释
        input = Regex.Replace(input, @"--.*$", "", RegexOptions.Multiline);
        
        // 移除多行注释
        input = Regex.Replace(input, @"/\*[\s\S]*?\*/", "");
        
        return input;
    }

    private string EscapeSpecialCharacters(string input)
    {
        // 转义单引号
        input = input.Replace("'", "''");
        
        // 转义反斜杠
        input = input.Replace("\\", "\\\\");
        
        // 转义通配符
        input = input.Replace("%", "\\%");
        input = input.Replace("_", "\\_");
        
        return input;
    }

    private string RemoveSqlKeywords(string input)
    {
        foreach (var keyword in _sqlKeywords)
        {
            var pattern = $@"\b{Regex.Escape(keyword)}\b";
            input = Regex.Replace(input, pattern, "", RegexOptions.IgnoreCase);
        }
        
        return input;
    }

    private string NormalizeWhitespace(string input)
    {
        // 替换多个空格为单个空格
        input = Regex.Replace(input, @"\s+", " ");
        
        // 移除字符串开头和结尾的空格
        return input.Trim();
    }
}

/// <summary>
/// 验证上下文
/// </summary>
public class ValidationContext
{
    public bool CheckSqlKeywords { get; set; } = true;
    public bool CheckSpecialCharacters { get; set; } = true;
    public int MaxLength { get; set; } = 0;
    public string InputType { get; set; } = "general";
}

/// <summary>
/// 验证结果
/// </summary>
public class ValidationResult
{
    public bool IsValid { get; set; } = true;
    public RiskLevel Risk { get; set; } = RiskLevel.None;
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();

    public void AddError(string error)
    {
        Errors.Add(error);
        IsValid = false;
    }

    public void AddWarning(string warning)
    {
        Warnings.Add(warning);
    }

    public static ValidationResult Success()
    {
        return new ValidationResult { IsValid = true, Risk = RiskLevel.None };
    }
}

/// <summary>
/// 风险级别
/// </summary>
public enum RiskLevel
{
    None = 0,
    Low = 1,
    Medium = 2,
    High = 3,
    Critical = 4
}

/// <summary>
/// 清理选项
/// </summary>
public class SanitizationOptions
{
    public bool RemoveComments { get; set; } = true;
    public bool EscapeSpecialCharacters { get; set; } = true;
    public bool RemoveSqlKeywords { get; set; } = false;
    public bool NormalizeWhitespace { get; set; } = true;
    public int MaxLength { get; set; } = 0;
}

/// <summary>
/// SQL注入防护接口
/// </summary>
public interface ISqlInjectionProtection
{
    ValidationResult ValidateInput(string input, ValidationContext context);
    string SanitizeInput(string input, SanitizationOptions options);
    string GenerateParameterName(string baseName);
    bool IsParameterizedQuery(string query);
}