using System;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace WhimLabAI.Infrastructure.Security;

/// <summary>
/// 安全服务扩展
/// </summary>
public static class SecurityServiceExtensions
{
    /// <summary>
    /// 添加安全服务
    /// </summary>
    public static IServiceCollection AddSecurityServices(this IServiceCollection services, IConfiguration configuration)
    {
        // 注册安全服务
        services.AddSingleton<IInputSanitizer>(provider =>
        {
            var options = new SanitizerOptions
            {
                StrictMode = configuration.GetValue<bool>("Security:Sanitizer:StrictMode", false),
                MaxLength = configuration.GetValue<int>("Security:Sanitizer:MaxLength", 0)
            };
            return new InputSanitizer(options);
        });

        services.AddSingleton<ISqlInjectionProtection, SqlInjectionProtection>();
        services.AddSingleton<IAntiBruteForceService, AntiBruteForceService>();
        services.AddSingleton<ISecureFileUploadService, SecureFileUploadService>();

        // 配置防暴力破解选项
        services.Configure<AntiBruteForceOptions>(configuration.GetSection("Security:AntiBruteForce"));
        
        // 配置文件上传选项
        services.Configure<FileUploadOptions>(configuration.GetSection("Security:FileUpload"));

        // 配置CORS
        services.AddCors(options =>
        {
            options.AddPolicy("SecurePolicy", builder =>
            {
                var allowedOrigins = configuration.GetSection("Security:Cors:AllowedOrigins").Get<string[]>() 
                    ?? new[] { "https://whimlab.ai", "https://admin.whimlab.ai" };
                
                builder.WithOrigins(allowedOrigins)
                    .AllowAnyMethod()
                    .AllowAnyHeader()
                    .AllowCredentials()
                    .SetPreflightMaxAge(TimeSpan.FromHours(24));
            });
        });

        // 配置防伪令牌
        services.AddAntiforgery(options =>
        {
            options.HeaderName = "X-CSRF-TOKEN";
            options.Cookie.Name = "CSRF-TOKEN";
            options.Cookie.HttpOnly = true;
            options.Cookie.SecurePolicy = CookieSecurePolicy.Always;
            options.Cookie.SameSite = SameSiteMode.Strict;
        });

        // 配置Cookie策略
        services.Configure<CookiePolicyOptions>(options =>
        {
            options.CheckConsentNeeded = context => true;
            options.MinimumSameSitePolicy = SameSiteMode.Strict;
            options.HttpOnly = Microsoft.AspNetCore.CookiePolicy.HttpOnlyPolicy.Always;
            options.Secure = CookieSecurePolicy.Always;
        });

        // 配置会话
        services.AddSession(options =>
        {
            options.IdleTimeout = TimeSpan.FromMinutes(30);
            options.Cookie.Name = "WhimLab.Session";
            options.Cookie.HttpOnly = true;
            options.Cookie.IsEssential = true;
            options.Cookie.SecurePolicy = CookieSecurePolicy.Always;
            options.Cookie.SameSite = SameSiteMode.Strict;
        });

        // 配置API行为选项
        services.Configure<ApiBehaviorOptions>(options =>
        {
            options.SuppressModelStateInvalidFilter = false;
            options.SuppressMapClientErrors = false;
        });

        // 添加HTTP严格传输安全
        services.AddHsts(options =>
        {
            options.Preload = true;
            options.IncludeSubDomains = true;
            options.MaxAge = TimeSpan.FromDays(365);
        });

        return services;
    }

    /// <summary>
    /// 使用安全中间件
    /// </summary>
    public static IApplicationBuilder UseSecurityMiddleware(this IApplicationBuilder app, IConfiguration configuration)
    {
        // 使用HTTPS重定向
        app.UseHttpsRedirection();

        // 使用HSTS
        app.UseHsts();

        // 使用安全头中间件
        var securityHeadersOptions = new SecurityHeadersOptions
        {
            UseHsts = configuration.GetValue<bool>("Security:Headers:UseHsts", true),
            HstsMaxAge = configuration.GetValue<int>("Security:Headers:HstsMaxAge", 31536000),
            XFrameOptions = configuration.GetValue<string>("Security:Headers:XFrameOptions", "DENY"),
            ReferrerPolicy = configuration.GetValue<string>("Security:Headers:ReferrerPolicy", "strict-origin-when-cross-origin"),
            ContentSecurityPolicy = configuration.GetValue<string>("Security:Headers:ContentSecurityPolicy")
        };
        
        app.UseMiddleware<SecurityHeadersMiddleware>(securityHeadersOptions);

        // 使用CORS
        app.UseCors("SecurePolicy");

        // 使用Cookie策略
        app.UseCookiePolicy();

        // 使用会话
        app.UseSession();

        // 使用速率限制中间件
        app.UseMiddleware<RateLimitingMiddleware>();

        // 使用请求验证中间件
        app.UseMiddleware<RequestValidationMiddleware>();

        return app;
    }
}

/// <summary>
/// 速率限制中间件
/// </summary>
public class RateLimitingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly IAntiBruteForceService _antiBruteForce;

    public RateLimitingMiddleware(RequestDelegate next, IAntiBruteForceService antiBruteForce)
    {
        _next = next;
        _antiBruteForce = antiBruteForce;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var clientIp = GetClientIpAddress(context);
        var endpoint = context.Request.Path.Value?.ToLowerInvariant() ?? "";

        // 检查IP是否在黑名单中
        if (await _antiBruteForce.IsBlacklistedAsync(clientIp))
        {
            context.Response.StatusCode = 403;
            await context.Response.WriteAsync("Access denied");
            return;
        }

        // 检查是否被锁定
        var lockKey = $"{clientIp}:{endpoint}";
        if (await _antiBruteForce.IsLockedAsync(lockKey, "api_request"))
        {
            context.Response.StatusCode = 429;
            context.Response.Headers.Add("Retry-After", "300");
            await context.Response.WriteAsync("Too many requests");
            return;
        }

        await _next(context);
    }

    private string GetClientIpAddress(HttpContext context)
    {
        // 检查X-Forwarded-For头
        var forwardedFor = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
        if (!string.IsNullOrEmpty(forwardedFor))
        {
            var ips = forwardedFor.Split(',', StringSplitOptions.RemoveEmptyEntries);
            if (ips.Length > 0)
            {
                return ips[0].Trim();
            }
        }

        // 检查X-Real-IP头
        var realIp = context.Request.Headers["X-Real-IP"].FirstOrDefault();
        if (!string.IsNullOrEmpty(realIp))
        {
            return realIp;
        }

        // 返回远程IP地址
        return context.Connection.RemoteIpAddress?.ToString() ?? "unknown";
    }
}

/// <summary>
/// 请求验证中间件
/// </summary>
public class RequestValidationMiddleware
{
    private readonly RequestDelegate _next;
    private readonly IInputSanitizer _sanitizer;
    private readonly ISqlInjectionProtection _sqlProtection;

    public RequestValidationMiddleware(
        RequestDelegate next,
        IInputSanitizer sanitizer,
        ISqlInjectionProtection sqlProtection)
    {
        _next = next;
        _sanitizer = sanitizer;
        _sqlProtection = sqlProtection;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        // 验证查询字符串
        foreach (var query in context.Request.Query)
        {
            var validation = _sqlProtection.ValidateInput(query.Value.ToString(), new ValidationContext
            {
                InputType = "query",
                MaxLength = 1000
            });

            if (!validation.IsValid && validation.Risk >= RiskLevel.High)
            {
                context.Response.StatusCode = 400;
                await context.Response.WriteAsync("Invalid request");
                return;
            }
        }

        // 验证请求头
        foreach (var header in context.Request.Headers)
        {
            // 跳过标准头
            if (IsStandardHeader(header.Key))
                continue;

            var headerValue = header.Value.ToString();
            if (headerValue.Length > 1000 || ContainsSuspiciousContent(headerValue))
            {
                context.Response.StatusCode = 400;
                await context.Response.WriteAsync("Invalid request headers");
                return;
            }
        }

        await _next(context);
    }

    private bool IsStandardHeader(string headerName)
    {
        var standardHeaders = new[]
        {
            "Accept", "Accept-Language", "Accept-Encoding", "Authorization",
            "Cache-Control", "Connection", "Content-Length", "Content-Type",
            "Cookie", "Host", "Origin", "Referer", "User-Agent", "X-Requested-With"
        };

        return standardHeaders.Contains(headerName, StringComparer.OrdinalIgnoreCase);
    }

    private bool ContainsSuspiciousContent(string content)
    {
        var suspiciousPatterns = new[]
        {
            "<script", "javascript:", "onerror=", "onclick=",
            "../", "..\\", "%2e%2e", "%00", "\0",
            "exec(", "eval(", "system(", "cmd.exe", "/bin/bash"
        };

        return suspiciousPatterns.Any(pattern => 
            content.Contains(pattern, StringComparison.OrdinalIgnoreCase));
    }
}