using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Infrastructure.Auditing;
using WhimLabAI.Infrastructure.BackgroundJobs;

namespace WhimLabAI.Infrastructure.Extensions;

/// <summary>
/// 审计服务扩展方法
/// </summary>
public static class AuditingServiceExtensions
{
    /// <summary>
    /// 添加审计服务
    /// </summary>
    public static IServiceCollection AddAuditing(this IServiceCollection services, IConfiguration configuration)
    {
        // 配置审计选项
        services.Configure<AuditOptions>(configuration.GetSection("Audit"));
        services.Configure<AuditArchiveOptions>(configuration.GetSection("AuditArchive"));

        // 注册审计日志服务
        services.AddSingleton<IAuditLogger, AuditLogService>();
        
        // 注册后台服务
        services.AddHostedService<AuditLogBackgroundService>();
        services.AddHostedService<AuditLogMaintenanceJob>();
        services.AddHostedService<AuditLogPerformanceOptimizationJob>();

        // 注册敏感操作拦截器
        services.AddScoped<SensitiveOperationInterceptor>();
        
        // 配置MVC选项以使用拦截器
        services.Configure<MvcOptions>(options =>
        {
            options.Filters.Add<SensitiveOperationInterceptor>();
        });

        return services;
    }
    
    /// <summary>
    /// 使用审计中间件
    /// </summary>
    public static IApplicationBuilder UseAuditing(this IApplicationBuilder app)
    {
        // 使用审计日志中间件
        app.UseMiddleware<AuditLogMiddleware>();
        
        return app;
    }
}