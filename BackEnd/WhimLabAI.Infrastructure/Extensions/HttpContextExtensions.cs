using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;

namespace WhimLabAI.Infrastructure.Extensions;

/// <summary>
/// HttpContext扩展方法
/// </summary>
public static class HttpContextExtensions
{
    /// <summary>
    /// 获取真实客户端IP地址
    /// </summary>
    public static string GetRealIpAddress(this HttpContext context)
    {
        // Check X-Forwarded-For header first (for proxy/load balancer scenarios)
        var forwardedFor = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
        if (!string.IsNullOrEmpty(forwardedFor))
        {
            var ips = forwardedFor.Split(',', StringSplitOptions.RemoveEmptyEntries);
            if (ips.Length > 0)
                return ips[0].Trim();
        }

        // Check X-Real-IP header
        var realIp = context.Request.Headers["X-Real-IP"].FirstOrDefault();
        if (!string.IsNullOrEmpty(realIp))
            return realIp.Trim();

        // Fall back to RemoteIpAddress
        return context.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
    }
}