using MassTransit;
using MediatR;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using WhimLabAI.Abstractions.Infrastructure;
using WhimLabAI.Domain.Repositories;
using WhimLabAI.Infrastructure.Data.Repositories;
using WhimLabAI.Infrastructure.EventSourcing;
using WhimLabAI.Infrastructure.Messaging;
using WhimLabAI.Infrastructure.Messaging.Consumers;

namespace WhimLabAI.Infrastructure.Extensions;

public static class EventDrivenExtensions
{
    public static IServiceCollection AddEventDrivenArchitecture(
        this IServiceCollection services, 
        IConfiguration configuration)
    {
        // Add MediatR for domain events
        services.AddMediatR(cfg =>
        {
            // TODO: Register event handlers when they are properly implemented
            // cfg.RegisterServicesFromAssembly(typeof(CustomerUserRegisteredEventHandler).Assembly);
            cfg.RegisterServicesFromAssembly(typeof(EventDrivenExtensions).Assembly);
        });

        // Add Event Store
        services.AddScoped<IEventStoreRepository, EventStoreRepository>();
        // services.AddScoped<IEventStore, EventStore>(); // TODO: Fix IEventStore interface implementation

        // Add MassTransit for RabbitMQ
        services.AddMassTransit(x =>
        {
            // Register consumers
            x.AddConsumer<SendEmailConsumer>();

            // Configure RabbitMQ
            x.UsingRabbitMq((context, cfg) =>
            {
                var rabbitMqConfig = configuration.GetSection("RabbitMQ");
                var host = rabbitMqConfig["Host"] ?? "localhost";
                var port = rabbitMqConfig.GetValue<int?>("Port") ?? 5672;
                var username = rabbitMqConfig["Username"] ?? "guest";
                var password = rabbitMqConfig["Password"] ?? "guest";
                var virtualHost = rabbitMqConfig["VirtualHost"] ?? "/";

                cfg.Host(host, virtualHost, h =>
                {
                    h.Username(username);
                    h.Password(password);
                });

                // Configure endpoints
                cfg.ConfigureEndpoints(context);

                // Configure retry policy
                cfg.UseMessageRetry(r => r.Intervals(
                    TimeSpan.FromSeconds(5),
                    TimeSpan.FromSeconds(15),
                    TimeSpan.FromSeconds(30)
                ));

                // Configure error handling
                cfg.UseInMemoryOutbox();
            });
        });

        // Add Message Queue Service
        services.AddScoped<IMessageQueueService, RabbitMQMessageBus>();

        return services;
    }

    public static IServiceCollection AddDomainEventHandlers(this IServiceCollection services)
    {
        // TODO: Register all domain event handlers when they are properly implemented
        // services.AddScoped<INotificationHandler<Domain.DomainEvents.CustomerUserRegisteredEvent>, CustomerUserRegisteredEventHandler>();
        // services.AddScoped<INotificationHandler<Domain.DomainEvents.PaymentCompletedEvent>, PaymentCompletedEventHandler>();
        // services.AddScoped<INotificationHandler<Domain.DomainEvents.AgentPublishedEvent>, AgentPublishedEventHandler>();
        // services.AddScoped<INotificationHandler<Domain.DomainEvents.SubscriptionCreatedEvent>, SubscriptionCreatedEventHandler>();

        return services;
    }
}