using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Mail;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using WhimLabAI.Abstractions.Infrastructure;

namespace WhimLabAI.Infrastructure.Email;

/// <summary>
/// 邮件服务实现
/// </summary>
public class EmailService : IEmailService
{
    private readonly ILogger<EmailService> _logger;
    private readonly IConfiguration _configuration;
    private readonly string _smtpHost;
    private readonly int _smtpPort;
    private readonly bool _useSsl;
    private readonly string _smtpUsername;
    private readonly string _smtpPassword;
    private readonly string _fromName;
    private readonly string _fromAddress;

    public EmailService(ILogger<EmailService> logger, IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
        
        // 从配置读取SMTP设置
        _smtpHost = _configuration["Email:Smtp:Host"] ?? "smtp.whimlab.com";
        _smtpPort = int.Parse(_configuration["Email:Smtp:Port"] ?? "465");
        _useSsl = bool.Parse(_configuration["Email:Smtp:UseSsl"] ?? "true");
        _smtpUsername = _configuration["Email:Smtp:Username"] ?? "<EMAIL>";
        _smtpPassword = _configuration["Email:Smtp:Password"] ?? "";
        _fromName = _configuration["Email:From:Name"] ?? "WhimLab AI";
        _fromAddress = _configuration["Email:From:Address"] ?? _smtpUsername;
    }

    public async Task<bool> SendEmailAsync(string to, string subject, string body, bool isHtml = true, CancellationToken cancellationToken = default)
    {
        return await SendEmailAsync(new List<string> { to }, subject, body, isHtml, cancellationToken);
    }

    public async Task<bool> SendEmailAsync(List<string> to, string subject, string body, bool isHtml = true, CancellationToken cancellationToken = default)
    {
        try
        {
            using var message = new MailMessage
            {
                From = new MailAddress(_fromAddress, _fromName),
                Subject = subject,
                Body = body,
                IsBodyHtml = isHtml
            };

            foreach (var recipient in to)
            {
                message.To.Add(recipient);
            }

            using var client = new SmtpClient(_smtpHost, _smtpPort)
            {
                Credentials = new NetworkCredential(_smtpUsername, _smtpPassword),
                EnableSsl = _useSsl,
                DeliveryMethod = SmtpDeliveryMethod.Network,
                Timeout = 30000 // 30秒超时
            };

            await client.SendMailAsync(message, cancellationToken);
            
            _logger.LogInformation("Email sent successfully to {Recipients} with subject: {Subject}", 
                string.Join(", ", to), subject);
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send email to {Recipients} with subject: {Subject}", 
                string.Join(", ", to), subject);
            return false;
        }
    }

    public async Task<bool> SendEmailWithAttachmentsAsync(string to, string subject, string body, List<string> attachments, bool isHtml = true, CancellationToken cancellationToken = default)
    {
        try
        {
            using var message = new MailMessage
            {
                From = new MailAddress(_fromAddress, _fromName),
                Subject = subject,
                Body = body,
                IsBodyHtml = isHtml
            };

            message.To.Add(to);

            // 添加附件
            foreach (var attachmentPath in attachments)
            {
                if (System.IO.File.Exists(attachmentPath))
                {
                    var attachment = new Attachment(attachmentPath);
                    message.Attachments.Add(attachment);
                }
                else
                {
                    _logger.LogWarning("Attachment file not found: {Path}", attachmentPath);
                }
            }

            using var client = new SmtpClient(_smtpHost, _smtpPort)
            {
                Credentials = new NetworkCredential(_smtpUsername, _smtpPassword),
                EnableSsl = _useSsl,
                DeliveryMethod = SmtpDeliveryMethod.Network,
                Timeout = 60000 // 60秒超时（因为有附件）
            };

            await client.SendMailAsync(message, cancellationToken);
            
            _logger.LogInformation("Email with {AttachmentCount} attachments sent successfully to {Recipient}", 
                attachments.Count, to);
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send email with attachments to {Recipient}", to);
            return false;
        }
    }

    public async Task<bool> SendTemplateEmailAsync(string to, string templateId, Dictionary<string, object> templateData, CancellationToken cancellationToken = default)
    {
        try
        {
            // 根据模板ID获取模板内容
            var (subject, body) = await GetEmailTemplateAsync(templateId, templateData, cancellationToken);
            
            if (string.IsNullOrEmpty(subject) || string.IsNullOrEmpty(body))
            {
                _logger.LogError("Email template {TemplateId} not found or empty", templateId);
                return false;
            }
            
            // 发送邮件
            return await SendEmailAsync(to, subject, body, true, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send template email {TemplateId} to {Recipient}", templateId, to);
            return false;
        }
    }

    private async Task<(string subject, string body)> GetEmailTemplateAsync(string templateId, Dictionary<string, object> templateData, CancellationToken cancellationToken)
    {
        // 这里可以从数据库或文件系统读取模板
        // 简单实现：基于templateId返回预定义模板
        
        await Task.CompletedTask; // 模拟异步操作
        
        return templateId switch
        {
            "welcome" => (
                "欢迎加入WhimLab AI！",
                $@"<h2>亲爱的用户，</h2>
                   <p>欢迎您注册WhimLab AI平台！</p>
                   <p>您的账号已经成功创建，现在可以开始探索我们的AI智能体了。</p>
                   <p>如有任何问题，请随时联系我们的客服团队。</p>
                   <p>祝您使用愉快！</p>
                   <p>WhimLab AI团队</p>"
            ),
            "password-reset" => (
                "重置您的WhimLab AI密码",
                $@"<h2>密码重置请求</h2>
                   <p>我们收到了您的密码重置请求。</p>
                   <p>请点击以下链接重置您的密码：</p>
                   <p><a href='{templateData.GetValueOrDefault("resetLink", "")}'>重置密码</a></p>
                   <p>此链接将在24小时后失效。</p>
                   <p>如果您没有请求重置密码，请忽略此邮件。</p>"
            ),
            "subscription-renewed" => (
                "您的WhimLab AI订阅已续费",
                $@"<h2>订阅续费成功</h2>
                   <p>您的订阅已成功续费。</p>
                   <p>套餐类型：{templateData.GetValueOrDefault("planName", "")}</p>
                   <p>有效期至：{templateData.GetValueOrDefault("expiryDate", "")}</p>
                   <p>感谢您的信任和支持！</p>"
            ),
            _ => ("", "")
        };
    }
    
    public async Task<bool> SendInvoiceEmailAsync(string to, string invoiceNumber, byte[] pdfBytes, CancellationToken cancellationToken = default)
    {
        try
        {
            var subject = $"您的WhimLab AI发票 - {invoiceNumber}";
            var body = $@"<h2>发票已准备就绪</h2>
                         <p>尊敬的客户，</p>
                         <p>您的发票 {invoiceNumber} 已经生成，请查看附件。</p>
                         <p>如有任何问题，请联系我们的客服团队。</p>
                         <p>感谢您使用WhimLab AI！</p>
                         <p>WhimLab AI团队</p>";
            
            // Create email message
            using var message = new MailMessage();
            message.From = new MailAddress(_fromAddress, _fromName);
            message.To.Add(new MailAddress(to));
            message.Subject = subject;
            message.Body = body;
            message.IsBodyHtml = true;
            
            // Attach the PDF
            using var stream = new MemoryStream(pdfBytes);
            var attachment = new Attachment(stream, $"invoice_{invoiceNumber}.pdf", "application/pdf");
            message.Attachments.Add(attachment);
            
            // Send email
            using var client = new SmtpClient(_smtpHost, _smtpPort);
            client.UseDefaultCredentials = false;
            client.Credentials = new NetworkCredential(_smtpUsername, _smtpPassword);
            client.EnableSsl = _useSsl;
            
            await client.SendMailAsync(message, cancellationToken);
            
            _logger.LogInformation("Invoice email sent successfully to {Email} for invoice {InvoiceNumber}", to, invoiceNumber);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send invoice email to {Email} for invoice {InvoiceNumber}", to, invoiceNumber);
            return false;
        }
    }
}