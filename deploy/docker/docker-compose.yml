version: '3.8'

services:
  # PostgreSQL 数据库 with pgvector
  postgres:
    build:
      context: ./docker/postgres
      dockerfile: Dockerfile
    image: pgvector/pgvector:pg16
    container_name: whimlab-postgres
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres123}
      POSTGRES_DB: ${POSTGRES_DB:-whimlabai}
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - whimlab-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: whimlab-redis
    restart: unless-stopped
    command: redis-server --requirepass ${REDIS_PASSWORD:-redis123}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - whimlab-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # RabbitMQ 消息队列
  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: whimlab-rabbitmq
    restart: unless-stopped
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_USER:-whimlab}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_PASS:-rabbitmq123}
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    ports:
      - "5672:5672"   # AMQP port
      - "15672:15672" # Management UI
    networks:
      - whimlab-network
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # MinIO 对象存储
  minio:
    image: minio/minio:latest
    container_name: whimlab-minio
    restart: unless-stopped
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER:-admin}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD:-admin123}
    volumes:
      - minio_data:/data
    ports:
      - "9000:9000"   # API
      - "9001:9001"   # Console
    networks:
      - whimlab-network
    healthcheck:
      test: ["CMD", "mc", "ready", "local"]
      interval: 10s
      timeout: 5s
      retries: 5

  # WhimLab AI 主应用
  whimlab-api:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - BUILDKIT_INLINE_CACHE=1
      cache_from:
        - whimlab-api:latest
        - whimlab-api:cache
    image: whimlab-api:latest
    container_name: whimlab-api
    restart: unless-stopped
    # 资源限制
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 512M
    environment:
      # 数据库连接
      ConnectionStrings__DefaultConnection: "Host=postgres;Port=5432;Database=${POSTGRES_DB:-whimlabai};Username=${POSTGRES_USER:-postgres};Password=${POSTGRES_PASSWORD:-postgres123}"
      # Redis连接
      ConnectionStrings__Redis: "redis:6379,password=${REDIS_PASSWORD:-redis123}"
      # RabbitMQ连接
      ConnectionStrings__RabbitMQ: "amqp://${RABBITMQ_USER:-whimlab}:${RABBITMQ_PASS:-rabbitmq123}@rabbitmq:5672"
      # MinIO连接
      Storage__MinIO__Endpoint: "minio:9000"
      Storage__MinIO__AccessKey: ${MINIO_ROOT_USER:-admin}
      Storage__MinIO__SecretKey: ${MINIO_ROOT_PASSWORD:-admin123}
      Storage__MinIO__UseSSL: "false"
      # JWT设置
      Jwt__Secret: ${JWT_SECRET:-ThisIsAVeryLongSecretKeyForJWT123456789012345678901234567890}
      Jwt__Issuer: "WhimLabAI"
      Jwt__Audience: "WhimLabAIUsers"
      # AI服务配置
      AI__Providers__OpenAI__ApiKey: ${OPENAI_API_KEY:-your-openai-api-key}
      AI__Providers__Azure__ApiKey: ${AZURE_API_KEY:-your-azure-api-key}
      AI__Providers__Azure__Endpoint: ${AZURE_ENDPOINT:-https://your-resource.openai.azure.com/}
      AI__Providers__Dify__BaseUrl: "http://dify:44080/v1"
      AI__Providers__Dify__ApiKey: ${DIFY_API_KEY:-your-dify-api-key}
      # 支付配置
      Payment__Alipay__AppId: ${ALIPAY_APP_ID:-your-alipay-appid}
      Payment__Alipay__PrivateKey: ${ALIPAY_PRIVATE_KEY:-your-alipay-private-key}
      Payment__Alipay__PublicKey: ${ALIPAY_PUBLIC_KEY:-your-alipay-public-key}
      Payment__WechatPay__AppId: ${WECHAT_APP_ID:-your-wechat-appid}
      Payment__WechatPay__MchId: ${WECHAT_MCH_ID:-your-wechat-mchid}
      Payment__WechatPay__ApiKey: ${WECHAT_API_KEY:-your-wechat-apikey}
      # 邮件服务
      Email__Smtp__Host: ${SMTP_HOST:-smtp.whimlab.com}
      Email__Smtp__Port: ${SMTP_PORT:-465}
      Email__Smtp__UseSsl: "true"
      Email__Smtp__Username: ${SMTP_USERNAME:-<EMAIL>}
      Email__Smtp__Password: ${SMTP_PASSWORD:-_,EsTATE,94}
      Email__From__Name: "WhimLab AI"
      Email__From__Address: ${SMTP_USERNAME:-<EMAIL>}
      # 应用设置
      ASPNETCORE_ENVIRONMENT: ${ASPNETCORE_ENVIRONMENT:-Production}
      ASPNETCORE_URLS: "http://+:5000;https://+:5001"
      # 性能优化
      DOTNET_TieredPGO: 1
      DOTNET_TC_QuickJitForLoops: 1
      DOTNET_ReadyToRun: 1
    volumes:
      - ./logs:/app/logs:rw
      - ./uploads:/app/uploads:rw
      - ./temp:/app/temp:rw
    ports:
      - "5000:5000"
      - "5001:5001"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      minio:
        condition: service_healthy
    networks:
      - whimlab-network
    # 健康检查配置
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 10s
    # 日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "service=whimlab-api"
    # 安全配置
    security_opt:
      - no-new-privileges:true
    read_only: false
    tmpfs:
      - /tmp

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: whimlab-nginx
    restart: unless-stopped
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./nginx/logs:/var/log/nginx
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - whimlab-api
    networks:
      - whimlab-network

  # Prometheus 监控
  prometheus:
    image: prom/prometheus:latest
    container_name: whimlab-prometheus
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./prometheus/rules:/etc/prometheus/rules:ro
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - whimlab-network

  # PostgreSQL Exporter
  postgres-exporter:
    image: prometheuscommunity/postgres-exporter:latest
    container_name: whimlab-postgres-exporter
    restart: unless-stopped
    environment:
      DATA_SOURCE_NAME: "postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-postgres123}@postgres:5432/${POSTGRES_DB:-whimlabai}?sslmode=disable"
    ports:
      - "9187:9187"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - whimlab-network

  # Redis Exporter
  redis-exporter:
    image: oliver006/redis_exporter:latest
    container_name: whimlab-redis-exporter
    restart: unless-stopped
    environment:
      REDIS_ADDR: "redis://redis:6379"
      REDIS_PASSWORD: ${REDIS_PASSWORD:-redis123}
    ports:
      - "9121:9121"
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - whimlab-network

  # Node Exporter for system metrics
  node-exporter:
    image: prom/node-exporter:latest
    container_name: whimlab-node-exporter
    restart: unless-stopped
    command:
      - '--path.rootfs=/host'
      - '--path.procfs=/host/proc'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    ports:
      - "9100:9100"
    networks:
      - whimlab-network

  # Alertmanager 告警管理
  alertmanager:
    image: prom/alertmanager:latest
    container_name: whimlab-alertmanager
    restart: unless-stopped
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
      - '--web.external-url=http://localhost:9093'
    volumes:
      - ./prometheus/alertmanager.yml:/etc/alertmanager/alertmanager.yml:ro
      - ./prometheus/templates:/etc/alertmanager/templates:ro
      - alertmanager_data:/alertmanager
    ports:
      - "9093:9093"
    networks:
      - whimlab-network

  # Grafana 可视化
  grafana:
    image: grafana/grafana:latest
    container_name: whimlab-grafana
    restart: unless-stopped
    environment:
      GF_SECURITY_ADMIN_USER: ${GRAFANA_USER:-admin}
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD:-admin123}
      GF_INSTALL_PLUGINS: grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning:ro
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
    ports:
      - "3000:3000"
    depends_on:
      - prometheus
    networks:
      - whimlab-network

  # Elasticsearch 日志存储
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: whimlab-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - whimlab-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Kibana 日志可视化
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: whimlab-kibana
    restart: unless-stopped
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      elasticsearch:
        condition: service_healthy
    networks:
      - whimlab-network

volumes:
  postgres_data:
  redis_data:
  rabbitmq_data:
  minio_data:
  prometheus_data:
  grafana_data:
  elasticsearch_data:
  alertmanager_data:

networks:
  whimlab-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16