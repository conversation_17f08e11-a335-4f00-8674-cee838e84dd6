<Project>
  <!-- 全局项目设置 -->
  <PropertyGroup>
    <!-- Only set TargetFramework for non-MAUI projects -->
    <TargetFramework Condition="'$(UseMaui)' != 'true'">net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <AnalysisLevel>latest-recommended</AnalysisLevel>
    <EnforceCodeStyleInBuild>true</EnforceCodeStyleInBuild>
    
    <!-- 公司和产品信息 -->
    <Company>WhimLab</Company>
    <Product>WhimLabAI Enterprise AI Agent Platform</Product>
    <Copyright>Copyright © WhimLab 2025</Copyright>
    
    <!-- 版本信息 -->
    <VersionPrefix>1.0.0</VersionPrefix>
    <VersionSuffix>alpha</VersionSuffix>
    <AssemblyVersion>1.0.0.0</AssemblyVersion>
    <FileVersion>1.0.0.0</FileVersion>
  </PropertyGroup>

  <!-- 统一的包版本管理 -->
  <PropertyGroup>
    <!-- ASP.NET Core -->
    <AspNetCoreVersion>9.0.0</AspNetCoreVersion>
    
    <!-- Microsoft Extensions -->
    <MicrosoftExtensionsVersion>9.0.0</MicrosoftExtensionsVersion>
    
    <!-- Entity Framework Core -->
    <EfCoreVersion>9.0.0</EfCoreVersion>
    
    <!-- 身份认证相关 -->
    <JwtBearerVersion>9.0.0</JwtBearerVersion>
    <IdentityVersion>9.0.0</IdentityVersion>
    
    <!-- 日志和监控 -->
    <SerilogVersion>4.2.0</SerilogVersion>
    <SerilogAspNetCoreVersion>8.0.5</SerilogAspNetCoreVersion>
    <OpenTelemetryVersion>1.10.0</OpenTelemetryVersion>
    
    <!-- 缓存和消息队列 -->
    <StackExchangeRedisVersion>2.8.16</StackExchangeRedisVersion>
    <RabbitMQVersion>7.0.0</RabbitMQVersion>
    
    <!-- API文档 -->
    <SwashbuckleVersion>7.2.0</SwashbuckleVersion>
    
    <!-- 验证和映射 -->
    <FluentValidationVersion>11.11.0</FluentValidationVersion>
    <AutoMapperVersion>13.0.1</AutoMapperVersion>
    
    <!-- AI相关 -->
    <SemanticKernelVersion>1.29.0</SemanticKernelVersion>
    
    <!-- 支付相关 -->
    <AlipaySDKVersion>2.5.1</AlipaySDKVersion>
    
    <!-- 测试框架 -->
    <xUnitVersion>2.9.2</xUnitVersion>
    <MoqVersion>4.20.72</MoqVersion>
    <FluentAssertionsVersion>7.0.0</FluentAssertionsVersion>
    
    <!-- Blazor相关 -->
    <MudBlazorVersion>8.0.0</MudBlazorVersion>
    <MasaBlazorVersion>1.8.0</MasaBlazorVersion>
    
    <!-- 工具类 -->
    <NewtonsoftJsonVersion>13.0.3</NewtonsoftJsonVersion>
    <PollyVersion>8.5.0</PollyVersion>
    <MinioVersion>6.0.4</MinioVersion>
    <OtpDotNetVersion>1.4.0</OtpDotNetVersion>
    
    <!-- MediatR -->
    <MediatRVersion>12.5.0</MediatRVersion>
    <MassTransitVersion>8.2.5</MassTransitVersion>
    
    <!-- 向量数据库 -->
    <PgvectorVersion>0.2.0</PgvectorVersion>
  </PropertyGroup>

  <!-- 代码分析包 -->
  <ItemGroup>
    <PackageReference Include="Microsoft.CodeAnalysis.NetAnalyzers" Version="9.0.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>
</Project>