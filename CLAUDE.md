# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

WhimLabAI is an enterprise-level AI agent platform built with .NET 9.0. It follows a monolithic deployment architecture while maintaining a clean multi-project layered structure for development.

### Key Technologies

- **Backend**: .NET 9.0, ASP.NET Core Web API
- **Admin Frontend**: Blazor Auto rendering mode with MudBlazor UI components
- **Customer Frontend**: .NET MAUI Blazor Hybrid (targeting Android, iOS, macOS, Windows) with MASA Blazor UI
- **Database**: PostgreSQL 16
- **Cache**: Redis 7
- **Message Queue**: RabbitMQ 3
- **Object Storage**: MinIO
- **AI Integration**: Semantic Kernel for custom AI agents, Dify platform integration
- **Monitoring**: Prometheus + Grafana
- **Logging**: Elasticsearch + Kibana
- **Testing**: xUnit 2.9.2, Moq, FluentAssertions

## Development Environment Setup

### Quick Setup
```bash
# Use the automated setup script
cd scripts
./setup-dev.sh
```

### Docker Services
```bash
# Start all required services
docker-compose up -d

# Services will be available at:
# PostgreSQL: localhost:5432
# Redis: localhost:6379
# RabbitMQ: localhost:15672 (Management UI)
# MinIO: localhost:9001 (Console)
# Kibana: localhost:5601
# Grafana: localhost:3000
```

### Default Development Passwords

For development consistency, all environments use unified default passwords:

- **PostgreSQL**: `postgres123`
- **Redis**: `redis123`
- **RabbitMQ**: `rabbitmq123`
- **MinIO**: `admin123`
- **Grafana**: `admin123`
- **JWT Secret**: `ThisIsAVeryLongSecretKeyForJWT123456789012345678901234567890`

⚠️ **IMPORTANT**: These passwords are for DEVELOPMENT ONLY. See `deploy/UNIFIED_PASSWORDS.md` for complete security guidelines.

## Common Development Commands

### Building the Solution
```bash
# Build entire solution (excluding MAUI)
dotnet build WhimLabAI.sln

# Build MAUI project separately (specify platform)
cd FrontEnd/Customer/WhimLabAI.Client.Customer/WhimLabAI.Client.Customer
dotnet build -f net9.0-maccatalyst    # macOS
dotnet build -f net9.0-android        # Android
dotnet build -f net9.0-ios           # iOS
dotnet build -f net9.0-windows10.0.19041.0  # Windows
```

### Running Components

**1. WebAPI (Backend)**
```bash
cd FrontEnd/WebApi/WhimLabAI.WebApi
dotnet run --launch-profile http
# API: http://localhost:5000 (https://localhost:5001)
# Swagger: http://localhost:5000/swagger (https://localhost:5001/swagger)
```

**2. Admin Portal**
```bash
cd FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin
dotnet run --launch-profile https
# HTTPS: https://localhost:7217
# HTTP: http://localhost:7216
```

**3. Customer Web Portal**
```bash
cd FrontEnd/Customer/WhimLabAI.Client.Customer/WhimLabAI.Client.Customer.Web
dotnet run --launch-profile https
# HTTPS: https://localhost:7041
# HTTP: http://localhost:7040
```

### Database Migrations
```bash
# Navigate to Infrastructure project
cd BackEnd/WhimLabAI.Infrastructure

# Add a new migration
dotnet ef migrations add [MigrationName] --startup-project ../../FrontEnd/WebApi/WhimLabAI.WebApi/WhimLabAI.WebApi.csproj --context WhimLabAIDbContext

# Update database
dotnet ef database update --startup-project ../../FrontEnd/WebApi/WhimLabAI.WebApi/WhimLabAI.WebApi.csproj --context WhimLabAIDbContext

# Generate SQL script
dotnet ef migrations script --startup-project ../../FrontEnd/WebApi/WhimLabAI.WebApi/WhimLabAI.WebApi.csproj --context WhimLabAIDbContext --output migrations.sql
```

### Testing
```bash
# Run all tests
dotnet test

# Run specific test project
dotnet test Tests/WhimLabAI.IntegrationTests/WhimLabAI.IntegrationTests.csproj

# Run with coverage
dotnet test --collect:"XPlat Code Coverage"
```

### Code Quality
The project uses .NET analyzers configured in `.editorconfig`. Key conventions:
- Interfaces prefixed with "I"
- Async methods suffixed with "Async"
- Private fields prefixed with "_"
- Code analysis enforced during build

## Architecture

⚠️ **重要：在实施任何功能前，必须先阅读以下文档：**
1. **[C企业级AI智能体平台完整业务需求文档.md](./dev-docs/企业级AI智能体平台完整业务需求文档.md)** - 企业级AI智能体平台完整业务需求文档（必读！）
1. **[CLAUDE_ARCHITECTURE_RULES_COMPLETE.md](./CLAUDE_ARCHITECTURE_RULES_COMPLETE.md)** - 完整的架构规则和编码规范（必读！）
2. **[CLAUDE_ARCHITECTURE_RULES.md](./CLAUDE_ARCHITECTURE_RULES.md)** - 基本架构规则快速参考

### 🔍 架构合规性检查

```bash
# 运行基础架构检查（快速）
./scripts/check-architecture.sh

# 运行完整架构检查（详细，推荐）
./scripts/check-architecture-complete.sh
```

The solution follows Clean Architecture principles with clear separation of concerns:

### Layer Dependencies
```
WebApi → Application, Abstractions
Application → Domain, Abstractions  
Domain → Abstractions (domain interfaces only)
Infrastructure → Domain, Abstractions
All layers → Shared
```

### 🚨 严格的实施规则

#### 必须遵守的目录结构
```
Infrastructure/
├── Data/
│   ├── Repositories/         # 所有Repository实现必须在这里
│   ├── Configurations/       # EF Core配置
│   └── Migrations/          # 数据库迁移
├── Auditing/                # 审计相关服务（不是Repository）
├── Security/                # 安全相关服务
├── PaymentGateways/         # 支付网关实现
└── ExternalServices/        # 外部服务集成

Application/
├── Services/
│   ├── Audit/              # 审计应用服务
│   ├── CustomerAuth/       # 客户认证服务
│   ├── Agent/              # AI代理服务
│   └── [Module]/           # 其他模块服务
└── DTOs/                   # 如果需要应用层特定DTO
```

#### 实施前检查清单
- [ ] 必须ultrathink
- [ ] 我已阅读企业级AI智能体平台完整业务需求文档.md
- [ ] 我已阅读CLAUDE_ARCHITECTURE_RULES.md
- [ ] 我已确认文件位置正确
- [ ] 我已验证依赖方向正确
- [ ] 我已检查是否有类似实现可参考
- [ ] 我已确认没有接口冗余

### Project Structure

```
BackEnd/
├── WhimLabAI.Abstractions     # Interface definitions
│   ├── Application/          # 应用层接口
│   └── Infrastructure/       # 基础设施层接口
├── WhimLabAI.Domain          # Entities, Value Objects, Domain Services
│   ├── Entities/            # 领域实体
│   ├── ValueObjects/        # 值对象
│   ├── Services/            # 领域服务
│   ├── Events/              # 领域事件
│   ├── Specifications/      # 规格模式
│   └── Repositories/        # Repository接口
├── WhimLabAI.Application     # Service implementations, DTOs
│   └── Services/            # 按模块组织的应用服务
├── WhimLabAI.Infrastructure  # EF Core, External integrations
│   ├── Data/
│   │   ├── Repositories/    # 所有Repository实现
│   │   ├── Configurations/  # EF配置
│   │   └── Migrations/      # 迁移文件
│   └── [TechnicalModules]/  # 技术模块实现
└── WhimLabAI.Shared          # Constants, Utilities, Common DTOs

FrontEnd/
├── WebApi/                   # ASP.NET Core Web API
├── Admin/                    # Blazor Admin Portal
└── Customer/                 # MAUI + Web Customer Apps
```

## Key Business Domains

### 1. Identity & Access Management (IAM)
- Customer authentication (JWT, OAuth, MFA)
- Admin RBAC with permissions
- WeChat, GitHub, Google, Microsoft OAuth providers

### 2. AI Agents
- Custom agents via Semantic Kernel
- Dify platform integration
- Agent versioning and publishing workflow
- Marketplace with ratings and reviews

### 3. Conversations
- Real-time chat with SignalR
- Streaming AI responses
- Rich media support
- Context management and history

### 4. Subscriptions & Payments
- Tiers: Free (5K tokens), Basic (50K/¥99), Pro (200K/¥199), Ultra (500K/¥299)
- Alipay and WeChat Pay integration
- Token tracking and usage analytics

### 5. System Operations
- Audit logging with middleware
- Performance monitoring (P95 < 200ms target)
- Multi-tenant support (planned)

## Development Patterns

### 🚨 核心开发规则（企业级生产项目）

1. **用户实体规则**
   - ❌ 绝对不允许使用通用的 User 实体
   - ✅ 只有 CustomerUser（客户用户）和 AdminUser（管理员用户）
   - ✅ 两者有明确的业务边界，不可混用
   - ✅ 所有涉及用户的业务逻辑必须明确区分是客户还是管理员

2. **代码实现标准**
   - ❌ 不允许 TODO、FIXME、待实现等注释
   - ❌ 不允许模拟数据、Mock数据、测试数据
   - ❌ 不允许"简化实现"、"暂时实现"等临时方案
   - ✅ 所有功能必须完整实现，业务闭环
   - ✅ 所有数据必须来自真实的数据库查询或计算
   - ✅ 所有业务逻辑必须符合实际企业运营需求

3. **数据分析要求**
   - ✅ 所有统计数据必须基于实际数据库记录计算
   - ✅ 所有趋势分析必须基于历史数据
   - ✅ 所有预测模型必须有明确的算法实现
   - ❌ 不允许硬编码的百分比或固定数值

### 🔴 实施规则验证

在开始任何开发工作前，必须：

1. **验证架构合规性**
   ```bash
   # 检查Repository位置
   find . -name "*Repository.cs" -not -path "*/Data/Repositories/*" -not -path "*/Domain/Repositories/*"
   # 如果有输出，说明Repository位置错误
   ```

2. **验证依赖方向**
   ```bash
   # 检查Infrastructure是否错误依赖Application
   grep -r "using WhimLabAI.Application" BackEnd/WhimLabAI.Infrastructure/ --include="*.cs"
   # 不应有任何输出
   ```

3. **运行时验证**
   ```bash
   # 不要只编译，必须运行
   dotnet run --project FrontEnd/WebApi/WhimLabAI.WebApi
   # 检查启动日志，确保无错误
   ```

### 🔥 任务执行和Git提交规则

#### 必须遵守的执行流程

1. **完成阶段任务后的验证步骤**
   - ✅ 编译项目，确保没有编译错误
   - ✅ 运行项目，验证功能正常工作
   - ✅ 等待运行稳定（至少运行1-2分钟）
   - ✅ 检查运行日志，确保没有错误信息
   - ✅ 只有在所有验证通过后才考虑提交Git

2. **具体执行命令**
   ```bash
   # 1. 编译项目
   dotnet build WhimLabAI.sln
   
   # 2. 运行WebAPI并等待稳定
   cd FrontEnd/WebApi/WhimLabAI.WebApi
   dotnet run --launch-profile https
   # 等待1-2分钟，观察日志输出
   
   # 3. 检查日志文件
   tail -f logs/whimlabai-*.txt
   # 确保没有ERROR级别的日志
   
   # 4. 如果一切正常，准备提交
   git add .
   git commit -m "feat: [功能描述]"
   ```

3. **资源管理规则**
   - 如果实施过程中不需要运行应用，立即杀掉进程或关闭所有运行的应用
   - 使用 `Ctrl+C` 优雅关闭 dotnet 进程
   - 必要时使用 `pkill -f dotnet` 强制终止所有 dotnet 进程
   - 释放资源以避免端口占用和内存泄漏

4. **验证清单**
   - [ ] 代码编译无错误
   - [ ] 应用启动正常
   - [ ] 功能测试通过
   - [ ] 日志无错误信息
   - [ ] 性能表现稳定
   - [ ] 资源占用正常

### Entity Framework Core
- Code-first approach
- Migrations in Infrastructure project
- Soft deletes on all entities
- Audit fields (CreatedAt, UpdatedAt, CreatedBy, UpdatedBy)

### API Design
- RESTful endpoints grouped by domain
- Consistent response format with ApiResponse<T>
- Global exception handling
- Request validation with FluentValidation

### Security
- JWT authentication with refresh tokens
- API key authentication for external clients
- Anti-brute force protection
- Input sanitization and XSS prevention
- SQL injection protection via parameterized queries

### Frontend Patterns
- Admin: Server-side rendering with interactive islands
- Customer: Shared Blazor components across MAUI and Web
- State management with cascading parameters
- Real-time updates via SignalR

## Important Notes

### 🚨 架构违规预防

1. **最常见的错误**
   - ❌ Repository放在错误的目录
   - ❌ 在Infrastructure层做DTO映射
   - ❌ Infrastructure层依赖Application层
   - ❌ 在Repository中返回DTO而不是Entity
   - ❌ 认为不同层的接口是冗余的

2. **正确的实践**
   - ✅ 所有Repository在Infrastructure/Data/Repositories
   - ✅ DTO映射只在Application层
   - ✅ Infrastructure只依赖Domain和Abstractions
   - ✅ Repository只返回Domain实体
   - ✅ 不同层的接口服务于不同目的

3. **审计日志的正确实现**
   - IAuditLogger：基础设施层记录接口
   - IAuditLogApplicationService：应用层查询接口
   - 两者职责不同，不是冗余！

1. **MAUI Development**: Build MAUI projects separately with platform-specific targets
2. **Database**: Ensure PostgreSQL is running before starting the API
3. **Redis**: Required for caching and session management
4. **Environment Variables**: Set in appsettings.Development.json or docker-compose
5. **SSL Certificates**: Generated automatically by setup script for local development

## Enterprise Development Standards

### Code Style and Naming Conventions

#### C# Coding Standards
```csharp
// File structure order
// 1. Using statements
// 2. Namespace
// 3. Class/Interface
// 4. Private fields
// 5. Public properties
// 6. Constructors
// 7. Public methods
// 8. Private methods

namespace WhimLabAI.Domain.Entities;

public class CustomerUser : AggregateRoot
{
    private readonly List<Subscription> _subscriptions = new();
    
    public string Email { get; private set; }
    public string Username { get; private set; }
    
    public CustomerUser(string email, string username)
    {
        Email = Guard.Against.NullOrEmpty(email, nameof(email));
        Username = Guard.Against.NullOrEmpty(username, nameof(username));
    }
    
    public void AddSubscription(Subscription subscription)
    {
        _subscriptions.Add(subscription);
        RaiseDomainEvent(new SubscriptionAddedEvent(Id, subscription.Id));
    }
}
```

#### API Endpoint Naming
```csharp
// RESTful conventions
[ApiController]
[Route("api/v1/[controller]")]
public class AgentsController : ControllerBase
{
    [HttpGet]              // GET /api/v1/agents
    [HttpGet("{id}")]      // GET /api/v1/agents/{id}
    [HttpPost]             // POST /api/v1/agents
    [HttpPut("{id}")]      // PUT /api/v1/agents/{id}
    [HttpDelete("{id}")]   // DELETE /api/v1/agents/{id}
    
    // Custom actions
    [HttpPost("{id}/publish")]   // POST /api/v1/agents/{id}/publish
    [HttpGet("{id}/versions")]    // GET /api/v1/agents/{id}/versions
}
```

#### Database Naming
```sql
-- Tables: PascalCase, plural
CREATE TABLE CustomerUsers (...)
CREATE TABLE Agents (...)

-- Columns: PascalCase
Id, Email, CreatedAt, UpdatedAt

-- Indexes: IX_TableName_ColumnName
CREATE INDEX IX_CustomerUsers_Email ON CustomerUsers(Email);

-- Foreign Keys: FK_ChildTable_ParentTable
ALTER TABLE Subscriptions ADD CONSTRAINT FK_Subscriptions_CustomerUsers
```

### Git Workflow and Commit Standards

#### Branch Strategy
```bash
main            # Production-ready code
├── develop     # Integration branch
├── feature/*   # New features (feature/add-payment-gateway)
├── bugfix/*    # Bug fixes (bugfix/fix-login-error)
├── hotfix/*    # Emergency fixes (hotfix/security-patch)
└── release/*   # Release preparation (release/v1.2.0)
```

#### Commit Message Format
```bash
# Format: <type>(<scope>): <subject>
feat(auth): add two-factor authentication
fix(payment): resolve Alipay callback timeout
docs(api): update agent creation endpoint
refactor(domain): extract subscription logic to service
test(integration): add payment flow tests
perf(query): optimize agent search with indexes
chore(deps): update Semantic Kernel to 1.29.0

# Breaking changes
feat(api)!: change authentication header format
```

### Documentation Standards

#### XML Documentation
```csharp
/// <summary>
/// Creates a new AI agent with the specified configuration.
/// </summary>
/// <param name="request">The agent creation request containing all necessary parameters.</param>
/// <returns>The created agent with assigned ID.</returns>
/// <exception cref="ValidationException">Thrown when request validation fails.</exception>
/// <exception cref="DuplicateAgentException">Thrown when an agent with the same name already exists.</exception>
public async Task<AgentDto> CreateAgentAsync(CreateAgentRequest request)
```

#### API Documentation
```csharp
/// <response code="200">Agent created successfully</response>
/// <response code="400">Invalid request parameters</response>
/// <response code="401">Unauthorized access</response>
/// <response code="409">Agent name already exists</response>
[HttpPost]
[ProducesResponseType(typeof(ApiResponse<AgentDto>), 200)]
[ProducesResponseType(typeof(ApiResponse), 400)]
```

## Quality Assurance Framework

### Testing Strategy

#### Unit Tests
```csharp
// Naming: MethodName_StateUnderTest_ExpectedBehavior
[Fact]
public async Task CreateAgent_WithValidRequest_ReturnsCreatedAgent()
{
    // Arrange
    var request = new CreateAgentRequest { Name = "Test Agent" };
    var mockRepo = new Mock<IAgentRepository>();
    var service = new AgentService(mockRepo.Object);
    
    // Act
    var result = await service.CreateAgentAsync(request);
    
    // Assert
    result.Should().NotBeNull();
    result.Name.Should().Be("Test Agent");
    mockRepo.Verify(x => x.AddAsync(It.IsAny<Agent>()), Times.Once);
}
```

#### Integration Tests
```csharp
public class AgentApiTests : IClassFixture<WebApplicationFactory<Program>>
{
    [Fact]
    public async Task POST_CreateAgent_ReturnsSuccessAndCorrectContentType()
    {
        // Arrange
        var client = _factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureTestServices(services =>
            {
                services.AddDbContext<WhimLabAIDbContext>(options =>
                    options.UseInMemoryDatabase("TestDb"));
            });
        }).CreateClient();
        
        // Act & Assert
        var response = await client.PostAsJsonAsync("/api/v1/agents", new { Name = "Test" });
        response.EnsureSuccessStatusCode();
        response.Content.Headers.ContentType.ToString().Should().Be("application/json");
    }
}
```

#### Test Coverage Requirements
- Unit Tests: ≥ 80% code coverage
- Integration Tests: All API endpoints covered
- Domain Logic: 100% coverage for critical business rules
- Exception Paths: All error scenarios tested

### Performance Benchmarks

#### API Response Time Targets
```yaml
Authentication APIs:
  - Login: < 100ms (P95)
  - Token Refresh: < 50ms (P95)
  
Agent APIs:
  - List Agents: < 200ms (P95)
  - Get Agent Details: < 100ms (P95)
  - Create Agent: < 300ms (P95)
  
Conversation APIs:
  - Start Conversation: < 150ms (P95)
  - Send Message: < 100ms (P95)
  - Stream First Token: < 1000ms (P95)
```

#### Database Query Performance
```sql
-- All queries should execute in < 100ms
-- Use EXPLAIN ANALYZE to verify

-- Example: Optimized agent search
EXPLAIN ANALYZE
SELECT a.*, COUNT(c.Id) as ConversationCount
FROM Agents a
LEFT JOIN Conversations c ON a.Id = c.AgentId
WHERE a.Status = 'Published' 
  AND a.Name ILIKE '%search%'
GROUP BY a.Id
ORDER BY a.CreatedAt DESC
LIMIT 20;
```

### Security Checklist

#### Authentication & Authorization
- [ ] JWT tokens expire after 15 minutes
- [ ] Refresh tokens expire after 7 days
- [ ] All APIs require authentication except public endpoints
- [ ] Role-based permissions enforced at controller level
- [ ] Admin operations require MFA

#### Data Protection
- [ ] All passwords hashed with BCrypt (work factor 12)
- [ ] API keys encrypted with AES-256
- [ ] PII data encrypted at rest
- [ ] Database connections use SSL
- [ ] Backup data encrypted

#### Input Validation
- [ ] All inputs validated with FluentValidation
- [ ] SQL injection protection via EF Core
- [ ] XSS prevention in Blazor components
- [ ] File upload size limits enforced
- [ ] Rate limiting on all endpoints

## Team Collaboration

### Code Review Process

#### Review Checklist
- [ ] Code follows naming conventions
- [ ] Unit tests included and passing
- [ ] No hardcoded values or secrets
- [ ] Error handling implemented
- [ ] Logging added for key operations
- [ ] Performance considerations addressed
- [ ] Security best practices followed
- [ ] Documentation updated

#### Review Workflow
```mermaid
graph LR
    A[Create PR] --> B[Automated Checks]
    B --> C{Pass?}
    C -->|No| D[Fix Issues]
    D --> B
    C -->|Yes| E[Code Review]
    E --> F{Approved?}
    F -->|No| G[Address Feedback]
    G --> E
    F -->|Yes| H[Merge to Develop]
```

### Release Process

#### Version Numbering
```
MAJOR.MINOR.PATCH
1.2.3
│ │ └── Bugfixes, patches
│ └──── New features, backwards compatible
└────── Breaking changes
```

#### Release Checklist
1. **Pre-release**
   - [ ] All tests passing
   - [ ] Security scan completed
   - [ ] Performance benchmarks met
   - [ ] Database migrations tested
   - [ ] Release notes prepared

2. **Release**
   - [ ] Tag release in Git
   - [ ] Deploy to staging
   - [ ] Smoke tests passed
   - [ ] Deploy to production
   - [ ] Monitor error rates

3. **Post-release**
   - [ ] Verify all services healthy
   - [ ] Check performance metrics
   - [ ] Monitor user feedback
   - [ ] Update documentation

## Operations Guide

### Monitoring & Alerting

#### Key Metrics
```yaml
Application Metrics:
  - Request Rate: requests/second by endpoint
  - Error Rate: 4xx and 5xx responses
  - Response Time: P50, P95, P99 latencies
  - Active Users: concurrent sessions
  
Business Metrics:
  - Registration Rate: new users/hour
  - Conversion Rate: free to paid %
  - Token Usage: consumption by tier
  - AI Response Quality: user ratings
  
Infrastructure Metrics:
  - CPU Usage: < 70% sustained
  - Memory Usage: < 80% sustained
  - Disk I/O: < 80% utilization
  - Network: bandwidth and latency
```

#### Alert Rules
```yaml
Critical (Page immediately):
  - API error rate > 5% for 5 minutes
  - Database connection failures
  - Payment gateway unreachable
  - Disk space < 10%
  
Warning (Notify team):
  - API P95 latency > 500ms
  - Queue depth > 1000 messages
  - Memory usage > 70%
  - Failed login attempts > 100/minute
```

### Incident Response

#### Severity Levels
- **P1 (Critical)**: Complete outage, data loss risk
- **P2 (Major)**: Significant feature unavailable
- **P3 (Minor)**: Degraded performance
- **P4 (Low)**: Cosmetic issues

#### Response Process
```mermaid
graph TD
    A[Incident Detected] --> B{Severity?}
    B -->|P1| C[Page On-Call]
    B -->|P2| D[Notify Team Lead]
    B -->|P3/P4| E[Create Ticket]
    C --> F[Incident Commander]
    F --> G[Assess Impact]
    G --> H[Implement Fix]
    H --> I[Verify Resolution]
    I --> J[Post-Mortem]
```

### Disaster Recovery

#### Backup Strategy
```yaml
Database:
  - Full backup: Daily at 2 AM
  - Incremental: Every 4 hours
  - Retention: 30 days
  - Offsite: S3 cross-region

Files:
  - User uploads: Real-time to S3
  - System configs: Daily to Git
  - Logs: 90 days retention

Recovery Targets:
  - RTO (Recovery Time): < 4 hours
  - RPO (Recovery Point): < 1 hour
```

#### Failover Procedures
1. **Database Failure**
   ```bash
   # Switch to replica
   ./scripts/failover-db.sh --target=replica1
   # Verify connections
   ./scripts/verify-db-connections.sh
   ```

2. **Application Server Failure**
   ```bash
   # Remove failed instance from load balancer
   # Scale up healthy instances
   kubectl scale deployment api --replicas=5
   ```

## Performance Optimization

### Database Optimization

#### Indexing Strategy
```sql
-- User lookup indexes
CREATE INDEX IX_CustomerUsers_Email ON CustomerUsers(Email);
CREATE INDEX IX_CustomerUsers_Username ON CustomerUsers(Username);

-- Agent search indexes
CREATE INDEX IX_Agents_Status_Name ON Agents(Status, Name);
CREATE INDEX IX_Agents_CreatedAt_DESC ON Agents(CreatedAt DESC);

-- Conversation performance
CREATE INDEX IX_Conversations_UserId_AgentId ON Conversations(UserId, AgentId);
CREATE INDEX IX_Messages_ConversationId_CreatedAt ON Messages(ConversationId, CreatedAt);

-- Analyze index usage
SELECT schemaname, tablename, indexname, idx_scan
FROM pg_stat_user_indexes
ORDER BY idx_scan;
```

#### Query Optimization
```csharp
// Bad: N+1 query
var agents = await _context.Agents.ToListAsync();
foreach (var agent in agents)
{
    agent.Conversations = await _context.Conversations
        .Where(c => c.AgentId == agent.Id)
        .ToListAsync();
}

// Good: Eager loading
var agents = await _context.Agents
    .Include(a => a.Conversations)
    .AsSplitQuery()
    .ToListAsync();

// Better: Projection
var agentSummaries = await _context.Agents
    .Select(a => new AgentSummaryDto
    {
        Id = a.Id,
        Name = a.Name,
        ConversationCount = a.Conversations.Count()
    })
    .ToListAsync();
```

### Caching Strategy

#### Cache Layers
```csharp
// 1. Memory Cache (Hot data)
services.AddMemoryCache();

// 2. Redis Cache (Shared data)
services.AddStackExchangeRedisCache(options =>
{
    options.Configuration = "localhost:6379";
    options.InstanceName = "WhimLabAI";
});

// 3. Response Cache (HTTP)
[ResponseCache(Duration = 300, VaryByQueryKeys = new[] { "page", "size" })]
public async Task<IActionResult> GetAgents([FromQuery] int page = 1, [FromQuery] int size = 20)
```

#### Cache Keys
```csharp
public static class CacheKeys
{
    public static string UserProfile(Guid userId) => $"user:profile:{userId}";
    public static string AgentDetails(Guid agentId) => $"agent:details:{agentId}";
    public static string SubscriptionPlans() => "subscription:plans:all";
    public static string UserTokenQuota(Guid userId) => $"user:quota:{userId}";
}
```

### Async Processing

#### Background Jobs
```csharp
// Using Hangfire for background jobs
services.AddHangfire(config =>
{
    config.UsePostgreSqlStorage(connectionString);
});

// Enqueue jobs
BackgroundJob.Enqueue(() => ProcessSubscriptionRenewal(subscriptionId));
BackgroundJob.Schedule(() => SendEmailNotification(userId, message), TimeSpan.FromMinutes(5));

// Recurring jobs
RecurringJob.AddOrUpdate("token-quota-reset", 
    () => ResetMonthlyTokenQuotas(), 
    Cron.Monthly(1));
```

## Security Implementation

### OWASP Top 10 Protection

#### 1. Injection Prevention
```csharp
// Always use parameterized queries
var user = await _context.CustomerUsers
    .Where(u => u.Email == email) // EF Core parameterizes automatically
    .FirstOrDefaultAsync();

// For raw SQL, use parameters
var users = await _context.CustomerUsers
    .FromSqlRaw("SELECT * FROM CustomerUsers WHERE Email = {0}", email)
    .ToListAsync();
```

#### 2. Authentication Security
```csharp
// Strong password policy
services.Configure<IdentityOptions>(options =>
{
    options.Password.RequiredLength = 8;
    options.Password.RequireDigit = true;
    options.Password.RequireLowercase = true;
    options.Password.RequireUppercase = true;
    options.Password.RequireNonAlphanumeric = true;
    options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(15);
    options.Lockout.MaxFailedAccessAttempts = 5;
});
```

#### 3. Data Encryption
```csharp
// Encrypt sensitive data
public class EncryptionService : IEncryptionService
{
    private readonly byte[] _key;
    
    public string Encrypt(string plainText)
    {
        using var aes = Aes.Create();
        aes.Key = _key;
        var encryptor = aes.CreateEncryptor();
        var encrypted = encryptor.TransformFinalBlock(
            Encoding.UTF8.GetBytes(plainText), 0, plainText.Length);
        return Convert.ToBase64String(encrypted);
    }
}
```

### API Security

#### Rate Limiting
```csharp
// Configure rate limiting
services.AddRateLimiter(options =>
{
    options.AddPolicy("api", policy =>
    {
        policy.Limit = 100;
        policy.Window = TimeSpan.FromMinutes(1);
        policy.QueueLimit = 10;
    });
    
    options.AddPolicy("auth", policy =>
    {
        policy.Limit = 5;
        policy.Window = TimeSpan.FromMinutes(15);
    });
});

// Apply to endpoints
[HttpPost("login")]
[EnableRateLimiting("auth")]
public async Task<IActionResult> Login([FromBody] LoginRequest request)
```

#### API Key Management
```csharp
// API Key authentication
public class ApiKeyAuthenticationHandler : AuthenticationHandler<ApiKeyAuthenticationOptions>
{
    protected override async Task<AuthenticateResult> HandleAuthenticateAsync()
    {
        if (!Request.Headers.TryGetValue("X-API-Key", out var apiKey))
            return AuthenticateResult.Fail("API Key missing");
            
        var validKey = await _apiKeyService.ValidateKeyAsync(apiKey);
        if (!validKey.IsValid)
            return AuthenticateResult.Fail("Invalid API Key");
            
        var claims = new[] { new Claim("ClientId", validKey.ClientId) };
        var identity = new ClaimsIdentity(claims, Scheme.Name);
        var principal = new ClaimsPrincipal(identity);
        return AuthenticateResult.Success(new AuthenticationTicket(principal, Scheme.Name));
    }
}
```

### Audit Logging

#### Audit Implementation
```csharp
// Audit middleware
public class AuditMiddleware
{
    public async Task InvokeAsync(HttpContext context, IAuditService auditService)
    {
        var auditLog = new AuditLog
        {
            UserId = context.User?.Identity?.Name,
            Action = $"{context.Request.Method} {context.Request.Path}",
            IpAddress = context.Connection.RemoteIpAddress?.ToString(),
            Timestamp = DateTime.UtcNow
        };
        
        await _next(context);
        
        auditLog.ResponseCode = context.Response.StatusCode;
        await auditService.LogAsync(auditLog);
    }
}
```

## Business Development Guide

### Domain-Driven Design

#### Aggregate Design
```csharp
// Agent Aggregate
public class Agent : AggregateRoot
{
    private readonly List<AgentVersion> _versions = new();
    
    public string Name { get; private set; }
    public AgentStatus Status { get; private set; }
    public IReadOnlyCollection<AgentVersion> Versions => _versions.AsReadOnly();
    
    public void Publish(Guid versionId)
    {
        var version = _versions.FirstOrDefault(v => v.Id == versionId);
        if (version == null)
            throw new VersionNotFoundException(versionId);
            
        // Business rule: Only draft versions can be published
        if (version.Status != VersionStatus.Draft)
            throw new InvalidOperationException("Only draft versions can be published");
            
        // Unpublish current version
        var currentPublished = _versions.FirstOrDefault(v => v.Status == VersionStatus.Published);
        currentPublished?.Unpublish();
        
        // Publish new version
        version.Publish();
        Status = AgentStatus.Published;
        
        RaiseDomainEvent(new AgentPublishedEvent(Id, versionId));
    }
}
```

#### Domain Events
```csharp
// Event definition
public record AgentPublishedEvent(Guid AgentId, Guid VersionId) : IDomainEvent;

// Event handler
public class AgentPublishedEventHandler : INotificationHandler<AgentPublishedEvent>
{
    public async Task Handle(AgentPublishedEvent notification, CancellationToken cancellationToken)
    {
        // Update search index
        await _searchService.IndexAgentAsync(notification.AgentId);
        
        // Notify subscribers
        await _notificationService.NotifyAgentPublishedAsync(notification.AgentId);
        
        // Update cache
        await _cacheService.InvalidateAgentCacheAsync(notification.AgentId);
    }
}
```

### API Design Standards

#### RESTful API Structure
```csharp
// Resource-based URLs
GET    /api/v1/agents              // List agents
GET    /api/v1/agents/{id}         // Get agent details
POST   /api/v1/agents              // Create agent
PUT    /api/v1/agents/{id}         // Update agent
DELETE /api/v1/agents/{id}         // Delete agent

// Sub-resources
GET    /api/v1/agents/{id}/versions          // List versions
POST   /api/v1/agents/{id}/versions          // Create version
PUT    /api/v1/agents/{id}/versions/{vid}    // Update version

// Actions
POST   /api/v1/agents/{id}/publish           // Publish agent
POST   /api/v1/agents/{id}/archive           // Archive agent
```

#### Response Format
```csharp
public class ApiResponse<T>
{
    public bool Success { get; set; }
    public T Data { get; set; }
    public string Message { get; set; }
    public List<ApiError> Errors { get; set; }
    public ApiMetadata Metadata { get; set; }
}

// Pagination metadata
public class ApiMetadata
{
    public int Page { get; set; }
    public int PageSize { get; set; }
    public int TotalCount { get; set; }
    public int TotalPages { get; set; }
}
```

### Event-Driven Architecture

#### Message Bus Integration
```csharp
// Configure MassTransit with RabbitMQ
services.AddMassTransit(x =>
{
    x.AddConsumer<SubscriptionCreatedConsumer>();
    x.AddConsumer<PaymentCompletedConsumer>();
    
    x.UsingRabbitMq((context, cfg) =>
    {
        cfg.Host("localhost", "/", h =>
        {
            h.Username("guest");
            h.Password("guest");
        });
        
        cfg.ConfigureEndpoints(context);
    });
});

// Event consumer
public class SubscriptionCreatedConsumer : IConsumer<SubscriptionCreatedEvent>
{
    public async Task Consume(ConsumeContext<SubscriptionCreatedEvent> context)
    {
        // Process subscription
        await _quotaService.InitializeQuotaAsync(
            context.Message.UserId, 
            context.Message.TokenLimit);
            
        // Send welcome email
        await _emailService.SendWelcomeEmailAsync(context.Message.UserId);
    }
}
```

## Troubleshooting Guide

### Common Issues

#### 1. Database Connection Issues
```bash
# Check PostgreSQL status
docker ps | grep postgres

# Test connection
psql -h localhost -U postgres -d whimlabai

# Common fixes:
# - Ensure PostgreSQL is running
# - Check connection string in appsettings
# - Verify firewall rules
# - Check max_connections in postgresql.conf
```

#### 2. Redis Connection Issues
```bash
# Test Redis connection
redis-cli ping

# Monitor Redis
redis-cli monitor

# Common fixes:
# - Check Redis is running
# - Verify Redis password
# - Check maxmemory settings
# - Clear Redis cache if corrupted
```

#### 3. Performance Issues
```csharp
// Enable detailed logging
services.AddDbContext<WhimLabAIDbContext>(options =>
{
    options.UseNpgsql(connectionString)
           .LogTo(Console.WriteLine, LogLevel.Information)
           .EnableSensitiveDataLogging()
           .EnableDetailedErrors();
});

// Use MiniProfiler
services.AddMiniProfiler(options =>
{
    options.RouteBasePath = "/profiler";
}).AddEntityFramework();
```

### Debugging Techniques

#### 1. Request Tracing
```csharp
// Add correlation ID middleware
app.Use(async (context, next) =>
{
    var correlationId = context.Request.Headers["X-Correlation-ID"].FirstOrDefault() 
        ?? Guid.NewGuid().ToString();
    context.Items["CorrelationId"] = correlationId;
    context.Response.Headers.Add("X-Correlation-ID", correlationId);
    
    using (_logger.BeginScope(new { CorrelationId = correlationId }))
    {
        await next();
    }
});
```

#### 2. Memory Leak Detection
```csharp
// Monitor memory usage
services.AddHostedService<MemoryMonitorService>();

public class MemoryMonitorService : BackgroundService
{
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            var memory = GC.GetTotalMemory(false) / 1024 / 1024;
            _logger.LogInformation($"Memory usage: {memory} MB");
            
            if (memory > 500)
            {
                _logger.LogWarning("High memory usage detected");
                GC.Collect();
            }
            
            await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken);
        }
    }
}
```

### Log Analysis

#### Structured Logging Queries
```json
// Elasticsearch queries for common scenarios

// Find all errors in last hour
{
  "query": {
    "bool": {
      "must": [
        { "match": { "level": "Error" } },
        { "range": { "@timestamp": { "gte": "now-1h" } } }
      ]
    }
  }
}

// Find slow API calls
{
  "query": {
    "bool": {
      "must": [
        { "match": { "RequestPath": "/api/v1/*" } },
        { "range": { "ElapsedMilliseconds": { "gte": 1000 } } }
      ]
    }
  }
}

// Track specific user activity
{
  "query": {
    "bool": {
      "must": [
        { "match": { "UserId": "user-guid-here" } },
        { "range": { "@timestamp": { "gte": "now-24h" } } }
      ]
    }
  }
}
```

## Deployment Checklist

### Pre-Deployment
- [ ] All unit tests passing (coverage > 80%)
- [ ] All integration tests passing
- [ ] Security scan completed (no high/critical vulnerabilities)
- [ ] Performance benchmarks met
- [ ] Database migrations reviewed and tested
- [ ] Configuration values verified for target environment
- [ ] Rollback plan documented
- [ ] Team notified of deployment window

### Deployment Steps
1. **Backup Current State**
   ```bash
   ./scripts/backup.sh --env=production --components=all
   ```

2. **Deploy Database Migrations**
   ```bash
   dotnet ef database update --context WhimLabAIDbContext --env Production
   ```

3. **Deploy Application**
   ```bash
   # Blue-green deployment
   ./scripts/deploy.sh --strategy=blue-green --target=production
   ```

4. **Verify Deployment**
   ```bash
   # Run smoke tests
   ./scripts/smoke-tests.sh --env=production
   
   # Check health endpoints
   curl https://api.whimlab.com/health

   # Check swagger.json endpoints
   curl https://api.whimlab.com/swagger/v1/swagger.json
   ```

### Post-Deployment
- [ ] Monitor error rates for 30 minutes
- [ ] Check key business metrics
- [ ] Verify all integrations working
- [ ] Update status page
- [ ] Send deployment notification
- [ ] Document any issues encountered

## Appendix: Quick Reference

### Environment Variables
```bash
# Database
ConnectionStrings__DefaultConnection="Host=localhost;Database=whimlabai;Username=postgres;Password=***"

# Redis
ConnectionStrings__Redis="localhost:6379,password=***"

# JWT
Jwt__Secret="your-256-bit-secret"
Jwt__Issuer="https://api.whimlabai.com"
Jwt__Audience="https://whimlabai.com"

# AI Providers
AI__OpenAI__ApiKey="sk-***"
AI__Anthropic__ApiKey="sk-ant-***"

# Payment Gateways
Payment__Alipay__AppId="***"
Payment__WeChat__AppId="***"
```

### Useful Commands
```bash
# Development
dotnet watch run                    # Run with hot reload
dotnet ef migrations add XYZ        # Add migration
dotnet test --filter FullyQualifiedName~UnitTests  # Run only unit tests

# Docker
docker-compose logs -f api          # Follow API logs
docker-compose exec db psql         # Connect to database
docker-compose restart redis        # Restart Redis

# Production
kubectl logs -f deployment/api      # View API logs
kubectl exec -it deployment/api -- /bin/bash  # Shell into pod
kubectl rollout status deployment/api  # Check deployment status
```

### Performance Tuning Parameters
```yaml
# PostgreSQL (postgresql.conf)
max_connections: 200
shared_buffers: 256MB
effective_cache_size: 1GB
work_mem: 4MB
maintenance_work_mem: 64MB

# Redis (redis.conf)
maxmemory: 2gb
maxmemory-policy: allkeys-lru
tcp-keepalive: 60

# .NET (appsettings.json)
"Kestrel": {
  "Limits": {
    "MaxConcurrentConnections": 1000,
    "MaxConcurrentUpgradedConnections": 1000,
    "MaxRequestBodySize": 52428800
  }
}
```

## 架构审查和验证

### 每日检查清单
- [ ] 所有新增Repository都在Data/Repositories目录
- [ ] 没有Infrastructure层依赖Application层的代码
- [ ] 所有DTO映射都在Application层
- [ ] 运行时没有依赖注入错误
- [ ] 遵循了命名约定

### 代码审查重点
1. 文件位置是否正确
2. 依赖方向是否正确  
3. 接口职责是否清晰
4. 是否有重复实现
5. 是否符合Clean Architecture原则

**代码审查前必须运行：**
```bash
./scripts/check-architecture-complete.sh
# 只有检查通过才能合并代码
```

### 持续改进
- 每次发现架构问题，更新CLAUDE_ARCHITECTURE_RULES.md
- 创建自动化架构检查脚本
- 定期团队分享架构最佳实践

This comprehensive guide ensures that all aspects of enterprise development, from coding standards to production operations, are thoroughly documented and easily accessible for efficient development and maintenance of the WhimLabAI platform.

**记住：保持架构的一致性和清晰性是长期成功的关键！**
