# WhimLabAI Development Docker Compose Configuration
# 用于在项目根目录直接运行开发环境: docker compose -f docker-compose.dev.yml up -d

version: '3.8'

services:
  # PostgreSQL 数据库 with pgvector
  postgres:
    image: pgvector/pgvector:pg16
    container_name: whimlab-postgres-dev
    restart: unless-stopped
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ***********
      POSTGRES_DB: whimlabai_dev
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - whimlab-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: whimlab-redis-dev
    restart: unless-stopped
    command: redis-server --requirepass redis123
    volumes:
      - redis_dev_data:/data
    ports:
      - "6379:6379"
    networks:
      - whimlab-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # RabbitMQ 消息队列
  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: whimlab-rabbitmq-dev
    restart: unless-stopped
    environment:
      RABBITMQ_DEFAULT_USER: whimlab
      RABBITMQ_DEFAULT_PASS: rabbitmq123
    volumes:
      - rabbitmq_dev_data:/var/lib/rabbitmq
    ports:
      - "5672:5672"   # AMQP port
      - "15672:15672" # Management UI
    networks:
      - whimlab-network
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # MinIO 对象存储
  minio:
    image: minio/minio:latest
    container_name: whimlab-minio-dev
    restart: unless-stopped
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: admin
      MINIO_ROOT_PASSWORD: admin123
    volumes:
      - minio_dev_data:/data
    ports:
      - "9000:9000"   # API
      - "9001:9001"   # Console
    networks:
      - whimlab-network
    healthcheck:
      test: ["CMD", "mc", "ready", "local"]
      interval: 10s
      timeout: 5s
      retries: 5

  # WhimLab AI API 开发环境
  whimlab-api-dev:
    build:
      context: .
      dockerfile: deploy/docker/Dockerfile.dev
      args:
        - BUILDKIT_INLINE_CACHE=1
    image: whimlab-api:dev
    container_name: whimlab-api-dev
    restart: unless-stopped
    environment:
      # 开发环境配置
      ASPNETCORE_ENVIRONMENT: Development
      ASPNETCORE_URLS: "http://+:5000;https://+:5001"
      # 启用详细日志
      Logging__LogLevel__Default: Debug
      Logging__LogLevel__Microsoft: Information
      Logging__LogLevel__Microsoft.Hosting.Lifetime: Information
      # 数据库连接
      ConnectionStrings__DefaultConnection: "Host=postgres;Port=5432;Database=whimlabai_dev;Username=postgres;Password=***********"
      # Redis连接
      ConnectionStrings__Redis: "redis:6379,password=redis123"
      # 其他服务连接
      ConnectionStrings__RabbitMQ: "amqp://whimlab:rabbitmq123@rabbitmq:5672"
      Storage__MinIO__Endpoint: "minio:9000"
      Storage__MinIO__AccessKey: admin
      Storage__MinIO__SecretKey: admin123
      Storage__MinIO__UseSSL: "false"
      # 开发用JWT（较长过期时间）
      Jwt__Secret: "ThisIsAVeryLongSecretKeyForJWT123456789012345678901234567890"
      Jwt__Issuer: "WhimLabAI-Dev"
      Jwt__Audience: "WhimLabAI-Dev-Users"
      Jwt__ExpiryMinutes: 1440  # 24小时
      # 开发调试选项
      DOTNET_USE_POLLING_FILE_WATCHER: "true"
      DOTNET_WATCH_SUPPRESS_LAUNCH_BROWSER: "true"
      DOTNET_WATCH_SUPPRESS_OUTPUT: "false"
    volumes:
      # 挂载源代码目录，支持热重载
      - ./BackEnd:/src/BackEnd:rw
      - ./FrontEnd:/src/FrontEnd:rw
      - ./WhimLab.AI.sln:/src/WhimLab.AI.sln:ro
      - ./Directory.Build.props:/src/Directory.Build.props:ro
      - ./NuGet.Config:/src/NuGet.Config:ro
      - ./.editorconfig:/src/.editorconfig:ro
      # 日志和上传目录
      - ./logs:/app/logs:rw
      - ./uploads:/app/uploads:rw
      # NuGet缓存（提高重启速度）
      - nuget-cache:/root/.nuget/packages
    ports:
      - "5000:5000"    # HTTP
      - "5001:5001"    # HTTPS
      - "9229:9229"    # 远程调试端口
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      minio:
        condition: service_healthy
    networks:
      - whimlab-network
    stdin_open: true
    tty: true
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 60s

  # WhimLab AI Admin 开发环境
  whimlab-admin-dev:
    build:
      context: .
      dockerfile: deploy/docker/Dockerfile.admin.dev
      args:
        - BUILDKIT_INLINE_CACHE=1
    image: whimlab-admin:dev
    container_name: whimlab-admin-dev
    restart: unless-stopped
    environment:
      # 开发环境配置
      ASPNETCORE_ENVIRONMENT: Development
      ASPNETCORE_URLS: "http://+:7216;https://+:7217"
      # API配置
      Api__BaseUrl: "https://localhost:5001"
      # JWT设置（与API保持一致）
      Jwt__Secret: "ThisIsAVeryLongSecretKeyForJWT123456789012345678901234567890"
      Jwt__Issuer: "WhimLabAI-Dev"
      Jwt__Audience: "WhimLabAI-Dev-Users"
      # 启用详细日志
      Logging__LogLevel__Default: Debug
      Logging__LogLevel__Microsoft: Information
      # 开发调试选项
      DOTNET_USE_POLLING_FILE_WATCHER: "true"
      DOTNET_WATCH_SUPPRESS_LAUNCH_BROWSER: "true"
    volumes:
      # 挂载源代码目录，支持热重载
      - ./BackEnd:/src/BackEnd:rw
      - ./FrontEnd:/src/FrontEnd:rw
      - ./Directory.Build.props:/src/Directory.Build.props:ro
      - ./NuGet.Config:/src/NuGet.Config:ro
      - ./.editorconfig:/src/.editorconfig:ro
      # 日志目录
      - ./logs/admin:/app/logs:rw
      # NuGet缓存
      - nuget-cache:/root/.nuget/packages
    ports:
      - "7216:7216"    # HTTP
      - "7217:7217"    # HTTPS
    depends_on:
      whimlab-api-dev:
        condition: service_healthy
    networks:
      - whimlab-network
    stdin_open: true
    tty: true
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7216/health"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 60s

  # WhimLab AI Customer 开发环境
  whimlab-customer-dev:
    build:
      context: .
      dockerfile: deploy/docker/Dockerfile.customer.dev
      args:
        - BUILDKIT_INLINE_CACHE=1
    image: whimlab-customer:dev
    container_name: whimlab-customer-dev
    restart: unless-stopped
    environment:
      # 开发环境配置
      ASPNETCORE_ENVIRONMENT: Development
      ASPNETCORE_URLS: "http://+:7040;https://+:7041"
      # API配置
      Api__BaseUrl: "https://localhost:5001"
      # JWT设置（与API保持一致）
      Jwt__Secret: "ThisIsAVeryLongSecretKeyForJWT123456789012345678901234567890"
      Jwt__Issuer: "WhimLabAI-Dev"
      Jwt__Audience: "WhimLabAI-Dev-Users"
      # 启用详细日志
      Logging__LogLevel__Default: Debug
      Logging__LogLevel__Microsoft: Information
      # 开发调试选项
      DOTNET_USE_POLLING_FILE_WATCHER: "true"
      DOTNET_WATCH_SUPPRESS_LAUNCH_BROWSER: "true"
    volumes:
      # 挂载源代码目录，支持热重载
      - ./BackEnd:/src/BackEnd:rw
      - ./FrontEnd:/src/FrontEnd:rw
      - ./Directory.Build.props:/src/Directory.Build.props:ro
      - ./NuGet.Config:/src/NuGet.Config:ro
      - ./.editorconfig:/src/.editorconfig:ro
      # 日志目录
      - ./logs/customer:/app/logs:rw
      # NuGet缓存
      - nuget-cache:/root/.nuget/packages
    ports:
      - "7040:7040"    # HTTP
      - "7041:7041"    # HTTPS
    depends_on:
      whimlab-api-dev:
        condition: service_healthy
    networks:
      - whimlab-network
    stdin_open: true
    tty: true
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7040/health"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 60s

  # 开发数据库GUI工具
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: whimlab-pgadmin-dev
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    ports:
      - "5050:80"
    depends_on:
      - postgres
    networks:
      - whimlab-network

  # Redis GUI工具
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: whimlab-redis-commander-dev
    restart: unless-stopped
    environment:
      REDIS_HOSTS: local:redis:6379:0:redis123
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - whimlab-network

  # 开发环境添加MailHog用于邮件测试
  mailhog:
    image: mailhog/mailhog:latest
    container_name: whimlab-mailhog-dev
    ports:
      - "1025:1025"  # SMTP端口
      - "8025:8025"  # Web UI
    networks:
      - whimlab-network

volumes:
  postgres_dev_data:
  redis_dev_data:
  rabbitmq_dev_data:
  minio_dev_data:
  nuget-cache:
  pgadmin_data:

networks:
  whimlab-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16