# WhimLabAI Port Configuration

## Standardized Port Assignments

### Backend Services
- **WebAPI**: 
  - HTTP: `http://localhost:5000`
  - HTTPS: `https://localhost:5001`
  - Swagger: `http://localhost:5000/swagger`

### Frontend Applications
- **Admin Portal**:
  - HTTP: `http://localhost:7216`
  - HTTPS: `https://localhost:7217`

- **Customer Web Portal**:
  - HTTP: `http://localhost:7040`
  - HTTPS: `https://localhost:7041`

### API Configuration
All frontend applications are configured to connect to the WebAPI at:
- Development: `http://localhost:5000`
- MAUI Android Emulator: `http://********:5000`

## Configuration Files Updated

### Admin Frontend
- `/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin/Properties/launchSettings.json`
- `/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin/appsettings.json`
- `/FrontEnd/Admin/WhimLabAI.Client.Admin/WhimLabAI.Client.Admin/wwwroot/appsettings.json`

### Customer Frontend
- `/FrontEnd/Customer/WhimLabAI.Client.Customer/WhimLabAI.Client.Customer.Web/Properties/launchSettings.json`
- `/FrontEnd/Customer/WhimLabAI.Client.Customer/WhimLabAI.Client.Customer.Web/appsettings.json`
- `/FrontEnd/Customer/WhimLabAI.Client.Customer/WhimLabAI.Client.Customer.Web/wwwroot/appsettings.json`
- `/FrontEnd/Customer/WhimLabAI.Client.Customer/WhimLabAI.Client.Customer/MauiProgram.cs`

## Notes
- All frontend applications connect to the WebAPI on port 5000 for development
- The WebAPI runs on HTTP by default to avoid SSL certificate issues in development
- For production, use environment-specific configuration or relative paths